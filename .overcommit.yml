# Use this file to configure the Overcommit hooks you wish to use. This will
# extend the default configuration defined in:
# https://github.com/brigade/overcommit/blob/master/config/default.yml
#
gemfile: .overcommit_gems.rb

# Where to store hook plugins specific to a repository. These are loaded in
# addition to the default hooks Overcommit comes with. The location is relative
# to the root of the repository.
plugin_directory: ".git-hooks"

# Whether to check if a hook plugin has changed since Overcommit last ran it.
# This is a defense mechanism when working with repositories which can contain
# untrusted code (e.g. when you fetch a pull request from a third party).
# See https://github.com/brigade/overcommit#security for more information.
verify_signatures: true

CommitMsg:
  ALL:
    requires_files: false
    quiet: false

  EmptyMessage:
    enabled: true
    description: "Checking for empty commit message"
    quiet: true

  CapitalizedSubject:
    enabled: false

  HardTabs:
    enabled: false
    description: "Checking for hard tabs"
    quiet: true

  RussianNovel:
    enabled: false
    description: "Checking length of commit message"
    quiet: true

  SingleLineSubject:
    enabled: true
    description: "Checking subject line"
    quiet: true

  TextWidth:
    enabled: true
    description: "Checking text width"
    max_subject_width: 60
    max_body_width: 72
    quiet: true

  TrailingPeriod:
    enabled: true
    description: "Checking for trailing periods in subject"
    quiet: true

PreCommit:
  ALL:
    problem_on_unmodified_line: report
    requires_files: true
    required: false
    quiet: true

  AuthorEmail:
    enabled: false
    description: "Checking author email"
    requires_files: false
    required: true
    quiet: true
    pattern: "^[^@]+@.*$"

  BrokenSymlinks:
    enabled: true
    description: "Checking for broken symlinks"
    quiet: true

  BundleCheck:
    enabled: true
    description: "Checking Gemfile dependencies"
    required_executable: "bundle"
    flags: ["check", "--gemfile", ".overcommit_gems.rb"]
    install_command: "gem install bundler"
    include:
      - ".overcommit_gems.rb"
      - ".overcommit_gems.rb.lock"

  AuthorName:
    enabled: false
    description: "Checking for author name"
    requires_files: false
    required: true
    quiet: true

  CaseConflicts:
    enabled: false
    description: "Check for case-insensitivity conflicts"
    quiet: true

  CaseConflicts2:
    enabled: true
    description: "Check for case-insensitivity conflicts"
    quiet: false

  MergeConflicts:
    enabled: true
    description: "Checking for merge conflicts"
    quiet: true
    required_executable: "grep"
    flags: ["-IHn", "^<<<<<<<[ \t]"]

  Scalastyle:
    enabled: false
    quiet: false
    description: "Analyzing with Scalastyle"
    required_executable: "java"
    command:
      ["project/tools/scalastyle.sh", "-c", "project/scalastyle_config.xml"]
    include:
      - "project/*.scala"
      - "platform/**/src/*/scala/**/*.scala"
      - "gondor/**/src/*/scala/**/*.scala"
      - "apps/**/src/*/scala/**/*.scala"
      - "modules/**/src/*/scala/**/*.scala"

  Scalafmt:
    enabled: true
    quiet: false
    description: "Analyzing with Scalafmt"
    command:
      [
        "project/tools/scalafmt.sh",
        "--list",
        "--git",
        "true",
        "--exclude",
        "target",
      ]
    include:
      - "project/*.scala"
      - "platform/**/src/*/scala/**/*.scala"
      - "gondor/**/src/*/scala/**/*.scala"
      - "apps/**/src/*/scala/**/*.scala"
      - "modules/**/src/*/scala/**/*.scala"

  ScalaLocation:
    enabled: true
    description: "Ensure scala code is only under certain directories"
    include:
      - "build/**/*.scala"
      - "gondor/**/*.scala"
      - "modules/**/*.scala"
      - "office-add-in/**/*.scala"
      - "app/**/*.scala"
      - "platform/**/*.scala"
      - "build/**/*.sbt"
      - "project/**/*.sbt"
      - "build/**/*.sbt"
      - "gondor/**/*.sbt"
      - "modules/**/*.sbt"
      - "office-add-in/**/*.sbt"
      - "app/**/*.sbt"
      - "platform/**/*.sbt"
    exclude:
      - "build.sbt"

  Scalafix:
    enabled: false
    quiet: false
    description: "Analyzing with Scalafix syntatically"
    required_executable: "java"
    command:
      [
        "./project/tools/scalafix-cli",
        "-J-Xmx1G",
        "--syntactic",
        "--check",
        "--exclude",
        "**/target/scala-2.12/src_managed/**/*.scala",
        "--config",
        ".scalafix.conf",
        "--files",
      ]
    include:
      - "platform/**/src/*/scala/**/*.scala"
      - "gondor/**/src/*/scala/**/*.scala"
      - "apps/**/src/*/scala/**/*.scala"
      - "modules/**/src/*/scala/**/*.scala"

  FixMe:
    enabled: true
    quiet: true
    include:
      - "**/*.scala"
      - "**/*.sbt"
      - "**/*.java"
      - "**/*.sh"
      - "**/*.scss"

  ShellCheck:
    enabled: false
    quiet: true

  ExecutePermissions:
    enabled: true
    exclude:
      - "project/**/*.sh"
      - "project/tools/coursier"
      - "project/tools/scalafix"
      - "project/tools/scalafmt/**/scalafmt"
      - "project/tools/scalafix-cli"
      - "sbt"
      - "sbt-repl"
      - "deployments/**/*.sh"
      - "docker/compose/integration/run.sh"
      - "docker/compose/integration-couchbase/run.sh"
      - "docker/compose/production/run.sh"
      - "docker/compose/unit/run.sh"
      - "docker/compose/release/run.sh"
      - "project/tools/rename_files.pl"
      - "project/tools/scalafmt-1.6.0-Rc4-cli"
      - "project/tools/scalastyle.sh"
      - "ci/scripts/**/*.sh"
      - "ci/docker-images/**/scripts/*.sh"
      - "ci/docker-images/stargazer/run.sh"
      - "ci/apps/app-configs/test-build/override-host.sh"
      - "serverless/**/*.sh"
      - ".yarn/releases/*.*"
      - "deepspaces/graphql-analyzer/proto/update_bindings.sh"
      - "project/tools/fdb/*.*"
      - "millw"

  HardTabs:
    enabled: true
    exclude:
      - "gondor/jvm/src/it/resources/rdfs/*.rdf"
      - "modules/ontology/ontology/jvm/src/resources/ontology/fibo/*.rdf"
      - "modules/evendim/evendimModel/jvm/src/main/graphql/datalake.graphql"
      - "gondor/gondorJsDependency/src/main/resources/external/**/*"
      - "gondor/webResources/src/main/public/fonts/**/*"
      - "keycloak/webResources/src/main/public/common/resources/fonts/**/*"
      - "office-add-in/js/src/main/resources/js/**/*"
      - "deployments/assets/docker-images/keycloak-sidecar/keycloak-init/**"
      - "deployments/assets/docker-images/sync-gateway-sidecar/init-sync-gateway/**"
      - "project/tools/tinyproxy.conf"
      - "ci/docker-images/keycloak-sidecar/keycloak-init/**"
      - "ci/docker-images/sync-gateway-sidecar/init-sync-gateway/**"
      - "ci/docker-images/tidb-changefeed/**"
      - "serverless/**/*"
      - ".yarn/releases/**"
      - ".yarn/plugins/@yarnpkg/**"
      - "**/*.enc.yml"
      - "gondor/gondorWebResources/public/css/monaco/monaco.min.css"
      - "deepspaces/graphql-analyzer/Makefile"
      - "deepspaces/graphql-analyzer/proto/**/*"
      - "deepspaces/graphql-analyzer/schemas/**/*"
      - "modules/evendim/evendimModel/jvm/src/main/graphql/Makefile"
      - "millw"

  TrailingWhitespace:
    enabled: false
    description: "Checking for trailing whitespace"
    required_executable: "grep"
    flags: ["-IHn", "[ \t]$"]
    exclude:
      - "gondor/gondorJsDependency/src/main/resources/external/**/*"

  YamlSyntax:
    enabled: true
    description: "Checking YAML syntax"
    required_library: "yaml"
    include:
      - "**/*.yaml"
      - "**/*.yml"
    exclude:
      - "deployments/assets/**/*.yaml"
      - "deployments/assets/**/*.yml"
      - "ci/**/*.yaml"
      - "ci/**/*.yml"
      - "serverless/**/*.yml"
#PostCheckout:
#  ALL: # Special hook name that customizes all hooks of this type
#    quiet: true # Change all post-checkout hooks to only display output on failure
#
#  IndexTags:
#    enabled: true # Generate a tags file with `ctags` each time HEAD changes
