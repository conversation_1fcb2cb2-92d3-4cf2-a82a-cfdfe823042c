# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Mill Configuration & Setup

### Mill Version & Wrapper Script
- **Always use `./mill`** instead of `mill` - this uses the wrapper script that ensures correct version
- **Build file**: `build.mill`
- **JVM Options**: Configured in `.mill-jvm-opts`

### Key Configuration Files
- `.mill-version` - Mill version specification
- `.mill-jvm-opts` - JVM memory and GC settings for Mill
- `build.mill` - Main build configuration
- `mill-build/build.mill` - Mill build module configuration

## Code Style and Language Guidelines

### General

* Scala 3 syntax only.
* No `null`: model absence with `Option`.
* Prefer `val` / immutability. Avoid `var`, shared mutable state, and side effects in pure code.
* Public API types explicit. Let local types infer; annotate return/field/constructor/public method types.

### Types & Safety

* Avoid `Any`/`isInstanceOf`/`asInstanceOf`: use precise types and pattern matching.
* Use ADTs: prefer `enum`/`case class`/`case object` over inheritance hierarchies.
* Union/Intersection: use `A | B`/`A & B` sparingly; when modeling domain alternatives, **prefer `enum`**.
* Opaque types for zero‑overhead newtypes:
  ```scala
  opaque type UserId = String
  object UserId: def apply(s: String): UserId = s
  ```

### Context & Typeclasses (replaces implicits)

* `given` / `using`: define instances with `given`; request them with `using`.

  ```scala
  trait Show[A]: def show(a: A): String
  given Show[Int] with def show(i: Int) = i.toString
  def render[A](a: A)(using s: Show[A]) = s.show(a)
  val s = summon[Show[Int]] // prefer over implicitly
  ```
* Derivation: use `derives` for standard typeclasses (e.g., `CanEqual`, `Ordering`, `Mirror`‑based libs):

  ```scala
  enum Color derives CanEqual: case Red, Green, Blue
  ```
* Avoid implicit conversions. If unavoidable, use `given Conversion[A, B]` locally and narrowly.

### Enums & ADTs

* Use `enum` for sum types (with parameters, methods, and companions):

  ```scala
  enum Result[+A, +E]:
    case Ok(a: A)
    case Err(e: E)
  ```
* Pattern matching must be exhaustive (enable `-Xfatal-warnings`/`-Werror` in CI to enforce).

### Errors & Effects

* No exceptions for control flow: use `Option`/`Either`/`Try` (or your effect type).
* Side effects at the edges: keep domain logic pure; isolate I/O.

### Imports & Packages

* **Wildcard is `*` in Scala 3** (`import foo.*`).
* Use selective/renamed imports; avoid `_*` legacy and package objects (prefer top‑level defs).

### Match Types (advanced; apply sparingly)

* Employ when type‑level computation removes casts/duplication; otherwise prefer normal polymorphism.

  ```scala
  type Elem[X] = X match
    case List[t] => t
    case _       => X
  ```

### Minimal Scala Idioms

```scala
// ADT
enum Shape: case Circle(r: Double), Rect(w: Double, h: Double)

// Exhaustive match
def area(s: Shape): Double = s match
  case Shape.Circle(r)   => Math.PI * r * r
  case Shape.Rect(w, h)  => w * h

// Context params
trait Clock: def now: Long
given Clock with def now = System.currentTimeMillis()
def timed[A](thunk: => A)(using c: Clock) = (thunk, c.now)

// Extension
extension (xs: List[Int]) def meanOption: Option[Double] =
  if xs.isEmpty then None else Some(xs.sum.toDouble / xs.length)

// Opaque newtype
opaque type Email = String
object Email:
  def apply(s: String): Option[Email] =
    Option(s).filter(_.contains("@")).map(_.trim)
```

## Essential Commands

### Core Mill Commands

#### Compilation Commands
```bash
# Compile all modules
./mill __.compile

# Compile specific module
./mill modules.fundsub.fundsub.jvm.compile
./mill gondor.gondorCore.jvm.compile
./mill itools.olympian.jvm.compile

# Check code style (scalafmt + scalafix)
./mill __.checkStyle
./mill __.checkStyleCached  # Use cached results when possible

# Reformat code
./mill __.reformat

# Clean all build artifacts
./mill clean
```
#### Test Commands

##### Unit Tests
```bash
# Run all unit tests
./mill __.test.test

# Run unit tests for specific module
./mill modules.fundsub.fundsub.jvm.test.test
./mill gondor.gondorCore.jvm.test.test

# Run specific unit test class
./mill modules.fundsub.fundsub.jvm.test.testOnly "com.anduin.fundsub.FundsubServiceTestSpec"
./mill gondor.gondor.jvm.test.testOnly "*FileServiceTestSpec"
```

##### Integration Tests
```bash
# Run all integration tests (cached)
./mill __.it.testCached

# Run integration tests for specific module
./mill modules.fundsub.fundsub.jvm.it.testCached
./mill itools.olympian.jvm.it.testCached

# Run specific integration test class
./mill __.fundsub.fundsub.jvm.it.testOnly "*FundsubServiceInteg"
./mill __.gondorCore.jvm.it.testOnly "*FileServiceInteg"
./mill __.olympian.jvm.it.testOnly "*ExportJobServiceInteg"
```

##### Multi-Region Tests
```bash
# Run multi-region integration tests
./mill __.multiregionit.testCached
./mill gondor.gondor.jvm.multiregionit.testCached
```

#### Utility Commands
```bash
# Show all available commands (warning: very long output)
./mill resolve

# Show module dependencies
./mill visualizeDeps

# Install JavaScript dependencies
./mill workspaceJsPackageInstall
```

### Client Building & Development

#### Development Client Building
```bash
# Build all clients in development mode
./mill devBuildAllClients

# Build all clients except Fundsub
./mill devBuildAllClientsExceptFundsub

# Build web resources first (required before client builds)
./mill devBuildWebResources

# Build specific clients (preferred for large codebase)
./mill devBuildFundsubClient
./mill devBuildDataExtractClient
./mill devBuildSignatureClient
./mill devBuildInvestorProfileClient
./mill devBuildDataroomClient
./mill devBuildFundDataClient
./mill devBuildPantheonClient
./mill devBuildItoolsClient
./mill devBuildNaryaClient
./mill devBuildMayaClient
./mill devBuildGondorClient
./mill devBuildRiaClient
./mill devBuildHeimdallClient
./mill devBuildIntegPlatformClient
```

#### AI Agent Build
Use the following to bypass linter checks during agent execution:
```bash
ANDUIN_BUILD_ENV=agent ./mill ...
```
This allows the AI agent to prioritize core logic implementation without incurring overhead from linter-related tool calls.

#### Production Client Building
```bash
# Build all clients in production mode
./mill prodBuildAllClients

# Build specific clients for production
./mill prodBuildFundsubClient
./mill prodBuildDataExtractClient
# ... (same pattern as dev builds)
```

### Server & Application Management

#### Development Server
```bash
# Start development server
./mill startDevServer

# Start server in background
./mill reStart

# Stop background server
./mill reStop
```

#### Database & Migration Commands
```bash
# Run database migrations
./mill runMigration

# Run hotfix migrations
./mill runHotfixMigration

# Initialize test data for integration tests
./mill initTestData
```

#### Application Utilities
```bash
# Generate endpoint report
./mill runEndpointReport

# Package application for deployment
./mill apps.gondor.gondorAppServer.universalPackageZip
```

## Performance & Optimization

### Parallel Execution
```bash
# Use -j flag for parallel execution
./mill -j 4 __.compile          # Compile with 4 parallel jobs
./mill -j 2 __.test.test         # Run tests with 2 parallel jobs
./mill -j 4 __.checkStyleCached  # Style check with 4 parallel jobs
```

### Memory Configuration
The codebase uses different JVM configurations for different scenarios:
- **Development**: 16GB heap (`.mill-jvm-opts`)
- **CI Build**: 20-22GB heap (`ci/scripts/build/jvmopts/`)
- **Tests**: 16GB heap with specific GC tuning

### Selective Execution
```bash
# Use cached results when possible
./mill __.testCached
./mill __.checkStyleCached

# Selective test execution (used in CI)
./mill selective.prepare __.testCached
./mill selective.run __.testCached
```

## Module Structure & Organization

### Top-Level Module Organization
```
build/          - Build configuration and utilities
platform/       - Core platform modules (stargazer, stargazerCore, stargazerModel, etc.)
modules/        - Business logic modules (bifrost, dataextract, fundsub, gaia, etc.)
gondor/         - Gondor application modules (gondor, gondorCore, gondorModel, etc.)
itools/         - Internal tools (olympian, pantheon)
apps/           - Application modules (maya, gondor)
js/             - JavaScript/frontend modules
```

### Module Path Mapping
Directory structure maps to Mill module paths using dot notation:
- `modules/fundsub/` → `modules.fundsub`
- `gondor/gondorCore/` → `gondor.gondorCore`
- `itools/olympian/` → `itools.olympian`
- `platform/stargazer/` → `platform.stargazer`

### Module-Specific Patterns

#### Business Logic Modules (modules/*)
- **Pattern**: `modules.{moduleName}.{subModule}.{platform}`
- **Examples**: 
  - `modules.fundsub.fundsub.jvm`
  - `modules.dataextract.dataextractCore.js`
  - `modules.gaia.gaia.jvm.it`

#### Platform Modules (platform/*)
- **Pattern**: `platform.{moduleName}.{platform}`
- **Examples**:
  - `platform.stargazer.jvm`
  - `platform.stargazerCore.js`
  - `platform.stargazerModel.jvm`

#### Application Modules (apps/*, gondor/*, itools/*)
- **Pattern**: `{topLevel}.{moduleName}.{platform}`
- **Examples**:
  - `apps.maya.mayaApp.js`
  - `gondor.gondorCore.jvm`
  - `itools.olympian.jvm.it`

## Architecture Overview

### Core Module Structure

**Platform Modules** (`platform/`): Core infrastructure shared across all applications
- `stargazer/` - Main platform module with JVM/JS cross-compilation
- `stargazerCore/` - Core utilities and shared functionality
- `stargazerId/` - Type-safe ID management system
- `stargazerModel/` - Shared data models and protobuf definitions
- `stargazerConfig/` - Configuration management
- `stargazerTest/` - Testing utilities and shared test infrastructure

**Business Modules** (`modules/`): Domain-specific functionality
- Financial: `fundsub/`, `fundData/`, `investorProfile/`, `ria/`
- Document management: `dataroom/`, `dataextract/`, `signature/`
- Identity/Access: `heimdall/`, `amlKyc/`
- Tools: `dynamicForm/`, `gaia/`, `ontology/`, `integplatform/`
- Communication: `narya/`, `evendim/`, `greylin/`
- Data: `graphql/`, `bifrost/`

**Applications** (`apps/`, `gondor/`, `itools/`):
- `gondor/` - Main financial services web platform
- `maya/` - Secondary application
- `olympian/` - Internal administrative tools
- `pantheon/` - Form builder and management

### Cross-Platform Pattern

Each module follows a consistent structure:
```
{module}/
├── js/                    # Scala.js frontend code
├── jvm/                   # JVM backend code  
├── shared/                # Code shared between platforms
├── package.mill           # Build configuration
└── dependency.mill        # Dependencies
```

**Shared Code**: Common models, protocols, and utilities in `shared/src/`
**Platform-Specific**: UI components in `js/src/`, server logic in `jvm/src/`
**Testing**: Both platform-specific and shared tests supported

### Technology Stack

**Backend (JVM)**:
- Scala 3 with ZIO for async programming
- Tapir for HTTP endpoints and API definitions
- FoundationDB for data storage with custom ZIO integration
- PostgreSQL for relational data
- Temporal workflows for long-running processes
- Kafka for event streaming

**Frontend (JS)**:
- Scala.js for type-safe client-side code
- Laminar for reactive DOM manipulation
- Airstream for reactive programming
- PostCSS for styling
- Yarn workspaces for dependency management

**Cross-Platform**:
- Protobuf for serialization between JVM and JS
- Mill build system with custom plugins
- Shared domain models and validation logic

### Key Entry Points

**Main Applications**:
- Gondor server: `com.anduin.stargazer.apps.stargazer.GondorServiceApp`
- Migration runner: `com.anduin.stargazer.apps.stargazer.GondorMigrationApp`
- Test initializer: `com.anduin.stargazer.apps.stargazer.GondorTestInitializer`

**Client Applications**:
- Heimdall (auth): Login/signup functionality
- Fundsub: Fund subscription management
- Dataroom: Document sharing and management
- Signature: E-signature workflows
- Pantheon: Dynamic form building
- Olympian: Internal administrative tools

### Important Notes

- **Large Codebase**: Prefer building individual clients over `devBuildAllClients`
- **Cross-Platform**: Most business logic is shared between JVM and JS
- **Mill Build System**: Uses custom plugins for Scala.js and cross-compilation
- **Hot Reloading**: Background server mode (`reStart`) allows continued development
- **Protobuf**: Extensive use for type-safe serialization between frontend and backend
- **Configuration**: Centralized config management through `stargazerConfig` module

## Troubleshooting

#### Test Failures
```bash
# Run tests with more verbose output
./mill modules.fundsub.fundsub.jvm.test.test -v

# Run single test for debugging
./mill __.fundsub.fundsub.jvm.it.testOnly "*SpecificTestInteg" -v
```

### Performance Issues
- Use `-j` flag for parallel execution
- Use `*Cached` variants when available
- Consider selective execution for large test suites

## Output Directory Structure

All build artifacts are stored in the `out/` directory:
- `out/modules/` - Module-specific build outputs
- `out/platform/` - Platform module outputs  
- `out/gondor/` - Gondor module outputs
- `out/mill-profile.json` - Build performance profile
- `out/mill-dependency-tree.json` - Dependency information

For detailed output structure, see: https://mill-build.com/mill/Out_Dir.html

## Additional Documentation
Refer to the documentations before for specific instructions on how to implement certain tasks:
- **[Build with Mill Guide](build-with-mill-guide.llms.txt)**: Comprehensive guide for building, testing, and deploying the codebase using Mill build tool.
- **[Protobuf Serialization Guide](protobuf-serialization-implementation-guide.llms.txt)**: Best practices for designing and implementing protobuf messages and serialization patterns.
- **[Tapir Endpoint Guide](tapir-endpoint-implementation-guide.llms.txt)**: Patterns for defining and implementing Tapir endpoints with proper authentication, validation, and error handling.
- **[Temporal Workflow Guide](temporal-workflow-implementation-guide.llms.txt)**: Best practices for designing and implementing Temporal workflows and activities.
- **[Frontend React/Laminar Guide](frontend-react-laminar-implementation-guide.llms.txt)**: Patterns for building frontend components with React and Laminar.
- **[Configuration Management Guide](configuration-management-implementation-guide.llms.txt)**: Patterns for loading, validating, and managing application configuration.
- **[FoundationDB Guide](foundationdb-implementation-guide.llms.txt)**: Best practices for using FoundationDB with the codebase's ZIO integration.

