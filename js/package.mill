package build.js

import mill.*
import anduin.build.*
import anduin.mill.*
import anduin.mill.jsdeps.*
import anduin.mill.utils.*

object `package` extends Module {

  object anduinBootstrap extends AnduinScalaJSModule with ESBuildScalaJSApp with AnduinBuildEnv {

    private def sourceFile = Task.Source(moduleDir / "src" / "AnduinBootstrap.js")

    private def destPath = Task {
      mill.api.BuildCtx.withFilesystemCheckerDisabled {
        val path = clientDirectory() / "js" / "bootstrap"
        os.makeDir.all(path)
        path / s"anduinbootstrap${jsResourceSuffix()}.js.gz"
      }
    }

    override def bundleParams = BundleParams(
      platform = "browser",
      target = "es2020",
      extraFlags = Seq(
        "--format=iife",
        "--global-name=AnduinBootstrap"
      )
    )

    override def jsSpecs = super.jsSpecs().copy(name = "anduin-bootstrap")

    override def jsPackageInstall = build_.package_.workspaceJsPackageInstall

    private def buildClient(isFullLink: Boolean) = Task.Anon {
      mill.api.BuildCtx.withFilesystemCheckerDisabled {
        val bundlePath = bundle()(
          PathRef(sourceFile().path / os.up),
          sourceFile().path.baseName,
          isFullLink
        )
        IOUtils.gzip()(bundlePath.path, destPath())
        PathRef(destPath())
      }
    }

    def fastBuildClient = Task {
      buildClient(false)()
    }

    def fullBuildClient = Task {
      buildClient(true)()
    }

  }

}
