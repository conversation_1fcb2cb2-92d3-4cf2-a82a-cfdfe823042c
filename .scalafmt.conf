version = 3.9.8

runner.dialect = scala3

project.git = true
style=default

maxColumn = 120

align.preset = some
align.openParenCallSite = false
align.openParenDefnSite = false

newlines.topLevelStatements = [before,after]
newlines.alwaysBeforeElseAfterCurlyIf = false
newlines.implicitParamListModifierForce = [before]
newlines.penalizeSingleSelectMultiArgList = false
newlines.avoidForSimpleOverflow = [punct]

verticalMultiline.atDefnSite = true
verticalMultiline.newlineAfterOpenParen = true
danglingParentheses.exclude = []

optIn.configStyleArguments = true

runner.optimizer.forceConfigStyleOnOffset = 70
runner.optimizer.forceConfigStyleMinArgCount = 3

continuationIndent.defnSite = 2
continuationIndent.callSite = 2
continuationIndent.ctorSite = 2

danglingParentheses.defnSite = true
danglingParentheses.callSite = true

assumeStandardLibraryStripMargin = true
runner.optimizer.maxVisitsPerToken = 100000
