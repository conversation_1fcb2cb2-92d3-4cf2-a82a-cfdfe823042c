name: "[Release] Feature"

on:
  push:
    branches:
      - release-mordor

concurrency:
  group: "${{ github.workflow }}-${{ github.ref }}"
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  release:
    uses: anduintransaction/gondolin/.github/workflows/stargazer-release.yml@master
    secrets: inherit
    with:
      build-env: canary
      artifact-folder: stargazer-features
      image-prefix: stargazer-features
      slack-notify: false
