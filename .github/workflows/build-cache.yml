name: Build Cache

on:
  workflow_dispatch:
    inputs:
      ref:
        required: false
        type: string
      use-cache:
        description: "Build cache using previous-built cache or not"
        required: true
        type: choice
        default: false
        options:
          - true
          - false
  push:
    branches:
      - master
      - staging
      - release
      - remote-cache
      
permissions:
  contents: read
  packages: read
  checks: write

jobs:
  build-with-cache:
    uses: anduintransaction/gondolin/.github/workflows/stargazer-build-cache.yml@master
    secrets: inherit
    with:
      ref: ${{ inputs.ref || github.sha }}
      use-cache: ${{ inputs.use-cache || 'true' }}

  build-without-cache:
    needs: build-with-cache
    if: always() && needs.build-with-cache.result == 'failure'
    uses: anduintransaction/gondolin/.github/workflows/stargazer-build-cache.yml@master
    with:
      ref: ${{ inputs.ref || github.sha }}
      use-cache: ${{ inputs.use-cache || 'false' }}
