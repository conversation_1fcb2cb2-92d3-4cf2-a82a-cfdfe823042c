name: "[Infra Test - Release] - Remote caching"

on:
  push:
    branches:
      - remote-cache

concurrency:
  group: "${{ github.workflow }}-${{ github.ref }}"
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  release:
    uses: anduintransaction/gondolin/.github/workflows/stargazer-release.yml@master
    secrets: inherit
    with:
      build-env: canary
      artifact-folder: stargazer-pr
      image-prefix: stargazer-pr
      slack-notify: false
