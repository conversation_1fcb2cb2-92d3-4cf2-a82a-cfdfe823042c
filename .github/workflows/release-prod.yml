name: "[Release] Production"

on:
  push:
    branches:
      - release

concurrency:
  group: "${{ github.workflow }}-${{ github.ref }}"
  cancel-in-progress: false

permissions:
  contents: read

jobs:
  release:
    uses: anduintransaction/gondolin/.github/workflows/stargazer-release.yml@master
    secrets: inherit
    with:
      build-env: release
      artifact-folder: stargazer-hotfix
      image-prefix: stargazer-hotfix
      slack-notify: true
      environment: release
