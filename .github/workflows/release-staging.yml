name: "[Release] Staging"

on:
  push:
    branches:
      - staging

concurrency:
  group: "${{ github.workflow }}-${{ github.ref }}"
  cancel-in-progress: false

permissions:
  contents: read

jobs:
  release:
    uses: anduintransaction/gondolin/.github/workflows/stargazer-release.yml@master
    secrets: inherit
    with:
      build-env: release
      artifact-folder: stargazer-releases
      image-prefix: stargazer-releases
      slack-notify: true
      environment: staging
