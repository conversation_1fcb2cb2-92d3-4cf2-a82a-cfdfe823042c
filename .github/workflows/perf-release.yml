name: "[Performance test] Release"

run-name: "Branch ${{ inputs.ref || 'release' }}"

concurrency:
  group: "${{ github.workflow }}-${{ inputs.ref || 'release' }}"
  cancel-in-progress: true

on:
  workflow_dispatch:
    inputs:
      ref:
        required: true
        type: string
        default: release
  schedule:
    - cron: "00 22 * * *"

permissions:
  contents: read
  checks: write
  packages: read

jobs:
  test:
    uses: anduintransaction/gondolin/.github/workflows/stargazer-perf-test.yml@master
    secrets: inherit
    with:
      ref: "${{ inputs.ref || 'release' }}"
