name: Remove migration

concurrency:
  group: remove-migration
  cancel-in-progress: true

on:
  workflow_dispatch: {}
  schedule:
    - cron: "17 5 * * 3"

permissions:
  contents: write
  pull-requests: write

jobs:
  purge:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"

      - name: Retrieve tag
        run: |
          echo "SPRINT_TAG=$(git describe --tags --abbrev=7)" >> $GITHUB_ENV

      - name: Remove migration
        uses: anduintransaction/gondolin/actions/prbot@master
        with:
          sprint-tag: "${{ env.SPRINT_TAG }}"

      - name: Create PR
        uses: peter-evans/create-pull-request@v5
        with:
          title: "chore(prbot): remove migration"
          commit-message: "chore: remove migration"
          branch: prbot/remove-migration
          branch-suffix: short-commit-hash
          delete-branch: true
          reviewers: vanhtuan0409,keimoon
          labels: |
            prbot/migration
