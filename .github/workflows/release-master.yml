name: "[Release] Master"

on:
  push:
    branches:
      - master

concurrency:
  group: "${{ github.workflow }}-${{ github.ref }}"
  cancel-in-progress: false

permissions:
  contents: read

jobs:
  release:
    uses: anduintransaction/gondolin/.github/workflows/stargazer-release.yml@master
    secrets: inherit
    with:
      build-env: canary
      artifact-folder: stargazer-canary
      image-prefix: stargazer-canary
      slack-notify: false
      environment: master
