name: CI

on:
  pull_request:
    types:
      - opened
      - synchronize
      - reopened
      - ready_for_review
      - labeled
      - unlabeled
    branches:
      - master
      - release
      - staging

concurrency:
  group: "${{ github.workflow }}-${{ github.ref }}"
  cancel-in-progress: true

permissions:
  contents: read
  packages: read
  checks: write
  pull-requests: write

jobs:
  secret-scan:
    uses: anduintransaction/gondolin/.github/workflows/stargazer-secret-scan.yml@master
    secrets: inherit

  config-check:
    uses: anduintransaction/gondolin/.github/workflows/stargazer-config-check.yml@master
    secrets: inherit

  migration-freeze:
    uses: anduintransaction/gondolin/.github/workflows/stargazer-migration-freeze.yml@master
    secrets: inherit

  endpoint-report:
    if: ${{ !contains(github.event.pull_request.title , 'wip') && !github.event.pull_request.draft && contains(github.event.pull_request.labels.*.name, 'Endpoint/Report')}}
    uses: anduintransaction/gondolin/.github/workflows/stargazer-endpoint-report.yml@master
    secrets: inherit

  build:
    if: ${{ !contains(github.event.pull_request.title , 'wip') && !github.event.pull_request.draft }}
    needs:
      - secret-scan
      - config-check
    uses: anduintransaction/gondolin/.github/workflows/stargazer-build-pr.yml@master
    secrets: inherit

  test:
    if: ${{ !contains(github.event.pull_request.title , 'wip') && !github.event.pull_request.draft }}
    needs:
      - build
    uses: anduintransaction/gondolin/.github/workflows/stargazer-unit-test.yml@master
    secrets: inherit

  integ-test:
    if: ${{ !contains(github.event.pull_request.title , 'wip') && !github.event.pull_request.draft }}
    needs:
      - build
    uses: anduintransaction/gondolin/.github/workflows/stargazer-integ-test.yml@master
    secrets: inherit
