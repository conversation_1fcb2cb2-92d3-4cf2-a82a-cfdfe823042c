name: "[Performance test] Master"

run-name: "Branch ${{ inputs.ref || 'master' }}"

concurrency:
  group: "${{ github.workflow }}-${{ inputs.ref || 'master' }}"
  cancel-in-progress: true

on:
  workflow_dispatch:
    inputs:
      ref:
        required: true
        type: string
        default: master
  schedule:
    - cron: "00 21 * * *"

permissions:
  contents: read
  checks: write
  packages: read

jobs:
  test:
    uses: anduintransaction/gondolin/.github/workflows/stargazer-perf-test.yml@master
    secrets: inherit
    with:
      ref: "${{ inputs.ref || 'master' }}"
