name: LLM Auto Review

on:
  issue_comment:
    types: [created]

permissions:
  contents: read
  pull-requests: write
  issues: write

jobs:
  auto-review:
    if: ${{ github.event.issue.pull_request && (contains(github.event.comment.body, '/g air') || contains(github.event.comment.body, '/g aia')) || contains(github.event.comment.body, '/g ai-approve') || contains(github.event.comment.body, '/g ai-review') }}
    runs-on: ubuntu-latest
    steps:
      - name: Use auto review
        uses: anduintransaction/gh-pr-ai-review@v1.5.0
        with:
          github-token: ${{ github.token }}
          llm-api-key: ${{ secrets.LLM_API_KEY }}
          llm-model: "openai/us.anthropic.claude-sonnet-4-20250514-v1:0"
          llm-base-url: "https://litellm.anduin.center"
          max-diff-size: "300000"
          include-files: "true" # Improve review quality, but increase cost
          api-timeout: "180"
          repo-review-instruction-file: "wikis/llms-txts/pr-review-guide.llms.txt"
          auto-approve-enabled: ${{ contains(github.event.comment.body, '/g aia') || contains(github.event.comment.body, '/g ai-approve') }}
          auto-approve-label: "auto-approve"
          approval-rating-threshold: "High"
          block-approval-on-security-issues: "true"
          block-approval-on-performance-issues: "false"
