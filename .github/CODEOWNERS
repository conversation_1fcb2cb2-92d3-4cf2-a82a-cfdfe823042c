gondor/jvm/src/main/scala/com/anduin/stargazer/service/database/migration/MigrationManager.scala @keimoon
deployments/* @keimoon
Dockerfile @keimoon
gondor/js/src/main/scala/com/anduin/stargazer/client/modules/routing/RedirectJsService.scala @keimoon
gondor/js/src/main/scala/com/anduin/stargazer/client/modules/routing/IdleTracking.scala @keimoon
modules/heimdall @keimoon

apps/gondor/gondorAppClient/webpack/yarn.lock @keimoon @ngbinh
modules/narya/narya/js/yarn.lock @keimoon @ngbinh

# Front End squad
platform/stargazerJs/ @nghuuphuoc
platform/stargazerJSDependency/ @nghuuphuoc

itools/olympian/olympian.sbt @flashmt
modules/signature/signature.sbt @flashmt

modules/cms/cms/jvm/src/main/scala/com/anduin/stargazer/service/ontology @trancuong81 @dxquang
modules/issuetracker/issuetracker/jvm @trancuong81 @dxquang

gondor/gondorModel/shared/src/anduin/cue/model/*.scala @trancuong81
modules/fundsub/fundsubModel/shared/src/anduin/fundsub/cue/model @trancuong81

modules/fundData @poorguy6295
modules/greylin @poorguy6295

modules/gaia/gaiaBuilder/js/src/anduin/forms/tools/pdf @HollandDM
modules/gaia/gaia/jvm/src/anduin/annotation/service @HollandDM

# Build stuffs
mill-build/build.mill @hoangmaihuy
build.mill @hoangmaihuy
*/package.mill @hoangmaihuy

modules/dataextract/dataextract/jvm/src/anduin/dataextract/integration/DataExtractIntegrationServiceImpl.scala @voxuannguyen2001

modules/integplatform @dungdq2002