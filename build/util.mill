package build.build

import build_.build.dependency_.CommonDependencies
import build.platform.stargazerModel
import mill.*
import mill.javalib.dependency.DependencyUpdatesModule
import anduin.build.*
import anduin.mill.*
import anduin.mill.jsdeps.*
import anduin.mill.utils.*
import mill.scalalib.TestModule
import mill.javalib.testrunner.TestResult
import mill.api.Result

trait StargazerModelCrossPlatformModule extends AnduinCrossPlatformModule {

  trait JvmModelModule extends JvmModule with AnduinPlatformScalaPBModule {

    override def moduleDeps = super.moduleDeps ++ Seq(stargazerModel.jvm)

  }

  trait JsModelModule extends JsModule with AnduinPlatformScalaPBModule {

    override def moduleDeps = super.moduleDeps ++ Seq(stargazerModel.js)

  }

}

trait AnduinIntegTests extends AnduinScalaModule with TestModule {

  override def testParallelism: T[Boolean] = Task(false)

  override def sources = Task.Sources(moduleDir / "src")

  override def testReportXml = Some("integration-test-report.xml")

  override def mvnDeps = super.mvnDeps() ++ CommonDependencies.zioTestDeps

}

trait AnduinWebClientModule
    extends AnduinScalaJSModule
    with ESBuildScalaJSApp
    with AnduinBuildEnv
    with DependencyUpdatesModule {

  def webModule: AnduinWebModules

  protected def destPath(isFullLink: Boolean) = Task.Anon {
    mill.api.BuildCtx.withFilesystemCheckerDisabled {
      val folder = clientDirectory() / webModule.value
      os.makeDir.all(folder)
      val prefix = if (isFullLink) "opt" else "fastopt"
      val suffix = if (isFullLink) ".min.js.gz" else ".js.gz"
      folder / s"${webModule.value}-$prefix-bundle${jsResourceSuffix()}${suffix}"
    }
  }

  override def jsSpecs = super.jsSpecs().copy(name = s"anduin-${webModule.value}")

  override def jsPackageInstall = build_.package_.workspaceJsPackageInstall

  def fastBuildClient = Task {
    mill.api.BuildCtx.withFilesystemCheckerDisabled {
      val bundlePath = fastBuildJS()
      val dest = destPath(false)()
      IOUtils.gzip()(bundlePath.path, dest)
      PathRef(dest)
    }
  }

  def fullBuildClient = Task {
    mill.api.BuildCtx.withFilesystemCheckerDisabled {
      val bundlePath = fullBuildJS()
      val dest = destPath(true)()
      IOUtils.gzip()(bundlePath.path, dest)
      PathRef(dest)
    }
  }

}
