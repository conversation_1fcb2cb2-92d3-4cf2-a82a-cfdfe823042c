package build.build

import build_.build.versions_.AnduinDesign
import io.github.hoangmaihuy.mill.caliban.CalibanBuildInfo
import mill.scalalib.*

import anduin.build.AnduinVersions

object CommonDependencies {

  lazy val scalaJavaTimeJSDeps = Seq(
    mvn"design.anduin::web-scala-java-time::${AnduinDesign.version}"
      .exclude("io.github.cquiroz" -> AnduinVersions.j2sjs("scala-java-time"))
      .exclude("io.github.cquiroz" -> AnduinVersions.j2sjs("scala-java-locales")),
    mvn"io.github.cquiroz::scala-java-time::${AnduinVersions.scalaJavaTime}"
      .exclude("io.github.cquiroz" -> AnduinVersions.j2sjs("scala-java-locales")),
    mvn"io.github.cquiroz::scala-java-locales::${AnduinVersions.scalaJavaLocale}"
  )

  lazy val catsDeps = Seq(
    mvn"org.typelevel::cats-core::${AnduinVersions.cats}"
  )

  lazy val catsJVM = Seq(
    mvn"org.typelevel::cats-core:${AnduinVersions.cats}"
  )

  lazy val circeDeps = Seq(
    mvn"org.typelevel::cats-core::${AnduinVersions.cats}",
    mvn"io.circe::circe-core::${AnduinVersions.circe}"
      .exclude("org.typelevel" -> AnduinVersions.j2sjs("cats-core"))
      .exclude("org.typelevel" -> AnduinVersions.j2s("cats-core")),
    mvn"io.circe::circe-generic::${AnduinVersions.circe}"
      .exclude("org.typelevel" -> AnduinVersions.j2s("jawn-parser"))
      .exclude("org.typelevel" -> AnduinVersions.j2sjs("jawn-parser"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-core"))
      .exclude("io.circe" -> AnduinVersions.j2sjs("circe-core"))
      .exclude("org.typelevel" -> AnduinVersions.j2sjs("cats-core"))
      .exclude("org.typelevel" -> AnduinVersions.j2s("cats-core")),
    mvn"org.typelevel::jawn-parser::${AnduinVersions.jawnParser}",
    mvn"io.circe::circe-parser::${AnduinVersions.circe}"
      .exclude("org.typelevel" -> AnduinVersions.j2s("jawn-parser"))
      .exclude("org.typelevel" -> AnduinVersions.j2sjs("jawn-parser"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-core"))
      .exclude("io.circe" -> AnduinVersions.j2sjs("circe-core"))
  )

  lazy val monocleDeps = Seq(
    mvn"org.typelevel::cats-core::${AnduinVersions.cats}",
    mvn"org.typelevel::cats-free::${AnduinVersions.cats}",
    // monocle
    mvn"dev.optics::monocle-core::${AnduinVersions.monocle}"
      .exclude("org.typelevel" -> AnduinVersions.j2sjs("cats-core"))
      .exclude("org.typelevel" -> AnduinVersions.j2s("cats-core"))
      .exclude("org.typelevel" -> AnduinVersions.j2sjs("cats-free"))
      .exclude("org.typelevel" -> AnduinVersions.j2s("cats-free")),
    mvn"dev.optics::monocle-macro::${AnduinVersions.monocle}"
      .exclude("dev.optics" -> AnduinVersions.j2s("monocle-core"))
      .exclude("dev.optics" -> AnduinVersions.j2sjs("monocle-core"))
  )

  lazy val monocleJVMDeps = Seq(
    mvn"org.typelevel::cats-core::${AnduinVersions.cats}",
    mvn"org.typelevel::cats-free::${AnduinVersions.cats}",
    // monocle
    mvn"dev.optics::monocle-core:${AnduinVersions.monocle}"
      .exclude("org.typelevel" -> AnduinVersions.j2s("cats-core"))
      .exclude("org.typelevel" -> AnduinVersions.j2s("cats-free")),
    mvn"dev.optics::monocle-macro:${AnduinVersions.monocle}"
      .exclude("dev.optics" -> AnduinVersions.j2s("monocle-core"))
  )

  val zio = Seq(
    mvn"dev.zio::izumi-reflect::${AnduinVersions.zioReflect}",
    mvn"dev.zio::zio::${AnduinVersions.zio}"
      .exclude("dev.zio" -> AnduinVersions.j2s("izumi-reflect"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("izumi-reflect")),
    mvn"dev.zio::zio-streams::${AnduinVersions.zio}"
      .exclude("dev.zio" -> AnduinVersions.j2s("izumi-reflect"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("izumi-reflect")),
    mvn"dev.zio::zio-prelude::${AnduinVersions.zioPrelude}"
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-schema"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio-schema"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio"))
  )

  lazy val kyoCore = Seq(
    mvn"io.getkyo::kyo-direct::${AnduinVersions.kyo}"
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("sourcecode"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("sourcecode"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("fansi"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("fansi")),
    mvn"io.getkyo::kyo-combinators::${AnduinVersions.kyo}"
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("sourcecode"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("sourcecode"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("fansi"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("fansi")),
    mvn"io.getkyo::kyo-zio::${AnduinVersions.kyo}"
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("sourcecode"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("sourcecode"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("fansi"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("fansi"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-test"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio-test"))
  )

  val zioSchema = zio ++ Seq(
    mvn"dev.zio::zio-schema-derivation::${AnduinVersions.zioSchema}"
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-schema"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio-schema"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-prelude"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio-prelude")),
    mvn"dev.zio::zio-schema::${AnduinVersions.zioSchema}"
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-prelude"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio-prelude")),
    mvn"dev.zio::zio-schema-protobuf::${AnduinVersions.zioSchema}"
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-schema"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio-schema"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-schema-derivation"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio-schema-derivation"))
  )

  lazy val loggingJVMDeps = Seq(
    // logging
    //  mvn"dev.zio::zio-logging:${AnduinVersions.zioLogging}"
    //    .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
    //    .exclude("dev.zio" -> AnduinVersions.j2s("zio-parser"))
    //    .exclude("dev.zio" -> AnduinVersions.j2s("zio-prelude"))
    //    .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams")),
    mvn"org.slf4j:slf4j-api:${AnduinVersions.slf4j_api}",
    mvn"com.outr::scribe:${AnduinVersions.scribe}"
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("sourcecode"))
      .exclude("com.github.jnr" -> "jnr-posix"),
    mvn"com.outr::scribe-slf4j2:${AnduinVersions.scribe}"
      .exclude("com.outr" -> AnduinVersions.j2s("scribe"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("sourcecode"))
      .exclude("org.slf4j" -> "slf4j-api"),
    mvn"com.outr::scribe-file:${AnduinVersions.scribe}"
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("sourcecode"))
      .exclude("com.outr" -> AnduinVersions.j2s("scribe")),
    mvn"com.outr::scribe-json::${AnduinVersions.scribe}"
      .exclude("com.outr" -> AnduinVersions.j2s("scribe"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("sourcecode")),
    mvn"com.github.plokhotnyuk.jsoniter-scala::jsoniter-scala-core::${AnduinVersions.jsoniter}",
    mvn"com.github.plokhotnyuk.jsoniter-scala::jsoniter-scala-macros::${AnduinVersions.jsoniter}",
    mvn"com.github.jnr:jnr-posix:${AnduinVersions.githubJnr}"
      .exclude("org.ow2.asm" -> "asm")
      .exclude("org.ow2.asm" -> "asm-commons")
      .exclude("org.ow2.asm" -> "asm-analysis")
      .exclude("org.ow2.asm" -> "asm-tree")
      .exclude("org.ow2.asm" -> "asm-util"),
    mvn"org.ow2.asm:asm:${AnduinVersions.asm}",
    mvn"org.ow2.asm:asm-commons:${AnduinVersions.asm}",
    mvn"org.ow2.asm:asm-analysis:${AnduinVersions.asm}",
    mvn"org.ow2.asm:asm-tree:${AnduinVersions.asm}",
    mvn"org.ow2.asm:asm-util:${AnduinVersions.asm}",
    mvn"com.lmax:disruptor:${AnduinVersions.lmaxDisruptor}",
    mvn"com.fasterxml.jackson.core:jackson-core:${AnduinVersions.jackson}",
    mvn"com.google.code.gson:gson:${AnduinVersions.gson}"
      .exclude("com.google.errorprone" -> "error_prone_annotations"),
    mvn"org.codehaus.janino:janino:${AnduinVersions.janino}"
      .exclude("org.slf4j" -> "slf4j-log4j12")
  )

  lazy val guavaDeps = Seq(
    mvn"com.google.code.findbugs:jsr305:${AnduinVersions.findBugs}",
    mvn"org.checkerframework:checker-qual:${AnduinVersions.checkerQual}",
    mvn"com.google.errorprone:error_prone_annotations:${AnduinVersions.errorProne}",
    mvn"org.codehaus.mojo:animal-sniffer-annotations:${AnduinVersions.snifferAnnotations}",
    mvn"com.google.guava:guava:${AnduinVersions.guava}"
      .exclude("com.google.code.findbugs" -> "jsr305")
      .exclude("org.checkerframework" -> "checker-qual")
      .exclude("org.codehaus.mojo" -> "animal-sniffer-annotations")
      .exclude("com.google.errorprone" -> "error_prone_annotations")
  )

  lazy val scalapbDeps = Seq(
    mvn"com.thesamet.scalapb::scalapb-runtime::${AnduinVersions.scalapb}"
      .exclude("com.google.protobuf" -> "protobuf-java")
  )

  lazy val googleHttpJVM = guavaDeps ++ Seq(
    mvn"javax.annotation:javax.annotation-api:${AnduinVersions.javaxAnnotation}",
    mvn"io.grpc:grpc-context:${AnduinVersions.grpc}",
    mvn"io.opencensus:opencensus-api:${AnduinVersions.opencensus}"
      .exclude("io.grpc" -> "grpc-context"),
    mvn"io.opencensus:opencensus-contrib-http-util:${AnduinVersions.opencensus}"
      .exclude("io.opencensus" -> "opencensus-api")
      .exclude("com.google.guava" -> "guava"),
    mvn"com.google.api.grpc:proto-google-common-protos:${AnduinVersions.protoCommon}"
      .exclude("com.google.protobuf" -> "protobuf-java"),
    mvn"com.google.auto.value:auto-value-annotations:${AnduinVersions.autoValueAnnotations}",
    mvn"com.google.code.gson:gson:${AnduinVersions.gson}"
      .exclude("com.google.errorprone" -> "error_prone_annotations"),
    mvn"com.google.http-client:google-http-client-gson:${AnduinVersions.googleJackson}"
      .exclude("com.google.code.gson" -> "gson")
      .exclude("com.google.j2objc" -> "j2objc-annotations")
      .exclude("com.google.http-client" -> "google-http-client"),
    mvn"com.google.code.findbugs:jsr305:${AnduinVersions.findBugs}",
    mvn"commons-codec:commons-codec:${AnduinVersions.commonsCodec}",
    mvn"commons-io:commons-io:${AnduinVersions.common_io}",
    mvn"org.apache.httpcomponents:httpcore:${AnduinVersions.httpCore}",
    mvn"org.apache.httpcomponents:httpclient:${AnduinVersions.httpClient}"
      .exclude("commons-codec" -> "commons-codec")
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .exclude("commons-logging" -> "commons-logging")
      .exclude("commons-configuration" -> "commons-configuration"),
    mvn"com.google.http-client:google-http-client:${AnduinVersions.googleJackson}"
      .exclude("io.grpc" -> "grpc-context")
      .exclude("com.google.code.findbugs" -> "jsr305")
      .exclude("com.google.j2objc" -> "j2objc-annotations")
      .exclude("com.google.errorprone" -> "error_prone_annotations")
      .exclude("com.google.guava" -> "guava")
      .exclude("io.opencensus" -> "opencensus-api")
      .exclude("io.opencensus" -> "opencensus-contrib-http-util")
      .exclude("org.apache.httpcomponents" -> "httpclient")
      .exclude("org.apache.httpcomponents" -> "httpcore")
  )

  lazy val quillEngineVersion = "4.8.4"
  lazy val zioQuillVersion = "4.8.6"

  lazy val quillJdbcJVM = Seq(
    mvn"com.softwaremill.magnolia1_3::magnolia:${AnduinVersions.magnolia}",
    mvn"dev.zio::zio-json:${AnduinVersions.zioJson}"
      .exclude("com.softwaremill.magnolia1_3" -> AnduinVersions.j2s("magnolia"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams")),
    mvn"com.lihaoyi::pprint:${AnduinVersions.pprint}"
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("sourcecode"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("fansi")),
    mvn"com.lihaoyi::fansi:${AnduinVersions.fansi}"
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("sourcecode")),
    mvn"com.zaxxer:HikariCP:${AnduinVersions.hikariCP}"
      .exclude("io.micrometer" -> "micrometer-core")
      .exclude("org.javassist" -> "javassist")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("io.dropwizard.metrics" -> "metrics-core")
      .exclude("io.dropwizard.metrics" -> "metrics-healthchecks")
      .exclude("io.prometheus" -> "simpleclient ")
      .exclude("org.hibernate" -> "hibernate-core"),
    mvn"com.typesafe.scala-logging::scala-logging:3.9.5"
      .exclude("org.slf4j" -> "slf4j-api"),
    mvn"io.suzaku::boopickle:1.5.0",
    mvn"org.scala-lang.modules::scala-collection-compat:2.13.0",
    mvn"org.scala-lang.modules::scala-parallel-collections:1.2.0",
    mvn"io.getquill::quill-engine:$quillEngineVersion"
      .exclude("org.scala-lang.modules" -> AnduinVersions.j2s("scala-collection-compat"))
      .exclude("com.github.ben-manes.caffeine" -> "caffeine")
      .exclude("com.typesafe.scala-logging" -> AnduinVersions.j2s("scala-logging"))
      .exclude("com.google.errorprone" -> "error_prone_annotations")
      .exclude("org.checkerframework" -> "checker-qual")
      .exclude("io.suzaku" -> AnduinVersions.j2s("boopickle"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-json"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-logging"))
      .exclude("com.typesafe" -> "config")
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("sourcecode"))
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("pprint"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("fansi"))
      .exclude("com.zaxxer" -> "HikariCP")
      .exclude("org.scalameta" -> "scalafmt-core_2.13"),
    mvn"io.getquill::quill-util:$quillEngineVersion"
      .exclude("org.scala-lang.modules" -> AnduinVersions.j2s("scala-collection-compat"))
      .exclude("org.scala-lang.modules" -> AnduinVersions.j2s("scala-parallel-collections"))
      .exclude("io.suzaku" -> AnduinVersions.j2s("boopickle"))
      .exclude("com.typesafe.scala-logging" -> AnduinVersions.j2s("scala-logging"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-json"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-logging"))
      .exclude("com.typesafe" -> "config")
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("sourcecode"))
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("pprint"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("fansi"))
      .exclude("com.zaxxer" -> "HikariCP")
      .exclude("org.scalameta" -> "scalafmt-core_2.13"),
    mvn"io.getquill::quill-jdbc-zio:$zioQuillVersion"
      .exclude("io.suzaku" -> AnduinVersions.j2s("boopickle"))
      .exclude("io.getquill" -> AnduinVersions.j2s("quill-engine"))
      .exclude("io.getquill" -> AnduinVersions.j2s("quill-util"))
      .exclude("com.typesafe.scala-logging" -> AnduinVersions.j2s("scala-logging"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-json"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-logging"))
      .exclude("com.typesafe" -> "config")
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("sourcecode"))
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("pprint"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("fansi"))
      .exclude("com.zaxxer" -> "HikariCP")
      .exclude("org.scalameta" -> "scalafmt-core_2.13")
  )

  lazy val calibanDeps = Seq(
    mvn"com.github.ghostdogpr::caliban:${CalibanBuildInfo.calibanVersion}"
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("fastparse"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-prelude"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-query"))
      .exclude("com.github.plokhotnyuk.jsoniter-scala" -> AnduinVersions.j2s("jsoniter-scala-core"))
      .exclude("com.softwaremill.magnolia1_3" -> AnduinVersions.j2s("magnolia")),
    mvn"com.github.ghostdogpr::caliban-client:${CalibanBuildInfo.calibanVersion}"
      .exclude("com.softwaremill.sttp.client3" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.client3" -> AnduinVersions.j2s("circe"))
      .exclude("com.softwaremill.sttp.client3" -> AnduinVersions.j2s("jsoniter"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-parser"))
      .exclude("com.github.plokhotnyuk.jsoniter-scala" -> AnduinVersions.j2s("jsoniter-scala-core"))
      .exclude("com.softwaremill.magnolia1_3" -> AnduinVersions.j2s("magnolia"))
  )

  lazy val calibanLaminextDeps = Seq(
    mvn"com.github.ghostdogpr::caliban-client-laminext::${CalibanBuildInfo.calibanVersion}"
      .exclude("io.laminext" -> AnduinVersions.j2sjs("core"))
      .exclude("com.raquo" -> AnduinVersions.j2sjs("laminar"))
      .exclude("com.softwaremill.sttp.client3" -> AnduinVersions.j2sjs("core"))
      .exclude("com.softwaremill.sttp.client3" -> AnduinVersions.j2sjs("circe"))
      .exclude("com.softwaremill.sttp.client3" -> AnduinVersions.j2sjs("jsoniter"))
      .exclude("com.github.plokhotnyuk.jsoniter-scala" -> AnduinVersions.j2sjs("jsoniter-scala-core"))
  )

  lazy val zioTestDeps = Seq(
    mvn"com.softwaremill.sttp.tapir::tapir-sttp-stub-server:${AnduinVersions.tapirVersion}"
      .exclude("com.softwaremill.sttp.tapir" -> AnduinVersions.j2s("tapir-server"))
      .exclude("com.softwaremill.sttp.tapir" -> AnduinVersions.j2s("tapir-sttp-client")),
    mvn"dev.zio::zio-test::${AnduinVersions.zio}"
      .exclude("dev.zio" -> AnduinVersions.j2s("izumi-reflect"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("izumi-reflect")),
    mvn"dev.zio::zio-test-sbt::${AnduinVersions.zio}"
      .exclude("dev.zio" -> AnduinVersions.j2s("izumi-reflect"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("izumi-reflect")),
    mvn"dev.zio::zio-test-magnolia::${AnduinVersions.zio}"
      .exclude("com.softwaremill.magnolia1_3" -> AnduinVersions.j2s("magnolia"))
      .exclude("com.softwaremill.magnolia1_3" -> AnduinVersions.j2sjs("magnolia"))
      .exclude("dev.zio" -> AnduinVersions.j2s("izumi-reflect"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("izumi-reflect"))
  )

  lazy val zioTemporalTestDeps = Seq(
    mvn"dev.vhonta::zio-temporal-testkit:${AnduinVersions.zioTemporal}"
      .exclude("io.temporal" -> "temporal-sdk")
      .exclude("io.temporal" -> "temporal-opentracing")
      .exclude("io.temporal" -> "temporal-testing")
      .exclude("com.fasterxml.jackson.module" -> AnduinVersions.j2s("jackson-module-scala"))
      .exclude("com.fasterxml.jackson.datatype" -> "jackson-datatype-jsr310")
      .exclude("com.fasterxml.jackson.core" -> AnduinVersions.j2s("jackson-module-scala"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("org.scalatest" -> AnduinVersions.j2s("scalatest")),
    mvn"io.temporal:temporal-testing:${AnduinVersions.temporalIO}"
      .exclude("io.temporal" -> "temporal-sdk")
      .exclude("com.jayway.jsonpath" -> "json-path")
      .exclude("com.fasterxml.jackson.datatype" -> "jackson-datatype-jsr310")
      .exclude("com.fasterxml.jackson.core" -> AnduinVersions.j2s("jackson-module-scala"))
      .exclude("org.ow2.asm" -> "asm")
      .exclude("com.google.guava" -> "guava")
      .exclude("org.slf4j" -> "slf4j-simple")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("io.grpc" -> "grpc-core")
      .exclude("io.grpc" -> "grpc-services")
  )

}
