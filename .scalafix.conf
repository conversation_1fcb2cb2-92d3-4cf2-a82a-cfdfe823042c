
rules = [
  DisableSyntax,
  CollectHead,
  CollectHeadOption,
  CompareSameValue,
  DirectoryAndPackageName,
  InterpolationToStringWarn,
  ObjectSelfType,
  OptionMapFlatMap,
  RedundantCaseClassVal,
  SameParamOverloading,
  UnnecessarySort,
  UnusedConstructorParams,
  UnusedTypeParams
]

DisableSyntax.noVars = true
DisableSyntax.noNulls = true
DisableSyntax.noReturns = true
DisableSyntax.noXml = true
DisableSyntax.noFinalVal = true
DisableSyntax.noFinalize = true
DisableSyntax.noValPatterns = true
DisableSyntax.noWhileLoops = true
DisableSyntax.noCovariantTypes = true
DisableSyntax.noContravariantTypes = true

# to turn on
DisableSyntax.noValInAbstract = false

DisableSyntax.noAsInstanceOf = true
DisableSyntax.noIsInstanceOf = true

DisableSyntax.keywords = ["implicit"]

DisableSyntax.regex = [
  {
    id = println
    pattern = "(?-i)\\bprint(ln|f)\\b"
    message = "println(...) or printf(...) or print(...) are not allowed"
  },
  {
    id = scalastyle
    pattern = "(?-i)\\bscalastyle\\b"
    message = "scalastyle does not work in Scala 3. Use scalafix instead."
  },
  {
    id = foreachPar
    pattern = "(?-i)\\bZIO.foreachPar\\b"
    message = "ZIO.foreachPar is not allowed, use ZIOUtils.foreachPar instead"
  }
]

DirectoryAndPackageName.baseDirectory = ["/src/"]
DirectoryAndPackageName.severity = ERROR

lint.error = [
  "CollectHead.*",
  "CollectHeadOption.*",
  "CompareSameValue.*",
  "DisableSyntax.*",
  "DirectoryAndPackageName.*",
  "InterpolationToStringWarn.*",
  "ObjectSelfType.*",
  "OptionMapFlatMap.*",
  "RedundantCaseClassVal.*",
  "SameParamOverloading.*",
  "UnnecessarySort.*",
  "UnusedConstructorParams.*",
  "UnusedTypeParams.*",
  "UnusedScalafixSuppression.*"
]