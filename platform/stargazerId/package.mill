// Copyright (C) 2014-2025 Anduin Transactions Inc.

package build.platform.stargazerId

import build_.platform.stargazerId.dependency_.StargazerId

import build.platform.stargazerTest
import mill.*, scalalib.*
import anduin.mill.*

object `package` extends AnduinCrossPlatformModule {

  object jvm extends JvmModule with SharedModule {
    override def mvnDeps = super.mvnDeps() ++ StargazerId.jvmDeps

    override def moduleDeps = Seq(stargazerTest.jvm)
  }

  object js extends JsModule with SharedModule {
    override def mvnDeps = super.mvnDeps() ++ StargazerId.jsDeps

    override def moduleDeps = Seq(stargazerTest.js)
  }

  trait SharedModule extends ScalaModule {
    override def mvnDeps = super.mvnDeps() ++ StargazerId.sharedDeps
  }

}
