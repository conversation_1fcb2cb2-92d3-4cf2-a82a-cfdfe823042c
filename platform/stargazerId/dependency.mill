package build.platform.stargazerId

import build_.build.dependency_.CommonDependencies
import mill.scalalib.*
import anduin.build.AnduinVersions

object StargazerId {

  lazy val sharedDeps =
    CommonDependencies.zioSchema ++ CommonDependencies.kyoCore ++ CommonDependencies.circeDeps ++ Seq(
      mvn"com.outr::scribe::${AnduinVersions.scribe}"
        .exclude("com.lihaoyi" -> AnduinVersions.j2s("sourcecode"))
        .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("sourcecode"))
        .exclude("com.github.jnr" -> "jnr-posix")
        .exclude("org.ow2.asm" -> "asm")
        .exclude("org.ow2.asm" -> "asm-commons")
        .exclude("org.ow2.asm" -> "asm-analysis")
        .exclude("org.ow2.asm" -> "asm-tree")
        .exclude("org.ow2.asm" -> "asm-util"),
      mvn"io.circe::circe-core::${AnduinVersions.circe}"
        .exclude("org.typelevel" -> AnduinVersions.j2sjs("cats-core"))
        .exclude("org.typelevel" -> AnduinVersions.j2s("cats-core")),
      mvn"io.circe::circe-generic::${AnduinVersions.circe}"
        .exclude("org.typelevel" -> AnduinVersions.j2sjs("cats-core"))
        .exclude("org.typelevel" -> AnduinVersions.j2s("cats-core"))
        .exclude("org.typelevel" -> AnduinVersions.j2sjs("circe-core"))
        .exclude("org.typelevel" -> AnduinVersions.j2s("circe-core")),
      mvn"io.bullet::borer-compat-circe::${AnduinVersions.borer}"
        .exclude("io.circe" -> AnduinVersions.j2sjs("circe-core"))
        .exclude("io.circe" -> AnduinVersions.j2s("circe-core")),
      mvn"com.lihaoyi::geny::${AnduinVersions.geny}",
      mvn"com.lihaoyi::fastparse::${AnduinVersions.fastParse}"
        .exclude("com.lihaoyi" -> AnduinVersions.j2s("sourcecode"))
        .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("sourcecode"))
        .exclude("com.lihaoyi" -> AnduinVersions.j2s("geny"))
        .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("geny")),
      mvn"com.lihaoyi::sourcecode::${AnduinVersions.sourcecode}",
      mvn"com.thesamet.scalapb::scalapb-runtime::${AnduinVersions.scalapb}"
        .exclude("com.google.protobuf" -> "protobuf-java")
        .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("fastparse"))
        .exclude("com.lihaoyi" -> AnduinVersions.j2s("fastparse")),
      mvn"com.lihaoyi::pprint::${AnduinVersions.pprint}"
        .exclude("com.lihaoyi" -> AnduinVersions.j2s("sourcecode"))
        .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("sourcecode"))
        .exclude("com.lihaoyi" -> AnduinVersions.j2s("fansi"))
        .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("fansi")),
      mvn"com.lihaoyi::fansi::${AnduinVersions.fansi}"
        .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("sourcecode"))
        .exclude("com.lihaoyi" -> AnduinVersions.j2s("sourcecode")),
      mvn"com.lihaoyi::sourcecode::${AnduinVersions.sourcecode}"
    )

  lazy val jvmDeps = CommonDependencies.loggingJVMDeps ++ Seq(
    mvn"com.google.protobuf:protobuf-java:${AnduinVersions.protobufJava}",
    mvn"org.typelevel::jawn-parser:${AnduinVersions.jawnParser}",
    mvn"io.circe::circe-jawn:${AnduinVersions.circe}"
      .exclude("org.typelevel" -> AnduinVersions.j2s("jawn-parser"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-core")),
    mvn"io.circe::circe-parser:${AnduinVersions.circe}"
      .exclude("io.circe" -> AnduinVersions.j2s("circe-core"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-jawn"))
      .exclude("org.typelevel" -> AnduinVersions.j2s("jawn-parser"))
      .exclude("org.typelevel" -> AnduinVersions.j2s("cats-core")),
    mvn"com.github.kolotaev::ride:${AnduinVersions.ride}",
    mvn"org.sqids::sqids:${AnduinVersions.sqids}"
  )

  lazy val jsDeps = CommonDependencies.scalaJavaTimeJSDeps ++ Seq(
    mvn"org.typelevel::jawn-parser::${AnduinVersions.jawnParser}",
    mvn"org.scala-js::scalajs-java-securerandom::${AnduinVersions.sjsSecuredRandom}"
      .withDottyCompat(AnduinVersions.scala),
    mvn"io.circe::circe-parser::${AnduinVersions.circe}"
      .exclude("org.typelevel" -> AnduinVersions.j2s("jawn-parser"))
      .exclude("org.typelevel" -> AnduinVersions.j2sjs("jawn-parser"))
      .exclude("org.typelevel" -> AnduinVersions.j2s("cats-core"))
      .exclude("org.typelevel" -> AnduinVersions.j2sjs("cats-core"))
  )

}
