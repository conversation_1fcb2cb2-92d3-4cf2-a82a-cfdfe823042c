// Copyright (C) 2014-2025 Anduin Transactions Inc.

package build.platform.stargazerModel

import build_.platform.stargazerModel.dependency_.StargazerModel
import build.platform.*

import mill.*, scalalib.*
import anduin.mill.*

object `package` extends AnduinCrossPlatformModule {

  object jvm extends JvmModule with SharedModule {

    override def moduleDeps = Seq(stargazerId.jvm)

    override def mvnDeps = super.mvnDeps() ++ StargazerModel.jvmDeps

    override def scalaPBLenses = true

  }

  object js extends JsModule with SharedModule {
    override def moduleDeps = Seq(stargazerId.js)
  }

  trait SharedModule extends AnduinPlatformScalaPBModule {
    override def scalaPBSearchDeps = true
    override def mvnDeps = super.mvnDeps() ++ StargazerModel.sharedDeps
  }

}
