syntax = "proto3";

package anduin.notification;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
    flat_package: true
    single_file: true
    lenses: false
    import: "anduin.notification.NotificationTypeMapper.given"
    import: "anduin.model.id.notification.*"
    import: "anduin.id.notification.NotificationId"
    import: "anduin.radix.RadixId"
    import: "anduin.refined.Refined"
    import: "scala.language.`future-migration`"
};

message NotificationSpaceIdMessage {
    string channel = 1 [(scalapb.field).type = "RadixId"];
    repeated string parents = 2 [(scalapb.field).type = "Refined[NotificationSpaceId]"];
    string suffix = 3 [(scalapb.field).type = "Refined[NotificationSpaceId]"];
}

message NotificationIdMessage {
    NotificationSpaceIdMessage notification_space_id = 1 [(scalapb.field).type = "NotificationSpaceId"];
    string suffix = 2 [(scalapb.field).type = "Refined[NotificationId]"];
}
