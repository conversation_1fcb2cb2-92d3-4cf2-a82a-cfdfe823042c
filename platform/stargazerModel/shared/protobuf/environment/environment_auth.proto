syntax = "proto3";

package environment;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.environment"
  single_file: true
  import: "anduin.id.environment.EnvironmentId"
  import: "anduin.id.account.EnterpriseLoginConfigId"
  import: "anduin.id.environment.EnvironmentSSOBindingId"
  import: "anduin.id.environment.GlobalEmailDomainEnvironmentSSOBindingId"
  import: "anduin.id.offering.GlobalOfferingId"
};

enum EnvironmentPolicyReauthenticationFallbackPolicy {
  BlockAll = 0;
  UseAnduinAuth = 1;
  ListProviders = 2;
  UseDefaultProvider = 3;
}

message EnvironmentAuthenticationActionMethod {
  bool useAnduinAuth = 1;
  string enterpriseLoginConfigId = 2 [(scalapb.field).type = "Option[EnterpriseLoginConfigId]"];
}

message EnvironmentAuthenticationActionFallback {
  EnvironmentPolicyReauthenticationFallbackPolicy fallbackPolicy = 1;
}

message EnvironmentAuthenticationActionUserBound {
  string bindingId = 1 [(scalapb.field).type = "EnvironmentSSOBindingId"];
}

message EnvironmentAuthenticationActionEmailBound {
  string domain = 1;
  string bindingId = 2 [(scalapb.field).type = "GlobalEmailDomainEnvironmentSSOBindingId"];
}

message EnvironmentAuthenticationActionNoAction {
}

message EnvironmentAuthenticationActionOverride {
  int64 startTime = 1;
}

message EnvironmentAuthenticationActionBox {
  oneof envAuthActions {
    EnvironmentAuthenticationActionFallback fallback = 1;
    EnvironmentAuthenticationActionUserBound userBound = 2;
    EnvironmentAuthenticationActionEmailBound emailBound = 3;
    EnvironmentAuthenticationActionNoAction noAction = 4;
    EnvironmentAuthenticationActionOverride override = 5;
  }
}

message EnvironmentAuthenticationAction {
  string environmentId = 1 [(scalapb.field).type = "EnvironmentId"];
  EnvironmentAuthenticationActionBox action = 2;
  EnvironmentAuthenticationActionMethod method = 3;
  string offeringId = 4 [(scalapb.field).type = "GlobalOfferingId"];
  repeated EnvironmentAuthenticationActionMethod actual_method = 5; // The origin action method the user login from
}