syntax = "proto3";

package anduin.asyncapi.v2.state;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.asyncapi.v2.state"
  single_file: true
  import: "anduin.id.asyncapiv2.AsyncApiId"
  import: "anduin.model.common.user.UserId"

  preamble: "sealed trait AsyncApiStateBase extends scalapb.GeneratedMessage {"
  preamble: "  def id: AsyncApiId"
  preamble: "  def status: AsyncApiStatus"
  preamble: "}"
};

enum AsyncApiStatus {
  CREATED = 0;
  RUNNING = 1;
  SUCCESS = 2;
  FAILED = 3;
}

message AsyncApiState {
  option (scalapb.message).extends = "AsyncApiStateBase";
  string id = 1 [(scalapb.field).type = "AsyncApiId"];
  string creator = 2 [(scalapb.field).type = "UserId"];
  int64 createdAt = 3;
  int64 updatedAt = 4;
  AsyncApiStatus status = 5;
}

message RecordTypeUnion {
  AsyncApiState _AsyncApiState = 1;
}