syntax = "proto3";

package anduin.asyncapi.v2.event;

import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";
import "google/protobuf/struct.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.asyncapi.v2.event"
  single_file: true
  import: "java.time.Instant"
  import: "anduin.id.asyncapiv2.AsyncApiId"
  import: "anduin.model.common.user.UserId"
  import: "anduin.circe.generic.semiauto.CirceCodec"
  import: "anduin.model.codec.ProtoCodecs.given"

  preamble: "sealed trait AsyncApiEvent extends scalapb.GeneratedMessage derives CirceCodec.WithDefaultsAndTypeName"
};

message AsyncApiEventCreated {
  option (scalapb.message).extends = "AsyncApiEvent";
  string asyncApiId = 1 [(scalapb.field).type = "AsyncApiId"];
  int64 index = 2;
  string actor = 3 [(scalapb.field).type = "UserId"];
  InstantMessage timestamp = 4 [(scalapb.field).type = "Instant"];
}

message AsyncApiEventRun {
  option (scalapb.message).extends = "AsyncApiEvent";
  string asyncApiId = 1 [(scalapb.field).type = "AsyncApiId"];
  int64 index = 2;
  string actor = 3 [(scalapb.field).type = "UserId"];
  InstantMessage timestamp = 4 [(scalapb.field).type = "Instant"];
}

message AsyncApiEventSuccess {
  option (scalapb.message).extends = "AsyncApiEvent";
  string asyncApiId = 1 [(scalapb.field).type = "AsyncApiId"];
  int64 index = 2;
  string actor = 3 [(scalapb.field).type = "UserId"];
  InstantMessage timestamp = 4 [(scalapb.field).type = "Instant"];
}

message AsyncApiEventFailed {
  option (scalapb.message).extends = "AsyncApiEvent";
  string asyncApiId = 1 [(scalapb.field).type = "AsyncApiId"];
  int64 index = 2;
  string actor = 3 [(scalapb.field).type = "UserId"];
  InstantMessage timestamp = 4 [(scalapb.field).type = "Instant"];
}

message RecordTypeUnion {
  AsyncApiEventCreated _AsyncApiEventCreated = 1;
  AsyncApiEventRun _AsyncApiEventRun = 2;
  AsyncApiEventSuccess _AsyncApiEventSuccess = 3;
  AsyncApiEventFailed _AsyncApiEventFailed = 4;
}