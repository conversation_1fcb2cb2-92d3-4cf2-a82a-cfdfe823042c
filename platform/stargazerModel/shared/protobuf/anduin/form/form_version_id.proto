syntax = "proto3";

package anduin.form;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.form.id"
  single_file: true
  import: "anduin.id.form.FormVersionId"
  import: "anduin.id.form.FormId"
  import: "anduin.refined.Refined"
  import: "anduin.form.id.FormVersionIdTypeMapper.given"
};

message FormVersionIdMessage {
  string form_id = 1 [(scalapb.field).type = "FormId"];
  string suffix = 2 [(scalapb.field).type = "Refined[FormVersionId]"];
}
