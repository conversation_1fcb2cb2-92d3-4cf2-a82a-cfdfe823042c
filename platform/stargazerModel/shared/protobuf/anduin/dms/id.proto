syntax = "proto3";

package anduin.dms;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  flat_package: true
  single_file: true
  lenses: false
  import: "anduin.dms.FileFolderIdTypeMapper.given"
  import: "anduin.model.id.*"
  import: "anduin.radix.RadixId"
  import: "anduin.refined.Refined"
};

message FolderIdMessage {
  string channel = 1 [(scalapb.field).type = "RadixId"];
  repeated string parents = 2 [(scalapb.field).type = "Refined[FolderId]"];
  string suffix = 3 [(scalapb.field).type = "Refined[FolderId]"];
}

message FileIdMessage {
  FolderIdMessage folder_id = 1 [(scalapb.field).type = "FolderId"];
  string suffix = 2 [(scalapb.field).type = "Refined[FileId]"];
}

message ShortcutIdMessage {
  FolderIdMessage folder_id = 1 [(scalapb.field).type = "FolderId"];
  string suffix = 2 [(scalapb.field).type = "Refined[ShortcutId]"];
}
