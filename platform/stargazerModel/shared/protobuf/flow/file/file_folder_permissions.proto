syntax = "proto3";

package flow.file;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.flow.file"
  single_file: true
  flat_package: true
  preserve_unknown_fields: false
};

enum FileFolderPermission {
  // Can view folders/files
  ViewOnly = 0;
  // Can view and download folders/files
  Read = 1;
  // Can view, download, rename and add folders/files
  Write = 2;
  // Can view, modify, add, delete and manage permissions of folders/files
  Own = 3;
}
