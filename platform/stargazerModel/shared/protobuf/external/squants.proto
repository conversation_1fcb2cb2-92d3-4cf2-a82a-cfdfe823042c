syntax = "proto3";

package external.squants;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.external.squants"
  single_file: true
};

message MoneyMessage {
  CurrencyMessage unit = 1;
  string value = 2;
}

enum CurrencyMessage {
  USD = 0;
  ARS = 1;
  AUD = 2;
  BRL = 3;
  CAD = 4;
  CHF = 5;
  CLP = 6;
  CNY = 7;
  CZK = 8;
  DKK = 9;
  EUR = 10;
  GBP = 11;
  HKD = 12;
  INR = 13;
  JPY = 14;
  KRW = 15;
  MXN = 16;
  MYR = 17;
  NOK = 18;
  NZD = 19;
  RUB = 20;
  SEK = 21;
  XAG = 22;
  XAU = 23;
  BTC = 24;
  ETH = 25;
  LTC = 26;
  ZAR = 27;
  NAD = 28;
  TRY = 29;
}

message DimensionlessMessage {
  string value = 1;
  DimensionlessUnitMessage unit = 2;
}

enum DimensionlessUnitMessage {
  Each = 0;
  Percent = 1;
  Dozen = 2;
  Score = 3;
  Gross = 4;
}

message TimeMessage {
  string value = 1;
  TimeUnitMessage unit = 2;
}

enum TimeUnitMessage {
  Nanoseconds = 0;
  Microseconds = 1;
  Milliseconds = 2;
  Seconds = 3;
  Minutes = 4;
  Hours = 5;
  Days = 6;
}

message InformationMessage {
  double value = 1;
  InformationUnitMessage unit = 2;
}

enum InformationUnitMessage {
  Bytes = 0;
  Octets = 1;
  Kilobytes = 2;
  Kibibytes = 3;
  Megabytes = 4;
  Mebibytes = 5;
  Gigabytes = 6;
  Gibibytes = 7;
  Terabytes = 8;
  Tebibytes = 9;
  Petabytes = 10;
  Pebibytes = 11;
  Exabytes = 12;
  Exbibytes = 13;
  Zettabytes = 14;
  Zebibytes = 15;
  Yottabytes = 16;
  Yobibytes = 17;
}
