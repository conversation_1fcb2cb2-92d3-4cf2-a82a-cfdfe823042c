syntax = "proto3";

import "scalapb/scalapb.proto";

import "google/protobuf/wrappers.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf"
  single_file: true
  import: "anduin.model.common.emailaddress.EmailAddress"
};

message EmailAddressMessage {
    option (scalapb.message).companion_extends = "EmailAddressMessageCompanionTrait";
    google.protobuf.StringValue name = 1;
    string address = 2;
}

message EmailAddressMessageList {
    repeated EmailAddressMessage addresses = 1 [(scalapb.field).type = "EmailAddress"];
}
