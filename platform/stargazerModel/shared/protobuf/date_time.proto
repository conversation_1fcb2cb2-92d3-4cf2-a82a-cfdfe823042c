syntax = "proto3";

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf"
  single_file: true
};

message LocalDateMessage {
  option (scalapb.message).companion_extends = "LocalDateMessageCompanionTrait";
  int32 year = 1;
  int32 month = 2;
  int32 day_of_month = 3;
}

message LocalDateTimeMessage {
  option (scalapb.message).companion_extends = "LocalDateTimeMessageCompanionTrait";
  int32 year = 1;
  int32 month = 2;
  int32 day_of_month = 3;
  int32 hour = 4;
  int32 minute = 5;
  int32 second = 6;
  int32 nano_of_second = 7;
}

message ZoneIdMessage {
  option (scalapb.message).companion_extends = "ZoneIdMessageCompanionTrait";
  string zone_id = 1;
}

message ZonedDateTimeMessage {
  option (scalapb.message).companion_extends = "ZonedDateTimeMessageCompanionTrait";
  LocalDateTimeMessage local_date_time = 1;
  ZoneIdMessage zone_id = 2;
}

message DurationMessage {
  option (scalapb.message).companion_extends = "DurationMessageCompanionTrait";
  int64 seconds = 1;
  int32 nanos = 2;
}

message InstantMessage {
  option (scalapb.message).companion_extends = "InstantMessageCompanionTrait";
  int64 seconds = 1;
  int32 nanos = 2;
}

message OldLocalDateMessage {
  int32 year = 1;
  int32 month = 2;
  int32 day_of_month = 3;
}

message OldLocalTimeMessage {
  int32 hour = 1;
  int32 minute = 2;
  int32 second = 3;
  int32 nano_of_second = 4;
}

message OldLocalDateTimeMessage {
  option (scalapb.message).companion_extends = "OldLocalDateTimeMessageCompanionTrait";
  OldLocalDateMessage date = 1;
  OldLocalTimeMessage time = 2;
}

message OldZoneIdMessage {
  option (scalapb.message).companion_extends = "OldZoneIdMessageCompanionTrait";
  string id_string = 1;
}

message OldZonedDateTimeMessage {
  option (scalapb.message).companion_extends = "OldZonedDateTimeMessageCompanionTrait";
  OldLocalDateTimeMessage date_time = 1;
  OldZoneIdMessage zone_id = 2;
}
