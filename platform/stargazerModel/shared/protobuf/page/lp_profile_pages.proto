syntax = "proto3";

package page;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.page"
  single_file: true
  flat_package: true
  import: "anduin.id.investmententity.InvestmentEntityId"
};

message InvestmentEntityProfilesPageMessage {
  string investment_entity_id = 1 [(scalapb.field).type = "InvestmentEntityId"];
}

message InvestmentEntityDocumentsPageMessage {
  string investment_entity_id = 1 [(scalapb.field).type = "InvestmentEntityId"];
}

message InvestmentEntitySubscriptionsPageMessage {
  string investment_entity_id = 1 [(scalapb.field).type = "InvestmentEntityId"];
}

message InvestmentEntityPermissionsPageMessage {
  string investment_entity_id = 1 [(scalapb.field).type = "InvestmentEntityId"];
}

message InvestmentEntityAuditLogPageMessage {
  string investment_entity_id = 1 [(scalapb.field).type = "InvestmentEntityId"];
}

message InvestmentEntityOnboardingPageMessage {
  string investment_entity_id = 1 [(scalapb.field).type = "InvestmentEntityId"];
}

message LpProfileRequestPageMessage {
  map<string, string> params = 1;
}
