syntax = "proto3";

package page.narya;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.page.narya"
  single_file: true
  import: "anduin.id.docrequest.FormSubmissionId"
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.id.fundsub.FundSubSupportingDocId"
  import: "anduin.id.signature.SignatureRequestId"
};

message NaryaPageBox {
  reserved 1 to 5, 7, 8, 12 to 15;
  oneof page {
    NaryaEmailAttachmentMessage email_attachment = 9;
    SignatureRecipientPageMessage signature_recipient_page = 10;
    SignatureReassignPageMessage signature_reassign_page = 18;
    FundSubLpPageMessage fund_sub_lp = 11;
    FundSubLpReassignPageMessage fund_sub_lp_reassign = 19;
    FundSubCounterSignPageMessage fund_sub_counter_sign = 16;
    FundSubCountersignReassignPageMessage fund_sub_countersign_reassign = 22;
    FundSubSupportingDocRequestPageMessage fund_sub_supporting_doc_request = 17;
    TaxFormSignatureRequestPageMessage tax_form_signature_request = 20;
    FundSubFormSubmissionRequestPageMessage form_submission_request = 21;
    FundSubSideLetterSignatureRequestPageMessage side_letter_signature_request = 23;
    FundSubGenericSignatureRequestPageMessage fund_sub_generic_signature_request = 24;
  }
}

message NaryaEmailAttachmentMessage {
  string token = 1;
}

message FundSubLpPageMessage {
  string fundSubLpId = 1 [(scalapb.field).type = "FundSubLpId"];
  map<string, string> params = 2;
}

message FundSubLpReassignPageMessage {
  string fundSubLpId = 1 [(scalapb.field).type = "FundSubLpId"];
  map<string, string> params = 2;
}

message FundSubCounterSignPageMessage {
  string fundSubLpId = 1 [(scalapb.field).type = "FundSubLpId"];
  map<string, string> params = 2;
}

message FundSubCountersignReassignPageMessage {
  string fundSubLpId = 1 [(scalapb.field).type = "FundSubLpId"];
  map<string, string> params = 2;
}

message TaxFormSignatureRequestPageMessage {
  string docId = 1 [(scalapb.field).type = "FundSubSupportingDocId"];
  string requestId = 2 [(scalapb.field).type = "SignatureRequestId"];
  map<string, string> params = 3;
}

message FundSubSupportingDocRequestPageMessage {
  string fundSubLpId = 1 [(scalapb.field).type = "FundSubLpId"];
  string signatureRequestIdOpt = 2 [(scalapb.field).type = "Option[SignatureRequestId]"];
  map<string, string> params = 3;
}

message FundSubFormSubmissionRequestPageMessage {
  string formSubmissionId = 1 [(scalapb.field).type = "FormSubmissionId"];
  string requestId = 2 [(scalapb.field).type = "SignatureRequestId"];
  map<string, string> params = 3;
}

message FundSubSideLetterSignatureRequestPageMessage {
  string lpId = 1 [(scalapb.field).type = "FundSubLpId"];
  string requestId = 2 [(scalapb.field).type = "SignatureRequestId"];
  map<string, string> params = 3;
}

message SignatureRecipientPageMessage {
  string signatureRequestId = 1 [(scalapb.field).type = "SignatureRequestId"];
  map<string, string> params = 2;
}

message SignatureReassignPageMessage {
  string signatureRequestId = 1 [(scalapb.field).type = "SignatureRequestId"];
  map<string, string> params = 2;
}

message FundSubGenericSignatureRequestPageMessage {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string request_id = 2 [(scalapb.field).type = "SignatureRequestId"];
  map<string, string> params = 3;
}
