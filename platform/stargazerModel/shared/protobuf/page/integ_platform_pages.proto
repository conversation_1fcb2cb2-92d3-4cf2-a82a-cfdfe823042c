syntax = "proto3";

package page;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.page"
  single_file: true
  flat_package: true
  import: "anduin.id.entity.EntityId"
};

message IntegPlatformMainPageMessage {
  string entityId = 1 [(scalapb.field).type = "EntityId"];
  map<string, string> params = 2;
}

message IntegPlatformBrowsePageMessage {
  string entityId = 1 [(scalapb.field).type = "EntityId"];
  map<string, string> params = 2;
}

message IntegPlatformManagePageMessage {
  string entityId = 1 [(scalapb.field).type = "EntityId"];
  map<string, string> params = 2;
}

message IntegPlatformMembersPageMessage {
  string entityId = 1 [(scalapb.field).type = "EntityId"];
  map<string, string> params = 2;
}