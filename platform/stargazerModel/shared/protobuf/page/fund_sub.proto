syntax = "proto3";

package page;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.page"
  single_file: true
  import: "anduin.id.docrequest.FormSubmissionId"
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.id.fundsub.FundSubSimulatorProgressId"
  import: "anduin.id.fundsub.FundSubSupportingDocId"
  import: "anduin.id.fundsub.FundSubSingleUserInvitationLinkId"
  import: "anduin.id.entity.EntityId"
  import: "anduin.id.signature.SignatureRequestId"
  import: "anduin.id.link.ProtectedLinkId"
};

message FundSubPageBox {
  reserved 4, 6;
  oneof page {
    FunSubAdminPageMessage admin_page = 1;
    FundSubAdminDocReviewPageMessage admin_doc_review_page = 11;
    FundSubLpPageMessage lp_page = 2;
    FundSubLpPageMessage lp_page_v2 = 12;
    FundSubLpPageMessage submit_page = 3;
    FundSubLpPageMessage doc_page = 7;
    FundSubSimulatorHomePageMessage simulator_home_page = 5;
    FundSubSimulatorLpEntryDashboardPageMessage simulator_lp_entry_dashboard_page = 8;
    FundSubSimulatorFaEntryDashboardPageMessage simulator_fa_entry_dashboard_page = 9;
    FundSubFundDashboardPageMessage fund_sub_fund_dashboard_page = 10;
    FundSubSubscriptionDocSignatureRequestPageMessage subscription_doc_signature_request = 13;
    FundSubSubscriptionDocSignatureRequestReassignPageMessage subscription_doc_signature_request_reassign = 14;
    FundSubCountersignSignatureRequestPageMessage countersign_signature_request = 15;
    FundSubCountersignSignatureRequestReassignPageMessage countersign_signature_request_reassign = 20;
    FundSubSupportingDocSignatureRequestPageMessage supporting_doc_signature_request = 16;
    FundSubSideLetterSignatureRequestPageMessage side_letter_signature_request = 24;
    FundSubTaxFormSignatureRequestPageMessage tax_form_signature_request = 17;
    FundSubFormSubmissionSignatureRequestPageMessage form_submission_signature_request = 18;
    FundSubProtectedLinkFundClosedPageMessage fund_sub_protected_link_fund_closed_page_message = 19;
    FundSubManageLpDocPageMessage fund_sub_manage_lp_doc_page_message = 21;
    FundSubCommentInboxPageMessage fund_sub_comment_inbox_page_message = 22;
    FundSubBatchCountersignPageMessage fund_sub_batch_countersign = 23;
    FundSubGenericSignatureRequestPageMessage fund_sub_generic_signature_request = 25;
    FundSubBatchCountersignRequestPageMessage fund_sub_batch_countersign_request = 26;
  }
}

message FunSubAdminPageMessage {
  string entity_id = 2 [(scalapb.field).type = "EntityId"];
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
  map<string, string> params = 3;
}

message FundSubAdminDocReviewPageMessage {
  string entity_id = 2 [(scalapb.field).type = "EntityId"];
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
}

message FundSubCommentInboxPageMessage {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
  map<string, string> params = 2;
}

message FundSubLpPageMessage {
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  map<string, string> params = 2;
}

message FundSubSimulatorHomePageMessage {
  string entity_id = 1 [(scalapb.field).type = "EntityId"];
  string progress_id = 2 [(scalapb.field).type = "FundSubSimulatorProgressId"];
}

message FundSubSimulatorLpEntryDashboardPageMessage {
  string fund_admin_entity_id = 1 [(scalapb.field).type = "EntityId"];
  string fund_sub_id = 2 [(scalapb.field).type = "FundSubId"];
}

message FundSubSimulatorFaEntryDashboardPageMessage {
  string fund_admin_entity_id = 1 [(scalapb.field).type = "EntityId"];
  string fund_sub_id = 2 [(scalapb.field).type = "FundSubId"];
}

message FundSubProtectedLinkFundClosedPageMessage {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
  string protected_link_id = 2 [(scalapb.field).type = "ProtectedLinkId"];
}

message FundSubFundDashboardPageMessage {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubSubscriptionDocSignatureRequestPageMessage {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  map<string, string> params = 2;
}

message FundSubSubscriptionDocSignatureRequestReassignPageMessage {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  map<string, string> params = 2;
}

message FundSubCountersignSignatureRequestPageMessage {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  map<string, string> params = 2;
}

message FundSubCountersignSignatureRequestReassignPageMessage {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  map<string, string> params = 2;
}

message FundSubSupportingDocSignatureRequestPageMessage {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string request_id = 2 [(scalapb.field).type = "SignatureRequestId"];
  map<string, string> params = 3;
}

message FundSubSideLetterSignatureRequestPageMessage {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string request_id = 2 [(scalapb.field).type = "SignatureRequestId"];
  map<string, string> params = 3;
}

message FundSubTaxFormSignatureRequestPageMessage {
  string doc_id = 1 [(scalapb.field).type = "FundSubSupportingDocId"];
  string request_id = 2 [(scalapb.field).type = "SignatureRequestId"];
  map<string, string> params = 3;
}

message FundSubFormSubmissionSignatureRequestPageMessage {
  string form_submission_id = 1 [(scalapb.field).type = "FormSubmissionId"];
  string request_id = 2 [(scalapb.field).type = "SignatureRequestId"];
  map<string, string> params = 3;
}

message FundSubManageLpDocPageMessage {
  string entity_id = 1 [(scalapb.field).type = "EntityId"];
  string lp_id = 2 [(scalapb.field).type = "FundSubLpId"];
  map<string, string> params = 3;
}

message FundSubHomePageMessage {
  map<string, string> params = 1;
}

message FundSubInvestInAdditionalEntityMessage {
  string link_id = 1 [(scalapb.field).type = "FundSubSingleUserInvitationLinkId"];
}

message FundSubBatchCountersignPageMessage {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubGenericSignatureRequestPageMessage {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string request_id = 2 [(scalapb.field).type = "SignatureRequestId"];
  map<string, string> params = 3;
}

message FundSubBatchCountersignRequestPageMessage {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
  string request_id = 2 [(scalapb.field).type = "SignatureRequestId"];
  map<string, string> params = 3;
}
