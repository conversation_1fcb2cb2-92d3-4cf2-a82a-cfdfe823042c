syntax = "proto3";

package page;

import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.page.account"
  single_file: true
  import: "anduin.id.offering.GlobalOfferingId"
};

message AccountLogin3 {
    google.protobuf.StringValue redirectUrl = 1;
    string global_offering_id = 2 [(scalapb.field).type = "Option[GlobalOfferingId]"];
    google.protobuf.StringValue email = 3;
}

message AccountSignup3 {
    google.protobuf.StringValue redirectUrl = 1;
    string global_offering_id = 2 [(scalapb.field).type = "Option[GlobalOfferingId]"];
    google.protobuf.StringValue email = 3;
}

message ResetPassword3 {
    google.protobuf.StringValue redirectUrl = 1;
    string global_offering_id = 2 [(scalapb.field).type = "Option[GlobalOfferingId]"];
    google.protobuf.StringValue email = 3;
}

message AccountLogin2 {
    google.protobuf.StringValue redirectUrl = 1;
    string global_offering_id = 2 [(scalapb.field).type = "Option[GlobalOfferingId]"];
}

message AccountSignup2 {
    google.protobuf.StringValue redirectUrl = 1;
    string global_offering_id = 2 [(scalapb.field).type = "Option[GlobalOfferingId]"];
}

message ResetPassword2 {
    google.protobuf.StringValue redirectUrl = 1;
    string global_offering_id = 2 [(scalapb.field).type = "Option[GlobalOfferingId]"];
}

message CompleteResetPassword {
    string token = 1;
}

message AccountRecovery {
    string token = 1;
}

message LinkAccount {
    string token = 1;
}

message CompleteLinkProfile {
    string status = 1;
    string redirectUrl = 2;
}

message RecaptchaBackdoor {
    string token = 1;
}

message SsoLinkAccount {
    string token = 1;
}

message LinkResolver {
    string resolveToken = 1;
    string redirectUrl = 2;
}

message AccountEnforcement {
    string accountEnforcementToken = 1;
    string redirectUrl = 2;
}

message AccountError {
    google.protobuf.StringValue errorCode = 1;
}

message CompleteSignup2 {
    string token = 1;
    google.protobuf.StringValue redirectUrl = 2;
}

message CompleteSignup1 {
    string token = 1;
}

enum AccountNoParamPages {
    AccountNoParamLogin = 0;
    AccountNoParamSignup = 1;
    AccountNoParamResetPassword = 2;
    AccountNoParamError = 3;
    AccountNoParamOauth2WhitelistBlock = 4;
}
