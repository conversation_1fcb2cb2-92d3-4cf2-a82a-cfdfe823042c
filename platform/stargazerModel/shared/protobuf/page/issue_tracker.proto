syntax = "proto3";

package page;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.page"
  single_file: true
  import: "anduin.id.TransactionId"
  import: "anduin.model.id.FileId"
  import: "anduin.id.entity.EntityId"
  import: "anduin.id.issuetracker.*"
};

enum IssueTrackerTargetView {
  Unspecified = 0;
  IssuesView = 1;
  DocumentsView = 2;
}

message IssueTrackerPageBox {
  oneof page {
    IssueTrackerModulePageMessage issue_tracker_module_page = 1;
    IssueTrackerDocumentPageMessage issue_tracker_document_page = 2;
    IssueTrackerLawFirmProjectDetailPageMessage issue_tracker_law_firm_project_detail_page = 3;
  }
}

message IssueTrackerLawFirmDashboardPageMessage {
  string entity_id_opt = 1 [(scalapb.field).type = "Option[EntityId]"];
}

message IssueTrackerModulePageMessage {
  string trxn_id = 1 [(scalapb.field).type = "TransactionId"];
  string channel_id_opt = 2 [(scalapb.field).type = "Option[IssueTrackerCollaborationChannelId]"];
  string entity_id_opt = 3 [(scalapb.field).type = "Option[EntityId]"];
}

message IssueTrackerDocumentPageMessage {
  string trxn_id = 1 [(scalapb.field).type = "TransactionId"];
  string file_id = 2 [(scalapb.field).type = "FileId"];
//  int32 target_view = 3; // TODO Bring back target view later
  string channel_id_opt = 4 [(scalapb.field).type = "Option[IssueTrackerCollaborationChannelId]"];
  string issue_id_opt = 5 [(scalapb.field).type = "Option[IssueId]"];
  string issue_list_id_opt = 6 [(scalapb.field).type = "Option[IssueListId]"];
  string entity_id_opt = 7 [(scalapb.field).type = "Option[EntityId]"];
}

message IssueTrackerLawFirmProjectDetailPageMessage {
  string trxn_id = 1 [(scalapb.field).type = "TransactionId"];
  string entity_id_opt = 2 [(scalapb.field).type = "Option[EntityId]"];
}
