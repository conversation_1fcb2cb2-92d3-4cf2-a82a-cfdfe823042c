syntax = "proto3";

package page;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.page"
  single_file: true
  import: "anduin.id.entity.EntityId"
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.model.id.DynamicFormId"
  import: "anduin.id.account.EnterpriseLoginConfigId"
  import: "anduin.id.environment.EnvironmentId"
  import: "anduin.id.funddata.FundDataFirmId"
  import: "anduin.id.ria.RiaEntityId"
};

message PortalDummyPageMessage {
  int32 dummy = 9999;
}

message EntityDetailPortalPageMessage {
  string entityId = 1 [(scalapb.field).type = "EntityId"];
}

message DynamicFormMessage {
  string formId = 1 [(scalapb.field).type = "DynamicFormId"];
}

message BuildS3FormPageMessage {
  string formId = 1 [(scalapb.field).type = "DynamicFormId"];
}

message FundSubPortalPageMessage {
  string fundSubId = 1 [(scalapb.field).type = "FundSubId"];
}

message EnterpriseLoginPageMessage {
  string configId = 1 [(scalapb.field).type = "EnterpriseLoginConfigId"];
}

message FundSubPortalAcceptRequestPageMessage {
  string token = 1;
}

message EnvironmentDetailPageMessage {
  string environmentId = 1 [(scalapb.field).type = "EnvironmentId"];
}

message ManageFundDataFirmPageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
}

message RiaEntityPortalPageMessage {
  string ria_entity_id = 1 [(scalapb.field).type = "RiaEntityId"];
}
