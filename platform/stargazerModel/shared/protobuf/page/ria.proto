syntax = "proto3";

package page;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.page"
  single_file: true
  import: "anduin.id.fundsub.ria.FundSubRiaGroupId"
  import: "anduin.id.ria.{RiaEntityId, RiaFundGroupId}"
};

message RiaHomePageMessage {
  int32 dummy = 9999;
}

message RiaEntityAdvisorAcceptInvitationPageMessage {
  string fund_sub_ria_group_id = 1 [(scalapb.field).type = "FundSubRiaGroupId"];
}

message RiaEntityPageMessage {
  string ria_entity_id = 1 [(scalapb.field).type = "RiaEntityId"];
}

message RiaEntityAdvisorPageMessage {
  string ria_entity_id = 1 [(scalapb.field).type = "RiaEntityId"];
}

enum FundPageTab {
  Subscriptions = 0;
  Advisors = 1;
}

message RiaEntityFundPageMessage {
  string ria_fund_group_id = 1 [(scalapb.field).type = "RiaFundGroupId"];
  FundPageTab tab = 2;
}