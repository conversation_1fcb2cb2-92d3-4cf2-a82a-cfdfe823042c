syntax = "proto3";

package page;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.page"
  single_file: true
  import: "anduin.id.entity.EntityId"
  };

message EntityPortalPageMessage {
  string entity_id = 1 [(scalapb.field).type = "EntityId"];
}

message EntityMembersPageMessage {
  string entity_id = 1 [(scalapb.field).type = "EntityId"];
}

message EntitySettingsPageMessage {
  string entity_id = 1 [(scalapb.field).type = "EntityId"];
}

message EntitySubscriptionsPageMessage {
  string entity_id = 1 [(scalapb.field).type = "EntityId"];
}

message EntityDataRoomPageMessage {
  string entity_id = 1 [(scalapb.field).type = "EntityId"];
}
