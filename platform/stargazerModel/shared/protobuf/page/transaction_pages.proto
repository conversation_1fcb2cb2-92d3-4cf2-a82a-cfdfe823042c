syntax = "proto3";

package page;

import "scalapb/scalapb.proto";
import "page/issue_tracker.proto";
import "page/data_room.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.page"
  single_file: true
};

message TransactionPageBox {
  reserved 1 to 14, 16, 17, 100, 200 to 204;
  reserved 10002, 20001;

  oneof page {
    DataRoomPageBox data_room = 15;

    IssueTrackerPageBox issue_tracker = 18;
  }
}
