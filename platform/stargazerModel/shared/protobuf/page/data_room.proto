syntax = "proto3";

package page;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.page"
  single_file: true
  import: "anduin.id.entity.EntityId"
  import: "anduin.id.link.ProtectedLinkId"
  import: "anduin.model.common.user.UserId"
  import: "anduin.refined.Refined"
  import: "anduin.model.id.*"
  import: "anduin.model.id.given"
  import: "anduin.model.id.stage.DataRoomWorkflowId"
  import: "anduin.id.dataroom.DataRoomGroupId"
};

message DataRoomPageBox {
  reserved 3, 7, 13;
  oneof sealed_value {
    DataRoomFolderPageMessage data_room_folder_page = 1;
    DataRoomAllParticipantsPageMessage data_room_all_participants_page = 2;
    DataRoomGeneralSettingsPageMessage data_room_general_settings_page = 4;
    DataRoomAcceptLinkInvitationPageMessage data_room_accept_link_invitation_page = 5;
    DataRoomLinkDashboardPageMessage data_room_link_dashboard_page_message = 6;
    DataRoomInsightsOverviewPageMessage data_room_insights_overview_page = 8;
    DataRoomInsightsParticipantsPageMessage data_room_insights_participants_page = 9;
    DataRoomInsightsContentPageMessage data_room_insights_content_page = 10;
    DataRoomInsightsCustomizePageMessage data_room_insights_customize_page = 11;
    DataRoomHomePageMessage data_room_home_page = 12;
    DataRoomInsightsActivityPageMessage data_room_insights_activity_page = 14;
    DataRoomRecentPageMessage data_room_recent_page = 15;
    DataRoomEmailSettingsPageMessage data_room_email_settings_page_message = 16;
    DataRoomIntegrationsSettingsPageMessage data_room_integrations_settings_page_message = 24;
    DataRoomContactSettingsPageMessage data_room_contact_settings_page_message = 17;
    DataRoomUnassignedParticipantsPageMessage data_room_unassigned_participants_page = 18;
    DataRoomAccessRequestsPageMessage data_room_access_requests_page = 19;
    DataRoomAllGroupsPageMessage data_room_all_groups_page = 20;
    DataRoomGroupDetailPageMessage data_room_group_detail_page = 21;
    DataRoomInsightsGroupsPageMessage data_room_insights_groups_page = 22;
    DataRoomTrashPageMessage data_room_trash_page = 23;
  }
}

message DataRoomHomePageMessage {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomFolderPageMessage {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string folder_id = 2 [(scalapb.field).type = "FolderId"];
  string file_id_suffix = 3 [(scalapb.field).type = "Option[Refined[FileId]]"];
}

message DataRoomRecentPageMessage {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string file_id_opt = 2 [(scalapb.field).type = "Option[FileId]"];
}

message DataRoomTrashPageMessage {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string file_id_opt = 2 [(scalapb.field).type = "Option[FileId]"];
}

message DataRoomAccessRequestsPageMessage {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomAllParticipantsPageMessage {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string edit_user_permissions = 2 [(scalapb.field).type = "Option[UserId]"];
  bool open_access_modal_by_default = 3;
}

message DataRoomUnassignedParticipantsPageMessage {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomAllGroupsPageMessage {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomGroupDetailPageMessage {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string data_room_group_id = 2 [(scalapb.field).type = "DataRoomGroupId"];
}

message DataRoomInsightsOverviewPageMessage {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomInsightsParticipantsPageMessage {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomInsightsGroupsPageMessage {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomInsightsContentPageMessage {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomInsightsActivityPageMessage {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomInsightsCustomizePageMessage {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomGeneralSettingsPageMessage {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomContactSettingsPageMessage {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomLinkDashboardPageMessage {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomEmailSettingsPageMessage {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomIntegrationsSettingsPageMessage {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  bool add_integrations = 2;
}

message DataRoomAcceptLinkInvitationPageMessage {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string link_id = 2 [(scalapb.field).type = "ProtectedLinkId"];
}

message DataRoomDashboardFilter {
  reserved 3, 4, 5, 6;

  DataRoomOrgFilterType org_filter = 9;
  DataRoomDashboardStatus status = 8;
  string contain_user = 1 [(scalapb.field).type = "Option[UserId]"];
  string creator_entity = 2 [(scalapb.field).type = "Option[EntityId]"];
  string creator = 7 [(scalapb.field).type = "Option[UserId]"];
}


enum DataRoomDashboardStatus {
  Active = 0;
  Invitation = 1;
  Archived = 2;
}

enum DataRoomOrgFilterType {
  All = 0;
  Internal = 1;
  External = 2;
  Single = 3;
}

// Invitation (Invitation, All)
// Single (Active, Single)
// External (Active, External)
// All (Active, All)
// Archived (Archived, All)

message DataRoomDashboardPageMessage {
  reserved 1;
  DataRoomDashboardFilter filter = 2;
}

message DataRoomSimulatorPendingDataPageMessage {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}
