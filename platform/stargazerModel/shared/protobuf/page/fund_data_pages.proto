syntax = "proto3";

package page;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.page"
  single_file: true
  flat_package: true
  import: "anduin.id.funddata.FundDataFirmId"
  import: "anduin.id.funddata.FundDataFundId"
  import: "anduin.id.funddata.fund.FundLegalEntityId"
  import: "anduin.id.funddata.FundDataOpportunityPageId"
  import: "anduin.id.funddata.FundDataInvestorId"
  import: "anduin.id.funddata.FundDataInvestmentEntityId"
  import: "anduin.id.contact.ContactId"
  import: "anduin.id.funddata.portal.PortalInstanceId"
  import: "anduin.id.flow.TFlowId"
};

message FundDataOverviewPageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
}

message FundDataFundPageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  map<string, string> params = 2;
}

message FundDataFund2PageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  map<string, string> params = 2;
}

message FundDataSettingPageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  map<string, string> params = 2;
}

message FundDataStandardFieldsPageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  map<string, string> params = 2;
}

message FundDataEntryPageMessage {
  map<string, string> params = 1;
}

message FundDataFundDetailPageMessage {
  string fund_id = 1 [(scalapb.field).type = "FundDataFundId"];
}

message FundDataFundDetail2PageMessage {
  string fund_id = 1 [(scalapb.field).type = "FundDataFundId"];
  map<string, string> params = 2;
}

message FundDataArchivedFundPageMessage{
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
}

message FundDataFundLegalEntityPageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  map<string, string> params = 2;
}

message FundDataFundLegalEntityDetailPageMessage {
  string fund_legal_entity_id = 1 [(scalapb.field).type = "FundLegalEntityId"];
  map<string, string> params = 2;
}

message FundDataInvestorPageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  map<string, string> params = 2;
}

message FundDataClientDetailPageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataInvestorId"];
  map<string, string> params = 2;
}

message FundDataContactPageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  map<string, string> params = 2;
}

message FundDataContactDetailPageMessage {
  string contact_id = 1 [(scalapb.field).type = "ContactId"];
  map<string, string> params = 2;
}

message FundDataInvestmentEntityDetailPageMessage {
  string investment_entity_id = 1 [(scalapb.field).type = "FundDataInvestmentEntityId"];
  map<string, string> params = 2;
}

message FundDataRequestPageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  map<string, string> params = 2;
}

message FundDataPermissionPageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  map<string, string> params = 2;
}

message FundDataAdminLandingPageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  map<string, string> params = 2;
}

message FundDataDataRoomPageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  map<string, string> params = 2;
}

message FundDataPortalHomePageBuilderPageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  map<string, string> params = 2;
}

message FundDataOpportunityPageBuilderPageMessage {
  string opportunity_page_id = 1 [(scalapb.field).type = "FundDataOpportunityPageId"];
  map<string, string> params = 2;
}

message FundDataPortalHomePageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  map<string, string> params = 2;
}

message FundDataOpportunityPageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  string opportunity_page_id = 2 [(scalapb.field).type = "FundDataOpportunityPageId"];
  map<string, string> params = 3;
}

message FundDataPageWithCustomUrlMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  string custom_url = 2;
  map<string, string> params = 3;
}

message FundDataShortenPortalHomePageMessage {
  map<string, string> params = 2;
}

message FundDataShortenOpportunityPageMessage {
  string opportunity_page_id = 1 [(scalapb.field).type = "FundDataOpportunityPageId"];
  map<string, string> params = 2;
}

message FundDataShortenPageWithCustomUrlMessage {
  string custom_url = 1;
  map<string, string> params = 2;
}

message FundDataVehiclePageBuilderPageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  map<string, string> params = 2;
}

message FundDataVehiclePageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  map<string, string> params = 2;
}

message FundDataShortenVehiclePageMessage {
  map<string, string> params = 2;
}

message FundDataPortalLpDocumentPageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  map<string, string> params = 2;
}

message FundDataShortenPortalLpDocumentPageMessage {
  map<string, string> params = 1; 
}

message FundDataNoAccessLpPageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
}

message FundDataNoAccessInsideFirmPageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
}

message FundDataPortalInstancePageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  map<string, string> params = 2;
}

message FundDataPortalDocumentPageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  map<string, string> params = 2;
}

message FundDataPortalCommunicationPageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  map<string, string> params = 2;
}

message FundDataPortalDistributionDetailPageMessage {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  string doc_distribution_id = 2 [(scalapb.field).type = "TFlowId"];
}

message FundDataPortalDetailPageMessage {
  string portal_instance_id = 1 [(scalapb.field).type = "PortalInstanceId"];
  map<string, string> params = 2;
}
