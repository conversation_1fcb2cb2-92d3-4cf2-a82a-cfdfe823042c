syntax = "proto3";

package page;

import "page/account.proto";
import "page/actiontoken.proto";
import "page/app_navigation.proto";
import "page/bootstrap.proto";
import "page/data_extract_pages.proto";
import "page/data_room.proto";
import "page/entity.proto";
import "page/error.proto";
import "page/fund_data_pages.proto";
import "page/fund_sub.proto";
import "page/issue_tracker.proto";
import "page/lp_profile_pages.proto";
import "page/narya/pages.proto";
import "page/portal.proto";
import "page/preview.proto";
import "page/protected_link.proto";
import "page/signature.proto";
import "page/transaction_pages.proto";
import "page/integ_platform_pages.proto";
import "page/ria.proto";
import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.page"
  single_file: true
};

message PageBox {
  reserved 4 to 7, 403, 502, 600 to 602, 702, 707, 708, 900, 1000 to 1003;
  reserved 1102, 1110, 1120;
  reserved 1200 to 1205, 1208 to 1210, 1212, 1213, 1215 to 1232, 1233 to 1242;
  reserved 1400, 1502 to 1505, 1600 to 1607, 1610, 1632, 1900, 2000, 2100, 2419;
  reserved 710, 711, 714;
  reserved 550, 603, 701;

  oneof page {
    StaticAuthPageMessage static_auth_page = 2;
    StaticNonAuthPageMessage static_non_auth_page = 3;

    ActionTokenStartMessage action_token_start = 703;
    AfterBootstrapMessage after_bootstrap = 705;
    CompleteSignupMessage complete_signup = 706;
    ProtectedLinkMessage protected_link = 709;

    ProtectedLinkNoAuthPageMessage protected_link_no_auth = 712;
    ProtectedLinkAuthPageMessage protected_link_auth = 713;

    TransactionPageBox transaction_page_box = 800;
    PreviewFilePageMessage preview_file = 185;

    // Entity pages.
    EntityMembersPageMessage entity_members = 1206;
    EntitySettingsPageMessage entity_settings = 1207;
    EntityDataRoomPageMessage entity_data_room = 1211;
    EntityPortalPageMessage entity_portal = 1214;
    EntitySubscriptionsPageMessage entity_subscriptions = 1243;

    // Admin portal
    PortalDummyPageMessage portal_main = 1100;
    PortalDummyPageMessage whitelist_domain = 1101;
    PortalDummyPageMessage custom_domain_portal = 1103;
    EntityDetailPortalPageMessage entity_detail_portal = 1104;
    PortalDummyPageMessage form_builder = 1105;
    DynamicFormMessage dynamic_form_builder = 1106;
    PortalDummyPageMessage manage_s3_form = 1107;
    BuildS3FormPageMessage build_s3_form = 1108;
    PortalDummyPageMessage manage_email = 1109;
    PortalDummyPageMessage manage_user = 1111;
    PortalDummyPageMessage admins = 1112;
    PortalDummyPageMessage manage_entity = 1113;
    PortalDummyPageMessage manage_fund_sub = 1114;
    PortalDummyPageMessage data_protection = 1115;
    PortalDummyPageMessage server_config = 1116;
    PortalDummyPageMessage signature_analytics = 1117;
    PortalDummyPageMessage data_room = 1118;
    PortalDummyPageMessage issue_tracker = 1119;
    PortalDummyPageMessage tax_form = 1121;
    PortalDummyPageMessage oauth2_integration = 1122;
    PortalDummyPageMessage qa_backdoor = 1123;
    FundSubPortalPageMessage fund_sub_portal = 1124;
    PortalDummyPageMessage enterprise_login_manager = 1125;
    EnterpriseLoginPageMessage enterprise_login_portal = 1126;
    PortalDummyPageMessage disclaimers = 1127;
    PortalDummyPageMessage manage_docusign_integration = 1153;
    PortalDummyPageMessage public_api = 1128;
    PortalDummyPageMessage manage_portal_fundsub_team = 1129;
    PortalDummyPageMessage manage_portal_fundsub_user = 1130;
    PortalDummyPageMessage lp_profiles = 1131;
    PortalDummyPageMessage data_analysis = 1132;
    FundSubPortalAcceptRequestPageMessage fund_sub_portal_accept_request = 1133;
    PortalDummyPageMessage environment = 1134;
    EnvironmentDetailPageMessage environment_detail = 1135;
    PortalDummyPageMessage manage_fund_data = 1136;
    ManageFundDataFirmPageMessage manage_fund_data_firm = 1137;
    PortalDummyPageMessage asa_management = 1138;
    PortalDummyPageMessage asa_ontology = 1139;
    PortalDummyPageMessage data_pipeline = 1140;
    PortalDummyPageMessage multi_region = 1141;
    PortalDummyPageMessage blueprint = 1142;
    PortalDummyPageMessage integ_platform = 1143;
    PortalDummyPageMessage announcement = 1144;
    PortalDummyPageMessage ria_portal = 1145;
    RiaEntityPortalPageMessage ria_entity_portal = 1146;
    PortalDummyPageMessage sa_management = 1147;
    PortalDummyPageMessage email_sending_manager_home = 1148;
    PortalDummyPageMessage email_sending_manager_urgent = 1149;
    PortalDummyPageMessage email_sending_manager_scheduled = 1150;
    PortalDummyPageMessage email_sending_manager_space = 1151;
    PortalDummyPageMessage email_sending_manager_provider = 1152;

    // No login
    narya.NaryaPageBox narya_page = 1300;

    // Fundsub
    FundSubPageBox fund_sub = 1500;
    FundSubHomePageMessage fund_sub_home_page = 1501;
    FundSubInvestInAdditionalEntityMessage fund_sub_invest_in_additional_entity = 1506;

    // Signature
    SignatureRequestPageMessage signature_request = 1801;
    SignatureDashboardPageMessage signature_dashboard = 1802;
    SignatureEntityDashboardPageMessage signature_entity_dashboard = 1803;

    // Issue tracker
    IssueTrackerLawFirmDashboardPageMessage issue_tracker_law_firm_dashboard = 2001;

    // Data room
    DataRoomDashboardPageMessage data_room_dashboard_page_message = 2200;
    DataRoomSimulatorPendingDataPageMessage data_room_simulator_pending_data_page_message = 2201;

    // LpProfile
    InvestmentEntityProfilesPageMessage investment_entity_profiles_page = 2300;
    InvestmentEntitySubscriptionsPageMessage investment_entity_subscriptions_page_message = 2301;
    InvestmentEntityPermissionsPageMessage investment_entity_permissions_page_message = 2302;
    InvestmentEntityAuditLogPageMessage investment_entity_audit_log_page_message = 2303;
    InvestmentEntityOnboardingPageMessage investment_entity_onboarding_page_message = 2304;
    InvestmentEntityDocumentsPageMessage investment_entity_documents_page_message = 2305;

    LpProfileRequestPageMessage lp_profile_request_page_message = 2399;

    // Fund Data
    FundDataOverviewPageMessage fund_data_overview_page_message = 2400;
    FundDataFundPageMessage fund_data_fund_page_message = 2401;
    FundDataFund2PageMessage fund_data_fund2_page_message = 2406;
    FundDataFundDetailPageMessage fund_data_fund_detail_page_message = 2402;
    FundDataFundDetail2PageMessage fund_data_fund_detail2_page_message = 2407;
    FundDataArchivedFundPageMessage fund_data_archived_fund_page_message = 2410;
    FundDataFundLegalEntityPageMessage fund_data_fund_legal_entity_page_message = 2430;
    FundDataFundLegalEntityDetailPageMessage fund_data_fund_legal_entity_detail_page_message = 2431;
    FundDataInvestorPageMessage fund_data_investor_page_message = 2403;
    FundDataClientDetailPageMessage fund_data_client_detail_page_message = 2434;
    FundDataContactPageMessage fund_data_contact_page_message = 2427;
    FundDataContactDetailPageMessage fund_data_contact_detail_page_message = 2428;
    FundDataInvestmentEntityDetailPageMessage fund_data_investment_entity_detail_page_message = 2404;
    FundDataRequestPageMessage fund_data_request_page_message = 2405;
    FundDataPermissionPageMessage fund_data_permission_page_message = 2411;
    FundDataNoAccessInsideFirmPageMessage fund_data_no_access_inside_firm_page_message = 2412;
    FundDataAdminLandingPageMessage fund_data_admin_landing_page_message = 2413;
    FundDataDataRoomPageMessage fund_data_data_room_page_message = 2414;

    FundDataPortalHomePageBuilderPageMessage fund_data_portal_home_page_builder_page_message = 2415;
    FundDataOpportunityPageBuilderPageMessage fund_data_opportunity_page_builder_page_message = 2416;
    FundDataPortalHomePageMessage fund_data_portal_home_page_message = 2417;
    FundDataOpportunityPageMessage fund_data_opportunity_page_message = 2418;

    FundDataVehiclePageBuilderPageMessage fund_data_vehicle_page_builder_page_message = 2420;
    FundDataVehiclePageMessage fund_data_vehicle_page_message = 2421;
    FundDataShortenVehiclePageMessage fund_data_shorten_vehicle_page_message = 2422;
    FundDataPortalLpDocumentPageMessage fund_data_portal_lp_document_page_message = 2429;
    FundDataShortenPortalLpDocumentPageMessage fund_data_shorten_portal_lp_document_page_message = 2432;
    FundDataShortenPageWithCustomUrlMessage fund_data_shorten_page_with_custom_url_message = 2423;
    FundDataShortenOpportunityPageMessage fund_data_shorten_opportunity_page_message = 2424;
    FundDataShortenPortalHomePageMessage fund_data_shorten_portal_home_page_message = 2425;
    FundDataPageWithCustomUrlMessage fund_data_page_with_custom_url_message = 2426;
    FundDataNoAccessLpPageMessage fund_data_no_access_lp_page_message = 2433;
    
    FundDataPortalInstancePageMessage fund_data_portal_instance_page_message = 2450;
    FundDataPortalDocumentPageMessage fund_data_portal_document_page_message = 2451;
    FundDataPortalCommunicationPageMessage fund_data_portal_communication_page_message = 2454;
    FundDataPortalDistributionDetailPageMessage fund_data_portal_distribution_detail_page_message = 2453;
    FundDataPortalDetailPageMessage fund_data_portal_detail_page_message = 2452;

    FundDataStandardFieldsPageMessage fund_data_standard_fields_page_message = 2497;
    FundDataEntryPageMessage fund_data_entry_page_message = 2498;
    FundDataSettingPageMessage fund_data_setting_page_message = 2499;

    //DataExtract
    DataExtractProjectPageMessage data_extract_project_page_message = 2500;

    // Integration Platform
    IntegPlatformMainPageMessage integ_platform_main_page_message = 2600;
    IntegPlatformBrowsePageMessage integ_platform_browse_page_message = 2601;
    IntegPlatformManagePageMessage integ_platform_manage_page_message = 2602;
    IntegPlatformMembersPageMessage integ_platform_members_page_message = 2700;

    // Ria
    RiaHomePageMessage ria_home_page_message = 2603;
    RiaEntityAdvisorAcceptInvitationPageMessage ria_entity_advisor_accept_invitation_page_message = 2604;
    RiaEntityPageMessage ria_entity_page_message = 2605;
    RiaEntityAdvisorPageMessage ria_entity_advisor_page_message = 2606;
    RiaEntityFundPageMessage ria_entity_fund_page_message = 2607;

    // Account
    AccountLogin3 account_login_3 = 10000;
    AccountSignup3 account_signup_3 = 10001;
    ResetPassword3 reset_password_3 = 10002;
    AccountLogin2 account_login_2 = 10100;
    AccountSignup2 account_signup_2 = 10101;
    ResetPassword2 reset_password_2 = 10102;
    CompleteResetPassword complete_reset_password = 10003;
    LinkAccount link_account = 10004;
    CompleteLinkProfile complete_link_profile = 10005;
    AccountError account_error = 10006;
    AccountNoParamPages account_no_param_page = 10007;
    RecaptchaBackdoor recaptcha_backdoor = 10008;
    SsoLinkAccount sso_link_account = 10010;
    LinkResolver link_resolver = 10011;
    CompleteSignup2 complete_signup_2 = 10012;
    CompleteSignup1 complete_signup_1 = 10013;
    AccountRecovery account_recovery = 10014;
    AccountEnforcement account_enforcement = 10015;

    // Cross app redirect
    CrossAppRedirectPageMessage cross_app_redirect_page = 10009;

    // Error
    ErrorPage error_page = 99999;
  }
}

enum StaticAuthPageMessage {
  reserved 4, 8, 9, 11, 12, 13, 17;
  Dashboard = 0;
  ObsoleteProfile = 1;
  ObsoleteChangePassword = 3;
  ObsoleteNotificationPreferences = 5;
  FileManager = 6;
  ApplicationSetting = 7; // Obsoleted

  RedirectToNaturalPersonOrg = 10;
  DataRoomHomePage = 14;
  FundSubSimulatorDashboardPage = 15;
  DataRoomSimulatorDashboardPage = 16;
  CommonEntryPoint = 1337;

  FundDataHomePage = 18;

  // Logout
  Logout = 42;
}

enum StaticNonAuthPageMessage {
  reserved 14, 15, 17 to 24, 32, 33;

  // Account pages.
  ObsoleteSignup = 0;
  ObsoleteEmailSent = 1;
  ObsoleteCompleteSignup = 2;
  ObsoleteSignupSucceed = 3;
  ObsoleteVerifyIdCode = 4;

  Login = 5;

  ObsoleteForgotPassword = 6;
  ObsoleteForgotPasswordEmailSent = 7;
  ObsoleteResetPassword = 8;
  ObsoleteResetPasswordSucceed = 9;

  // Miscellaneous pages.
  Terms = 10;
  Privacy = 11;
  DesignWorkspacePage = 12;
  NoAccessPage = 13;
  FundSubFlexibleDemo = 31;
  DataRoomSimulatorSignup = 26;

  // Bootstrapping pages
  PrivateGuardPage = 16;
  NoLoginGuardPage = 25;

  // Gandalf
  GandalfDashboard = 30;
}
