syntax = "proto3";

package page;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.page"
  single_file: true
  import: "anduin.id.TransactionId"
  import: "anduin.model.id.FolderId"
};

message DealDataRoomPageMessage {
  string trxn_id = 1 [(scalapb.field).type = "TransactionId"];
  string folder_id_opt = 2 [(scalapb.field).type = "Option[FolderId]"];
}
