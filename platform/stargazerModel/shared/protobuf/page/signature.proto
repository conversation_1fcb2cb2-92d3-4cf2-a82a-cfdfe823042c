syntax = "proto3";

package page;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.page"
  single_file: true
  import: "anduin.id.entity.EntityId"
  import: "anduin.id.signature.SignatureRequestId"
  };

message SignatureDashboardPageMessage {
  int32 dummy = 1;
}

message SignatureEntityDashboardPageMessage {
  string entityId = 1 [(scalapb.field).type = "EntityId"];
}

message SignatureRequestPageMessage {
  string signatureRequestId = 1 [(scalapb.field).type = "SignatureRequestId"];
}
