syntax = "proto3";

package page;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.page"
  single_file: true
  flat_package: true
  import: "anduin.id.link.ProtectedLinkId"
};

message ProtectedLinkMessage {
  string link_id = 1 [(scalapb.field).type = "ProtectedLinkId"];
}

message ProtectedLinkNoAuthPageMessage {
  string link_id = 1 [(scalapb.field).type = "ProtectedLinkId"];
}

message ProtectedLinkAuthPageMessage {
  string link_id = 1 [(scalapb.field).type = "ProtectedLinkId"];
}
