syntax = "proto3";

package service.file;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.service.file"
  single_file: true
  flat_package: true
  preserve_unknown_fields: false
  import: "anduin.id.entity.EntityId"
};

message RootFolderViewKey {
}

message DataRoomProductFolderViewKey {
}

message InternalDataRoomListFolderViewKey {
  string entity_id = 1 [(scalapb.field).type = "EntityId"];
}

message ExternalDataRoomListFolderViewKey {
}

message FolderViewKey {
  reserved 1 to 9;
  oneof sealed_value {
    RootFolderViewKey root = 10;
    DataRoomProductFolderViewKey data_room = 11;
    InternalDataRoomListFolderViewKey internal_data_room = 12;
    ExternalDataRoomListFolderViewKey external_data_room = 13;
  }
}
