syntax = "proto3";

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf"
  single_file: true
  import: "anduin.id.user.{UserRestrictedId, UserTrackingId}"
  import: "anduin.id.signature.UserSignatureId"

  preamble: "sealed trait UserModelBase extends scalapb.GeneratedMessage {"
  preamble: "}"
};

enum UserTypeMessage {
    reserved 2, 6;
    ZombieUser = 0;
    BotUser = 1;
    RegisteredUser = 3;
    NonUser = 4;
    AdminUser = 5;
    ServiceAccount = 7;
}

enum PortalModuleMessage {
    reserved 1, 2;
    DataRoom = 0;
    DataRoomPremiumFeatures = 3;
    FundSubContact = 4;
    FundSubNPSSurvey = 5;
}

message UserRestrictedModel {
    option (scalapb.message).extends = "UserModelBase";
    string user_restricted_id = 1 [(scalapb.field).type = "UserRestrictedId"];
    UserTypeMessage user_type = 2;
    ZoneIdMessage timezone = 3 [(scalapb.field).type = "java.time.ZoneId"];
}

// Empty
message UserSignatureModel {
  option (scalapb.message).extends = "UserModelBase";
  string user_signature_id = 1 [(scalapb.field).type = "UserSignatureId"];
}

message UserTrackingModel {
    reserved 2, 4, 5, 6, 7, 8, 9, 10, 11;
    reserved "invitation_entity_ids", "invited_with_entity_white_label", "seen_custom_column_filter_guide", "seen_doc_mapping_guide_message";
    reserved "seen_lp_onboard_guide_tour", "seen_data_room_notification_onboard", "seen_attach_comment_guide", "seen_auto_selected_comments_banner";
    reserved "fund_sub_recent_dashboard_id";
    option (scalapb.message).extends = "UserModelBase";
    string user_tracking_id = 1 [(scalapb.field).type = "UserTrackingId"];
    ZonedDateTimeMessage last_login = 3 [(scalapb.field).type = "java.time.ZonedDateTime"];
}

message RecordTypeUnion {
  UserRestrictedModel _UserRestrictedModel = 1;
  UserSignatureModel _UserSignatureModel = 2;
  UserTrackingModel _UserTrackingModel = 3;
}
