// Copyright (C) 2014-2025 Anduin Transactions Inc.

package build.platform.stargazerModel

import build_.build.dependency_.CommonDependencies
import mill.scalalib.*
import anduin.build.AnduinVersions

object StargazerModel {

  lazy val sharedDeps = Seq(
    mvn"org.scodec::scodec-bits::${AnduinVersions.scodecBits}"
      .exclude("com.chuusai" -> AnduinVersions.j2sjs("shapeless"))
      .exclude("com.chuusai" -> AnduinVersions.j2s("shapeless")),
    mvn"org.scodec::scodec-core::${AnduinVersions.scodecCore}"
      .exclude("org.scodec" -> AnduinVersions.j2sjs("scodec-bits"))
      .exclude("org.scodec" -> AnduinVersions.j2s("scodec-bits"))
      .exclude("com.chuusai" -> AnduinVersions.j2sjs("shapeless"))
      .exclude("com.chuusai" -> AnduinVersions.j2s("shapeless")),
    mvn"io.github.scalapb-json::scalapb-circe::${AnduinVersions.scalapbCirce}"
      .exclude("com.google.protobuf" -> "protobuf-java")
      .exclude("com.thesamet.scalapb" -> AnduinVersions.j2sjs("scalapb-runtime"))
      .exclude("com.thesamet.scalapb" -> AnduinVersions.j2s("scalapb-runtime"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-parser"))
      .exclude("io.circe" -> AnduinVersions.j2sjs("circe-parser"))
      .exclude("io.github.cquiroz" -> AnduinVersions.j2sjs("scala-java-time"))
      .exclude("io.github.cquiroz" -> AnduinVersions.j2s("scala-java-time")),
    mvn"com.lihaoyi::upickle::${AnduinVersions.upickle}"
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("ujson"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("ujson"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("geny"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("geny")),
    mvn"com.lihaoyi::ujson::${AnduinVersions.ujson}"
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("geny"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("geny")),
    mvn"com.lihaoyi::geny::${AnduinVersions.geny}",
    mvn"org.typelevel::squants::${AnduinVersions.squants}"
      .exclude("org.scalameta" -> AnduinVersions.j2s("mdoc")),
    mvn"org.gnieh::diffson-circe::${AnduinVersions.diffson}"
      .exclude("io.circe" -> AnduinVersions.j2s("circe-core"))
      .exclude("io.circe" -> AnduinVersions.j2sjs("circe-core"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-parser"))
      .exclude("io.circe" -> AnduinVersions.j2sjs("circe-parser"))
      .exclude("org.gnieh" -> AnduinVersions.j2s("diffson-core"))
      .exclude("org.gnieh" -> AnduinVersions.j2sjs("diffson-core")),
    mvn"org.gnieh::diffson-core::${AnduinVersions.diffson}"
      .exclude("org.typelevel" -> AnduinVersions.j2sjs("cats-core"))
      .exclude("org.typelevel" -> AnduinVersions.j2s("cats-core")),
    mvn"com.github.plokhotnyuk.jsoniter-scala::jsoniter-scala-core::${AnduinVersions.jsoniter}"
      .exclude("io.github.cquiroz" -> AnduinVersions.j2sjs("scala-java-time"))
      .exclude("io.github.cquiroz" -> AnduinVersions.j2sjs("scala-java-locales")),
    mvn"com.github.plokhotnyuk.jsoniter-scala::jsoniter-scala-circe::${AnduinVersions.jsoniter}"
      .exclude("com.github.plokhotnyuk.jsoniter-scala" -> AnduinVersions.j2sjs("jsoniter-scala-core"))
      .exclude("com.github.plokhotnyuk.jsoniter-scala" -> AnduinVersions.j2s("jsoniter-scala-core"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-core"))
      .exclude("io.circe" -> AnduinVersions.j2sjs("circe-core")),
    mvn"org.apache.commons:commons-lang3:${AnduinVersions.apacheCommonLang}"
  )

  lazy val jvmDeps = Seq(
    mvn"org.typelevel::jawn-parser:${AnduinVersions.jawnParser}",
    mvn"com.github.jnr:jnr-posix:${AnduinVersions.githubJnr}"
      .exclude("org.ow2.asm" -> "asm")
      .exclude("org.ow2.asm" -> "asm-commons")
      .exclude("org.ow2.asm" -> "asm-analysis")
      .exclude("org.ow2.asm" -> "asm-tree")
      .exclude("org.ow2.asm" -> "asm-util")
  )

}
