package build.platform.stargazerCore

import build_.build.dependency_.CommonDependencies
import mill.scalalib.*
import anduin.build.AnduinVersions

object StargazerCore {

  lazy val svixVersion = AnduinVersions.svix

  lazy val sharedDeps = CommonDependencies.monocleDeps ++ Seq(
    mvn"dev.zio::izumi-reflect::${AnduinVersions.zioReflect}",
    mvn"dev.zio::zio::${AnduinVersions.zio}"
      .exclude("dev.zio" -> AnduinVersions.j2s("izumi-reflect"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("izumi-reflect")),
    mvn"dev.zio::zio-concurrent::${AnduinVersions.zio}"
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio")),
    mvn"dev.zio::zio-managed::${AnduinVersions.zio}"
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio-streams")),
    mvn"dev.zio::zio-streams::${AnduinVersions.zio}"
      .exclude("dev.zio" -> AnduinVersions.j2s("izumi-reflect"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("izumi-reflect")),
    mvn"dev.zio::zio-query::${AnduinVersions.zioQuery}"
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio")),
    mvn"dev.zio::zio-prelude::${AnduinVersions.zioPrelude}"
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio-streams"))
      .exclude("io.github.cquiroz" -> AnduinVersions.j2sjs("scala-java-time"))
      .exclude("io.github.cquiroz" -> AnduinVersions.j2sjs("scala-java-time-tzdb")),
    mvn"nl.vroste::rezilience::${AnduinVersions.rezilience}"
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio-streams")),
    mvn"com.softwaremill.sttp.model::core::${AnduinVersions.sttpModelCore}"
      .exclude("io.github.cquiroz" -> AnduinVersions.j2sjs("scala-java-time"))
      .exclude("io.github.cquiroz" -> AnduinVersions.j2sjs("scala-java-locales")),
    mvn"com.softwaremill.sttp.shared::ws::${AnduinVersions.sttpShared}"
      .exclude("com.softwaremill.sttp.model" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.model" -> AnduinVersions.j2sjs("core")),
    mvn"com.softwaremill.sttp.shared::core::${AnduinVersions.sttpShared}",
    mvn"com.softwaremill.sttp.shared::zio::${AnduinVersions.sttpShared}"
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2sjs("core"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio-streams")),
    mvn"com.softwaremill.magnolia1_3::magnolia::${AnduinVersions.magnolia}",
    mvn"com.softwaremill.sttp.tapir::tapir-core::${AnduinVersions.tapirVersion}"
      .exclude("com.softwaremill.magnolia1_3" -> AnduinVersions.j2s("magnolia"))
      .exclude("com.softwaremill.magnolia1_3" -> AnduinVersions.j2sjs("magnolia"))
      .exclude("com.softwaremill.sttp.model" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.model" -> AnduinVersions.j2sjs("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2sjs("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2s("ws"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2sjs("ws")),
    mvn"com.softwaremill.sttp.tapir::tapir-files::${AnduinVersions.tapirVersion}"
      .exclude("com.softwaremill.sttp.tapir" -> AnduinVersions.j2s("tapir-core"))
      .exclude("com.softwaremill.sttp.tapir" -> AnduinVersions.j2sjs("tapir-core")),
    mvn"com.softwaremill.sttp.tapir::tapir-json-circe::${AnduinVersions.tapirVersion}"
      .exclude("io.circe" -> AnduinVersions.j2s("circe-core"))
      .exclude("io.circe" -> AnduinVersions.j2sjs("circe-core"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-parser"))
      .exclude("io.circe" -> AnduinVersions.j2sjs("circe-parser"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-generic"))
      .exclude("io.circe" -> AnduinVersions.j2sjs("circe-generic"))
      .exclude("com.softwaremill.sttp.tapir" -> AnduinVersions.j2s("tapir-core"))
      .exclude("com.softwaremill.sttp.tapir" -> AnduinVersions.j2sjs("tapir-core")),
    mvn"com.softwaremill.sttp.tapir::tapir-sttp-client::${AnduinVersions.tapirVersion}"
      .exclude("com.softwaremill.sttp.client3" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.client3" -> AnduinVersions.j2sjs("core"))
      .exclude("com.softwaremill.sttp.tapir" -> AnduinVersions.j2s("tapir-core"))
      .exclude("com.softwaremill.sttp.tapir" -> AnduinVersions.j2sjs("tapir-core"))
      .exclude("com.softwaremill.sttp.model" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.model" -> AnduinVersions.j2sjs("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2sjs("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2s("ws"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2sjs("ws")),
    mvn"com.softwaremill.sttp.client3::core::${AnduinVersions.sttp}"
      .exclude("com.softwaremill.sttp.model" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.model" -> AnduinVersions.j2sjs("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2sjs("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2s("ws"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2sjs("ws")),
    mvn"com.softwaremill.sttp.client3::zio::${AnduinVersions.sttp}"
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-interop-reactivestreams"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio-interop-reactivestreams"))
      .exclude("io.github.cquiroz" -> AnduinVersions.j2sjs("scala-java-time"))
      .exclude("io.github.cquiroz" -> AnduinVersions.j2sjs("scala-java-time-tzdb"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2s("zio"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2sjs("zio"))
      .exclude("com.softwaremill.sttp.client3" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.client3" -> AnduinVersions.j2sjs("core")),
    mvn"io.github.arainko::ducktape::${AnduinVersions.ducktape}",
    mvn"com.squareup.okhttp3:okhttp:4.12.0"
      .exclude("com.squareup.okio" -> "okio")
      .exclude("org.jetbrains.kotlin" -> "kotlin-stdlib")
      .exclude("org.jetbrains.kotlin" -> "kotlin-stdlib-jdk8"),
    mvn"com.svix:svix:$svixVersion"
      .exclude("com.squareup.okhttp3" -> "okhttp")
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind")
      .exclude("com.fasterxml.jackson.datatype" -> "jackson-datatype-jdk8")
      .exclude("com.fasterxml.jackson.datatype" -> "jackson-datatype-jsr310")
      .exclude("com.google.code.findbugs" -> "jsr305")
  )

  lazy val jvmDeps = CommonDependencies.guavaDeps ++ CommonDependencies.loggingJVMDeps ++ Seq(
    // logging
    mvn"org.ow2.asm:asm:${AnduinVersions.asm}",
    mvn"com.google.code.findbugs:jsr305:${AnduinVersions.findBugs}",
    mvn"com.google.errorprone:error_prone_annotations:${AnduinVersions.errorProne}",
    mvn"org.checkerframework:checker-qual:${AnduinVersions.checkerQual}",
    mvn"com.google.guava:guava:${AnduinVersions.guava}"
      .exclude("org.checkerframework" -> "checker-qual")
      .exclude("com.google.errorprone" -> "error_prone_annotations")
      .exclude("com.google.code.findbugs" -> "jsr305"),
    mvn"org.typelevel::cats-collections-core:${AnduinVersions.catsCollection}"
      .exclude("org.typelevel" -> AnduinVersions.j2s("algebra"))
      .exclude("org.typelevel" -> AnduinVersions.j2s("cats-core")),
    mvn"org.reactivestreams:reactive-streams:${AnduinVersions.reactiveStream}",
    mvn"dev.zio::zio-interop-reactivestreams:${AnduinVersions.zioInteropReactiveStreams}"
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("org.reactivestreams" -> "reactive-streams"),
    mvn"dev.zio::zio-cache::${AnduinVersions.zioCache}"
      .exclude("dev.zio" -> AnduinVersions.j2s("zio")),
    mvn"dev.zio::zio-metrics-connectors:${AnduinVersions.zioMetricsConnectors}"
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-json"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-http")),
    mvn"io.micrometer:micrometer-core:${AnduinVersions.micrometer}",
    mvn"io.micrometer:micrometer-registry-prometheus:${AnduinVersions.micrometer}"
      .exclude("com.google.protobuf" -> "protobuf-java")
      .exclude("io.micrometer" -> "micrometer-core"),
    mvn"dev.zio::zio-metrics-connectors-micrometer:${AnduinVersions.zioMetricsConnectors}"
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-metrics-connectors"))
      .exclude("io.micrometer" -> "micrometer-core"),
    mvn"org.yaml:snakeyaml:${AnduinVersions.snakeYaml}",
    mvn"io.circe::circe-yaml:1.15.0"
      .exclude("io.circe" -> AnduinVersions.j2s("circe-core"))
      .exclude("org.yaml" -> "snakeyaml"),
    mvn"com.softwaremill.sttp.apispec::openapi-circe-yaml:${AnduinVersions.sttpApi}"
      .exclude("io.circe" -> AnduinVersions.j2s("circe-core"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-parser"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-generic"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-yaml"))
      .exclude("com.softwaremill.sttp.model" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2s("ws"))
      .excludeOrg("com.softwaremill.sttp.apispec")
      .exclude("org.yaml" -> "snakeyaml"),
    mvn"com.softwaremill.sttp.tapir::tapir-swagger-ui-bundle:${AnduinVersions.tapirVersion}"
      .exclude("io.circe" -> AnduinVersions.j2s("circe-core"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-parser"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-generic"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-yaml"))
      .exclude("com.softwaremill.sttp.tapir" -> AnduinVersions.j2s("tapir-core"))
      .exclude("com.softwaremill.sttp.tapir" -> AnduinVersions.j2s("tapir-openapi-docs"))
      .exclude("com.softwaremill.sttp.model" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2s("ws"))
      .excludeOrg("com.softwaremill.sttp.apispec")
      .exclude("org.yaml" -> "snakeyaml"),
    mvn"com.softwaremill.sttp.tapir::tapir-openapi-docs:${AnduinVersions.tapirVersion}"
      .exclude("io.circe" -> AnduinVersions.j2s("circe-core"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-parser"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-generic"))
      .exclude("com.softwaremill.sttp.tapir" -> AnduinVersions.j2s("tapir-core"))
      .exclude("com.softwaremill.sttp.model" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2s("ws"))
      .excludeOrg("com.softwaremill.sttp.apispec")
      .exclude("org.yaml" -> "snakeyaml"),
    mvn"com.softwaremill.sttp.tapir::tapir-armeria-server-zio:${AnduinVersions.tapirVersion}"
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2s("zio"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2s("armeria"))
      .exclude("com.softwaremill.sttp.tapir" -> AnduinVersions.j2s("tapir-core"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-interop-reactivestreams"))
      .exclude("com.linecorp.armeria" -> "armeria"),
    mvn"com.softwaremill.sttp.tapir::tapir-zio-metrics:${AnduinVersions.tapirVersion}"
      .exclude("com.softwaremill.sttp.tapir" -> AnduinVersions.j2s("tapir-server"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio")),
    mvn"com.fasterxml.jackson.datatype:jackson-datatype-jdk8:${AnduinVersions.jackson}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind"),
    mvn"com.fasterxml.jackson.datatype:jackson-datatype-jsr310:${AnduinVersions.jackson}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind"),
    mvn"io.projectreactor:reactor-core:${AnduinVersions.reactorCore}"
      .exclude("org.reactivestreams" -> "reactive-streams"),
    mvn"com.softwaremill.sttp.shared::armeria:${AnduinVersions.sttpShared}"
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2s("core"))
      .exclude("com.linecorp.armeria" -> "armeria"),
    mvn"com.linecorp.armeria:armeria:${AnduinVersions.armeria}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind")
      .exclude("com.fasterxml.jackson.datatype" -> "jackson-datatype-jdk8")
      .exclude("com.fasterxml.jackson.datatype" -> "jackson-datatype-jsr310")
      .exclude("io.micrometer" -> "micrometer-core")
      .exclude("io.netty" -> "netty-codec-http2")
      .exclude("io.netty" -> "netty-codec-haproxy")
      .exclude("io.netty" -> "netty-resolver-dns")
      .exclude("io.netty" -> "netty-transport")
      .exclude("io.netty" -> "netty-resolver-dns-native-macos")
      .exclude("io.netty" -> "netty-transport-native-unix-common")
      .exclude("io.netty" -> "netty-transport-native-epoll")
      .exclude("io.netty" -> "netty-tcnative-boringssl-static")
      .exclude("io.netty" -> "netty-transport-native-kqueue")
      .exclude("io.netty" -> "netty-handler-proxy")
      .exclude("io.netty.incubator" -> "netty-incubator-transport-native-io_uring")
      .exclude("org.reactivestreams" -> "reactive-streams")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams")
      .exclude("org.slf4j" -> "slf4j-api")
  )

}
