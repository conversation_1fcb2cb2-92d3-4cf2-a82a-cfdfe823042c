// Copyright (C) 2014-2025 Anduin Transactions Inc.

package build.platform.stargazerCore

import build_.platform.stargazerCore.dependency_.StargazerCore
import build.platform.*
import mill.*, scalalib.*
import anduin.mill.*

object `package` extends AnduinCrossPlatformModule {

  object jvm extends JvmModule with SharedModule {

    override def scalacOptions = super.scalacOptions()

    override def moduleDeps = Seq(stargazerModel.jvm)

    override def mvnDeps = super.mvnDeps() ++ StargazerCore.jvmDeps

  }

  object js extends JsModule with SharedModule {
    override def moduleDeps = Seq(stargazerModel.js)
  }

  trait SharedModule extends ScalaModule {
    override def mvnDeps = super.mvnDeps() ++ StargazerCore.sharedDeps
  }

}
