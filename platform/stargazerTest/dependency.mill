// Copyright (C) 2014-2025 Anduin Transactions Inc.

package build.platform.stargazerTest

import build_.build.dependency_.CommonDependencies
import mill.scalalib.*
import anduin.build.AnduinVersions

object StargazerTest {

  lazy val sharedDeps =
    CommonDependencies.catsDeps ++ CommonDependencies.circeDeps ++ Seq(
      mvn"dev.zio::izumi-reflect::${AnduinVersions.zioReflect}",
      mvn"dev.zio::zio::${AnduinVersions.zio}"
        .exclude("dev.zio" -> AnduinVersions.j2s("izumi-reflect"))
        .exclude("dev.zio" -> AnduinVersions.j2sjs("izumi-reflect")),
      mvn"org.scala-lang.modules::scala-xml::${AnduinVersions.scala_xml}",
      mvn"org.scala-sbt:test-interface:1.0",
      mvn"org.scalatest::scalatest-core::${AnduinVersions.scalatest}"
        .exclude("org.scala-js" -> "scalajs-test-interface_2.13")
        .exclude("org.scala-js" -> AnduinVersions.j2sjs("scalajs-library"))
        .exclude("org.scala-lang.modules" -> AnduinVersions.j2s("scala-xml"))
        .exclude("org.scala-lang.modules" -> AnduinVersions.j2sjs("scala-xml")),
      mvn"org.scala-js:scalajs-test-bridge_2.13:${AnduinVersions.sjs}",
      mvn"org.scalatest::scalatest::${AnduinVersions.scalatest}"
        .exclude("org.scala-js" -> "scalajs-test-interface_2.13")
        .exclude("org.scala-js" -> AnduinVersions.j2sjs("scalajs-library"))
        .exclude("org.scalatest" -> AnduinVersions.j2s("scalatest-core"))
        .exclude("org.scalatest" -> AnduinVersions.j2sjs("scalatest-core"))
        .exclude("org.scala-lang.modules" -> AnduinVersions.j2s("scala-parser-combinators"))
        .exclude("org.scala-lang.modules" -> AnduinVersions.j2sjs("scala-parser-combinators"))
        .exclude("org.scala-lang.modules" -> AnduinVersions.j2s("scala-xml"))
        .exclude("org.scala-lang.modules" -> AnduinVersions.j2sjs("scala-xml")),
      mvn"org.scalatestplus::scalacheck-1-17::${AnduinVersions.scalatestScalacheck}"
        .exclude("org.scalatest" -> AnduinVersions.j2s("scalatest"))
        .exclude("org.scalatest" -> AnduinVersions.j2sjs("scalatest"))
        .exclude("org.scalatest" -> AnduinVersions.j2s("scalatest-core"))
        .exclude("org.scalatest" -> AnduinVersions.j2sjs("scalatest-core"))
        .exclude("org.scalacheck" -> AnduinVersions.j2s("scalacheck"))
        .exclude("org.scalacheck" -> AnduinVersions.j2sjs("scalacheck")),
      mvn"org.scalactic::scalactic::${AnduinVersions.scalatest}"
        .exclude("org.scala-lang.modules" -> AnduinVersions.j2s("scala-parser-combinators"))
        .exclude("org.scala-lang.modules" -> AnduinVersions.j2sjs("scala-parser-combinators"))
        .exclude("org.scala-lang.modules" -> AnduinVersions.j2s("scala-xml"))
        .exclude("org.scala-lang.modules" -> AnduinVersions.j2sjs("scala-xml")),
      mvn"org.scalacheck::scalacheck::${AnduinVersions.scalacheck}",
      mvn"com.lihaoyi::sourcecode::${AnduinVersions.sourcecode}",
      mvn"org.gnieh::diffson-circe::${AnduinVersions.diffson}"
        .exclude("io.circe" -> AnduinVersions.j2s("circe-core"))
        .exclude("io.circe" -> AnduinVersions.j2sjs("circe-core"))
        .exclude("io.circe" -> AnduinVersions.j2s("circe-parser"))
        .exclude("io.circe" -> AnduinVersions.j2sjs("circe-parser"))
        .exclude("org.gnieh" -> AnduinVersions.j2s("diffson-core"))
        .exclude("org.gnieh" -> AnduinVersions.j2sjs("diffson-core")),
      mvn"org.gnieh::diffson-core::${AnduinVersions.diffson}"
        .exclude("org.typelevel" -> AnduinVersions.j2sjs("cats-core"))
        .exclude("org.typelevel" -> AnduinVersions.j2s("cats-core"))
    )

  lazy val jsDeps = Seq(
    // Testing libraries
    mvn"com.github.japgolly.scalajs-react::test::${AnduinVersions.reactSJS}"
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("utest"))
      .exclude("org.typelevel" -> AnduinVersions.j2sjs("cats-core"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("sourcecode"))
      .exclude("org.scalaz" -> AnduinVersions.j2sjs("scalaz-core"))
      .exclude("org.scalaz" -> AnduinVersions.j2sjs("scalaz-effect"))
      .exclude("com.github.julien-truffaut" -> AnduinVersions.j2sjs("monocle-core"))
  )

  lazy val jvmDeps =
    CommonDependencies.guavaDeps ++
      CommonDependencies.loggingJVMDeps ++ Seq(
        mvn"commons-codec:commons-codec:${AnduinVersions.commonsCodec}",
        mvn"commons-io:commons-io:${AnduinVersions.common_io}",
        mvn"org.apache.httpcomponents:httpcore:${AnduinVersions.httpCore}",
        mvn"org.apache.httpcomponents:httpclient:${AnduinVersions.httpClient}"
          .exclude("org.apache.httpcomponents" -> "httpcore")
          .exclude("commons-codec" -> "commons-codec")
          .exclude("commons-logging" -> "commons-logging")
          .exclude("commons-configuration" -> "commons-configuration"),
        mvn"com.beust:jcommander:${AnduinVersions.jcommander}"
      )

}
