// Copyright (C) 2014-2025 Anduin Transactions Inc.
package build.platform.stargazerTest

import build_.platform.stargazerTest.dependency_.StargazerTest
import mill.*, scalalib.*
import anduin.mill.*

object `package` extends AnduinCrossPlatformModule {

  object jvm extends JvmModule with SharedModule {
    override def mvnDeps = super.mvnDeps() ++ StargazerTest.jvmDeps
  }

  object js extends JsModule with SharedModule {
    override def mvnDeps = super.mvnDeps() ++ StargazerTest.jsDeps
  }

  trait SharedModule extends ScalaModule {
    override def mvnDeps = super.mvnDeps() ++ StargazerTest.sharedDeps
  }

}
