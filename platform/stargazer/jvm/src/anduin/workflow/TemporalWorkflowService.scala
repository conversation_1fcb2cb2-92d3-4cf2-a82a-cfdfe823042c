// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.workflow

import anduin.workflow.common.TemporalWorkflow

import scala.reflect.ClassTag
import com.google.protobuf.empty.Empty
import io.temporal.api.common.v1.WorkflowExecution
import io.temporal.api.enums.v1.WorkflowIdReusePolicy
import io.temporal.api.workflow.v1.WorkflowExecutionInfo
import io.temporal.api.workflowservice.v1.DescribeWorkflowExecutionRequest
import zio.temporal.activity.{IsActivity, ZActivity, ZActivityOptions, ZActivityRunOptions}
import zio.temporal.workflow.*
import zio.temporal.{TemporalIO, ZRetryOptions, ZWorkflowExecution}
import zio.{Schedule, Task, URLayer, ZEnvironment, ZIO, ZLayer}

import anduin.execution.ZIOExecutor
import anduin.model.id.TemporalWorkflowId
import anduin.telemetry.TelemetryEnvironment.AggregatedTracing
import anduin.workflow.common.*
import anduin.workflow.data.TemporalData
import com.anduin.stargazer.service.utils.{Logging<PERSON>ey, ZIOLoggingUtils, ZIOTelemetryUtils, ZIOUtils}

final case class TemporalWorkflowService(
  tracingEnvironment: ZEnvironment[AggregatedTracing],
  workflowClient: ZWorkflowClient
)(
  using ZActivityRunOptions[Any]
) {

  def executeTask[A](
    task: Task[A],
    workflowOperation: String,
    heartbeat: Option[zio.Duration] = None
  ): A = {
    val workflowId = ZActivity.executionContext.toJava.getInfo.getWorkflowId
    val taskWithTracing = ZIOTelemetryUtils
      .traceWithRootSpan(
        "app/temporal-workflow",
        Map("operation" -> workflowOperation, "workflow_id" -> workflowId)
      )(
        ZIOTelemetryUtils.injectMetrics("gondor_temporal", Map("op" -> workflowOperation))(
          ZIOLoggingUtils
            .annotate(
              LoggingKey.Operation -> workflowOperation,
              LoggingKey.Type -> "temporal-workflow",
              LoggingKey.WorkflowId -> workflowId
            )(
              task
                .tapError(error => ZIO.logErrorCause(s"Temporal activity error in $workflowOperation", error.toCause))
                .tapDefect(cause => ZIO.logErrorCause(s"Temporal activity defect in $workflowOperation", cause))
            )
        )
      )
      .provideEnvironment(tracingEnvironment)

    val finalTask = heartbeat.fold(taskWithTracing) { heartbeat =>
      val heartbeatTask = ZActivity.executionContext
        .heartbeat(Empty())
        .map(_ => true)
        .catchAll { error =>
          ZIO.logErrorCause("Heartbeat error", error.toCause).as(false)
        }
        .catchAllDefect { defect =>
          ZIO.logErrorCause("Heartbeat defect", defect.toCause).as(false)
        }
        .repeat(Schedule.fixed(heartbeat) && Schedule.recurWhile(identity))
        .as(new RuntimeException("Heartbeat failure"))
      heartbeatTask.raceEither(taskWithTracing).flatMap(ZIO.fromEither)
    }

    ZActivity.run(finalTask)
  }

  def newWorkflowStub[
    I: {TemporalData, ClassTag},
    O: {TemporalData, ClassTag},
    T <: TemporalWorkflow[I, O]: {ClassTag, IsWorkflow}
  ](
    presetWorkflowIdOpt: Option[TemporalWorkflowId] = None,
    startDelayOpt: Option[java.time.Duration] = None,
    workflowIdReusePolicy: WorkflowIdReusePolicy = WorkflowIdReusePolicy.WORKFLOW_ID_REUSE_POLICY_UNSPECIFIED
  )(
    using companion: TemporalWorkflowCompanion[T]
  ): Task[TemporalWorkflowStub[T]] = {
    val workflowId = companion.workflowId(presetWorkflowIdOpt)
    workflowClient
      .newWorkflowStub[T](
        ZWorkflowOptions
          .withWorkflowId(workflowId.idString)
          .withTaskQueue(companion.queue.queueName)
          .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(companion.maximumRetryAttempts))
          .withWorkflowIdReusePolicy(workflowIdReusePolicy)
          .transformJavaOptions { builder =>
            startDelayOpt.foreach(builder.setStartDelay)
            companion.workflowTaskTimeout.foreach(builder.setWorkflowTaskTimeout)
            companion.workflowRunTimeout.foreach(builder.setWorkflowRunTimeout)
            companion.workflowExecutionTimeout.foreach(builder.setWorkflowExecutionTimeout)
            companion.transformJavaOptions(builder)
          }
      )
  }

  def getWorkflowStub[T <: TemporalWorkflow[?, ?]: {ClassTag, IsWorkflow}](
    workflowId: TemporalWorkflowId,
    runId: Option[String]
  ): Task[TemporalWorkflowStub[T]] = {
    workflowClient.newWorkflowStub[T](workflowId.idString, runId)
  }

  def getUntypedWorkflowStub(
    workflowId: TemporalWorkflowId,
    runId: Option[String]
  ): Task[TemporalUntypedWorkflowStub] = {
    workflowClient.newUntypedWorkflowStub(workflowId.idString, runId)
  }

  def getWorkflowExecutionInfo(
    workflowId: TemporalWorkflowId
  ): Task[WorkflowExecutionInfo] = {
    ZIO
      .attemptBlocking {
        val workflowExecution = WorkflowExecution
          .newBuilder()
          .setWorkflowId(workflowId.idString)
          .build()
        val describeWorkflowExecutionRequest = DescribeWorkflowExecutionRequest
          .newBuilder()
          .setNamespace(workflowClient.toJava.getOptions.getNamespace)
          .setExecution(workflowExecution)
          .build()
        val workflowResp = workflowClient.toJava.getWorkflowServiceStubs
          .blockingStub()
          .describeWorkflowExecution(describeWorkflowExecutionRequest)
        workflowResp.getWorkflowExecutionInfo
      }
      .catchAll { error =>
        ZIO.logError(s"Failed to get workflow execution info for ${workflowId.idString}: ${error.getMessage}") *>
          ZIO.fail(new RuntimeException(s"Failed to describe workflow execution: ${error.getMessage}", error))
      }
      .tapBoth(
        error => ZIO.logError(s"Error getting workflow execution info for ${workflowId.idString}: ${error.getMessage}"),
        info => ZIO.logDebug(s"Retrieved workflow execution info for ${workflowId.idString}: status=${info.getStatus}")
      )
  }

  // scalafix:off DisableSyntax.asInstanceOf
  inline def runAsync[
    Input: {TemporalData, ClassTag},
    Output: {TemporalData, ClassTag},
    Interface <: TemporalWorkflow[Input, Output]: {ClassTag, IsWorkflow}
  ](
    input: Input,
    presetWorkflowIdOpt: Option[TemporalWorkflowId] = None
  )(
    using companion: TemporalWorkflowCompanion[Interface]
  ): TemporalIO[ZWorkflowExecution] = {
    val task = for {
      workflowStub <- newWorkflowStub[Input, Output, Interface](presetWorkflowIdOpt)
      workflowExecution <- ZWorkflowStub
        .start(workflowStub.run(input))
        .catchAll { error =>
          ZIO.logError(s"Failed to start workflow ${companion.workflowName}: ${error.getMessage}") *>
            ZIO.fail(new RuntimeException(s"Failed to start workflow: ${error.getMessage}", error))
        }
      _ <- ZIO.logInfo(
        s"Started async workflow: ${companion.workflowName}, workflowId=${workflowExecution.workflowId}"
      )
      _ <- ZIOTelemetryUtils.setAttributes(
        Map(
          "workflowId" -> workflowExecution.workflowId,
          "runId" -> workflowExecution.runId,
          "workflowName" -> companion.workflowName
        )
      )
    } yield workflowExecution
    ZIOTelemetryUtils
      .traceWithChildSpan(spanName = "service/temporal/run-async")(task)
      .provideEnvironment(tracingEnvironment)
      .asInstanceOf[TemporalIO[ZWorkflowExecution]]
  }

  inline def runSync[
    Input: {TemporalData, ClassTag},
    Output: {TemporalData, ClassTag},
    Interface <: TemporalWorkflow[Input, Output]: {ClassTag, IsWorkflow}
  ](
    input: Input,
    presetWorkflowIdOpt: Option[TemporalWorkflowId] = None
  )(
    using companion: TemporalWorkflowCompanion[Interface]
  ): TemporalIO[Output] = {
    val task = for {
      workflowStub <- newWorkflowStub[Input, Output, Interface](presetWorkflowIdOpt)
      _ <- ZIO.logInfo(s"Start run sync ${companion.workflowName}")
      _ <- ZIOTelemetryUtils.setAttributes(
        Map(
          "workflowId" -> companion.workflowId(presetWorkflowIdOpt).idString,
          "workflowName" -> companion.workflowName
        )
      )
      output <- ZWorkflowStub
        .execute(workflowStub.run(input))
        .catchAll { error =>
          ZIO.logError(s"Failed to execute workflow ${companion.workflowName}: ${error.getMessage}") *>
            ZIO.fail(new RuntimeException(s"Failed to execute workflow: ${error.getMessage}", error))
        }
      _ <- ZIO.logInfo(s"Completed sync workflow: ${companion.workflowName}")
    } yield output
    ZIOTelemetryUtils
      .traceWithChildSpan(spanName = "service/temporal/run-sync")(task)
      .provideEnvironment(tracingEnvironment)
      .asInstanceOf[TemporalIO[Output]]
  }
  // scalafix:on DisableSyntax.asInstanceOf

  def terminateWorkflow(
    workflowId: TemporalWorkflowId,
    reason: String = "Workflow cancelled by user request"
  ): Task[Unit] = {
    for {
      workflowStub <- getUntypedWorkflowStub(workflowId, None)
      _ <- workflowStub
        .terminate(Some(reason))
        .catchAll { error =>
          ZIO.logError(s"Failed to cancel workflow ${workflowId.idString}: ${error.getMessage}") *>
            ZIO.fail(new RuntimeException(s"Failed to cancel workflow: ${error.getMessage}", error))
        }
      _ <- ZIO.logInfo(s"Successfully cancelled workflow ${workflowId.idString}: $reason")
    } yield ()
  }

}

object TemporalWorkflowService {

  private val executor = ZIOExecutor.io("zio-temporal-executor")

  private val activityRunOptions: URLayer[ZWorkflowClient, ZActivityRunOptions[Any]] = {
    ZLayer.succeed(
      new ZActivityRunOptions[Any](
        ZIOUtils.unsafeBuildRuntimeFromLayer(
          zio.Runtime.setExecutor(executor) ++ zio.Runtime.setBlockingExecutor(executor)
        ),
        None
      )
    )
  }

  def make(tracingEnvironment: ZEnvironment[AggregatedTracing]): URLayer[ZWorkflowClient, TemporalWorkflowService] = {
    activityRunOptions >>> ZLayer.fromZIO {
      for {
        workflowClient <- ZIO.service[ZWorkflowClient]
        activityOptions <- ZIO.service[ZActivityRunOptions[Any]]
      } yield TemporalWorkflowService(tracingEnvironment, workflowClient)(
        using activityOptions
      )
    }
  }

  def newActivityStub[T <: TemporalActivity: {ClassTag, IsActivity}](
    using companion: TemporalActivityCompanion[T]
  ): TemporalActivityStub[T] = {
    val defaultOptions = ZActivityOptions
      .withStartToCloseTimeout(companion.startToCloseTimeout)
      .withTaskQueue(companion.queue.queueName)
      .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(companion.maximumRetryAttempts))
    ZWorkflow.newActivityStub[T](
      companion.heartbeatTimeout.fold(defaultOptions)(heartbeatTimeout =>
        defaultOptions.withHeartbeatTimeout(heartbeatTimeout)
      )
    )
  }

  private def defaultChildWorkflowOptions[T <: TemporalWorkflow[?, ?]: {ClassTag, IsWorkflow}](
    using companion: TemporalWorkflowCompanion[T]
  ): ZChildWorkflowOptions = {
    val workflowId = TemporalWorkflowId.unsafeFromSuffix(ZWorkflow.randomUUID.toString)
    ZChildWorkflowOptions
      .withWorkflowId(workflowId.idString)
      .withTaskQueue(companion.queue.queueName)
      .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(companion.maximumRetryAttempts))
      .transformJavaOptions { builder =>
        companion.workflowTaskTimeout.foreach(builder.setWorkflowTaskTimeout)
        companion.workflowRunTimeout.foreach(builder.setWorkflowRunTimeout)
        companion.workflowExecutionTimeout.foreach(builder.setWorkflowExecutionTimeout)
        builder
      }
  }

  def newChildWorkflowStub[T <: TemporalWorkflow[?, ?]: {ClassTag, IsWorkflow}](
    using companion: TemporalWorkflowCompanion[T]
  ): TemporalChildWorkflowStub[T] = {
    ZWorkflow.newChildWorkflowStub[T](defaultChildWorkflowOptions[T])
  }

  def newChildWorkflowStubWithOptionsCustomizer[T <: TemporalWorkflow[?, ?]: {ClassTag, IsWorkflow}](
    customizer: ZChildWorkflowOptions => ZChildWorkflowOptions
  )(
    using companion: TemporalWorkflowCompanion[T]
  ): TemporalChildWorkflowStub[T] = {
    ZWorkflow.newChildWorkflowStub[T](customizer(defaultChildWorkflowOptions[T]))
  }

  private def workflowOperation(
    using name: sourcecode.FullName,
    pkg: sourcecode.Pkg
  ): String = {
    name.value.stripPrefix(s"${pkg.value}.")
  }

  extension [A](task: Task[A]) {

    inline def runActivity(
      using temporalWorkflowService: TemporalWorkflowService,
      name: sourcecode.FullName,
      pkg: sourcecode.Pkg
    ): A = {
      temporalWorkflowService.executeTask(task, workflowOperation)
    }

    inline def runActivityWithHeartbeat(
      heartbeat: zio.Duration
    )(
      using temporalWorkflowService: TemporalWorkflowService,
      name: sourcecode.FullName,
      pkg: sourcecode.Pkg
    ): A = {
      temporalWorkflowService.executeTask(task, workflowOperation, Some(heartbeat))
    }

  }

}
