// Copyright (C) 2014-2025 Anduin Transactions Inc.

package build.platform.stargazer

import build_.platform.stargazer.dependency_.*
import build.platform.*
import mill.*, scalalib.*
import anduin.mill.*

object `package` extends AnduinCrossPlatformModule {

  object jvm extends JvmModule with SharedModule with AnduinPlatformScalaPBModule {

    // turn this to true because we want to include the protos from
    // https://github.com/FoundationDB/fdb-record-layer/tree/main/fdb-record-layer-core/src/main/proto
    override def scalaPBSearchDeps = true

    override def scalaPBProtoClasspath = {
      super.scalaPBProtoClasspath().filter(_.path.wrapped.toFile.getName.startsWith("fdb-record-layer-core"))
    }

    override def scalacOptions: T[Seq[String]] = super.scalacOptions()

    override def moduleDeps = Seq(
      stargazerCore.jvm,
      stargazerConfig.jvm,
      build.gondor.gondorModel.jvm
    )

    override def mvnDeps = super.mvnDeps() ++
      Stargazer.jvmDeps ++
      FoundationDb.jvmDeps ++
      PostgresqlDb.jvmDeps ++
      WorkflowEngine.jvmDeps ++
      HttpClient.jvmDeps ++
      Nats.jvmDeps ++
      ModelDb.jvmDeps ++
      Kafka.jvmDeps

  }

  object js extends JsModule with SharedModule {

    override def moduleDeps = Seq(stargazerConfig.js, stargazerCore.js, webModules)

    override def mvnDeps = super.mvnDeps() ++
      Stargazer.jsDeps ++
      HttpClient.jsDeps

  }

  trait SharedModule extends ScalaModule {

    override def mvnDeps = super.mvnDeps() ++
      HttpClient.sharedDeps

  }

}
