package build.platform.stargazer

import build_.build.dependency_.CommonDependencies
import build_.build.versions_.AnduinDesign
import mill.scalalib.*
import anduin.build.AnduinVersions

object Stargazer {

  lazy val tinkVersion = "1.15.0"
  lazy val macWireVersion = "2.6.6"
  // cannot upgrade because svix depends on older okhttp version 4.12.0 instead of the 5.X
  lazy val openTelemetryVersion = "1.51.0"

  lazy val jvmDeps = CommonDependencies.catsDeps ++ Seq(
    mvn"org.typelevel::shapeless3-deriving:${AnduinVersions.shapeless}",
    mvn"com.softwaremill.macwire::macros:$macWireVersion",
    mvn"com.lihaoyi::scalatags:${AnduinVersions.scalatags}"
      .exclude("org.scala-js" -> AnduinVersions.j2s("scalajs-dom"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("sourcecode"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("geny")),
    mvn"com.lihaoyi::geny:${AnduinVersions.geny}",
    mvn"com.lihaoyi::ujson:${AnduinVersions.ujson}"
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("geny")),
    mvn"org.uaparser::uap-scala:${AnduinVersions.uapParser}"
      .exclude("org.yaml" -> "snakeyaml"),
    mvn"net.java.dev.jna:jna-platform:${AnduinVersions.jna}",
    mvn"org.jetbrains:annotations:26.0.2",
    mvn"org.jetbrains.kotlin:kotlin-stdlib-jdk8:${AnduinVersions.kotlinStdLib}"
      .exclude("org.jetbrains" -> "annotations"),
    mvn"org.conscrypt:conscrypt-openjdk-uber:2.5.2",
    mvn"com.squareup.okio:okio:${AnduinVersions.okio}"
      .exclude("com.squareup.okio" -> "okio-jvm"),
    mvn"com.squareup.okio:okio-jvm:${AnduinVersions.okio}"
      .exclude("org.jetbrains.kotlin" -> "kotlin-stdlib-jdk8")
      .exclude("org.jetbrains.kotlin" -> "kotlin-stdlib")
      .exclude("org.jetbrains.kotlin" -> "kotlin-stdlib-common"),
    mvn"dev.zio::zio-opentelemetry:${AnduinVersions.zioOpenTelemetry}"
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-stacktracer"))
      .exclude("dev.zio" -> AnduinVersions.j2s("izumi-reflect"))
      .exclude("io.opentelemetry" -> "opentelemetry-api")
      .exclude("io.opentelemetry" -> "opentelemetry-context"),
    mvn"io.opentelemetry:opentelemetry-exporter-otlp:$openTelemetryVersion"
      .exclude("com.squareup.okio" -> "okio")
      .exclude("com.squareup.okio" -> "okio-jvm")
      .exclude("org.jetbrains.kotlin" -> "kotlin-stdlib")
      .exclude("org.jetbrains.kotlin" -> "kotlin-stdlib-jdk8")
      .exclude("com.fasterxml.jackson.core" -> "jackson-core"),
    mvn"io.opentelemetry.semconv:opentelemetry-semconv:${AnduinVersions.openTememetrySemconv}"
      .exclude("io.opentelemetry" -> "opentelemetry-api"),
    mvn"io.opentelemetry:opentelemetry-sdk:$openTelemetryVersion",
    mvn"io.opentelemetry:opentelemetry-api-incubator:$openTelemetryVersion-alpha"
      .exclude("io.opentelemetry" -> "opentelemetry-api"),
    mvn"io.opentelemetry:opentelemetry-opentracing-shim:$openTelemetryVersion"
      .exclude("io.opentelemetry" -> "opentelemetry-api")
      .exclude("io.opentracing" -> "opentracing-api"),
    // Encryption
    mvn"com.google.code.gson:gson:${AnduinVersions.gson}"
      .exclude("com.google.errorprone" -> "error_prone_annotations"),
    mvn"com.google.crypto.tink:tink:$tinkVersion"
      .exclude("com.google.protobuf" -> "protobuf-java")
      .exclude("joda-time" -> "joda-time")
      .exclude("org.apache.httpcomponents" -> "httpclient")
      .exclude("com.google.http-client" -> "google-http-client")
      .exclude("com.google.errorprone" -> "error_prone_annotations")
      .exclude("com.google.code.gson" -> "gson"),
    mvn"commons-logging:commons-logging:${AnduinVersions.commonsLogging}",
    mvn"com.google.auto.service:auto-service:${AnduinVersions.googleAutoService}"
      .exclude("com.google.guava" -> "guava"),
    mvn"software.amazon.awssdk:kms:${AnduinVersions.awsSdk2Version}"
      .exclude("software.amazon.awssdk" -> "apache-client")
      .exclude("software.amazon.awssdk" -> "netty-nio-client")
      .exclude("org.slf4j" -> "slf4j-api"),
    // scala bcrypt
    // TODO: @binh to update
    mvn"de.svenkubiak:jBCrypt:0.4.3",
    mvn"com.github.t3hnar::scala-bcrypt:${AnduinVersions.scala_bcrypt}"
      .withDottyCompat(AnduinVersions.scala)
      .exclude("de.svenkubiak" -> "jBCrypt"),
    // Template engine
    mvn"com.github.eikek::yamusca-core:${AnduinVersions.yamusca}",
    mvn"com.github.pathikrit::better-files:${AnduinVersions.better_files}",
    mvn"org.duckdb:duckdb_jdbc:${AnduinVersions.duckdb}",
    // Memcached
    mvn"net.spy:spymemcached:${AnduinVersions.spymemcached}",
    // Liquibase
    mvn"org.apache.commons:commons-collections4:4.5.0",
    mvn"org.liquibase:liquibase-core:${AnduinVersions.liquibase}"
      .exclude("org.yaml" -> "snakeyaml")
      .exclude("org.apache.commons" -> "commons-lang3")
      .exclude("org.apache.commons" -> "commons-collections4")
      .exclude("org.apache.commons" -> "commons-text")
      .exclude("commons-io" -> "commons-io"),
    mvn"design.anduin::web-builderModel-jvm:${AnduinDesign.version}"
      .exclude("com.thesamet.scalapb" -> AnduinVersions.j2s("scalapb-runtime"))

    // "io.getkyo" %% "kyo-caliban" % "0.14.1"
  )

  lazy val jsDeps = Seq(
    mvn"com.lihaoyi::sourcecode::${AnduinVersions.sourcecode}",
    mvn"org.scala-js::scalajs-dom::${AnduinVersions.scalajsdom}",
    mvn"com.lihaoyi::geny::${AnduinVersions.geny}",
    mvn"com.lihaoyi::ujson::${AnduinVersions.ujson}"
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("geny")),
    mvn"com.raquo::domtypes::${AnduinVersions.domTypes}"
      .exclude("org.scala-js" -> AnduinVersions.j2sjs("scalajs-dom")),
    mvn"com.raquo::airstream::${AnduinVersions.airstream}"
      .exclude("org.scala-js" -> AnduinVersions.j2sjs("scalajs-dom")),
    mvn"com.raquo::laminar::${AnduinVersions.laminar}"
      .exclude("com.raquo" -> AnduinVersions.j2sjs("airstream"))
      .exclude("com.raquo" -> AnduinVersions.j2sjs("domtypes"))
  ) ++ Seq(
    mvn"design.anduin::web-style::${AnduinDesign.version}",
    mvn"design.anduin::web-core::${AnduinDesign.version}"
      .exclude("io.github.cquiroz" -> AnduinVersions.j2sjs("scala-java-time"))
      .exclude("io.github.cquiroz" -> AnduinVersions.j2sjs("scala-java-locales")),
    mvn"design.anduin::web-editor::${AnduinDesign.version}",
    mvn"design.anduin::web-viewer::${AnduinDesign.version}",
    mvn"design.anduin::web-chart::${AnduinDesign.version}",
    mvn"design.anduin::web-mention::${AnduinDesign.version}",
    mvn"design.anduin::web-table::${AnduinDesign.version}",
    mvn"design.anduin::web-builder::${AnduinDesign.version}"
  ).map {
    _.exclude("dev.zio" -> AnduinVersions.j2sjs("zio"))
      .exclude("com.google.protobuf" -> "protobuf-java")
      .exclude("com.github.japgolly.scalajs-react" -> AnduinVersions.j2sjs("core"))
      .exclude("com.olvind" -> AnduinVersions.j2sjs("scalablytyped-runtime"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("sourcecode"))
      .exclude("org.typelevel" -> AnduinVersions.j2sjs("circe-core"))
      .exclude("org.typelevel" -> AnduinVersions.j2sjs("cats-core"))
      .exclude("design.anduin" -> AnduinVersions.j2sjs("web-modules"))
      .exclude("org.scalatest" -> AnduinVersions.j2sjs("scalatest"))
      .exclude("org.scalatest" -> AnduinVersions.j2sjs("scalatest-core"))
  } ++ Seq(
    mvn"com.olvind::scalablytyped-runtime::${AnduinVersions.scalablytypedRuntime}"
  )

}

object HttpClient {

  lazy val sharedDeps = Seq(
    mvn"org.scodec::scodec-bits::${AnduinVersions.scodecBits}"
      .exclude("com.chuusai" -> AnduinVersions.j2sjs("shapeless"))
      .exclude("com.chuusai" -> AnduinVersions.j2s("shapeless")),
    mvn"com.softwaremill.sttp.client3::core::${AnduinVersions.sttp}"
      .exclude("com.softwaremill.sttp.model" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.model" -> AnduinVersions.j2sjs("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2sjs("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2s("ws"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2sjs("ws")),
    mvn"com.softwaremill.sttp.client3::jsoniter::${AnduinVersions.sttp}"
      .exclude("com.softwaremill.sttp.client3" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.client3" -> AnduinVersions.j2sjs("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2sjs("core"))
      .exclude("com.github.plokhotnyuk.jsoniter-scala" -> AnduinVersions.j2s("jsoniter-scala-core"))
      .exclude("com.github.plokhotnyuk.jsoniter-scala" -> AnduinVersions.j2sjs("jsoniter-scala-core")),
    mvn"com.softwaremill.sttp.client3::circe::${AnduinVersions.sttp}"
      .exclude("com.softwaremill.sttp.client3" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.client3" -> AnduinVersions.j2sjs("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2sjs("core"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-parser"))
      .exclude("io.circe" -> AnduinVersions.j2sjs("circe-parser"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-core"))
      .exclude("io.circe" -> AnduinVersions.j2sjs("circe-core")),
    mvn"com.softwaremill.sttp.client3::zio::${AnduinVersions.sttp}"
      .exclude("org.reactivestreams" -> "reactive-streams")
      .exclude("com.softwaremill.sttp.client3" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.client3" -> AnduinVersions.j2sjs("core"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2s("zio"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2sjs("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-interop-reactivestreams"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio-interop-reactivestreams"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio-streams"))
  )

  lazy val jvmDeps = CommonDependencies.loggingJVMDeps ++ Seq(
    // PureConfig
    mvn"com.typesafe:config:${AnduinVersions.typesafeConfig}",
    mvn"org.reactivestreams:reactive-streams:${AnduinVersions.reactiveStream}",
    mvn"com.typesafe.netty:netty-reactive-streams:${AnduinVersions.typesafeNetty}"
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("io.netty" -> "netty-handler")
      .exclude("org.reactivestreams" -> "reactive-streams"),
    mvn"com.sun.activation:jakarta.activation:2.0.1",
    mvn"io.netty:netty-common:${AnduinVersions.netty}",
    mvn"io.netty:netty-buffer:${AnduinVersions.netty}",
    mvn"io.netty:netty-resolver-dns:${AnduinVersions.netty}",
    mvn"io.netty:netty-transport:${AnduinVersions.netty}",
    mvn"io.netty:netty-codec-http2:${AnduinVersions.netty}",
    mvn"io.netty:netty-transport-native-unix-common:${AnduinVersions.netty}",
    mvn"io.netty:netty-transport-native-epoll:${AnduinVersions.netty}",
    mvn"io.netty:netty-tcnative-boringssl-static:${AnduinVersions.nettyBoringSsl}",
    mvn"io.netty:netty-handler-proxy:${AnduinVersions.netty}",
    mvn"io.netty:netty-resolver-dns-native-macos:${AnduinVersions.netty}",
    mvn"io.netty:netty-resolver-dns-native-macos:${AnduinVersions.netty};classifier=osx-aarch_64",
    mvn"io.netty:netty-codec-haproxy:${AnduinVersions.netty}",
    mvn"io.netty:netty-tcnative-classes:${AnduinVersions.nettyTcNativeClasses}",
    mvn"io.netty:netty-codec-http:${AnduinVersions.netty}",
    mvn"io.netty:netty-handler:${AnduinVersions.netty}",
    mvn"io.netty:netty-codec-socks:${AnduinVersions.netty}",
    mvn"io.netty:netty-handler-proxy:${AnduinVersions.netty}",
    mvn"io.netty:netty-transport-native-epoll:${AnduinVersions.netty}",
    mvn"io.netty:netty-transport-native-kqueue:${AnduinVersions.netty}",
    mvn"io.netty:netty-resolver-dns:${AnduinVersions.netty}",
    mvn"io.netty:netty-buffer:${AnduinVersions.netty}",
    // sttp fs2 backend
    mvn"com.softwaremill.sttp.client3::armeria-backend-zio::${AnduinVersions.sttp}"
      .exclude("com.softwaremill.sttp.client3" -> AnduinVersions.j2s("zio"))
      .exclude("com.softwaremill.sttp.client3" -> AnduinVersions.j2s("core"))
      .exclude("com.linecorp.armeria" -> "armeria")
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-interop-reactivestreams")),
    mvn"io.micrometer:micrometer-core:${AnduinVersions.micrometer}",
    mvn"com.fasterxml.jackson.datatype:jackson-datatype-jdk8:${AnduinVersions.jackson}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind"),
    mvn"com.fasterxml.jackson.datatype:jackson-datatype-jsr310:${AnduinVersions.jackson}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind"),
    mvn"com.linecorp.armeria:armeria:${AnduinVersions.armeria}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind")
      .exclude("com.fasterxml.jackson.datatype" -> "jackson-datatype-jdk8")
      .exclude("com.fasterxml.jackson.datatype" -> "jackson-datatype-jsr310")
      .exclude("io.micrometer" -> "micrometer-core")
      .exclude("io.netty" -> "netty-codec-http2")
      .exclude("io.netty" -> "netty-codec-haproxy")
      .exclude("io.netty" -> "netty-resolver-dns")
      .exclude("io.netty" -> "netty-transport")
      .exclude("io.netty" -> "netty-resolver-dns-native-macos")
      .exclude("io.netty" -> "netty-transport-native-unix-common")
      .exclude("io.netty" -> "netty-transport-native-epoll")
      .exclude("io.netty" -> "netty-tcnative-boringssl-static")
      .exclude("io.netty" -> "netty-transport-native-kqueue")
      .exclude("io.netty" -> "netty-handler-proxy")
      .exclude("io.netty.incubator" -> "netty-incubator-transport-native-io_uring")
      .exclude("org.reactivestreams" -> "reactive-streams")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams")
      .exclude("org.slf4j" -> "slf4j-api"),
    mvn"io.netty.incubator:netty-incubator-transport-native-io_uring:${AnduinVersions.netty_io_uring}"
      .exclude("io.netty" -> "netty-common")
      .exclude("io.netty" -> "netty-buffer")
      .exclude("io.netty" -> "netty-transport-native-unix-common")
      .exclude("io.netty" -> "netty-resolver-dns-native-macos")
      .exclude("io.netty" -> "netty-transport"),
    mvn"com.softwaremill.sttp.client3::scribe-backend::${AnduinVersions.sttp}"
      .exclude("com.outr" -> AnduinVersions.j2s("scribe"))
      .exclude("com.softwaremill.sttp.shared" -> AnduinVersions.j2s("core"))
      .exclude("com.softwaremill.sttp.client3" -> AnduinVersions.j2s("core")),
    mvn"org.parboiled::parboiled::${AnduinVersions.parboiled}"
      .exclude("com.chuusai" -> AnduinVersions.j2s("shapeless")),
    mvn"org.scodec::scodec-bits::${AnduinVersions.scodecBits}"
      .exclude("com.chuusai" -> AnduinVersions.j2sjs("shapeless"))
      .exclude("com.chuusai" -> AnduinVersions.j2s("shapeless")),
    mvn"io.vavr:vavr:${AnduinVersions.vavr}",
    mvn"io.github.resilience4j:resilience4j-ratelimiter:${AnduinVersions.resilience4j}"
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("io.vavr" -> "vavr")
      .exclude("org.jetbrains.kotlin" -> "kotlin-stdlib-jdk8"),
    mvn"io.github.resilience4j:resilience4j-circuitbreaker:${AnduinVersions.resilience4j}"
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("io.vavr" -> "vavr")
      .exclude("org.jetbrains.kotlin" -> "kotlin-stdlib-jdk8")
  )

  lazy val jsDeps = Seq(
    mvn"org.scala-js::scalajs-dom::${AnduinVersions.scalajsdom}"
  )

}

object FoundationDb {

  lazy val fdbRecord = AnduinVersions.fdbRecord
  lazy val fdbVersion = "7.3.67"

  lazy val jvmDeps = Seq(
    mvn"org.apache.commons:commons-lang3:${AnduinVersions.apacheCommonLang}",
    mvn"com.google.auto.service:auto-service-annotations:${AnduinVersions.googleAutoService}",
    mvn"com.google.auto.service:auto-service:${AnduinVersions.googleAutoService}"
      .exclude("com.google.guava" -> "guava"),
    // lucence
    mvn"org.apache.lucene:lucene-core:${AnduinVersions.lucene}",
    mvn"org.apache.lucene:lucene-analyzers-common:${AnduinVersions.lucene}",
    mvn"org.apache.lucene:lucene-queryparser:${AnduinVersions.lucene}",
    mvn"org.apache.lucene:lucene-suggest:${AnduinVersions.lucene}",
    mvn"org.apache.lucene:lucene-highlighter:${AnduinVersions.lucene}",
    mvn"org.foundationdb:fdb-java:${fdbVersion}",
    mvn"org.foundationdb:fdb-record-layer-core:$fdbRecord"
      .exclude("org.foundationdb" -> "fdb-java")
      .exclude("com.google.guava" -> "guava")
      .exclude("com.google.protobuf" -> "protobuf-java")
      .exclude("org.apache.commons" -> "commons-lang3")
      .exclude("org.slf4j" -> "slf4j-api"),
    mvn"org.foundationdb:fdb-record-layer-lucene:$fdbRecord"
      .exclude("org.foundationdb" -> "fdb-java")
      .exclude("org.foundationdb" -> "fdb-record-layer-core")
      .exclude("org.apache.lucene" -> "lucene-core")
      .exclude("org.apache.lucene" -> "lucene-analyzers-common")
      .exclude("org.apache.lucene" -> "lucene-queryparser")
      .exclude("org.apache.lucene" -> "lucene-suggest")
      .exclude("org.apache.lucene" -> "lucene-highlighter")
      .exclude("com.google.auto.service" -> "auto-service")
      .exclude("com.google.guava" -> "guava")
      .exclude("com.google.protobuf" -> "protobuf-java")
      .exclude("org.apache.commons" -> "commons-lang3")
      .exclude("org.slf4j" -> "slf4j-api")
  )

}

object PostgresqlDb {

  lazy val jvmDeps = CommonDependencies.quillJdbcJVM ++ Seq(
    mvn"com.fasterxml.jackson.dataformat:jackson-dataformat-toml:${AnduinVersions.jacksonCbor}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind"),
    mvn"org.flywaydb:flyway-core:${AnduinVersions.flyway}"
      .exclude("com.fasterxml.jackson.dataformat" -> "jackson-dataformat-toml")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind")
      .exclude("com.fasterxml.jackson.datatype" -> "jackson-datatype-jsr310")
      .exclude("com.google.code.gson" -> "gson")
      .exclude("com.fasterxml.jackson.core" -> "jackson-core"),
    mvn"org.flywaydb:flyway-database-postgresql:${AnduinVersions.flyway}"
      .exclude("org.flywaydb" -> "flyway-core"),
    mvn"org.checkerframework:checker-qual:${AnduinVersions.checkerQual}",
    mvn"org.postgresql:postgresql:${AnduinVersions.postgresql}"
      .exclude("org.checkerframework" -> "checker-qual")
  )

}

object WorkflowEngine {

  lazy val jvmDeps = Seq(
    mvn"com.google.code.gson:gson:${AnduinVersions.gson}"
      .exclude("com.google.errorprone" -> "error_prone_annotations"),
    mvn"com.fasterxml.jackson.core:jackson-databind:${AnduinVersions.jacksonDatabind}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations"),
    mvn"com.fasterxml.jackson.module:${AnduinVersions.j2s("jackson-module-scala")}:${AnduinVersions.jacksonDatabind}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind"),
    mvn"com.fasterxml.jackson.datatype:jackson-datatype-jsr310:${AnduinVersions.jackson}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind"),
    mvn"com.fasterxml.jackson.core:jackson-annotations:${AnduinVersions.jacksonAnnotations}",
    mvn"com.google.protobuf:protobuf-java:${AnduinVersions.protobufJava}",
    mvn"com.google.protobuf:protobuf-java-util:${AnduinVersions.protobufJava}"
      .exclude("com.google.code.gson" -> "gson")
      .exclude("com.google.guava" -> "guava")
      .exclude("com.google.errorprone" -> "error_prone_annotations")
      .exclude("com.google.j2objc" -> "j2objc-annotations")
      .exclude("com.google.protobuf" -> "protobuf-java"),
    mvn"com.google.protobuf:protobuf-javalite:${AnduinVersions.protobufJava}",
    mvn"com.google.api.grpc:proto-google-common-protos:${AnduinVersions.protoCommon}"
      .exclude("com.google.protobuf" -> "protobuf-java"),
    mvn"org.codehaus.mojo:animal-sniffer-annotations:${AnduinVersions.snifferAnnotations}",
    mvn"io.grpc:grpc-context:${AnduinVersions.grpc}",
    mvn"io.grpc:grpc-api:${AnduinVersions.grpc}"
      .exclude("com.google.code.findbugs" -> "jsr305")
      .exclude("com.google.errorprone" -> "error_prone_annotations")
      .exclude("com.google.guava" -> "guava")
      .exclude("org.codehaus.mojo" -> "animal-sniffer-annotations")
      .exclude("io.grpc" -> "grpc-context"),
    mvn"io.grpc:grpc-protobuf:${AnduinVersions.grpc}"
      .exclude("com.google.api.grpc" -> "proto-google-common-protos")
      .exclude("com.google.code.findbugs" -> "jsr305")
      .exclude("com.google.protobuf" -> "protobuf-java")
      .exclude("com.google.protobuf" -> "protobuf-javalite")
      .exclude("com.google.errorprone" -> "error_prone_annotations")
      .exclude("com.google.guava" -> "guava")
      .exclude("org.codehaus.mojo" -> "animal-sniffer-annotations")
      .exclude("io.grpc" -> "grpc-api"),
    mvn"io.grpc:grpc-stub:${AnduinVersions.grpc}"
      .exclude("com.google.errorprone" -> "error_prone_annotations")
      .exclude("org.codehaus.mojo" -> "animal-sniffer-annotations")
      .exclude("com.google.guava" -> "guava")
      .exclude("io.grpc" -> "grpc-api"),
    mvn"io.grpc:grpc-services:${AnduinVersions.grpc}"
      .exclude("com.google.code.findbugs" -> "jsr305")
      .exclude("com.google.protobuf" -> "protobuf-java-util")
      .exclude("com.google.errorprone" -> "error_prone_annotations")
      .exclude("com.google.code.gson" -> "gson")
      .exclude("com.google.j2objc" -> "j2objc-annotations")
      .exclude("com.google.guava" -> "guava")
      .exclude("org.codehaus.mojo" -> "animal-sniffer-annotations")
      .exclude("io.grpc" -> "grpc-protobuf")
      .exclude("io.grpc" -> "grpc-stubs")
      .exclude("io.grpc" -> "grpc-core"),
    mvn"com.google.android:annotations:${AnduinVersions.androidAnnotations}",
    mvn"io.perfmark:perfmark-api:${AnduinVersions.perfmark}",
    mvn"io.grpc:grpc-core:${AnduinVersions.grpc}"
      .exclude("com.google.android" -> "annotations")
      .exclude("com.google.code.gson" -> "gson")
      .exclude("com.google.code.findbugs" -> "jsr305")
      .exclude("io.perfmark" -> "perfmark-api")
      .exclude("com.google.errorprone" -> "error_prone_annotations")
      .exclude("com.google.guava" -> "guava")
      .exclude("org.codehaus.mojo" -> "animal-sniffer-annotations")
      .exclude("io.grpc" -> "grpc-api"),
    mvn"io.grpc:grpc-netty-shaded:${AnduinVersions.grpc}"
      .exclude("io.grpc" -> "grpc-core")
      .exclude("com.google.errorprone" -> "error_prone_annotations")
      .exclude("org.codehaus.mojo" -> "animal-sniffer-annotations")
      .exclude("com.google.guava" -> "guava")
      .exclude("io.perfmark" -> "perfmark-api"),
    mvn"io.micrometer:micrometer-core:${AnduinVersions.micrometer}",
    mvn"com.uber.m3:tally-core:${AnduinVersions.uberTally}",
    mvn"io.temporal:temporal-sdk:${AnduinVersions.temporalIO}"
      .exclude("com.google.code.gson" -> "gson")
      .exclude("com.google.errorprone" -> "error_prone_annotations")
      .exclude("io.micrometer" -> "micrometer-core")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind")
      .exclude("com.fasterxml.jackson.datatype" -> "jackson-datatype-jsr310")
      .exclude("com.fasterxml.jackson.datatype" -> "jackson-datatype-jdk8")
      .exclude("com.google.guava" -> "guava")
      .exclude("com.google.protobuf" -> "protobuf-java-util")
      .exclude("com.uber.m3" -> "tally-core")
      .exclude("io.grpc" -> "grpc-inprocess")
      .exclude("io.grpc" -> "grpc-api")
      .exclude("io.grpc" -> "grpc-protobuf")
      .exclude("io.grpc" -> "grpc-core")
      .exclude("io.grpc" -> "grpc-stub")
      .exclude("io.grpc" -> "grpc-netty-shaded")
      .exclude("io.grpc" -> "grpc-services")
      .exclude("org.slf4j" -> "slf4j-api"),
    mvn"io.temporal:temporal-opentracing:${AnduinVersions.temporalIO}"
      .exclude("io.opentracing" -> "opentracing-api")
      .exclude("com.google.guava" -> "guava"),
    mvn"io.temporal:temporal-testing:${AnduinVersions.temporalIO}"
      .exclude("io.temporal" -> "temporal-sdk")
      .exclude("com.jayway.jsonpath" -> "json-path")
      .exclude("com.fasterxml.jackson.datatype" -> "jackson-datatype-jsr310")
      .exclude("com.fasterxml.jackson.core" -> AnduinVersions.j2s("jackson-module-scala"))
      .exclude("org.ow2.asm" -> "asm")
      .exclude("com.google.guava" -> "guava")
      .exclude("org.slf4j" -> "slf4j-simple")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("io.grpc" -> "grpc-core")
      .exclude("io.grpc" -> "grpc-services"),
    mvn"io.temporal:temporal-testing:${AnduinVersions.temporalIO}"
      .exclude("io.temporal" -> "temporal-sdk")
      .exclude("com.jayway.jsonpath" -> "json-path")
      .exclude("com.fasterxml.jackson.datatype" -> "jackson-datatype-jsr310")
      .exclude("com.fasterxml.jackson.core" -> AnduinVersions.j2s("jackson-module-scala"))
      .exclude("org.ow2.asm" -> "asm")
      .exclude("com.google.guava" -> "guava")
      .exclude("org.slf4j" -> "slf4j-simple")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("io.grpc" -> "grpc-core")
      .exclude("io.grpc" -> "grpc-services"),
    mvn"dev.vhonta::zio-temporal-core:${AnduinVersions.zioTemporal}"
      .exclude("io.temporal" -> "temporal-sdk")
      .exclude("io.temporal" -> "temporal-opentracing")
      .exclude("com.fasterxml.jackson.module" -> AnduinVersions.j2s("jackson-module-scala"))
      .exclude("com.fasterxml.jackson.datatype" -> "jackson-datatype-jsr310")
      .exclude("com.fasterxml.jackson.core" -> AnduinVersions.j2s("jackson-module-scala"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("org.scalatest" -> AnduinVersions.j2s("scalatest")),
    mvn"dev.vhonta::zio-temporal-protobuf:${AnduinVersions.zioTemporal}"
      .exclude("io.temporal" -> "temporal-sdk")
      .exclude("io.temporal" -> "temporal-opentracing")
      .exclude("com.fasterxml.jackson.datatype" -> "jackson-datatype-jsr310")
      .exclude("com.fasterxml.jackson.core" -> AnduinVersions.j2s("jackson-module-scala"))
      .exclude("com.fasterxml.jackson.module" -> AnduinVersions.j2s("jackson-module-scala"))
      .exclude("com.thesamet.scalapb" -> AnduinVersions.j2s("scalapb-runtime"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.vhonta" -> AnduinVersions.j2s("zio-temporal-core"))
      .exclude("org.scalatest" -> AnduinVersions.j2s("scalatest"))
  )

}

object ModelDb {

  lazy val jvmDeps = CommonDependencies.monocleJVMDeps ++ Seq(
    mvn"org.scodec::scodec-bits:${AnduinVersions.scodecBits}"
      .exclude("com.chuusai" -> AnduinVersions.j2sjs("shapeless"))
      .exclude("com.chuusai" -> AnduinVersions.j2s("shapeless")),
    // Couchbase java client
    mvn"io.opentracing:opentracing-api:${AnduinVersions.opentracing}",
    mvn"io.micrometer:context-propagation:${AnduinVersions.microMeterContext}",
    mvn"com.google.errorprone:error_prone_annotations:${AnduinVersions.errorProne}",
    mvn"org.checkerframework:checker-qual:${AnduinVersions.checkerQual}",
    mvn"com.github.ben-manes.caffeine:caffeine:${AnduinVersions.caffeine}"
      .exclude("org.checkerframework" -> "checker-qual")
      .exclude("com.google.errorprone" -> "error_prone_annotations"),
    mvn"io.getkyo::kyo-cache::${AnduinVersions.kyo}"
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("sourcecode"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("sourcecode"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("fansi"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("fansi"))
      .exclude("com.github.ben-manes.caffeine" -> "caffeine")
  )

}

object Nats {

  lazy val jvmDeps = Seq(
    mvn"io.nats:jnats:${AnduinVersions.nats}"
      .exclude("org.bouncycastle" -> "bcprov-lts8on")
  )

}

object Kafka {

  lazy val kafkaVersion = "4.0.0"

  lazy val jvmDeps =
    Seq(
      mvn"com.github.luben:zstd-jni:${AnduinVersions.zstdJni}",
      mvn"org.apache.kafka:kafka-clients:$kafkaVersion"
        .exclude("com.github.luben" -> "zstd-jni")
        .exclude("org.slf4j" -> "slf4j-api"),
      mvn"com.fasterxml.jackson.datatype:jackson-datatype-jdk8:${AnduinVersions.jackson}"
        .exclude("com.fasterxml.jackson.core" -> "jackson-databind"),
      mvn"org.apache.kafka:kafka-streams-scala_2.13:$kafkaVersion"
        .exclude("org.apache.kafka" -> "kafka-streams"),
      mvn"org.apache.kafka:kafka-streams:$kafkaVersion"
        .exclude("org.slf4j" -> "slf4j-api")
        .exclude("org.apache.kafka" -> "kafka-clients")
        .exclude("com.fasterxml.jackson.core" -> "jackson-databind")
        .exclude("com.fasterxml.jackson.core" -> "jackson-annotations"),
      mvn"dev.zio::zio-kafka:${AnduinVersions.zioKafka}"
        .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
        .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
        .exclude("org.apache.kafka" -> "kafka-clients")
        .exclude("com.fasterxml.jackson.core" -> "jackson-databind")
    )

}
