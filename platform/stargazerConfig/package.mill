package build.platform.stargazerConfig

import build_.platform.stargazerConfig.dependency_.StargazerConfig
import build.platform.*
import mill.*, scalalib.*
import anduin.mill.*

object `package` extends AnduinCrossPlatformModule {

  object jvm extends JvmModule {
    override def mvnDeps = super.mvnDeps() ++ StargazerConfig.jvmDeps

    override def moduleDeps = Seq(stargazerBuildInfo.jvm, stargazerModel.jvm)

    override def scalacOptions = super.scalacOptions() ++ Seq("-Xmax-inlines:128")

    override def zincIncrementalCompilation = false
  }

  object js extends JsModule {
    override def moduleDeps = Seq(stargazerModel.js)

    override def zincIncrementalCompilation = false
  }

}
