package build.platform.stargazerConfig

import build_.build.dependency_.CommonDependencies
import mill.scalalib.*
import anduin.build.AnduinVersions

object StargazerConfig {

  lazy val jvmDeps = Seq(
    mvn"dev.zio::izumi-reflect::${AnduinVersions.zioReflect}",
    mvn"com.typesafe:config:${AnduinVersions.typesafeConfig}",
    mvn"dev.zio::zio::${AnduinVersions.zio}"
      .exclude("dev.zio" -> AnduinVersions.j2s("izumi-reflect"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("izumi-reflect")),
    mvn"dev.zio::zio-config:${AnduinVersions.zioConfig}"
      .exclude("dev.zio" -> AnduinVersions.j2s("zio")),
    mvn"dev.zio::zio-config-typesafe:${AnduinVersions.zioConfig}"
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-config"))
      .exclude("com.typesafe" -> "config"),
    mvn"dev.zio::zio-config-derivation:${AnduinVersions.zioConfig}"
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-config")),
    mvn"dev.zio::zio-config-magnolia:${AnduinVersions.zioConfig}"
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-config"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-config-derivation"))
  )

}
