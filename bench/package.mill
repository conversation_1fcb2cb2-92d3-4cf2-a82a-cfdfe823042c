package build.bench

import build.modules.gaia
import mill.contrib.jmh.JmhModule
import mill.*
import mill.scalalib.*
import anduin.mill.*
import anduin.build.AnduinVersions

object `package` extends Module {

  private def commonBenchmarkDeps = Seq(
    mvn"org.apache.fury:fury-core:${AnduinVersions.fury}"
      .exclude("com.google.guava" -> "guava")
      .exclude("org.slf4j" -> "slf4j-api"),
    mvn"org.apache.fury::fury-scala::${AnduinVersions.fury}"
      .exclude("org.apache.fury" -> "fury-core")
      .exclude("com.google.guava" -> "guava")
      .exclude("org.slf4j" -> "slf4j-api"),
    mvn"org.scala-lang.modules::scala-xml::${AnduinVersions.scala_xml}",
    mvn"org.scalatest::scalatest-core::${AnduinVersions.scalatest}"
      .exclude("org.scala-lang.modules" -> AnduinVersions.j2s("scala-xml"))
      .exclude("org.scala-lang.modules" -> AnduinVersions.j2sjs("scala-xml")),
    mvn"org.scalatest::scalatest::${AnduinVersions.scalatest}"
      .exclude("org.scalatest" -> AnduinVersions.j2s("scalatest-core"))
      .exclude("org.scalatest" -> AnduinVersions.j2sjs("scalatest-core"))
      .exclude("org.scala-lang.modules" -> AnduinVersions.j2s("scala-parser-combinators"))
      .exclude("org.scala-lang.modules" -> AnduinVersions.j2sjs("scala-parser-combinators"))
      .exclude("org.scala-lang.modules" -> AnduinVersions.j2s("scala-xml"))
      .exclude("org.scala-lang.modules" -> AnduinVersions.j2sjs("scala-xml")),
    mvn"org.scalatestplus::scalacheck-1-17::${AnduinVersions.scalatestScalacheck}"
      .exclude("org.scalatest" -> AnduinVersions.j2s("scalatest"))
      .exclude("org.scalatest" -> AnduinVersions.j2sjs("scalatest"))
      .exclude("org.scalatest" -> AnduinVersions.j2s("scalatest-core"))
      .exclude("org.scalatest" -> AnduinVersions.j2sjs("scalatest-core"))
      .exclude("org.scalacheck" -> AnduinVersions.j2s("scalacheck"))
      .exclude("org.scalacheck" -> AnduinVersions.j2sjs("scalacheck")),
    mvn"org.scalactic::scalactic::${AnduinVersions.scalatest}"
      .exclude("org.scala-lang.modules" -> AnduinVersions.j2s("scala-parser-combinators"))
      .exclude("org.scala-lang.modules" -> AnduinVersions.j2sjs("scala-parser-combinators"))
      .exclude("org.scala-lang.modules" -> AnduinVersions.j2s("scala-xml"))
      .exclude("org.scala-lang.modules" -> AnduinVersions.j2sjs("scala-xml")),
    mvn"org.scalacheck::scalacheck::${AnduinVersions.scalacheck}",
    mvn"com.lihaoyi::sourcecode::${AnduinVersions.sourcecode}"
  )

  trait BenchModule extends AnduinScalaModule with JmhModule {

    override def jmhCoreVersion: T[String] = "1.37" // AnduinVersions.jmh

  }

  object `form-serialization-bench` extends BenchModule {

    override def mvnDeps = super.mvnDeps() ++ commonBenchmarkDeps

    override def moduleDeps = super.moduleDeps ++ Seq(gaia.gaia.jvm)
  }

  object `id-parser-bench` extends BenchModule {

    override def mvnDeps = super.mvnDeps() ++ commonBenchmarkDeps

    override def moduleDeps = super.moduleDeps ++ Seq(gaia.gaia.jvm)
  }

}
