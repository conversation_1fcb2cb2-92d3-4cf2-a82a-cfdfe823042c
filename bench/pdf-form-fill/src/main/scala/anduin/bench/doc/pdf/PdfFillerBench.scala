// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.bench.doc.pdf

import anduin.documentcontent.pdf.FillPDF
import anduin.documentcontent.pdf.FillPDF.FlattenOption
import org.apache.pdfbox.pdmodel.PDDocument
import org.apache.pdfbox.pdmodel.interactive.form.{PDAcroForm, PDCheckBox, PDField, PDRadioButton}
import org.apache.pdfbox.io.RandomAccessReadBuffer.RandomAccessReadBuffer
import zio.Unsafe

import scala.jdk.CollectionConverters.*
import org.openjdk.jmh.annotations.*

// pdfFormFill/jmh:run -i 3 -wi 3 -f1 -t1 .*PdfFillerBench
@State(Scope.Thread)
class PdfFillerBench {
  var pdfDoc: PDDocument = scala.compiletime.uninitialized // scalafix:off DisableSyntax.var
  var valuesMap: Map[String, String] = Map.empty // scalafix:ok DisableSyntax.var

  private val pdfPath = "/documents/bench/pipeline.pdf"

  @Setup
  def prepare(): Unit = {
    val stream = getClass.getResourceAsStream(pdfPath)
    pdfDoc = org.apache.pdfbox.Loader.loadPDF(new RandomAccessReadBuffer(stream))
    valuesMap = getMap(pdfDoc.getDocumentCatalog.getAcroForm)
  }

  private def getMap(acroForm: PDAcroForm): Map[String, String] = {
    val iterator = acroForm.getFieldIterator.asScala
    iterator.map {
      case box: PDCheckBox => box.getFullyQualifiedName -> box.getOnValue
      case radio: PDRadioButton =>
        radio.getFullyQualifiedName -> radio.getOnValues.asScala.headOption.getOrElse("")
      case field: PDField =>
        field.getFullyQualifiedName -> field.getFullyQualifiedName
    }.toMap
  }

  @Benchmark
  @Fork(jvmArgsAppend = Array("-server", "-Xmx1G"))
  def fillPdf(): Unit = {
    val task = FillPDF.fillPdDocumentZio(
      pdfDoc,
      valuesMap,
      flattenOption = FlattenOption.FlattenAllFields
    )
    Unsafe.unsafely {
      zio.Runtime.default.unsafe.run(task.unit).getOrThrow()
    }
  }

}
