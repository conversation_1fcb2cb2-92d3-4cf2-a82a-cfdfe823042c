// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.bench.doc.pdf

import java.io.ByteArrayOutputStream
import java.nio.charset.Charset
import com.anduin.stargazer.AsyncUnitSpec
import org.apache.commons.io.IOUtils
import org.apache.pdfbox.pdmodel.PDDocument
import org.apache.pdfbox.pdmodel.interactive.form.{PDAcroForm, PDCheckBox, PDField, PDNonTerminalField, PDRadioButton, PDTerminalField}
import zio.ZIO
import org.apache.pdfbox.io.RandomAccessReadBuffer.RandomAccessReadBuffer
import org.scalatest.freespec.AsyncFreeSpec

import scala.jdk.CollectionConverters.*
import zio.implicits.*

final class FillPdfSpecs extends AsyncFreeSpec {

  private def printPDField(field: PDField, level: Int): Unit = {
    val tabs = " " * level
    field match {
      case p: PDNonTerminalField =>
        scribe.info(tabs + s"Group $field")
        p.getChildren.asScala.foreach(c => printPDField(c, level + 1))
      case _: PDTerminalField =>
        scribe.info(tabs + s"$field")
    }
  }

  private def getMap(acroForm: PDAcroForm): Map[String, String] = {
    val iterator = acroForm.getFieldIterator.asScala
    iterator.map {
      case box: PDCheckBox => box.getFullyQualifiedName -> box.getOnValue
      case radio: PDRadioButton =>
        radio.getFullyQualifiedName -> radio.getOnValues.asScala.headOption.getOrElse("")
      case field: PDField =>
        field.getFullyQualifiedName -> field.getFullyQualifiedName
    }.toMap
  }

  private def fillForm(acroForm: PDAcroForm, values: Map[String, String]): Unit = {
    val iterator = acroForm.getFieldIterator.asScala
    for (elem <- iterator) {
      fillPdfForm(elem, values.getOrElse(elem.getFullyQualifiedName, ""))
    }
  }

  private def fillPdfForm(field: PDField, value: String): Unit = {
    field match {
      case box: PDCheckBox => if (value == "On") box.check() else box.unCheck()
      case radio: PDRadioButton =>
        if (radio.getOnValues.asScala.contains(value)) {
          field.setValue(value)
        } else {
          ()
        }
      case _ => field.setValue(value)
    }
  }

  val pdfPath = "/documents/bench/pipeline.pdf"
  "Get acroform" in {
    val task = for {
      pdfDocument <- ZIO.attempt {
        val stream = getClass.getResourceAsStream(pdfPath)
        org.apache.pdfbox.Loader.loadPDF(new RandomAccessReadBuffer(stream))
      }
      docCatalog <- ZIO.attempt {
        pdfDocument.getDocumentCatalog
      }
      _ <- ZIO.attempt {
        val lines =
          IOUtils.readLines(docCatalog.getMetadata.exportXMPMetadata(), Charset.defaultCharset()).asScala.toList
        scribe.info(lines.mkString("\n"))
      }
      forms <- ZIO.attempt {
        docCatalog.getAcroForm
      }
      _ <- ZIO.attempt {
        forms.getFields.asScala.foreach(p => printPDField(p, 1))
      }
    } yield ()

    task.either.map(_ shouldBe Symbol("right")).runToFuture
  }

  "Fill pdf form" in {
    val task = for {
      pdfDocument <- ZIO.attempt {
        val stream = getClass.getResourceAsStream(pdfPath)
        org.apache.pdfbox.Loader.loadPDF(new RandomAccessReadBuffer(stream))
      }
      docCatalog <- ZIO.attempt {
        pdfDocument.getDocumentCatalog
      }
      forms <- ZIO.attempt {
        val form = docCatalog.getAcroForm
        form.setCacheFields(true)
        form
      }
      values <- ZIO.attempt {
        getMap(forms)
      }
      _ <- ZIO.attempt {
        fillForm(forms, values)
      }
      newDoc <- ZIO.attempt {
        val outputStream = new ByteArrayOutputStream
        pdfDocument.save(outputStream)
        org.apache.pdfbox.Loader.loadPDF(outputStream.toByteArray)
      }
      newValues <- ZIO.attempt {
        getMap(newDoc.getDocumentCatalog.getAcroForm)
      }
    } yield {
      newValues shouldBe values
    }

    task.runToFuture
  }

}
