package build.modules.bifrost

import build_.build.util_.*
import build.modules.heimdall
import build.platform.stargazer

import mill.*
import anduin.mill.*

object `package` extends AnduinCrossPlatformModule {

  object jvm extends JvmModule {

    override def moduleDeps =
      super.moduleDeps ++ Seq(heimdall.heimdall.jvm, build.gondor.gondorCore.jvm, stargazer.jvm)

    object it extends AnduinZioTests with AnduinIntegTests {
      override def moduleDeps = super.moduleDeps ++ Seq(build.gondor.gondorTest, jvm)
    }

  }

  object js extends JsModule {

    override def moduleDeps = super.moduleDeps ++ Seq(heimdall.heimdall.js)

  }

}
