// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.workflow.upload.impl

import zio.temporal.failure.ApplicationFailure
import zio.{Task, ZIO}

import anduin.dataroom.notification.*
import anduin.dataroom.service.DataRoomFileUploadEmailService
import anduin.dataroom.workflow.*
import anduin.dataroom.workflow.upload.DataRoomPostUploadActivities
import anduin.dms.DmsFeature.DataRoom
import anduin.dms.service.FileService
import anduin.documentservice.pagecount.FilePageCountService
import anduin.fdb.record.FDBRecordDatabase
import anduin.fdb.record.model.RecordIO
import anduin.model.common.user.UserId
import anduin.model.id.notification.NotificationSpaceId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.model.id.{FileId, NotificationIdFactory}
import anduin.notification.NotificationService
import anduin.protobuf.flow.file.FileFolderPermissionMap
import anduin.protobuf.notification.*
import anduin.team.TeamServiceUtils
import anduin.team.flow.memberflow.TeamMemberStateStoreProvider
import anduin.workflow.TemporalWorkflowService
import anduin.workflow.TemporalWorkflowService.runActivity
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.utils.ZIOUtils

final case class DataRoomPostUploadActivitiesImpl(
  backendConfig: GondorBackendConfig,
  fileService: FileService,
  filePageCountService: FilePageCountService,
  notificationService: NotificationService,
  dataRoomFileUploadEmailService: DataRoomFileUploadEmailService,
  dataRoomNotificationService: DataRoomNotificationService
)(
  using val temporalWorkflowService: TemporalWorkflowService
) extends DataRoomPostUploadActivities {

  private def getUserIdsFromPermissionMap(
    permissionMap: FileFolderPermissionMap,
    excludedUsers: Set[UserId]
  ): Task[Set[UserId]] = {
    for {
      teamUserIs <- FDBRecordDatabase
        .transact(TeamMemberStateStoreProvider.Production) { teamStore =>
          RecordIO.parTraverseN(4)(permissionMap.teamPermissions.keySet) { teamId =>
            TeamServiceUtils
              .getTeamMemberInfo(teamStore)(teamId)
              .map(_.joinedMembers.toSeq)
          }
        }
        .map(_.flatten.toSet)
    } yield {
      (permissionMap.userPermissions.keys.toSet ++ teamUserIs) -- excludedUsers
    }
  }

  private def createNewFileNotification(
    dataRoomWorkflowId: DataRoomWorkflowId,
    fileIds: Set[FileId],
    receivers: Set[UserId],
    action: NotificationAction,
    actor: UserId
  ): Task[Unit] = {
    val notificationSpaceId = NotificationSpaceId.channelSystemNotificationSpaceId(dataRoomWorkflowId)
    notificationService.create(
      NotificationIdFactory.unsafeRandomId(notificationSpaceId),
      NotificationType.DataRoomNewFile,
      action,
      DataRoomNewFileNotificationData(dataRoomWorkflowId, fileIds),
      receivers,
      Some(actor)
    )
  }

  private def addDataRoomNewFileNotification(
    dataRoomWorkflowId: DataRoomWorkflowId,
    fileIds: Set[FileId],
    receivers: Set[UserId],
    actor: UserId
  ): Task[Unit] = {
    for {
      dataRoomNotificationConfigs <- dataRoomNotificationService
        .getDataRoomNotificationConfigs(dataRoomWorkflowId, actor, bypassAdminCheck = true)
      _ <- dataRoomNotificationConfigs.newFileNotificationConfig.fold {
        ZIO.logWarning(s"Data room ${dataRoomWorkflowId} does not set new file notification config")
      } { notificationConfig =>
        notificationConfig.notificationMode match {
          case _: NotificationMode.Unrecognized => ZIO.unit
          case NotificationMode.DontNotify      => ZIO.unit
          case NotificationMode.Notify          =>
            createNewFileNotification(
              dataRoomWorkflowId,
              fileIds,
              receivers,
              DataRoomFileUploadEmailService
                .getNewFileNotificationAction(notificationConfig.notificationFrequency)
                .getOrElse[NotificationAction](NotificationAction.UnspecifiedAction),
              actor
            )
          case NotificationMode.ParticipantsChoice =>
            for {
              receiverByFrequency <- ZIO
                .foreach(receivers.grouped(100).toList) { userIds =>
                  FDBRecordDatabase.transact(DataRoomNotificationSettingsOperations.Production) { notificationOps =>
                    RecordIO
                      .parTraverseN(8)(userIds) { userId =>
                        notificationOps
                          .get(dataRoomWorkflowId, userId, Some(dataRoomNotificationConfigs))
                          .map(settings =>
                            userId -> Option.when(settings.newFileNotification == NewFileNotification.AllFiles)(
                              settings.newFileNotificationFrequency
                            )
                          )
                      }
                  }
                }
                .map(_.flatten.groupMap(_._2)(_._1))
              _ <- ZIO.foreachDiscard(receiverByFrequency) { (frequencyOpt, userIds) =>
                createNewFileNotification(
                  dataRoomWorkflowId,
                  fileIds,
                  userIds.toSet,
                  frequencyOpt
                    .flatMap(DataRoomFileUploadEmailService.getNewFileNotificationAction)
                    .getOrElse(NotificationAction.UnspecifiedAction),
                  actor
                )
              }
            } yield ()
        }
      }
    } yield ()
  }

  override def addNewFileNotification(params: AddNewFileNotificationParams): AddNewFileNotificationResponse = {
    val task = for {
      receivers <- params.permissionMapOpt.fold {
        for {
          folderPermissionMap <- fileService.getFolderPermissionMap(params.actor)(params.folderId)
          userIds <- getUserIdsFromPermissionMap(folderPermissionMap, Set(params.actor))
        } yield userIds
      } { permissionMap =>
        getUserIdsFromPermissionMap(permissionMap, Set(params.actor))
      }
      _ <- ZIOUtils.when(receivers.nonEmpty) {
        if (params.sendNotificationOnUpload) {
          dataRoomFileUploadEmailService.sendNewUploadEmails(
            params.dataRoomWorkflowId,
            params.fileIds,
            receivers
          )
        } else {
          addDataRoomNewFileNotification(
            params.dataRoomWorkflowId,
            params.fileIds,
            receivers,
            params.actor
          )
        }
      }
    } yield AddNewFileNotificationResponse()

    task.runActivity
  }

  override def getPageCount(params: GetPageCountParams): GetPageCountResponse = {
    val task = ZIO
      .foreach(params.fileIds) { fileId =>
        filePageCountService.getPageCount(params.actor, fileId)
      }
      .map(_ => GetPageCountResponse())
      .catchAll(e =>
        ZIO.fail(
          ApplicationFailure.newFailure(s"Failed to get page count: ${e.getMessage}", e.getClass.getName)
        )
      )

    task.runActivity
  }

  override def modifyLastUpdate(params: ModifyLastUpdateParams): ModifyLastUpdateResponse = {
    val task = for {
      _ <- ZIO.foreach(params.fileIds) { fileId =>
        fileService.modifyFileLastUpdatedAt(params.actor)(fileId)
      }
    } yield ModifyLastUpdateResponse()

    task.runActivity
  }

}
