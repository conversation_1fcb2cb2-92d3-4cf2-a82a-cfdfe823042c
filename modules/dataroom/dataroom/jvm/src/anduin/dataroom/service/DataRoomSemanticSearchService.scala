// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.service

import com.apple.foundationdb.record.TupleRange
import zio.{Task, ZIO}

import anduin.dataroom.DataRoomUserData.UserInfo
import anduin.dataroom.flow.DataRoomStateStoreProvider
import anduin.dataroom.flow.DataRoomValidateOperations.PlanCheck
import anduin.dataroom.service.DataRoomSemanticSearchService.*
import anduin.dataroom.state.DataRoomCreatedSharedFlowState
import anduin.dataroom.validator.DataRoomValidator
import anduin.dataroom.workflow.migration.DataRoomMigrateSearchIndexWorkflow
import anduin.dataroom.workflow.{DataRoomMigrateSearchIndexParams, DataRoomMigrateSearchIndexResponse}
import anduin.dms.DmsFeature
import anduin.dms.service.FileService
import anduin.environment.EnvironmentCheck
import anduin.fdb.record.FDBRecordDatabase
import anduin.model.common.user.UserId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.model.id.{FileId, FolderId}
import anduin.portaluser.ExecutiveAdmin
import anduin.rag.model.{RagFileContentKeywordSearchResult, RagFilenameKeywordSearchResult, TextWithHighlights}
import anduin.rag.{RagIndexService, RagSearchService, SearchIndexState}
import anduin.stargazer.service.dataroom.*
import anduin.workflow.TemporalWorkflowService
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.utils.ZIOUtils

final case class DataRoomSemanticSearchService(
  backendConfig: GondorBackendConfig,
  fileService: FileService,
  dataRoomValidator: DataRoomValidator,
  ragIndexService: RagIndexService,
  ragSearchService: RagSearchService,
  executiveAdmin: ExecutiveAdmin,
  temporalWorkflowService: TemporalWorkflowService
) {
  given dmsFeature: DmsFeature = DmsFeature.DataRoom

  private val config = backendConfig.dataRoomSemanticSearchConfig
  val isEnabled: Boolean = config.isEnabled

  private def isShowSearchEnabled(dataRoomWorkflowId: DataRoomWorkflowId, actor: UserId): Task[Boolean] = {
    DataRoomServiceUtils
      .getDataRoomState(
        actor = actor,
        dataRoomWorkflowId = dataRoomWorkflowId,
        isArchived = false,
        includeInvited = false,
        includeToaNotAccepted = false,
        includeGroup = false,
        planCheck = PlanCheck.RequirePlan(Set()),
        environmentCheck = EnvironmentCheck.Bypass
      )
      .map(_.createdState.showSearch)
  }

  def isDataRoomIndexed(
    dataRoomWorkflowId: DataRoomWorkflowId
  ): Task[Boolean] = {
    ragIndexService
      .getChannelIndexState(dataRoomWorkflowId)(
        using DmsFeature.DataRoom
      )
      .map(state =>
        isEnabled
          && state != SearchIndexState.NotIndexed
          && state != SearchIndexState.Indexing
          && state != SearchIndexState.Error
      )
  }

  def indexFolders(
    dataRoomWorkflowId: DataRoomWorkflowId,
    folderIds: Seq[FolderId],
    actor: UserId,
    withTags: Option[Seq[String]] = None
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    for {
      showSearch <- isShowSearchEnabled(dataRoomWorkflowId, actor)
      _ <- ZIOUtils.when(isEnabled && showSearch) {
        ragIndexService.batchSyncFolderIndex(folderIds, actor, withTags)
      }
    } yield ()
  }

  def deleteFolderIndexes(
    folderIds: Seq[FolderId],
    actor: UserId,
    isPermanent: Boolean = false
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    ZIOUtils.when(isEnabled) {
      ragIndexService.batchDeleteFolderIndexes(folderIds, actor, isPermanent)
    }
  }

  def startIndexFiles(
    dataRoomWorkflowId: DataRoomWorkflowId,
    fileIds: Seq[FileId],
    actor: UserId,
    withTags: Option[Seq[String]] = None
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    for {
      showSearch <- isShowSearchEnabled(dataRoomWorkflowId, actor)
      _ <- ZIOUtils.when(isEnabled && showSearch) {
        ragIndexService.batchSyncFileIndex(fileIds, actor, withTags)
      }
    } yield ()
  }

  def deleteFileIndexes(
    fileIds: Seq[FileId],
    actor: UserId,
    isPermanent: Boolean = false
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    ZIOUtils.when(isEnabled) {
      ragIndexService.batchDeleteFileIndexes(fileIds, actor, isPermanent)
    }
  }

  def startUpdateSearchIndexWorkflow(
    dataRoomWorkflowId: DataRoomWorkflowId,
    actor: UserId
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    ZIOUtils.when(isEnabled) {
      ragIndexService.startUpdateIndexWorkflow(dataRoomWorkflowId, actor)
    }
  }

  def keywordSearchFolder(
    dataRoomWorkflowId: DataRoomWorkflowId,
    query: String,
    actor: UserId,
    limit: Int = 10
  ): Task[List[DataRoomFolderSearchResult]] = {
    ragSearchService
      .keywordSearchFolder(actor, dataRoomWorkflowId, Left(query), limit)(
        using DmsFeature.DataRoom
      )
      .map(
        _.map(result =>
          DataRoomFolderSearchResult(
            itemId = result.folderId,
            nameWithHighlights = result.folderNameWithHighlight,
            createdUserInfo = UserInfo(result.createdUserInfo),
            relevanceScore = result.relevanceScore,
            lastModifiedTime = result.lastModifiedTime
          )
        )
      )
  }

  private def mergeFilenameAndContentSearchResults(
    filenameResults: List[RagFilenameKeywordSearchResult],
    fileContentResults: List[RagFileContentKeywordSearchResult],
    actor: UserId
  ): Task[List[DataRoomFileSearchResult]] = {
    val fileIdToFilenameResultsMap = filenameResults.map(result => result.fileId -> result).toMap
    val fileIdToBestContentResultsMap = fileContentResults.groupBy(_.fileId).map { case (fileId, results) =>
      fileId -> results.sortBy(-_.relevanceScore).take(maxNumContentResultForEachFile)
    }
    val allFileIds = fileIdToFilenameResultsMap.keySet ++ fileIdToBestContentResultsMap.keySet

    for {
      parentFolderNameMap <- fileService.batchGetFolderName(actor)(allFileIds.map(_.parent).toSeq)
      results <- ZIOUtils
        .foreachParN(parallelism)(allFileIds.toList) { fileId =>
          for {
            parentFolderName <- ZIOUtils.fromOption(parentFolderNameMap.get(fileId.parent))
            filenameResultOpt = fileIdToFilenameResultsMap.get(fileId)
            contentResultOpts = fileIdToBestContentResultsMap.get(fileId).fold(List(None))(_.map(Some(_)))

            results <- ZIO.foreach(contentResultOpts) { contentResultOpt =>
              for {
                nameWithHighlights <- filenameResultOpt.fold(
                  ZIOUtils.fromOption(contentResultOpt.map(_.filename).map(TextWithHighlights.WithoutHighlights(_)))
                )(r => ZIO.succeed(r.filenameWithHighlight))
                createdUserInfo <- ZIOUtils.fromOption(
                  filenameResultOpt
                    .map(_.createdUserInfo)
                    .orElse(contentResultOpt.map(_.createdUserInfo))
                    .map(UserInfo(_))
                )
                relevanceScore = filenameResultOpt.map(_.relevanceScore).getOrElse(0d)
                  + contentResultOpt.map(_.relevanceScore).getOrElse(0d)
                lastModifiedTime = filenameResultOpt
                  .flatMap(_.lastModifiedTime)
                  .orElse(contentResultOpt.flatMap(_.lastModifiedTime))
              } yield DataRoomFileSearchResult(
                itemId = fileId,
                nameWithHighlights = nameWithHighlights,
                parentFolderName = parentFolderName,
                contentSnippetOpt = contentResultOpt.map(_.contentSnippet),
                pageIndex = contentResultOpt.map(_.pageIndex),
                createdUserInfo = createdUserInfo,
                relevanceScore = relevanceScore,
                lastModifiedTime = lastModifiedTime
              )
            }
          } yield results
        }
        .map(_.flatten.sortBy(-_.relevanceScore))
    } yield results
  }

  def keywordSearchFile(
    dataRoomWorkflowId: DataRoomWorkflowId,
    query: String,
    actor: UserId,
    limit: Int = 10,
    withContent: Boolean = true
  ): Task[List[DataRoomFileSearchResult]] = {
    for {
      preprocessedQuery <- ragSearchService.preprocessQuery(query)
      filenameResults <- ragSearchService
        .keywordSearchFile(actor, dataRoomWorkflowId, Right(preprocessedQuery), limit)
      // take more chunks to address the case that many chunks are from the same file, but we only keep the best chunks
      fileContentResult <-
        if (withContent) {
          ragSearchService
            .keywordSearchFileContent(actor, dataRoomWorkflowId, Right(preprocessedQuery), limit * 2)
        } else { ZIO.succeed(List.empty[RagFileContentKeywordSearchResult]) }
      mergedResult <- mergeFilenameAndContentSearchResults(filenameResults, fileContentResult, actor)
    } yield mergedResult.take(limit)
  }

  private def getAllDataRoomsStateWithFilterUnsafe(filter: DataRoomCreatedSharedFlowState => Boolean)
    : Task[List[DataRoomCreatedSharedFlowState]] = {
    for {
      stream <- FDBRecordDatabase
        .largeScanStream(
          DataRoomStateStoreProvider.Production,
          DataRoomStateStoreProvider.mapping,
          TupleRange.ALL,
          limit = 200
        )
      states <- stream.map(_._2).filter(filter).runCollect.map(_.toList)
    } yield states
  }

  def getAllDataRoomsIndexStateUnsafe: Task[GetAllDataRoomsSearchIndexStateResponse] = {
    for {
      dataRoomStates <- getAllDataRoomsStateWithFilterUnsafe(!_.isArchived)
      dataRoomIndexStates <- ZIOUtils.foreachParN(parallelism)(dataRoomStates) { state =>
        ragIndexService
          .getChannelIndexState(state.dataRoomWorkflowId)(
            using DmsFeature.DataRoom
          )
          .map(searchIndexState =>
            DataRoomSearchIndexStateInfo(
              entityId = state.creatorEntityId,
              dataRoomWorkflowId = state.dataRoomWorkflowId,
              dataRoomName = state.name,
              showSearch = state.showSearch,
              searchIndexState = searchIndexState
            )
          )
      }
    } yield GetAllDataRoomsSearchIndexStateResponse(dataRoomIndexStates)
  }

  def reindexDataRoomsUnsafe(
    params: ReindexDataRoomsParams
  ): Task[DataRoomEmptyResponse] = {
    for {
      searchEnabledDataRoomIds <- getAllDataRoomsStateWithFilterUnsafe(state => state.showSearch && !state.isArchived)
        .map(_.map(_.dataRoomWorkflowId))
      toReindexDataRooms <-
        if (params.indexFailedOnly) {
          ZIOUtils
            .foreachParN(parallelism)(searchEnabledDataRoomIds) { dataRoomWorkflowId =>
              ragIndexService
                .getChannelIndexState(dataRoomWorkflowId)(
                  using DmsFeature.DataRoom
                )
                .map(state => Option.when(state == SearchIndexState.Error)(dataRoomWorkflowId))
            }
            .map(_.flatten)
        } else {
          ZIO.succeed(searchEnabledDataRoomIds)
        }
      executiveAdminUser <- executiveAdmin.userId
      _ <- temporalWorkflowService.runAsync[
        DataRoomMigrateSearchIndexParams,
        DataRoomMigrateSearchIndexResponse,
        DataRoomMigrateSearchIndexWorkflow
      ](
        DataRoomMigrateSearchIndexParams(
          dataRoomWorkflowIds = toReindexDataRooms,
          actor = executiveAdminUser,
          forceReindex = true
        )
      )
    } yield DataRoomEmptyResponse()
  }

}

object DataRoomSemanticSearchService {
  private val parallelism = 16
  private val maxNumContentResultForEachFile = 3
}
