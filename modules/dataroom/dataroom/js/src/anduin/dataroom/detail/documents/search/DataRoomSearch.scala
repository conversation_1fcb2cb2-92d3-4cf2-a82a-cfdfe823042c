// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.documents.search

import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.radio.Radio
import design.anduin.components.textbox.TextBox
import design.anduin.components.toast.Toast
import design.anduin.components.tracker.react.VisibilityTrackerR
import design.anduin.components.util.ComponentUtils
import design.anduin.facades.reactvirtualized.{ReactVirtualizedAutoSizer, ReactVirtualizedList}
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import japgolly.scalajs.react.vdom.html_<^.*
import org.scalajs.dom.KeyCode

import anduin.dataroom.detail.documents.search.DataRoomSearch.SearchType
import anduin.dataroom.detail.documents.{DataRoomFileViewer, SkeletonListFolder}
import anduin.dataroom.endpoints.DataRoomEndpointClient
import anduin.dataroom.share.{Column, ColumnHead}
import anduin.enumeration.StringEnum
import anduin.file.TooltipOnTruncate
import anduin.file.explorer.DmsIcon
import anduin.file.table.{FileTable, FileUpdatedTime}
import anduin.frontend.WebAppRenderer
import anduin.id.dataroom.DataRoomSearchId
import anduin.model.id.FolderId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.scalajs.pluralize.Pluralize
import anduin.service.GeneralServiceException
import anduin.stargazer.service.dataroom.*
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.endpoints.{FileInfo, FolderInfo}
import stargazer.model.routing.{DynamicAuthPage, Page}

final case class DataRoomSearch(
  router: RouterCtl[Page],
  dataRoomWorkflowId: DataRoomWorkflowId,
  initialQuery: String,
  searchType: SearchType = SearchType.Document,
  onClose: Callback = Callback.empty,
  controlled: Boolean = false
) {
  def apply(): VdomElement = DataRoomSearch.component(this)
}

object DataRoomSearch {

  private val searchLimit = 100

  private type Props = DataRoomSearch

  private final case class State(
    currentSearch: SearchQueryModel,
    isSearching: Boolean,
    lastSearchOpt: Option[SearchQueryModel],
    lastResultOpt: Option[SearchResultModel]
  )

  private final case class SearchQueryModel(
    query: String,
    searchType: SearchType
  )

  private final case class SearchResultModel(
    searchId: DataRoomSearchId,
    totalCount: Int,
    nextOffset: Int,
    results: List[DataRoomSearchResult]
  )

  enum SearchType(val value: String) extends StringEnum {
    case Document extends SearchType("Document")
    case Folder extends SearchType("Folder")
  }

  sealed trait SearchResultColumn extends Column

  private object SearchResultColumn {
    object Name extends SearchResultColumn
    object Location extends SearchResultColumn
    object CreatedBy extends SearchResultColumn
    object UpdatedAt extends SearchResultColumn
  }

  private final class Backend(scope: BackendScope[Props, State]) {

    private val currentSearchZoom: scope.WithMappedState[SearchQueryModel] =
      scope.zoomState(_.currentSearch)(newSearch => _.copy(currentSearch = newSearch))

    def render(props: Props, state: State): VdomNode = {
      val results = state.lastResultOpt.map(_.results).getOrElse(List.empty)
      <.div(
        tw.flex.flexCol.hPc100.overflowHidden,
        // Top toolbar
        <.div(
          tw.flex.flexCol.pt24.px24,
          renderHeader(props, state),
          <.div(
            tw.maxWPx640,
            renderSearchBar(state),
            renderSearchTarget(props, state)
          )
        ),
        if (state.isSearching) {
          SkeletonListFolder()()
        } else if (results.isEmpty) {
          renderEmptyState
        } else {
          <.div(
            ComponentUtils.testId(DataRoomSearch, "SearchResult"),
            tw.flexFill.overflowYAuto,
            renderSearchResults(props, state),
            // Infinite scrolling tracker
            VisibilityTrackerR(
              renderChildren = _ => EmptyVdom,
              onVisibilityChanged = { isVisible =>
                Callback.when(isVisible)(onSearch())
              }
            )()
          )
        }
      )
    }

    private def renderTitle(state: State) = {
      val query = state.lastSearchOpt.map(_.query.trim).getOrElse("")
      if (query.nonEmpty) {
        <.div(
          ComponentUtils.testId(DataRoomSearch, "ResultTitle"),
          tw.flex.itemsCenter.heading3.mr4.truncate,
          <.div(
            tw.textGray6.whitespaceNowrap,
            "Results for"
          ),
          <.div(
            tw.truncate.textGray8.ml4,
            query
          )
        )
      } else {
        <.div(
          tw.heading3.mr4,
          "Search data room"
        )
      }
    }

    private def renderHeader(props: Props, state: State) = {
      <.div(
        tw.flex,
        renderTitle(state),
        Button(
          testId = "DataRoomSearchPanel_Close",
          style = Button.Style.Minimal(icon = Option(Icon.Glyph.Cross)),
          onClick = props.onClose
        )()
      )
    }

    private def renderSearchBar(state: State) = {
      <.div(
        tw.flexFill.flexCol,
        <.div(
          tw.flex.flexFill.itemsCenter.mt12,
          <.div(
            ComponentUtils.testId(DataRoomSearch, "SearchInput"),
            tw.flexFill,
            TextBox(
              value = state.currentSearch.query,
              onChange = query => currentSearchZoom.modState(_.copy(query = query)),
              placeholder = s"Search for ${state.currentSearch.searchType.value}",
              onClear = Some(currentSearchZoom.modState(_.copy(query = ""))),
              onKeyDown = { (e: ReactKeyboardEventFromInput) =>
                val keyCode = e.keyCode
                Callback.when(keyCode == KeyCode.Enter)(onSearch(reload = true))
              }
            )()
          ),
          <.div(
            ComponentUtils.testId(DataRoomSearch, "SearchBtn"),
            tw.ml8,
            Button(
              style = Button.Style.Full(color = Button.Color.Primary, isBusy = state.isSearching),
              isDisabled = state.currentSearch.query.trim.isEmpty,
              onClick = onSearch(reload = true)
            )("Search")
          )
        ), {
          val count = state.lastResultOpt.map(_.results.size).getOrElse(0)
          val fetchedAll = state.lastResultOpt.exists(res => res.nextOffset >= res.totalCount)
          <.div(
            ComponentUtils.testId(DataRoomSearch, "SearchResultBrief"),
            tw.textSmall.textGray7.hPx16.mt4,
            Option
              .when(state.lastSearchOpt.isDefined && !state.isSearching)(
                s"$count${if (fetchedAll) "" else "+"} ${Pluralize("result", count)}"
              )
          )
        }
      )
    }

    private def renderSearchTarget(props: Props, state: State) = {
      <.div(
        tw.flex.itemsCenter.mt12,
        <.div(
          tw.textBody.textGray7.fontSemiBold,
          "Search by:"
        ),
        renderSearchTargetOptions(props, state)
      )
    }

    private def renderSearchTargetOptions(props: Props, state: State) = {
      val searchTypes = List(
        SearchType.Document,
        SearchType.Folder
      )
      searchTypes.map { searchType =>
        <.div(
          ComponentUtils.testId(DataRoomSearch, s"Search${searchType.value}"),
          ^.key := searchType.value,
          tw.ml16,
          Radio(
            isChecked = state.currentSearch.searchType == searchType,
            onChange = currentSearchZoom.modState(
              _.copy(searchType = searchType),
              onSearch()
            )
          )(searchType.value)
        )
      }.toVdomArray
    }

    private def renderEmptyState = {
      <.div(
        tw.hPc100.flex.flexCol.itemsCenter.justifyCenter,
        <.img(
          ^.width := "50px",
          ^.src := WebAppRenderer.assetsCtx.cdnMixedUrl("/web/gondor/images/dataroom/search-content-illustration.svg")
        ),
        <.div(
          ComponentUtils.testId(DataRoomSearch, "EmptyResult"),
          tw.textBody.textGray7.mt16,
          "No results matching your keyword"
        )
      )
    }

    private def getResultColumnList(state: State): List[SearchResultColumn] = {
      state.lastSearchOpt
        .map(_.searchType match {
          case SearchType.Document =>
            List(
              SearchResultColumn.Name,
              SearchResultColumn.Location,
              SearchResultColumn.CreatedBy,
              SearchResultColumn.UpdatedAt
            )
          case SearchType.Folder =>
            List(
              SearchResultColumn.Name,
              SearchResultColumn.CreatedBy,
              SearchResultColumn.UpdatedAt
            )
        })
        .getOrElse(List.empty[SearchResultColumn])
    }

    private def renderColumnHeaders(state: State) = {
      val columns = getResultColumnList(state)
      <.div(
        ComponentUtils.testId(DataRoomSearch, "Table-Head"),
        tw.flex.itemsCenter.borderBottom.borderTop.borderGray3,
        // Stick at top
        tw.sticky.z1.top0,
        columns.zipWithIndex.toVdomArray(
          using { case (column, index) =>
            val col = column match {
              case SearchResultColumn.Name =>
                <.div(
                  tw.flexFill,
                  ColumnHead(
                    column = column,
                    renderColumn = _.renderUnsortableHead("Name")
                  )()
                )
              case SearchResultColumn.Location =>
                <.div(
                  ^.width := FileTable.expandableMediumColumnWidth,
                  ColumnHead(
                    column = column,
                    renderColumn = _.renderUnsortableHead("Location")
                  )()
                )
              case SearchResultColumn.CreatedBy =>
                <.div(
                  ^.width := FileTable.expandableMediumColumnWidth,
                  ColumnHead(
                    column = column,
                    renderColumn = _.renderUnsortableHead("Created By")
                  )()
                )
              case SearchResultColumn.UpdatedAt =>
                <.div(
                  ^.width := FileTable.mediumColumnWidth,
                  ColumnHead(
                    column = column,
                    renderColumn = _.renderUnsortableHead("Updated")
                  )()
                )
            }
            col(^.key := index)
          }
        )
      )
    }

    private def goToFolder(props: Props)(folderId: FolderId) = {
      props.router.set(
        DynamicAuthPage.DataRoomFolderPage(
          props.dataRoomWorkflowId,
          folderId,
          None
        )
      )
    }

    private def renderFileFolderNameAndExcerpt(
      data: DataRoomSearchResult,
      onClick: Callback
    ) = {
      <.div(
        tw.flex.itemsCenter.cursorPointer,
        <.div(
          tw.flex.itemsCenter,
          DmsIcon(
            data match {
              case _: DataRoomFolderSearchResult  => DmsIcon.Folder(childCount = 1)
              case file: DataRoomFileSearchResult => DmsIcon.File(file.name)
            },
            Icon.Size.Px32
          )()
        ),
        <.div(
          tw.ml12.flexFill.textLeft,
          DataRoomSearchUtils.renderName(data),
          DataRoomSearchUtils.renderExcerpt(data)
        ),
        ^.onClick --> onClick
      )
    }

    private def renderRowNameFile(
      props: Props,
      file: DataRoomFileSearchResult,
      onClickTrackingCb: Callback
    ) = {
      DataRoomFileViewer(
        router = props.router,
        dataRoomWorkflowId = props.dataRoomWorkflowId,
        file = FileInfo(
          itemId = file.itemId,
          name = file.name,
          lastModifiedTime = file.lastModifiedTime
        ),
        initialContentPosition = file.pageIndex,
        renderTarget = _ => openViewer => renderFileFolderNameAndExcerpt(file, onClick = onClickTrackingCb >> openViewer)
      )()
    }

    private def renderRowNameFolder(
      props: Props,
      folder: DataRoomFolderSearchResult,
      onClickTrackingCb: Callback
    ) = {
      renderFileFolderNameAndExcerpt(folder, onClick = onClickTrackingCb >> goToFolder(props)(folder.itemId))
    }

    private def renderRowName(
      props: Props,
      data: DataRoomSearchResult,
      onClickTrackingCb: Callback
    ): VdomNode = {
      data match {
        case file: DataRoomFileSearchResult =>
          renderRowNameFile(props, file, onClickTrackingCb)
        case folder: DataRoomFolderSearchResult =>
          renderRowNameFolder(props, folder, onClickTrackingCb)
      }
    }

    private def renderRowLocation(
      props: Props,
      data: DataRoomSearchResult
    ) = {
      data match {
        case file: DataRoomFileSearchResult =>
          <.div(
            tw.flex.itemsCenter.cursorPointer,
            <.div(
              tw.flex.itemsCenter.mr4,
              DmsIcon(
                DmsIcon.Folder(childCount = 1),
                Icon.Size.Px24
              )()
            ),
            <.div(
              tw.flexFill.textLeft,
              TooltipOnTruncate(
                renderTarget = <.div.withRef(_)(
                  tw.truncate,
                  file.parentFolderName
                ),
                content = file.parentFolderName
              )()
            ),
            ^.onClick --> goToFolder(props)(file.itemId.parent)
          )
        case _: DataRoomFolderSearchResult => EmptyVdom
      }
    }

    private def renderRowUpdatedAt(
      data: DataRoomSearchResult
    ) = {
      FileUpdatedTime(
        data match {
          case folder: DataRoomFolderSearchResult =>
            FolderInfo
              .empty(folder.itemId)
              .copy(
                name = folder.name,
                lastModifiedTime = folder.lastModifiedTime
              )
          case file: DataRoomFileSearchResult =>
            FileInfo(
              itemId = file.itemId,
              name = file.name,
              lastModifiedTime = file.lastModifiedTime
            )
        }
      )()
    }

    private def renderRow(
      props: Props,
      state: State,
      searchResult: SearchResultModel,
      renderRowProps: ReactVirtualizedList.RowRenderProps
    ) = {
      val rowIndex = renderRowProps.index
      val rowData = searchResult.results(rowIndex)
      val columns = getResultColumnList(state)

      <.div(
        ComponentUtils.testId(DataRoomSearch, "Table-Row"),
        ^.key := renderRowProps.key,
        ^.style := renderRowProps.style,
        tw.flex.itemsCenter.borderBottom.borderGray3.hover(tw.bgPrimary1.bgOpacity20),
        columns.toVdomArray(
          using {
            case SearchResultColumn.Name =>
              <.div(
                tw.px12.py8.flexFill,
                renderRowName(
                  props,
                  rowData,
                  DataRoomSearchUtils.logViewSearchResultCallback(searchResult.searchId, rowIndex)
                )
              )
            case SearchResultColumn.Location =>
              <.div(
                tw.px12.py8,
                ^.width := FileTable.expandableMediumColumnWidth,
                renderRowLocation(props, rowData)
              )
            case SearchResultColumn.CreatedBy =>
              <.div(
                tw.px12.py8,
                ^.width := FileTable.expandableMediumColumnWidth,
                rowData.createdUserInfo.fullName
              )
            case SearchResultColumn.UpdatedAt =>
              <.div(
                tw.px12.py8,
                ^.width := FileTable.mediumColumnWidth,
                renderRowUpdatedAt(rowData)
              )
          }
        )
      )
    }

    private def renderSearchResults(props: Props, state: State) = {
      <.div(
        ComponentUtils.testId(DataRoomSearch, "ResultTable"),
        tw.flex.flexCol.hPc100.px24.pt24.z2.selectNone,
        // Column headers
        renderColumnHeaders(state),
        // List results
        <.div(
          tw.hPc100,
          ReactVirtualizedAutoSizer(
            childrenParam = renderProps => {
              ReactVirtualizedList(
                heightParam = renderProps.height,
                rowCountParam = state.lastResultOpt.map(_.results.length).getOrElse(0),
                rowHeightParam = _ => if (state.lastSearchOpt.exists(_.searchType == SearchType.Folder)) 57 else 77,
                rowRendererParam = renderRowProps =>
                  state.lastResultOpt
                    .fold(
                      EmptyVdom
                    )(searchResult =>
                      renderRow(
                        props,
                        state,
                        searchResult,
                        renderRowProps
                      )
                    )
                    .rawNode,
                widthParam = renderProps.width
              ).rawNode
            }
          )
        )
      )
    }

    private def isSameSearch(state: State, reload: Boolean) = {
      state.lastSearchOpt.contains(state.currentSearch) && !reload
    }

    private def shouldSearch(state: State, reload: Boolean) = {
      val currentQuery = state.currentSearch.query.trim
      val hasMoreResults = state.lastResultOpt.exists(result => result.nextOffset < result.totalCount)
      !state.isSearching && currentQuery.nonEmpty && (!isSameSearch(state, reload) || hasMoreResults)
    }

    def onSearch(reload: Boolean = false): Callback = {
      for {
        props <- scope.props
        state <- scope.state
        _ <- Callback.when(shouldSearch(state, reload)) {
          scope.modState(
            _.copy(isSearching = true),
            ZIOUtils.toReactCallback {
              DataRoomEndpointClient
                .fullSearch(
                  FullSearchDataRoomParams(
                    dataRoomWorkflowId = props.dataRoomWorkflowId,
                    isFileSearch = state.currentSearch.searchType == SearchType.Document,
                    query = state.currentSearch.query.trim,
                    limit = searchLimit
                  )
                )
                .map(
                  _.fold(
                    handleFailedSearch,
                    handleSuccessSearch(state, isSameSearch(state, reload))
                  )
                )
            }
          )
        }
      } yield ()
    }

    private def handleFailedSearch(error: GeneralServiceException): Callback = {
      scope.modState(
        _.copy(isSearching = false),
        Toast.errorCallback(error.getMessage)
      )
    }

    private def handleSuccessSearch(state: State, isSameSearch: Boolean)(resp: FullSearchDataRoomResponse): Callback = {
      scope.modState(
        _.copy(
          isSearching = false,
          lastSearchOpt = Some(state.currentSearch),
          lastResultOpt = Some(
            SearchResultModel(
              searchId = resp.searchId,
              totalCount = resp.results.length,
              results = resp.results,
              nextOffset = resp.results.length // disable pagination
            )
          )
        )
      )
    }

  }

  private def defaultState(props: Props) =
    State(
      currentSearch = SearchQueryModel(
        props.initialQuery,
        searchType = props.searchType
      ),
      isSearching = false,
      lastSearchOpt = None,
      lastResultOpt = None
    )

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialStateFromProps(defaultState)
    .renderBackend[Backend]
    .componentDidMount { scope =>
      Callback.when(scope.props.initialQuery.trim.nonEmpty) { scope.backend.onSearch(reload = true) }
    }
    .componentDidUpdate { scope =>
      val shouldReload = (scope.prevProps.initialQuery != scope.currentProps.initialQuery) ||
        (scope.prevProps.searchType != scope.currentProps.searchType) ||
        (scope.prevProps.dataRoomWorkflowId != scope.currentProps.dataRoomWorkflowId)
      Callback.when(shouldReload) {
        scope.setState(
          defaultState(scope.currentProps),
          scope.backend.onSearch(reload = true)
        )
      }
    }
    .build

}
