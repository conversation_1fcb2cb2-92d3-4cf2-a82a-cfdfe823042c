// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.documents

import java.time.Instant

import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.dataroom.endpoints.DataRoomEndpointClient
import anduin.file.explorer.{FileViewer, NameBar, UrlLoader}
import anduin.file.tracker.DmsViewTracker
import anduin.frontend.WebAppRenderer
import anduin.model.id.FileId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.protobuf.flow.file.FileFolderPermission
import anduin.stargazer.service.dataroom.*
import com.anduin.stargazer.client.utils.ZIOUtils
import com.anduin.stargazer.endpoints.FileInfo
import com.anduin.stargazer.service.file.BatchDownloadRequest
import com.anduin.stargazer.utils.FileFolderPermissionUtils
import stargazer.model.routing.DynamicAuthPage.{
  DataRoomDocumentsPage,
  DataRoomFolderPage,
  DataRoomRecentPage,
  DataRoomTrashPage
}
import stargazer.model.routing.{DynamicAuthPage, Page}

final case class DataRoomFileViewer(
  router: RouterCtl[Page],
  dataRoomWorkflowId: DataRoomWorkflowId,
  file: FileInfo,
  renderTarget: FileViewer.RenderTarget,
  modalType: FileViewer.ModalType = FileViewer.ModalType.Uncontrolled,
  initialContentPosition: Option[Int] = None,
  forceGetWatermark: Boolean = false,
  includeDeleted: Boolean = false,
  onClose: Callback = Callback.empty
) {
  def apply(): VdomElement = DataRoomFileViewer.component(this)
}

object DataRoomFileViewer {

  def controlled(
    router: RouterCtl[Page],
    page: DataRoomDocumentsPage
  ): FileViewer.ModalType.Controlled = {
    val fileIdOpt = DataRoomDocumentsPage.getFileIdOpt(page)
    FileViewer.ModalType.Controlled(
      isOpen = fileIdOpt.contains,
      onOpen = _ => Callback.empty,
      onClose = fileId =>
        router.set(
          page match {
            case page: DataRoomRecentPage => page.copy(fileIdOpt = None)
            case page: DataRoomTrashPage  => page.copy(fileIdOpt = None)
            case page: DataRoomFolderPage => page.copy(fileIdSuffix = None)
          }
        )
    )
  }

  private type Props = DataRoomFileViewer

  private def getViewUrl(props: Props, fileId: FileId, forceGetWatermark: Boolean, includeDeleted: Boolean) = {
    DataRoomEndpointClient
      .getViewUrl(
        GetViewUrlParams(
          dataRoomWorkflowId = props.dataRoomWorkflowId,
          fileId = fileId,
          password = None,
          forceGetWatermark = forceGetWatermark,
          includeDeleted = includeDeleted
        )
      )
      .map(
        _.map(res =>
          UrlLoader.UrlData(
            res.url,
            res.sessionOpt,
            res.extensionOpt
          )
        )
      )
  }

  private def getDownloadUrl(props: Props, fileId: FileId, forceGetWatermark: Boolean, includeDeleted: Boolean) = {
    Option {
      DataRoomEndpointClient
        .getDownloadUrl(
          GetDownloadUrlParams(
            props.dataRoomWorkflowId,
            BatchDownloadRequest(fileIds = Seq(fileId)),
            forceGetWatermark,
            includeDeleted
          )
        )
        .map(
          _.map(res =>
            UrlLoader.UrlData(
              res.downloadUrl,
              sessionOpt = None, // both data are unused by FileViewer.getDownloadUrl
              extensionOpt = None
            )
          )
        )
    }
  }

  private def getShortcutUrl(props: Props, fileId: FileId) = {
    Option {
      DataRoomEndpointClient
        .extractShortcut(ExtractShortcutParams(props.dataRoomWorkflowId, fileId, includeDeleted = props.includeDeleted))
        .map(_.map(res => UrlLoader.UrlData(res.url, None)))
    }
  }

  private def getNonPreviewableAndDownloadable(props: Props) = {
    FileViewer.NonPreviewableAndDownloadable(
      subtitle = React.Fragment(
        "This file format can't be previewed in your browser. Contact the person",
        <.br(),
        "who invited you or a data room admin to request ",
        <.span(tw.fontSemiBold, FileFolderPermissionUtils.getPermissionName(FileFolderPermission.Read)),
        " permission."
      ),
      action = Button(
        style = Button.Style.Full(color = Button.Color.Primary, icon = Some(Icon.Glyph.UserGroup)),
        tpe = Button.Tpe.Link(
          href = props.router
            .urlFor(
              DynamicAuthPage.DataRoomAllParticipantsPage(props.dataRoomWorkflowId, None)
            )
            .value
        )
      )("Go to Participants")
    )
  }

  private def onTracking(props: Props, fileId: FileId)(session: Instant): DmsViewTracker.OnTracking = { intervals =>
    ZIOUtils.when(intervals.events.exists(_.page.nonEmpty)) {
      DataRoomEndpointClient
        .recordPageView(
          RecordPageViewParams(
            props.dataRoomWorkflowId,
            fileId,
            versionIndexOpt = None,
            session,
            intervals
          )
        )
        .unit
    }
  }

  private def renderCloseToggle(props: Props, onCloseViewer: Callback): VdomNode = {
    FileViewer.defaultRenderCloseToggle(onCloseViewer >> props.onClose, Button.Color.Gray0)
  }

  private def renderFailedToLoadFile(props: Props)
    : PartialFunction[Throwable, FileViewer.RenderFailedToLoadFileParams => VdomNode] = {
    case _: GetViewUrlException.FailedToGenerateWatermarkException =>
      (renderParams: FileViewer.RenderFailedToLoadFileParams) =>
        <.div(
          tw.wPc100.hPc100.bgGray1,
          <.div(
            tw.flex.itemsCenter.wPc100.hPx48,
            tw.borderBottom.border1.borderGray3.px16,
            NameBar(
              fileInfo = props.file,
              getDownloadUrl = None,
              title = Some(props.file.name),
              isDarkBackground = false,
              renderCloseToggle = _ => renderCloseToggle(props, onCloseViewer = renderParams.onClose)
            )()
          ),
          <.div(
            tw.flex.flexCol.itemsCenter.justifyCenter.hPc100,
            ^.paddingBottom := "15%",
            <.img(
              ^.src := WebAppRenderer.assetsCtx.cdnMixedUrl("/web/gondor/images/dataroom/file-cannot-be-viewed.svg")
            ),
            <.div(
              tw.textGray8.fontBold.text17.leading28.pt20,
              "Unable to view the document"
            ),
            <.div(
              tw.pt4.pb16.textCenter,
              <.div("The system encountered a problem applying a watermark on this file."),
              <.div("Please contact support or your data room admin for assistance.")
            ),
            Button(
              style = Button.Style.Full(),
              tpe = Button.Tpe.Link(href = "mailto:<EMAIL>")
            )("Contact support")
          )
        )
  }

  private def render(props: Props) = {
    val fileId = props.file.fold(identity, _.itemId)
    FileViewer(
      file = props.file,
      renderTarget = props.renderTarget,
      getViewUrl = getViewUrl(
        props,
        fileId,
        props.forceGetWatermark,
        props.includeDeleted
      ),
      getDownloadUrl = getDownloadUrl(
        props,
        fileId,
        props.forceGetWatermark,
        props.includeDeleted
      ),
      getShortcutUrl = getShortcutUrl(props, fileId),
      initialContentPosition = props.initialContentPosition,
      modalType = props.modalType,
      nonPreviewableAndDownloadable = getNonPreviewableAndDownloadable(props),
      onTracking = onTracking(props, fileId),
      renderCloseToggle = (onCloseViewer, _) => renderCloseToggle(props, onCloseViewer),
      renderWithPermission = withPermissionFn => {
        val allowToPrint = !props.file.userPermission.contains(FileFolderPermission.ViewOnly)
        withPermissionFn(FileViewer.Permission(allowToPrint = allowToPrint))
      },
      renderFailedToLoadFile = renderFailedToLoadFile(props)
    )()
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .render_P(render)
    .build

}
