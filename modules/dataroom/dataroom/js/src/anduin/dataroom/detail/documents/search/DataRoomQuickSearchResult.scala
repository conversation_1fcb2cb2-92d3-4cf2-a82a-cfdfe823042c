// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dataroom.detail.documents.search

import design.anduin.components.button.Button
import design.anduin.components.icon.Icon
import design.anduin.components.tooltip.react.TooltipR
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
import design.anduin.style.tw.react.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.extra.router.RouterCtl
import japgolly.scalajs.react.vdom.html_<^.*

import anduin.dataroom.detail.documents.DataRoomFileViewer
import anduin.file.explorer.DmsIcon
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.model.id.{FileId, FolderId}
import anduin.stargazer.service.dataroom.{DataRoomFileSearchResult, DataRoomFolderSearchResult, DataRoomSearchResult}
import com.anduin.stargazer.endpoints.*
import stargazer.model.routing.{DynamicAuthPage, Page}

final case class DataRoomQuickSearchResult(
  router: RouterCtl[Page],
  dataRoomWorkflowId: DataRoomWorkflowId,
  result: DataRoomSearchResult,
  onClickTrackingCb: Callback = Callback.empty,
  onClose: Callback = Callback.empty
) {
  def apply(): VdomNode = DataRoomQuickSearchResult.component(this)
}

object DataRoomQuickSearchResult {
  private type Props = DataRoomQuickSearchResult

  private final case class State(
    isHovering: Boolean = false
  )

  private final class Backend(scope: BackendScope[Props, State]) {

    def render(props: Props, state: State): VdomNode = {
      props.result match {
        case folder: DataRoomFolderSearchResult =>
          renderSearchResult(
            props = props,
            state = state,
            result = folder,
            name = folder.name,
            onClick = props.onClickTrackingCb >> goToFolder(props)(folder.itemId)
          )
        case file: DataRoomFileSearchResult =>
          DataRoomFileViewer(
            router = props.router,
            dataRoomWorkflowId = props.dataRoomWorkflowId,
            file = FileInfo(
              itemId = file.itemId,
              name = file.name,
              lastModifiedTime = file.lastModifiedTime
            ),
            initialContentPosition = file.pageIndex,
            renderTarget = _ =>
              openViewer =>
                renderSearchResult(
                  props = props,
                  state = state,
                  result = file,
                  name = file.name,
                  onClick = props.onClickTrackingCb >> openViewer
                )
          )()
      }
    }

    private def goToFolder(props: Props)(folderId: FolderId) = {
      props.router.set(
        DynamicAuthPage.DataRoomFolderPage(
          props.dataRoomWorkflowId,
          folderId,
          None
        )
      ) >> props.onClose
    }

    private def renderSearchResult(
      props: Props,
      state: State,
      result: DataRoomSearchResult,
      name: String,
      onClick: Callback
    ): VdomNode = {
      <.div(
        tw.flex.itemsCenter.p12.wPc100,
        tw.rounded4.cursorPointer.hover(tw.bgGray4),
        ^.onClick --> onClick,
        ^.onMouseEnter --> scope.modState(_.copy(isHovering = true)),
        ^.onMouseLeave --> scope.modState(_.copy(isHovering = false)),
        <.div(
          tw.wPx24.hPx32.flex.itemsCenter,
          DmsIcon(
            result match {
              case _: DataRoomFolderSearchResult  => DmsIcon.Folder(childCount = 1)
              case file: DataRoomFileSearchResult => DmsIcon.File(file.name)
            },
            Icon.Size.Px24
          )()
        ),
        <.div(
          ComponentUtils.testId(DataRoomQuickSearchResult, "FileName"),
          tw.ml12.flexFill.textLeft,
          DataRoomSearchUtils.renderName(result)
        ),
        (result match {
          case _: DataRoomFolderSearchResult  => None
          case file: DataRoomFileSearchResult => Some(file.itemId)
        }).filter(_ => state.isHovering).map { fileId =>
          TooltipR(
            renderTarget = renderOpenParentFolder(props, fileId),
            renderContent = _("Open file location")
          )()
        }
      )
    }

    private def renderOpenParentFolder(props: Props, fileId: FileId) = {
      Button(
        testId = "DataRoomSearchResult_ParentFolder",
        style = Button.Style.Minimal(icon = Some(Icon.Glyph.FolderOpenLine)),
        unsafeTagMod = TagMod(
          ^.onClick ==> { e =>
            e.preventDefaultCB >> e.stopPropagationCB >> goToFolder(props)(fileId.parent)
          }
        )
      )()
    }

  }

  private val component = ScalaComponent
    .builder[Props](this.getClass.getSimpleName)
    .initialState(State())
    .renderBackend[Backend]
    .build

}
