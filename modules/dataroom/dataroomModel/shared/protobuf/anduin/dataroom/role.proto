syntax = "proto3";

package anduin.dataroom.role;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  single_file: true
  preamble: "sealed trait DataRoomNonAdminRole extends DataRoomRole {"
  preamble: "  def canInvite: Boolean"
  preamble: "  def withCanInvite(canInvite: <PERSON>olean): DataRoomNonAdminRole"
  preamble: "}"
};

message Admin {
}

message Member {
  option (scalapb.message).extends = "DataRoomNonAdminRole";
  bool can_invite = 1;
}

message Guest {
  option (scalapb.message).extends = "DataRoomNonAdminRole";
  bool can_invite = 1;
}

message Restricted {
  option (scalapb.message).extends = "DataRoomNonAdminRole";
  bool can_invite = 1;
}

message DataRoomRole {
  oneof sealed_value {
    Admin admin = 1;
    Member member = 2;
    Guest guest = 3;
    Restricted restricted = 4;
  }
}

message DataRoomRoleWrapper {
  DataRoomRole value = 1;
}
