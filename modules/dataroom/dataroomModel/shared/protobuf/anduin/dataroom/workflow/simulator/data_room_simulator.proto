syntax = "proto3";

package anduin.dataroom.workflow.simulator;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.dataroom.workflow.simulator"
  single_file: true
  import: "anduin.model.id.stage.DataRoomWorkflowId"
  import: "anduin.model.id.FileId"
  import: "anduin.model.common.user.UserId"
};

message PopulateDataRoomSimulatorParams {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string actor = 2 [(scalapb.field).type = "UserId"];
  optional string custom_fund_sub_url_opt = 3;
  Scenario scenario = 4;
}

enum Scenario {
  DEFAULT = 0;
  NUCLEUS_CAPITAL = 1;
  NUCLEUS_INNOVATION = 2;
}


message PopulateDataRoomSimulatorParticipantsResponse {
  repeated string member_ids = 1 [(scalapb.field).type = "UserId"];
}

message PopulateDataRoomSimulatorDocumentsParams {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string actor = 2 [(scalapb.field).type = "UserId"];
  repeated string member_ids = 3 [(scalapb.field).type = "UserId"];
}

message PopulateDataRoomSimulatorDocumentsResponse {
  repeated string homepage_file_ids = 1 [(scalapb.field).type = "FileId", (scalapb.field).collection_type = "Set"];
  repeated string documents_file_ids = 2 [(scalapb.field).type = "FileId", (scalapb.field).collection_type = "Set"];
}

message PopulateDataRoomSimulatorHomepageParams {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string actor = 2 [(scalapb.field).type = "UserId"];
  repeated string homepage_file_ids = 3 [(scalapb.field).type = "FileId", (scalapb.field).collection_type = "Set"];
  optional string custom_fund_sub_url_opt = 4;
  Scenario scenario = 5;
}

message PopulateDataRoomSimulatorInsightsParams {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string actor = 2 [(scalapb.field).type = "UserId"];
  repeated string file_ids = 3 [(scalapb.field).type = "FileId", (scalapb.field).collection_type = "Set"];
  repeated string member_ids = 4 [(scalapb.field).type = "UserId"];
}

message EmptyResponse {}