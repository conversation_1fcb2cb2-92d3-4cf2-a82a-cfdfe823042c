syntax = "proto3";

package anduin.dataroom.activity.file;

import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";
import "date_time.proto";
import "anduin/dms/id.proto";
import "flow/file/permission_map.proto";

option (scalapb.options) = {
  single_file: true
  import: "java.time.Instant"
  import: "java.util.UUID"
  import: "anduin.model.common.user.UserId"
  import: "anduin.model.id.FileId"
  import: "anduin.dms.FileFolderIdTypeMapper.given"
  import: "anduin.model.document.DocumentStorageId"
  import: "anduin.dms.DocumentStorageIdMapper.given"
  import: "anduin.id.UUIDTypeMapper.given"
};

enum DataRoomFileActivityType {
  option (scalapb.enum_options).extends = "anduin.scalapb.ProtocolGeneratedEnum";
  Create = 0;
  UploadNewVersion = 1;
  Rename = 2;
  Delete = 3;
  Restore = 4;
  View = 5;
  Download = 6;
  ModifyPermissions = 7;
  ChangeWatermark = 8;
  Copy = 9;
  Move = 10;
  PermanentDelete = 11;
}

message CreateFileActivityData {
  string name = 1;
  string storage_id = 2 [(scalapb.field).type = "DocumentStorageId"];
  flow.file.FileFolderPermissionMap permission_map = 3;
  map<string, string> metadata = 4;
}

message UploadNewVersionActivityData {
  int32 version_index = 1;
  string name = 2;
  optional string original_name_opt = 3;
  string storage_id = 4 [(scalapb.field).type = "DocumentStorageId"];
  map<string, string> metadata = 5;
}

message RenameFileActivityData {
  string new_name = 1;
  optional string old_name_opt = 2;
}

message RestoreFileActivityData {
  flow.file.FileFolderPermissionMap permission_map = 1;
}

enum ModifyPermissionReason {
  Unspecified = 0;
  ManualModify = 1;
  InviteUsers = 2;
  UpdateUserPermissions = 3;
  RemoveUsers = 4;
  UserDeclineInvitation = 5;
  UserJoinViaLink = 6;
  CreateGroup = 7;
  UpdateGroupPermissions = 8;
  DeleteGroup = 9;
  AddUsersToGroup = 10;
  RemoveUsersFromGroup = 11;
}

message ModifyPermissionsActivityData {
  flow.file.FileFolderPermissionMap updated_permissions = 1;
  flow.file.RevokedPermissionSet revoked_permissions = 2;
  map<string, string> metadata = 3;
  flow.file.FileFolderPermissionMap final_permissions = 4;
  ModifyPermissionReason reason = 5;
}

message ChangeWatermarkActivityData {
  bool is_watermark_exception = 1;
}

message MoveFileActivityData {
  anduin.dms.FileIdMessage original_file_id = 1 [(scalapb.field).type = "FileId"];
  string name = 2;
  flow.file.FileFolderPermissionMap permission_map = 3;
}

message CopyFileActivityData {
  anduin.dms.FileIdMessage original_file_id = 1 [(scalapb.field).type = "FileId"];
  string name = 2;
  flow.file.FileFolderPermissionMap permission_map = 3;
}

message DataRoomFileActivityData {
  oneof sealed_value_optional {
    CreateFileActivityData create_file_data = 1;
    UploadNewVersionActivityData upload_new_version_data = 2;
    RenameFileActivityData rename_file_data = 3;
    ModifyPermissionsActivityData modify_permissions_data = 4;
    RestoreFileActivityData restore_file_data = 5;
    ChangeWatermarkActivityData change_watermark_data = 6;
    MoveFileActivityData move_file_data = 7;
    CopyFileActivityData copy_file_data = 8;
  }
}

message DataRoomFileActivityModel {
  anduin.dms.FileIdMessage file_id = 1 [(scalapb.field).type = "FileId", (scalapb.field).no_box = true];
  string id = 2 [(scalapb.field).type = "UUID"];
  string actor = 3 [(scalapb.field).type = "UserId"];
  InstantMessage timestamp = 4 [(scalapb.field).type = "Instant", (scalapb.field).no_box = true];
  google.protobuf.StringValue actor_ip = 5;
  DataRoomFileActivityType activity_type = 6;
  DataRoomFileActivityData data_opt = 7;
  int32 version_index = 8;
}

message RecordTypeUnion {
  DataRoomFileActivityModel _DataRoomFileActivityModel = 1;
}

