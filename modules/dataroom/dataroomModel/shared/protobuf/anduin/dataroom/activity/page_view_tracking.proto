syntax = "proto3";

package anduin.dataroom.activity.pageview;

import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";
import "date_time.proto";
import "anduin/dms/id.proto";

option (scalapb.options) = {
  single_file: true
  import: "java.time.{Instant, Duration}"
  import: "anduin.model.id.FileId"
  import: "anduin.dms.FileFolderIdTypeMapper.given"
  import: "anduin.model.common.user.UserId"
};

message FilePageViewActivity {
  anduin.dms.FileIdMessage file_id = 1 [(scalapb.field).type = "FileId", (scalapb.field).no_box = true];
  int32 version_index = 2;
  string user_id = 3 [(scalapb.field).type = "UserId"];
  InstantMessage session = 4 [(scalapb.field).type = "Instant", (scalapb.field).no_box = true];
  map<int32, DurationMessage> page_view_duration = 5 [(scalapb.field).value_type = "Duration"];
}

message RecordTypeUnion {
  FilePageViewActivity _FilePageViewActivity = 1;
}