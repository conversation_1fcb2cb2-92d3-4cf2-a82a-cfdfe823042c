syntax = "proto3";

package anduin.dataroom.simulator;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.dataroom.simulator"
  single_file: true
  import: "anduin.model.common.user.UserId"
  import: "java.time.Instant"
  import: "anduin.model.id.stage.DataRoomWorkflowId"
};

enum DataRoomSimulatorProgress {
  Created = 0;
  Populated = 99;
  Failed = 100;
}

message DataRoomSimulatorSession {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  string creator = 2 [(scalapb.field).type = "UserId"];
  InstantMessage created_at = 3 [(scalapb.field).type = "Instant"];
  DataRoomSimulatorProgress progress = 4;
  int32 percentage_progress = 5;
}

message RecordTypeUnion {
  DataRoomSimulatorSession _DataRoomSimulatorSession = 1;
}
