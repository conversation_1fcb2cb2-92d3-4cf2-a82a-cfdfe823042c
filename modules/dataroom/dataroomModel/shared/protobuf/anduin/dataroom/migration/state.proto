syntax = "proto3";

package anduin.dataroom.migration;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  import: "java.time.Instant"
  import: "anduin.model.id.stage.DataRoomWorkflowId"
};

enum MigrationType {
  DmsActivity = 0;
  FileFlowEvent = 1;
  FileContentKeywordSearchIndex = 2;
}

enum MigrationStatus {
  NotMigrated = 0;
  Migrating = 1;
  Migrated = 2;
  Failed = 3;
}

message DataRoomMigrationState {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
  MigrationType migration_type = 2;
  MigrationStatus status = 3;
  InstantMessage last_updated = 4 [(scalapb.field).type = "Instant", (scalapb.field).no_box = true];
}

message RecordTypeUnion {
  DataRoomMigrationState _DataRoomMigrationState = 1;
}