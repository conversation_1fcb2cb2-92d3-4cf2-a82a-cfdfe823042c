syntax = "proto3";

package anduin.dataroom.email;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.dataroom.email"
  single_file: true
};

enum DataRoomEmailTemplateType {
  Invitation = 0;
  Reminder = 1;
  InvitationAccepted = 2;
  UserRemovalToUser = 3;
  HomepageMessage = 4;
  ManualNotification = 5;
  FileUploadDigest = 6;
  AccessRequestRejection = 7;
  RequestAccess = 8;
  DataRoomArchived = 9;
  UserRemovalToAdmin = 10;
  JoinViaLinkInvitation = 11;
  DataRoomCreated = 12;
}