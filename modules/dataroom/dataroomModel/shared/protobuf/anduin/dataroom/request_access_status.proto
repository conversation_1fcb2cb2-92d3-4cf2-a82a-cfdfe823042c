syntax = "proto3";

package anduin.dataroom.link;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  import: "java.time.Instant"
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.dataroom.DataRoomGroupId"
};

message RequestAccessStatus {
  oneof sealed_value {
    PendingRequest pending_request = 1;
    ApprovedRequest approved_request = 2;
    DeclinedRequest declined_request = 3;
  }
}

message PendingRequest {
  InstantMessage requested_at = 1 [(scalapb.field).type = "Instant"];
}

message ApprovedRequest {
  string approved_by = 1 [(scalapb.field).type = "UserId"];
  InstantMessage approved_at = 2 [(scalapb.field).type = "Instant"];
  InstantMessage requested_at = 3 [(scalapb.field).type = "Instant"];
}

message DeclinedRequest {
  string declined_by = 1 [(scalapb.field).type = "UserId"];
  InstantMessage declined_at = 2 [(scalapb.field).type = "Instant"];
  InstantMessage requested_at = 3 [(scalapb.field).type = "Instant"];

}

message DataRoomAccessRequest {
  RequestAccessStatus status = 1;
  DeclinedRequest last_declined = 2;
//  string group_id_opt = 3 [(scalapb.field).type = "Option[DataRoomGroupId]"];
  repeated string group_ids = 4 [(scalapb.field).collection_type = "Set", (scalapb.field).type = "DataRoomGroupId"];
}
