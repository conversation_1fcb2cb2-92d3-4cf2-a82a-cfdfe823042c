syntax = "proto3";

package anduin.dataroom.notification;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  single_file: true
};

enum NotificationMode {
  Notify = 0;
  DontNotify = 1;
  ParticipantsChoice = 2;
}

enum NotificationFrequency {
  Hourly = 0;
  Daily = 1;
  Weekly = 2;
}

message NewFileNotificationConfig {
  NotificationMode notification_mode = 1;
  NotificationFrequency notification_frequency = 2;
}
