syntax = "proto3";

package anduin.protobuf.dataroom.link;

import "scalapb/scalapb.proto";
import "anduin/dataroom/role.proto";
import "com/anduin/stargazer/endpoints/asset_permission_changes.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  import: "java.time.Instant"
  import: "anduin.model.id.stage.DataRoomWorkflowId"
  import: "anduin.id.dataroom.DataRoomGroupId"
};

message DataRoomLinkTypes {
  oneof sealed_value {
    DataRoomLinkInvitation link_invitation = 1;
  }
}

message DataRoomLinkInvitation {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomLinkInvitationParamsData {
  reserved 5;
  string name = 1;
  InstantMessage expiry_date = 2 [(scalapb.field).type = "Instant"];
  bool is_disabled = 3;
  repeated string whitelisted_domains = 4 [(scalapb.field).collection_type = "Set"];
  com.anduin.stargazer.endpoints.AssetPermissionChanges permission_changes = 6;
  anduin.dataroom.role.DataRoomRole role = 7;
  bool is_required_admin_approval = 8;
  repeated string group_ids = 9 [(scalapb.field).collection_type = "Set", (scalapb.field).type = "DataRoomGroupId"];
  bool is_toa_whitelisted = 10;
}
