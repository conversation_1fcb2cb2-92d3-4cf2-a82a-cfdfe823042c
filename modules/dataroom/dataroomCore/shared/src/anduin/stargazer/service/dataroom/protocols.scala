// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.stargazer.service.dataroom

import java.time.{Duration, Instant, ZoneId}
import io.circe.Codec
import anduin.circe.generic.semiauto.*
import anduin.dataroom.DataRoomData.InvitedUserData
import anduin.dataroom.DataRoomUserData.UserInfo
import anduin.dataroom.activity.file.DataRoomFileActivityType
import anduin.dataroom.activity.{DataRoomActivity, DataRoomGenericActivityInfo}
import anduin.dataroom.dashboard.DataRoomDashboardData
import anduin.dataroom.documents.DataRoomFileFolderPermissionDetail
import anduin.dataroom.email.*
import anduin.dataroom.globaldatabase.DataRoomParticipantState
import anduin.dataroom.homepage.DataRoomHomePageConstants.DataRoomHomePageFileType
import anduin.dataroom.homepage.DataRoomHomePageState
import anduin.dataroom.integration.CloudProviderType
import anduin.dataroom.migration.{MigrationStatus, MigrationType}
import anduin.dataroom.notification.{
  InvitationAcceptedEmailNotification,
  NewFileNotification,
  NewFileNotificationConfig,
  NotificationFrequency
}
import anduin.dataroom.role.DataRoomRole
import anduin.dataroom.simulator.DataRoomSimulatorProgress
import anduin.dataroom.tracking.{DataRoomActivityType, QueryUserRange}
import anduin.dataroom.{DataRoomData, DataRoomEntityModel, DataRoomOrgBillingModel, DataRoomUserData}
import anduin.dms.DmsUploadActionType
import anduin.dms.tracking.{
  DmsTrackingActivityType,
  QueryTimeRange,
  TopActiveFilesRankingCriteria,
  TopActiveRankingCriteria
}
import anduin.enumeration.{IntEnum, IntEnumCompanion, StringEnum, StringEnumCompanion}
import anduin.environment.EnvironmentEmailProviderProtocols.EmailProviderSource
import anduin.file.endpoints.FileUploadParams
import anduin.file.tracker.PageViewInterval
import anduin.id.dataroom.{DataRoomGroupId, DataRoomSearchId}
import anduin.id.entity.EntityId
import anduin.id.environment.EnvironmentId
import anduin.id.link.ProtectedLinkId
import anduin.model.codec.EitherCodec.given
import anduin.model.codec.MapCodecs.given
import anduin.model.codec.ProtoCodecs.given
import anduin.model.common.emailaddress.EmailAddress
import anduin.model.common.user.UserId
import anduin.model.document.DocumentStorageId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.model.id.{DataRoomHomeSectionId, FileId, FolderId}
import anduin.multiregion.MultiRegionCode
import anduin.orgbilling.model.plan.{BillingPlanStatus, DataRoomPlan}
import anduin.protobuf.dataroom.link.DataRoomLinkInvitationParamsData
import anduin.protobuf.flow.file.{FileFolderPermission, FileFolderPermissionMap}
import anduin.radix.RadixId
import anduin.rag.SearchIndexState
import anduin.rag.model.TextWithHighlights
import anduin.stargazer.service.dataroom.DataRoomFileFolderActivityInfo.DataRoomFileActivityInfo
import anduin.stargazer.service.dataroom.GetAllDataRoomMigrationStatusResponse.DataRoomMigrationInfo
import anduin.stargazer.service.dataroom.GetAllFileVersionsResponse.FileVersionInfo
import anduin.stargazer.service.dataroom.GetDataRoomActiveInOrgResponse.DataRoomActiveInOrg
import anduin.stargazer.service.dataroom.UpdateDataRoomWhiteLabelParams.WhiteLabelFileUpdate
import anduin.stargazer.service.dataroom.validation.*
import anduin.tapir.endpoint.EmptyEndpointValidationParams
import anduin.whitelabel.dashboard.EnvironmentDashboardWhitelabelProtocols.DashboardWhitelabelData
import com.anduin.stargazer.endpoints.AssetPermissionChanges
import com.anduin.stargazer.service.file.{BatchDownloadRequest, SingleFileVersionDownloadRequest}

/** Create a new data room.
  *
  * @param name
  *   New data room's name.
  * @param entityId
  *   The entity that this data room is billed to.
  * @param watermarkMetadata
  *   None if watermark setting is off. Otherwise contains the watermark settings.
  * @param tempToaFileIdOpt
  *   A temporary file holding Terms of Access, will be copied to new DR's ToA folder
  * @param showIndex
  *   Whether show index in settings is on/off.
  * @param showHomePage
  *   Whether show home page in settings is on/off.
  * @param showWhiteLabel
  *   Whether show whitelabel in settings is on/off.
  */
final case class CreateDataRoomParams(
  name: String,
  entityId: EntityId,
  watermarkMetadata: Option[WatermarkMetadataParams] = None,
  tempToaFileIdOpt: Option[FileId] = None,
  showIndex: Boolean = true,
  showSearch: Boolean = false,
  showHomePage: Boolean = false,
  showWhiteLabel: Boolean = false,
  newFileNotificationConfig: Option[NewFileNotificationConfig] = None
) extends DataRoomEntityMemberCheckParams

/** If this params is sent with API createDataRoomWithTerms, the uploaded file is the terms of access.
  */
object CreateDataRoomParams extends FileUploadParams.Companion[CreateDataRoomParams]("createDataRoomWithTerms") {
  given Codec.AsObject[CreateDataRoomParams] = deriveCodecWithDefaults
}

/** Create a duplicate data room from an existing data room. The files and settings are preserved, while none of the
  * existing participants are added.
  *
  * @param fromDataRoomWorkflowId
  *   The source data room.
  * @param newName
  *   Name of the new data room.
  */
final case class DuplicateDataRoomParams(
  fromDataRoomWorkflowId: DataRoomWorkflowId,
  newName: String
) extends DataRoomRoleCheckParams {
  override def dataRoomWorkflowId: DataRoomWorkflowId = fromDataRoomWorkflowId
}

object DuplicateDataRoomParams {
  given Codec.AsObject[DuplicateDataRoomParams] = deriveCodecWithDefaults
}

/** UNSAFE: Should only be used by admin portal. Create a new data room on behalf of another user.
  *
  * @param createDataRoomParams
  *   The normal create data room parameters.
  * @param onBehalfOf
  *   The actor email address passed to data room service.
  * @param planOpt
  *   Optional plan overriding org's plan.
  */
final case class CreateDataRoomOnBehalfParams(
  createDataRoomParams: CreateDataRoomParams,
  onBehalfOf: EmailAddress,
  planOpt: Option[DataRoomPlan] = None
) extends DataRoomPortalPermissionCheckParams

object CreateDataRoomOnBehalfParams {
  given Codec.AsObject[CreateDataRoomOnBehalfParams] = deriveCodecWithDefaults
}

/** UNSAFE: Should only be used by admin portal. Set another plan for a specific data room.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param packageOpt
  *   Plan overriding org plan.
  */
final case class ChangeSingleDataRoomPlanParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  packageOpt: Option[DataRoomPlan]
) extends DataRoomPortalPermissionCheckParams

object ChangeSingleDataRoomPlanParams {
  given Codec.AsObject[ChangeSingleDataRoomPlanParams] = deriveCodecWithDefaults
}

final case class SendDataRoomFileUploadDigestEmailParams(
  dataRoomWorkflowId: DataRoomWorkflowId
) extends DataRoomPortalPermissionCheckParams

object SendDataRoomFileUploadDigestEmailParams {
  given Codec.AsObject[SendDataRoomFileUploadDigestEmailParams] = deriveCodecWithDefaults
}

/** UNSAFE: Should only be used by admin portal. Get data room info.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  */
final case class GetDataRoomInfoParams(
  dataRoomWorkflowId: DataRoomWorkflowId
) extends DataRoomPortalPermissionCheckParams

object GetDataRoomInfoParams {
  given Codec.AsObject[GetDataRoomInfoParams] = deriveCodecWithDefaults
}

/** UNSAFE: Should only be used by admin portal. Response of data room info to be displayed in admin portal.
  *
  * @param name
  *   Name of the data room.
  * @param creatorEntity
  *   EntityId of the entity owning the data room.
  * @param creatorEntityName
  *   Name of the entity owning the data room.
  * @param creatorEntityPlan
  *   Data room plan of owning entity
  * @param planOpt
  *   Optional overriding plan.
  */
final case class GetDataRoomInfoResponse(
  name: String,
  creatorEntity: EntityId,
  creatorEntityName: String,
  creatorEntityPlan: DataRoomPlan,
  planOpt: Option[DataRoomPlan],
  enableWebhook: Boolean,
  isArchived: Boolean
) {
  def isExpired: Boolean = !BillingPlanStatus.checkStatus(planOpt.getOrElse(creatorEntityPlan))
}

object GetDataRoomInfoResponse {
  given Codec.AsObject[GetDataRoomInfoResponse] = deriveCodecWithDefaults
}

final case class AutoAcceptInvitationAndTermsOfAccessParams(dataRoomWorkflowId: DataRoomWorkflowId)

object AutoAcceptInvitationAndTermsOfAccessParams {
  given Codec.AsObject[AutoAcceptInvitationAndTermsOfAccessParams] = deriveCodecWithDefaults
}

final case class CleanWatermarkFilesParams(dataRoomWorkflowId: DataRoomWorkflowId)
    extends DataRoomPortalPermissionCheckParams

object CleanWatermarkFilesParams {
  given Codec.AsObject[CleanWatermarkFilesParams] = deriveCodecWithDefaults
}

final case class FixUserPermissionsParams(
  dataRoomWorkflowId: DataRoomWorkflowId
) extends DataRoomPortalPermissionCheckParams derives CirceCodec.WithDefaults

final case class FixUserPermissionsResponse(
  corruptedParticipants: Seq[FixUserPermissionResponse.CorruptedParticipant]
) derives CirceCodec.WithDefaults

object FixUserPermissionResponse {

  final case class CorruptedParticipant(
    userId: UserId,
    groupId: DataRoomGroupId,
    isGroupMember: Boolean,
    isTeamMember: Boolean
  ) derives CirceCodec.WithDefaults

}

final case class ChangeDataRoomSearchSettingParams(dataRoomWorkflowId: DataRoomWorkflowId, showSearch: Boolean)
    extends DataRoomPortalPermissionCheckParams

object ChangeDataRoomSearchSettingParams {
  given Codec.AsObject[ChangeDataRoomSearchSettingParams] = deriveCodecWithDefaults
}

/** Archive or unarchive a data room. Optionally notify admins.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param isArchived
  *   True if archive. False if unarchive.
  * @param doNotNotifyByEmail
  *   True if no email will be sent out.
  */
final case class SetIsArchivedDataRoomParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  isArchived: Boolean,
  doNotNotifyByEmail: Boolean
) extends DataRoomRoleCheckParams {
  override def checkArchivedStatus = Some(!isArchived)
}

object SetIsArchivedDataRoomParams {
  given Codec.AsObject[SetIsArchivedDataRoomParams] = deriveCodecWithDefaults
}

/** Remove some users before archiving the data room.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param removedUsers
  *   Set of removed users.
  * @param doNotNotifyByEmail
  *   True if no email will be sent out to both admins and removed users.
  */
final case class ArchiveDataRoomAndRemoveUsersParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  removedUsers: Set[UserId],
  doNotNotifyByEmail: Boolean
) extends DataRoomRoleCheckParams
    with DataRoomInvitedOrJoinedMembersCheckParams {
  override def userIds = removedUsers
}

object ArchiveDataRoomAndRemoveUsersParams {
  given Codec.AsObject[ArchiveDataRoomAndRemoveUsersParams] = deriveCodecWithDefaults
}

final case class DataRoomSetViewedGroupOnboardingParams(dataRoomWorkflowId: DataRoomWorkflowId)
    extends DataRoomJoinedUserCheckParams

object DataRoomSetViewedGroupOnboardingParams {
  given Codec.AsObject[DataRoomSetViewedGroupOnboardingParams] = deriveCodecWithDefaults
}

final case class DataRoomSetViewedSearchOnboardingParams(dataRoomWorkflowId: DataRoomWorkflowId)
    extends DataRoomJoinedUserCheckParams

object DataRoomSetViewedSearchOnboardingParams {
  given Codec.AsObject[DataRoomSetViewedSearchOnboardingParams] = deriveCodecWithDefaults
}

/** Mark all data room recent files as seen
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  */
final case class MarkDataRoomRecentFilesAsSeenParams(dataRoomWorkflowId: DataRoomWorkflowId)
    extends DataRoomJoinedUserCheckParams

object MarkDataRoomRecentFilesAsSeenParams {
  given Codec.AsObject[MarkDataRoomRecentFilesAsSeenParams] = deriveCodecWithDefaults
}

/** Update data room email configs
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room
  * @param senderNameEnabled
  *   whether the custom email sender name is enabled
  * @param senderName
  *   the custome email sender name
  * @param customEmailReplyEnabled
  *   whether the custom email reply is enabled
  * @param replyToEmails
  *   the list of emails to CC
  */
final case class UpdateSingleDataRoomEmailConfigsParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  inheritFromEntity: Boolean,
  updateData: UpdateDataRoomEmailConfigsData
) extends DataRoomPortalPermissionCheckParams

object UpdateSingleDataRoomEmailConfigsParams {
  given Codec.AsObject[UpdateSingleDataRoomEmailConfigsParams] = deriveCodecWithDefaults
}

final case class UpdateEntityDataRoomEmailConfigsParams(
  entityId: EntityId,
  updateData: UpdateDataRoomEmailConfigsData
) extends DataRoomPortalPermissionCheckParams

object UpdateEntityDataRoomEmailConfigsParams {
  given Codec.AsObject[UpdateEntityDataRoomEmailConfigsParams] = deriveCodecWithDefaults
}

final case class UpdateDataRoomEmailConfigsData(
  senderNameEnabled: Boolean,
  senderName: String,
  senderAddressNamePartEnabled: Boolean,
  senderAddressNamePart: String,
  customEmailReplyEnabled: Boolean,
  replyToEmails: Seq[EmailAddress],
  replyToEmailsTemplateTypes: Set[DataRoomEmailTemplateType],
  replyToSenderEnabled: Boolean,
  replyToSenderTemplateTypes: Set[DataRoomEmailTemplateType],
  customCcEmailsEnabled: Boolean,
  customCcEmails: Seq[EmailAddress],
  customCcTemplateTypes: Set[DataRoomEmailTemplateType],
  customBccEmailsEnabled: Boolean,
  customBccEmails: Seq[EmailAddress],
  customBccTemplateTypes: Set[DataRoomEmailTemplateType],
  emailProviderSource: EmailProviderSource,
  allowDisablingInvitationEmail: Boolean
)

object UpdateDataRoomEmailConfigsData {

  given Codec.AsObject[UpdateDataRoomEmailConfigsData] = deriveCodecWithDefaults

  def fromPortalDataRoomEmailConfigs(emailConfigs: PortalDataRoomEmailConfigs): UpdateDataRoomEmailConfigsData =
    UpdateDataRoomEmailConfigsData(
      senderNameEnabled = emailConfigs.senderName.isEnabled,
      senderName = emailConfigs.senderName.value,
      senderAddressNamePartEnabled = emailConfigs.senderAddressName.isEnabled,
      senderAddressNamePart = emailConfigs.senderAddressName.value,
      customEmailReplyEnabled = emailConfigs.replyToEmails.isEnabled,
      replyToEmails = emailConfigs.replyToEmails.emails.flatMap(EmailAddress.unapply),
      replyToEmailsTemplateTypes = emailConfigs.replyToEmails.templateTypes,
      replyToSenderEnabled = emailConfigs.replyToSender.isEnabled,
      replyToSenderTemplateTypes = emailConfigs.replyToSender.templateTypes,
      customCcEmailsEnabled = emailConfigs.ccEmails.isEnabled,
      customCcEmails = emailConfigs.ccEmails.emails.flatMap(EmailAddress.unapply),
      customCcTemplateTypes = emailConfigs.ccEmails.templateTypes,
      customBccEmailsEnabled = emailConfigs.bccEmails.isEnabled,
      customBccEmails = emailConfigs.bccEmails.emails.flatMap(EmailAddress.unapply),
      customBccTemplateTypes = emailConfigs.bccEmails.templateTypes,
      emailProviderSource = emailConfigs.emailProviderSource,
      allowDisablingInvitationEmail = emailConfigs.invitationEmail.allowDisabling
    )

}

final case class GetEntityDataRoomEmailConfigsParams(
  entityId: EntityId
) extends DataRoomPortalPermissionCheckParams

object GetEntityDataRoomEmailConfigsParams {
  given Codec.AsObject[GetEntityDataRoomEmailConfigsParams] = deriveCodecWithDefaults
}

final case class GetEntityDataRoomEmailConfigsResponse(
  entityDataRoomEmailConfigsOpt: Option[PortalDataRoomEmailConfigs]
)

object GetEntityDataRoomEmailConfigsResponse {
  given Codec.AsObject[GetEntityDataRoomEmailConfigsResponse] = deriveCodecWithDefaults
}

final case class GetEntityDataRoomsParams(
  entityId: EntityId
) extends DataRoomPortalPermissionCheckParams

object GetEntityDataRoomsParams {
  given Codec.AsObject[GetEntityDataRoomsParams] = deriveCodecWithDefaults
}

final case class GetEntityDataRoomsResponse(
  dataRoomData: Seq[DataRoomInfo]
)

object GetEntityDataRoomsResponse {
  given Codec.AsObject[GetEntityDataRoomsResponse] = deriveCodecWithDefaults
}

final case class DataRoomInfo(
  dataRoomWorkflowId: DataRoomWorkflowId,
  name: String,
  createdAt: Option[Instant],
  dataRoomEmailConfigs: Option[PortalDataRoomEmailConfigs],
  entityDataRoomEmailConfigs: Option[PortalDataRoomEmailConfigs],
  showWhiteLabel: Boolean,
  showSearch: Boolean,
  integrationConfig: PortalDataRoomIntegrationConfig,
  activeAdmins: Set[UserId],
  pendingAdmins: Set[UserId],
  isArchived: Boolean,
  isAccessedByEntityMembers: Boolean,
  enableWebhook: Boolean,
  environmentData: Option[EmailProviderSource.Environment]
)

object DataRoomInfo {
  given Codec.AsObject[DataRoomInfo] = deriveCodecWithDefaults
}

final case class PortalDataRoomIntegrationConfig(
  internalIntegratedIds: Set[RadixId],
  cloudIntegratedTypes: Set[CloudProviderType]
)

object PortalDataRoomIntegrationConfig {
  given Codec.AsObject[PortalDataRoomIntegrationConfig] = deriveCodecWithDefaults
}

final case class DataRoomInsights(
  dataRoomInfo: DataRoomInsights.Info,
  roleInsights: DataRoomInsights.Role,
  visitorInsights: DataRoomInsights.Visitor,
  linkInsights: DataRoomInsights.InvitationLink,
  groupInsights: DataRoomInsights.Group,
  settings: DataRoomInsights.Settings,
  users: Seq[DataRoomInsights.User]
)

object DataRoomInsights {

  given Codec.AsObject[DataRoomInsights] = deriveCodecWithDefaults

  final case class Info(
    dataRoomWorkflowId: DataRoomWorkflowId,
    dataRoomName: String,
    entityId: EntityId,
    entityName: String,
    plan: String,
    extraSeat: Int,
    totalSeat: Int,
    expirationDate: Long // epoch milli
  )

  object Info {
    given Codec.AsObject[Info] = deriveCodecWithDefaults
  }

  final case class Role(
    adminCount: Int,
    memberCount: Int,
    contributorCount: Int,
    observerCount: Int
  )

  object Role {
    given Codec.AsObject[Role] = deriveCodecWithDefaults
  }

  final case class Visitor(
    internalVisitCount: Long,
    externalVisitCount: Long
  )

  object Visitor {
    given Codec.AsObject[Visitor] = deriveCodecWithDefaults
  }

  final case class InvitationLink(
    linkCount: Int
  )

  object InvitationLink {
    given Codec.AsObject[InvitationLink] = deriveCodecWithDefaults
  }

  final case class Group(
    groupCount: Int,
    groupNames: Seq[String]
  )

  object Group {
    given Codec.AsObject[Group] = deriveCodecWithDefaults
  }

  final case class Settings(
    whiteLabelEnabled: Boolean,
    documentSearchEnabled: Boolean,
    homePageEnabled: Boolean,
    termsOfAccessEnabled: Boolean,
    watermarkEnabled: Boolean,
    integrationEnabled: Boolean,
    notificationMode: String,
    notificationFrequency: String
  )

  object Settings {
    given Codec.AsObject[Settings] = deriveCodecWithDefaults
  }

  final case class User(
    userId: UserId,
    dataRoomWorkflowId: DataRoomWorkflowId,
    role: String,
    groupName: String
  )

  object User {
    given Codec.AsObject[User] = deriveCodecWithDefaults
  }

}

final case class DataRoomEntityWithChangeLogs(
  entityId: EntityId,
  entityName: String,
  dataRoomCount: Int,
  changeLogs: List[DataRoomEntityWithChangeLogs.ChangeLog]
)

object DataRoomEntityWithChangeLogs {

  given Codec.AsObject[DataRoomEntityWithChangeLogs] = deriveCodecWithDefaults

  final case class ChangeLog(
    at: Long,
    plan: String
  )

  object ChangeLog {
    given Codec.AsObject[ChangeLog] = deriveCodecWithDefaults
  }

}

final case class GetInternalDataRoomUserInsightsParams(
  queryTimeRange: QueryTimeRange
)

object GetInternalDataRoomUserInsightsParams {
  given Codec.AsObject[GetInternalDataRoomUserInsightsParams] = deriveCodecWithDefaults
}

final case class GetInternalDataRoomUserInsightsResponse(
  dataRoomUserInsights: Seq[DataRoomUserInsights]
)

object GetInternalDataRoomUserInsightsResponse {
  given Codec.AsObject[GetInternalDataRoomUserInsightsResponse] = deriveCodecWithDefaults
}

final case class DataRoomUserInsights(
  userId: UserId,
  userName: String,
  visitCount: Long,
  nViews: Long,
  nDownloads: Long,
  accessedFiles: Long,
  viewDuration: Duration
)

object DataRoomUserInsights {
  given Codec.AsObject[DataRoomUserInsights] = deriveCodecWithDefaults
}

final case class GetInternalDataRoomUserAnalyticsParams(
  queryTimeRange: QueryTimeRange
)

object GetInternalDataRoomUserAnalyticsParams {
  given Codec.AsObject[GetInternalDataRoomUserAnalyticsParams] = deriveCodecWithDefaults
}

final case class GetInternalDataRoomUserAnalyticsResponse(
  userAnalytics: Seq[InternalDataRoomUserAnalytics]
)

object GetInternalDataRoomUserAnalyticsResponse {
  given Codec.AsObject[GetInternalDataRoomUserAnalyticsResponse] = deriveCodecWithDefaults
}

final case class InternalDataRoomUserAnalytics(
  userName: String,
  userId: UserId,
  dataRoomVisitCount: Int,
  accessedFileCount: Int,
  viewFileCount: Int,
  downloadFileCount: Int,
  timeSpent: Option[Duration]
)

object InternalDataRoomUserAnalytics {
  given Codec.AsObject[InternalDataRoomUserAnalytics] = deriveCodecWithDefaults
}

final case class BatchRemoveUserFromDataRoomParams(
  removedUserMap: Map[DataRoomWorkflowId, Set[UserId]]
) extends DataRoomPortalPermissionCheckParams

object BatchRemoveUserFromDataRoomParams {
  given Codec.AsObject[BatchRemoveUserFromDataRoomParams] = deriveCodecWithDefaults
}

/** Rename a data room
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param name
  *   New data room name.
  */
final case class RenameDataRoomParams(dataRoomWorkflowId: DataRoomWorkflowId, name: String)

object RenameDataRoomParams {
  given Codec.AsObject[RenameDataRoomParams] = deriveCodecWithDefaults
}

/** Role and permission settings used in invite and modify permissions.
  *
  * @param roleSet
  *   Optional new role. Must be non-empty in invitation case.
  * @param assetPermissions
  *   Permission setting changes.
  */
final case class DataRoomPermissionChanges(
  roleSet: Option[DataRoomRole],
  assetPermissions: AssetPermissionChanges,
  isToaWhitelisted: Boolean = false
)

object DataRoomPermissionChanges {
  given Codec.AsObject[DataRoomPermissionChanges] = deriveCodecWithDefaults
}

final case class DataRoomGroupPermissionChanges(
  groupIds: Set[DataRoomGroupId],
  canInvite: Boolean,
  isToaWhitelisted: Boolean = false
)

object DataRoomGroupPermissionChanges {
  given Codec.AsObject[DataRoomGroupPermissionChanges] = deriveCodecWithDefaults
}

final case class DataRoomParticipantRoleChange(
  userId: UserId,
  oldRoleOpt: Option[DataRoomRole],
  newRoleOpt: Option[DataRoomRole]
)

/** Invite new users to the data room.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param individualPermissionMap
  *   Map from email address to the role and permission settings.
  * @param isToaRequired
  *   True if invited users are required to accept terms of access before joining.
  * @param subject
  *   Subject of the invitation email.
  * @param message
  *   Body of the invitation email.
  * @param buttonLabel
  *   CTA label in the invitation email.
  * @param isApproved
  *   True if invitations sent because admins approve access requests
  */
final case class InviteUsersToDataRoomParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  individualPermissionMap: Map[String, DataRoomPermissionChanges] = Map(),
  groupPermissionMap: Map[String, DataRoomGroupPermissionChanges] = Map(),
  isToaRequired: Boolean,
  subject: String,
  message: String,
  buttonLabel: String,
  isApproved: Boolean = false,
  shouldSendEmail: Boolean = true
) extends DataRoomJoinedUserCheckParams
    with DataRoomGroupCheckParams {
  override def groupIds = groupPermissionMap.values.flatMap(_.groupIds).toSet
}

object InviteUsersToDataRoomParams {
  given Codec.AsObject[InviteUsersToDataRoomParams] = deriveCodecWithDefaults
}

/** Modify role and permission settings.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param updatedUserMap
  *   Map from user to updated role and permission settings.
  */
final case class ModifyDataRoomPermissionsParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  updatedUserMap: Map[UserId, DataRoomPermissionChanges],
  isToaRequiredMap: Map[UserId, Boolean] = Map()
) extends DataRoomJoinedUserCheckParams
    with DataRoomInvitedOrJoinedMembersCheckParams {
  override def userIds = updatedUserMap.keySet ++ isToaRequiredMap.keySet
}

object ModifyDataRoomPermissionsParams {
  given Codec.AsObject[ModifyDataRoomPermissionsParams] = deriveCodecWithDefaults
}

/** Add a new event of data room visit.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  */
final case class TrackUserVisitDataRoomParams(
  dataRoomWorkflowId: DataRoomWorkflowId
) extends DataRoomJoinedUserCheckParams

object TrackUserVisitDataRoomParams {
  given Codec.AsObject[TrackUserVisitDataRoomParams] = deriveCodecWithDefaults
}

/** Reorder content of a folder in data room.
  *
  * @param dataRoomId
  *   Id of the target data room.
  * @param folderId
  *   Parent folder id. The channel must be [[dataRoomId]].
  * @param order
  *   The new order, mapping from id of folder or file to the new index.
  * @param showIndex
  *   None if this setting is unchanged. Otherwise, the new value of show index settings.
  */
final case class ChangeFolderOrderParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  folderId: FolderId,
  order: Seq[(RadixId, Long)],
  showIndex: Option[Boolean]
) extends DataRoomRoleCheckParams
    with DataRoomAssetCheckParams {
  override def fileIds = Set.empty

  override def folderIds = Set(folderId)
}

object ChangeFolderOrderParams {
  given Codec.AsObject[ChangeFolderOrderParams] = deriveCodecWithDefaults
}

/** Accept invitation to a data room, optionally with terms of access.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param toaFileIdOpt
  *   The current version of terms of access, if any.
  */
final case class AcceptInvitationToDataRoomParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  toaFileIdOpt: Option[FileId]
) extends DataRoomInvitedUserCheckParams

object AcceptInvitationToDataRoomParams {
  given Codec.AsObject[AcceptInvitationToDataRoomParams] = deriveCodecWithDefaults
}

/** Decline invitation to a data room.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  */
final case class DeclineInvitationToDataRoomParams(dataRoomWorkflowId: DataRoomWorkflowId)
    extends DataRoomInvitedUserCheckParams

object DeclineInvitationToDataRoomParams {
  given Codec.AsObject[DeclineInvitationToDataRoomParams] = deriveCodecWithDefaults
}

/** Send an invitation reminder.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param userIds
  *   Users to be reminded.
  */
final case class RemindInvitationToDataRoomParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  userIds: Set[UserId],
  subject: String,
  message: String,
  buttonLabel: String
) extends DataRoomJoinedUserCheckParams
    with DataRoomInvitedOrJoinedMembersCheckParams

object RemindInvitationToDataRoomParams {
  given Codec.AsObject[RemindInvitationToDataRoomParams] = deriveCodecWithDefaults
}

/** Remove users from a data room, possibly including both invited and joined users.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param userIds
  *   Set of removed users
  * @param doNotNotifyByEmail
  *   Whether an email notification should be sent.
  */
final case class RemoveUsersFromDataRoomParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  userIds: Set[UserId],
  doNotNotifyByEmail: Boolean
) extends DataRoomJoinedUserCheckParams
    with DataRoomInvitedOrJoinedMembersCheckParams

object RemoveUsersFromDataRoomParams {
  given Codec.AsObject[RemoveUsersFromDataRoomParams] = deriveCodecWithDefaults
}

/** Accept new terms of access.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param toaFileId
  *   Id of terms of access.
  */
final case class AcceptTermsOfAccessToDataRoomParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  toaFileId: FileId,
  linkIdOpt: Option[ProtectedLinkId]
) extends DataRoomTermsOfAccessCheckParams

object AcceptTermsOfAccessToDataRoomParams {
  given Codec.AsObject[AcceptTermsOfAccessToDataRoomParams] = deriveCodecWithDefaults
}

final case class GetDataRoomHomePageParams(
  dataRoomWorkflowId: DataRoomWorkflowId
) extends DataRoomHomePageCheckParams derives CanEqual

object GetDataRoomHomePageParams {
  given Codec.AsObject[GetDataRoomHomePageParams] = deriveCodecWithDefaults
}

final case class DataRoomHomePageFileInfo(name: String, userPermission: Option[FileFolderPermission])

object DataRoomHomePageFileInfo {
  given Codec.AsObject[DataRoomHomePageFileInfo] = deriveCodecWithDefaults
}

final case class DataRoomHomePageAdditional(fileMap: Map[FileId, DataRoomHomePageFileInfo])

object DataRoomHomePageAdditional {
  given Codec.AsObject[DataRoomHomePageAdditional] = deriveCodecWithDefaults
}

final case class GetDataRoomHomePageResponse(
  state: DataRoomHomePageState,
  additional: DataRoomHomePageAdditional
) derives CanEqual

object GetDataRoomHomePageResponse {
  given Codec.AsObject[GetDataRoomHomePageResponse] = deriveCodecWithDefaults
}

/** Publish or revert to draft data room home page.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param setToPublished
  *   True if publish the home page. False if revert the published home page to draft mode.
  */
final case class ToggleDataRoomHomePagePublishStatusParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  setToPublished: Boolean
) extends DataRoomRoleCheckParams

object ToggleDataRoomHomePagePublishStatusParams {
  given Codec.AsObject[ToggleDataRoomHomePagePublishStatusParams] = deriveCodecWithDefaults
}

/** Update home page content.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param lastUpdate
  *   Last update timestamp of the home page that the new content is based on.
  * @param newState
  *   The new content.
  * @param forceUpdate
  *   If false and there is conflict based on [[lastUpdate]], do not update but return the conflict detail. If false and
  *   there is no conflict, update the content. If true, update the content anyway regardless conflict.
  */
final case class UpdateDataRoomHomePageParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  lastUpdate: Option[Instant],
  newState: DataRoomHomePageState,
  fileMap: Map[DataRoomHomePageFileType, FileId],
  fileThumbnail: Map[FileId, FileId],
  forceUpdate: Boolean = false
) extends DataRoomRoleCheckParams

/** API can be sent with optional media files, as background images and videos.
  */
object UpdateDataRoomHomePageParams {
  given Codec.AsObject[UpdateDataRoomHomePageParams] = deriveCodecWithDefaults
}

final case class UploadDataRoomHomePageFilesParams(
  dataRoomWorkflowId: DataRoomWorkflowId
) extends FileUploadParams.WithEncoder[UploadDataRoomHomePageFilesParams]

object UploadDataRoomHomePageFilesParams
    extends FileUploadParams.Companion[UploadDataRoomHomePageFilesParams]("uploadDataRoomHomePageFiles") {
  given Codec.AsObject[UploadDataRoomHomePageFilesParams] = deriveCodecWithDefaults
}

final case class UploadDataRoomHomePageFilesResponse(
  fileMap: Map[DataRoomHomePageFileType, FileId],
  thumbnailMap: Map[FileId, FileId]
)

object UploadDataRoomHomePageFilesResponse {
  given Codec.AsObject[UploadDataRoomHomePageFilesResponse] = deriveCodecWithDefaults
}

/** Conflict detail.
  *
  * @param conflictAt
  *   The current latest version timestamp.
  * @param modifier
  *   The author of the latest version.
  */
final case class UpdateHomePageConflictDetail(
  conflictAt: Instant,
  modifier: UserId
)

object UpdateHomePageConflictDetail {
  given Codec.AsObject[UpdateHomePageConflictDetail] = deriveCodecWithDefaults
}

/** Response of the update home page API.
  *
  * @param lastConflictUpdated
  *   None if update successfully. Otherwise the update conflict.
  */
final case class UpdateDataRoomHomePageResponse(
  lastConflictUpdated: Option[UpdateHomePageConflictDetail]
)

object UpdateDataRoomHomePageResponse {
  given Codec.AsObject[UpdateDataRoomHomePageResponse] = deriveCodecWithDefaults
}

final case class SendDataRoomHomePageMessageParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  sectionId: DataRoomHomeSectionId,
  subject: String,
  message: String,
  sendCopy: Boolean
) extends DataRoomJoinedUserCheckParams

object SendDataRoomHomePageMessageParams {
  given Codec.AsObject[SendDataRoomHomePageMessageParams] = deriveCodecWithDefaults
}

/** Modify data room settings.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param modifyTermsOfAccessOpt
  *   Optional terms of access changes.
  * @param nameOpt
  *   Optional new data room name.
  * @param isWatermarkMetadataChanged
  *   True if the watermark setting is changed. Otherwise only on/off change.
  * @param watermarkMetadata
  *   Optional new value of show whitelabel settings.
  * @param showIndex
  *   Optional new value of show index settings. None if unchanged.
  * @param showHomePage
  *   Optional new value of show home page settings. None if unchanged.
  * @param showWhiteLabel
  *   Optional new value of show whitelabel settings. None if unchanged.
  * @param newFileNotificationConfig
  *   Optional new value of new file notification. None if unchanged
  */
final case class ModifyDataRoomGeneralSettingsParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  modifyTermsOfAccessOpt: Option[ModifyDataRoomTermsOfAccessTypes] = None,
  nameOpt: Option[String] = None,
  isWatermarkMetadataChanged: Boolean = false,
  watermarkMetadata: Option[WatermarkMetadataParams] = None,
  showIndex: Option[Boolean] = None,
  showSearch: Option[Boolean] = None,
  showHomePage: Option[Boolean] = None,
  showWhiteLabel: Option[Boolean] = None,
  newFileNotificationConfig: Option[NewFileNotificationConfig] = None
) extends FileUploadParams.WithEncoder[ModifyDataRoomGeneralSettingsParams]
    with DataRoomRoleCheckParams

/** API can be sent with new terms of access, if mode is [[ModifyDataRoomTermsOfAccessTypes.Upload]]
  */
object ModifyDataRoomGeneralSettingsParams
    extends FileUploadParams.Companion[ModifyDataRoomGeneralSettingsParams]("modifyDataRoomGeneralSettings") {
  given Codec.AsObject[ModifyDataRoomGeneralSettingsParams] = deriveCodecWithDefaults
}

/** Different modes of updating terms of access settings.
  */
sealed trait ModifyDataRoomTermsOfAccessTypes derives CanEqual

object ModifyDataRoomTermsOfAccessTypes {

  given Codec.AsObject[ModifyDataRoomTermsOfAccessTypes] = deriveCodecWithDefaults

  /** Turn off terms of access.
    */
  final case class Disable() extends ModifyDataRoomTermsOfAccessTypes

  object Disable {
    given Codec.AsObject[Disable] = deriveCodecWithDefaults
  }

  /** Reuse old terms of access.
    *
    * @param toaFileId
    *   Id of the old terms of access.
    */
  final case class Enable(toaFileId: FileId) extends ModifyDataRoomTermsOfAccessTypes

  object Enable {
    given Codec.AsObject[Enable] = deriveCodecWithDefaults
  }

  /** Upload new terms of access.
    */
  final case class Upload(tempToaFileId: FileId) extends ModifyDataRoomTermsOfAccessTypes

  object Upload {
    given Codec.AsObject[Upload] = deriveCodecWithDefaults
  }

}

/** Watermark color.
  *
  * @param value
  *   Hex value of the color.
  * @param name
  *   Human-readable color name.
  */
enum WatermarkColor(val value: Int, val name: String) extends IntEnum {

  case Gray extends WatermarkColor(0x4f6f89, "Gray")

  case Blue extends WatermarkColor(0x48aff0, "Blue")

  case Red extends WatermarkColor(0xff7373, "Red")
}

object WatermarkColor extends IntEnumCompanion[WatermarkColor] {

  given Codec[WatermarkColor] = deriveIntEnumCodec

  def fromInt(color: Int): Option[WatermarkColor] = {
    values.find(_.value == color)
  }

}

/** Layout setting of watermark.
  *
  * @param value
  *   Density of watermark.
  * @param name
  *   Human-readable name of the density.
  */
enum WatermarkLayout(val value: Int, val name: String) extends IntEnum {

  case Subtle extends WatermarkLayout(0, "Subtle")

  case Normal extends WatermarkLayout(1, "Normal")

  case Heavy extends WatermarkLayout(2, "Heavy")
}

object WatermarkLayout extends IntEnumCompanion[WatermarkLayout] {

  given Codec[WatermarkLayout] = deriveIntEnumCodec

  def fromInt(value: Int): Option[WatermarkLayout] = {
    values.find(_.value == value)
  }

}

/** Watermark transparency settings.
  *
  * @param value
  *   Transparency level of watermark.
  * @param name
  *   Human-readable name of the transparency.
  */
enum WatermarkTransparency(val value: Int, val name: String) extends IntEnum {

  def toAlpha: Int = {
    100 - value
  }

  case NoTransparency extends WatermarkTransparency(0, "No transparency")

  case TwentyFive extends WatermarkTransparency(25, "25%")

  case Fifty extends WatermarkTransparency(50, "50%")

  case SeventyFive extends WatermarkTransparency(75, "75%")

}

object WatermarkTransparency extends IntEnumCompanion[WatermarkTransparency] {

  given Codec[WatermarkTransparency] = deriveIntEnumCodec

  def fromInt(value: Int): Option[WatermarkTransparency] = {
    values.find(_.value == value)
  }

  def fromAlpha(alpha: Int): Option[WatermarkTransparency] = {
    val percent = 100 - alpha
    WatermarkTransparency.fromInt(percent)
  }

}

/** Settings of watermark.
  *
  * @param text
  *   The text to be printed together with viewer's email address.
  * @param color
  *   Color setting.
  * @param layout
  *   Density setting.
  * @param transparency
  *   Transparency setting.
  */
final case class WatermarkMetadataParams(
  text: String,
  color: WatermarkColor,
  layout: WatermarkLayout,
  transparency: WatermarkTransparency
)

object WatermarkMetadataParams {
  given Codec.AsObject[WatermarkMetadataParams] = deriveCodecWithDefaults
}

/** When a file tree is upload, the folder structure is recreated. This decides the strategy when there is an existing
  * folder with the same name.
  */
enum UploadFileFolderExistHandler(override val value: String) extends StringEnum {

  /** Ignore existing folder with the same name and create new folder.
    */
  case CreateNew extends UploadFileFolderExistHandler("CreateNew")

  /** Reuse the existing folder.
    */
  case UseExisting extends UploadFileFolderExistHandler("UseExisting")
}

object UploadFileFolderExistHandler extends StringEnumCompanion[UploadFileFolderExistHandler] {

  given Codec[UploadFileFolderExistHandler] = deriveStringEnumCodec

}

/** Upload new files to a folder.
  *
  * @param folderId
  *   Id of the Parent folder.
  * @param permissionMapOpt
  *   None if the new files inherit parent folder's permission settings. Otherwise, override with actor has Own.
  * @param folderExistHandler
  *   When a file tree is upload, the folder structure is recreated. This decides the strategy when there is an existing
  *   folder with the same name.
  * @param sendNotificationOnUpload
  *   True if should send notification right after uploaded for the uploaded files. Otherwise, keep the current
  *   notification settings
  */
final case class UploadDataRoomFileParams(
  folderId: FolderId,
  permissionMapOpt: Option[FileFolderPermissionMap],
  folderExistHandler: UploadFileFolderExistHandler = UploadFileFolderExistHandler.CreateNew,
  sendNotificationOnUpload: Boolean = false,
  actionType: DmsUploadActionType = DmsUploadActionType.Upload
) extends FileUploadParams.WithEncoder[UploadDataRoomFileParams]

object UploadDataRoomFileParams extends FileUploadParams.Companion[UploadDataRoomFileParams]("uploadDataRoomFile") {
  given Codec.AsObject[UploadDataRoomFileParams] = deriveCodecWithDefaults
}

/** Upload new version of a file.
  *
  * @param fileId
  *   Id of the file.
  * @param sendNotificationOnUpload
  *   True if should send notification right after uploaded for the uploaded files. Otherwise, keep the current
  *   notification settings
  * @param actionType
  *   Tracking upload type, it will be UploadNewVersion by default.
  */
final case class UploadDataRoomNewVersionParams(
  fileId: FileId,
  sendNotificationOnUpload: Boolean = false
) extends FileUploadParams.WithEncoder[UploadDataRoomNewVersionParams]

object UploadDataRoomNewVersionParams
    extends FileUploadParams.Companion[UploadDataRoomNewVersionParams]("uploadDataRoomNewVersionParams") {
  given Codec.AsObject[UploadDataRoomNewVersionParams] = deriveCodecWithDefaults
}

final case class UploadAndImportFilesIntoFoldersParams(
  parentFolderId: FolderId,
  sendNotificationOnUpload: Boolean
) extends FileUploadParams.WithEncoder[UploadAndImportFilesIntoFoldersParams]

object UploadAndImportFilesIntoFoldersParams
    extends FileUploadParams.Companion[UploadAndImportFilesIntoFoldersParams]("distributeFilesToDataRoomFolder") {
  given Codec.AsObject[UploadAndImportFilesIntoFoldersParams] = deriveCodecWithDefaults
}

sealed trait MappedFolderResult derives CanEqual {
  def name: String
}

object MappedFolderResult {
  given Codec.AsObject[MappedFolderResult] = deriveCodecWithDefaults

  case class DuplicatedFolders(folders: Seq[FolderId]) extends MappedFolderResult {
    override def name: String = "duplicated_folders"
  }

  case object DuplicatedFolders {
    given Codec.AsObject[DuplicatedFolders] = deriveCodecWithDefaults
  }

  case class MatchedFolder(folder: FolderId) extends MappedFolderResult {
    override def name: String = "matched_folder"
  }

  case object MatchedFolder {
    given Codec.AsObject[MatchedFolder] = deriveCodecWithDefaults
  }

  case object NoMatchedFolders extends MappedFolderResult {
    override def name: String = "no_matched_folders"
  }

  given Codec.AsObject[NoMatchedFolders.type] = deriveCodecWithDefaults
}

final case class FileUploadAndImportResult(
  fileIdOpt: Option[FileId],
  fileName: String,
  mappedFolderResult: MappedFolderResult,
  isUploadSuccessful: Boolean,
  size: Double
)

object FileUploadAndImportResult {
  given Codec.AsObject[FileUploadAndImportResult] = deriveCodecWithDefaults
}

final case class TemporaryFolderInfoForUploadAndAutoImport(
  folderId: FolderId,
  folderName: String
)

object TemporaryFolderInfoForUploadAndAutoImport {
  given Codec.AsObject[TemporaryFolderInfoForUploadAndAutoImport] = deriveCodecWithDefaults
}

final case class UploadAndImportFilesIntoFoldersResponse(
  fileUploadResults: Seq[FileUploadAndImportResult],
  temporaryFolderInfoOpt: Option[TemporaryFolderInfoForUploadAndAutoImport]
) {

  def failedToMapFileCount: Int = fileUploadResults.count { result =>
    result.mappedFolderResult match {
      case MappedFolderResult.NoMatchedFolders | _: MappedFolderResult.DuplicatedFolders => true
      case _: MappedFolderResult.MatchedFolder                                           => false
    }
  }

}

object UploadAndImportFilesIntoFoldersResponse {
  given Codec.AsObject[UploadAndImportFilesIntoFoldersResponse] = deriveCodecWithDefaults
}

final case class ExportReportForUploadAndAutoImportFilesParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  fileUploadAndImportResults: Seq[FileUploadAndImportResult]
) extends DataRoomRoleCheckParams

object ExportReportForUploadAndAutoImportFilesParams {
  given Codec.AsObject[ExportReportForUploadAndAutoImportFilesParams] = deriveCodecWithDefaults
}

final case class ExportReportForUploadAndAutoImportFilesResponse(
  reportFileId: FileId
)

object ExportReportForUploadAndAutoImportFilesResponse {
  given Codec.AsObject[ExportReportForUploadAndAutoImportFilesResponse] = deriveCodecWithDefaults
}

final case class FileWithMappedFolder(
  fileName: String,
  size: Double,
  mappedFolderResult: MappedFolderResult
)

object FileWithMappedFolder {
  given Codec.AsObject[FileWithMappedFolder] = deriveCodecWithDefaults
}

final case class ExportReportForFolderMappingResultsParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  folderMappingResults: Seq[FileWithMappedFolder]
) extends DataRoomRoleCheckParams

object ExportReportForFolderMappingResultsParams {
  given Codec.AsObject[ExportReportForFolderMappingResultsParams] = deriveCodecWithDefaults
}

final case class ExportReportForFolderMappingResultsResponse(
  reportFileId: FileId
)

object ExportReportForFolderMappingResultsResponse {
  given Codec.AsObject[ExportReportForFolderMappingResultsResponse] = deriveCodecWithDefaults
}

/** Create a new folder inside a folder.
  *
  * @param parentFolder
  *   Id of the parent folder.
  * @param name
  *   Name of the new folder.
  * @param permissionMapOpt
  *   None if the new files inherit parent folder's permission settings. Otherwise, override with actor has Own.
  */
final case class AddFolderParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  parentFolder: FolderId,
  name: String,
  permissionMapOpt: Option[FileFolderPermissionMap]
) extends DataRoomAssetCheckParams {
  override def fileIds = Set.empty

  override def folderIds = Set(parentFolder)
}

object AddFolderParams {
  given Codec.AsObject[AddFolderParams] = deriveCodecWithDefaults
}

/** Response of add new folder.
  *
  * @param newFolderId
  *   Id of the newly created folder.
  */
final case class AddFolderResponse(
  newFolderId: FolderId
)

object AddFolderResponse {
  given Codec.AsObject[AddFolderResponse] = deriveCodecWithDefaults
}

/** Rename a folder in data room.
  *
  * @param folderId
  *   Id of the target folder.
  * @param name
  *   New folder's name.
  */
final case class RenameFolderParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  folderId: FolderId,
  name: String
) extends DataRoomAssetCheckParams {
  override def fileIds = Set.empty

  override def folderIds = Set(folderId)
}

object RenameFolderParams {
  given Codec.AsObject[RenameFolderParams] = deriveCodecWithDefaults
}

/** Rename a file in data room.
  *
  * @param fileId
  *   Id of the target file.
  * @param name
  *   New file's name.
  */
final case class RenameFileParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  fileId: FileId,
  name: String
) extends DataRoomAssetCheckParams {
  override def fileIds = Set(fileId)

  override def folderIds = Set.empty
}

object RenameFileParams {
  given Codec.AsObject[RenameFileParams] = deriveCodecWithDefaults
}

/** Get all file versions of a file in data room.
  *
  * @param fileId
  *   Id of the target file.
  */
final case class GetAllFileVersionsParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  fileId: FileId
) extends DataRoomAssetCheckParams {
  override def fileIds = Set(fileId)

  override def folderIds = Set.empty
}

object GetAllFileVersionsParams {
  given Codec.AsObject[GetAllFileVersionsParams] = deriveCodecWithDefaults
}

/** Delete folders and files in the data room.
  *
  * @param folderIds
  *   Folders to be recursively deleted.
  * @param fileIds
  *   Files to be deleted.
  */
final case class DeleteFilesAndFoldersParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  folderIds: Set[FolderId],
  fileIds: Set[FileId],
  isPermanent: Boolean = false
) extends DataRoomAssetCheckParams

object DeleteFilesAndFoldersParams {
  given Codec.AsObject[DeleteFilesAndFoldersParams] = deriveCodecWithDefaults
}

/** Download folders and files.
  *
  * @param request
  *   The folders and files to be downloaded.
  * @param forceGetWatermark
  *   True if watermark should be applied, even if actor can access raw files.
  */
final case class GetDownloadUrlParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  request: BatchDownloadRequest,
  forceGetWatermark: Boolean,
  includeDeleted: Boolean
) extends DataRoomAssetCheckParams {
  override def folderIds = request.folderIds.toSet

  override def fileIds = request.fileIds.toSet
}

object GetDownloadUrlParams {
  given Codec.AsObject[GetDownloadUrlParams] = deriveCodecWithDefaults
}

/** View a file on browser.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param fileId
  *   Id of the file to be viewed.
  * @param password
  *   File password. This is broken for now.
  * @param forceGetWatermark
  *   True if watermark should be applied, even if actor can access raw files.
  */
final case class GetViewUrlParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  fileId: FileId,
  password: Option[String],
  forceGetWatermark: Boolean,
  includeDeleted: Boolean = false
) extends DataRoomAssetCheckParams {
  override def fileIds = Set(fileId)

  override def folderIds = Set.empty
}

object GetViewUrlParams {
  given Codec.AsObject[GetViewUrlParams] = deriveCodecWithDefaults
}

sealed abstract class GetViewUrlException(message: String) extends RuntimeException derives CanEqual {
  override def getMessage: String = message
}

object GetViewUrlException {

  case class FailedToGenerateWatermarkException(fileNames: Seq[String], numberOfSuccessfulFiles: Int)
      extends GetViewUrlException(s"Unable to generate watermark for ${fileNames.size} file(s)")

  case object ServerException extends GetViewUrlException("Internal server error") {
    given Codec.AsObject[ServerException.type] = deriveCodecWithDefaults
  }

  object FailedToGenerateWatermarkException {
    given Codec.AsObject[FailedToGenerateWatermarkException] = deriveCodecWithDefaults
  }

  given Codec.AsObject[GetViewUrlException] = deriveCodecWithDefaults
}

/** View or download the terms of access.
  *
  * @param toaFileId
  *   Id of the latest terms of access.
  * @param purpose
  *   Whether view or download.
  */
final case class GetTermsOfAccessUrl(
  toaFileId: FileId,
  linkIdOpt: Option[ProtectedLinkId],
  purpose: DmsTrackingActivityType
) extends DataRoomTermsOfAccessCheckParams

object GetTermsOfAccessUrl {
  given Codec.AsObject[GetTermsOfAccessUrl] = deriveCodecWithDefaults
}

/** Pre-condition check of copy files and folders.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param folderIds
  *   Folders to be copied.
  * @param fileIds
  *   Files to be copied.
  */
final case class CopyPreCheckParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  copyFolderIds: Set[FolderId],
  copyFileIds: Set[FileId],
  includeDeleted: Boolean
) extends DataRoomAssetCheckParams {
  override def folderIds = copyFolderIds

  override def fileIds = copyFileIds
}

object CopyPreCheckParams {
  given Codec.AsObject[CopyPreCheckParams] = deriveCodecWithDefaults
}

/** Pre-condition check response for copy.
  *
  * @param notCopiedFileMap
  *   List of files unable to be copied.
  * @param parentFolderMap
  *   Map from parent folder to its name.
  */
final case class CopyPreCheckResponse(notCopiedFileMap: Map[FileId, String], parentFolderMap: Map[FolderId, String])

object CopyPreCheckResponse {
  given Codec.AsObject[CopyPreCheckResponse] = deriveCodecWithDefaults
}

/** Pre-condition check of move files and folders.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param targetFolderId
  *   Id of the target folder.
  * @param folderIds
  *   Folders to be moved.
  * @param fileIds
  *   Files to be moved.
  */
final case class MovePreCheckParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  targetFolderId: FolderId,
  moveFolderIds: Set[FolderId],
  moveFileIds: Set[FileId],
  includeDeleted: Boolean
) extends DataRoomAssetCheckParams {
  override def folderIds = moveFolderIds

  override def fileIds = moveFileIds
}

object MovePreCheckParams {
  given Codec.AsObject[MovePreCheckParams] = deriveCodecWithDefaults
}

/** Pre-condition check response for move.
  *
  * @param differentPermissions
  *   Whether the target folder's permission is different from the source's.
  */
final case class MovePreCheckResponse(differentPermissions: Boolean)

object MovePreCheckResponse {
  given Codec.AsObject[MovePreCheckResponse] = deriveCodecWithDefaults
}

/** Send an email to participants in a data room.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param recipients
  *   Set of email recipients.
  * @param subject
  *   Subject of the email.
  * @param message
  *   Quoted content of the email.
  */
final case class DataRoomManualNotificationParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  recipients: Set[UserId],
  subject: String,
  message: String
) extends DataRoomJoinedUserCheckParams
    with DataRoomInvitedOrJoinedMembersCheckParams {
  override def userIds = recipients
}

object DataRoomManualNotificationParams {
  given Codec.AsObject[DataRoomManualNotificationParams] = deriveCodecWithDefaults
}

/** Export the list of participants to CSV file.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param userIds
  *   Set of users to be exported.
  * @param fields
  *   List of fields to be exported.
  * @param timezoneOffsetInMinutes
  *   Actor's timezone.
  */
final case class DataRoomExportParticipantsParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  userIds: Set[UserId],
  fields: Set[DataRoomExportParticipantsParams.Field],
  timezoneOffsetInMinutes: Int,
  includeRemovedParticipants: Boolean
) extends DataRoomRoleCheckParams
    with DataRoomInvitedOrJoinedMembersCheckParams

object DataRoomExportParticipantsParams {

  given Codec.AsObject[DataRoomExportParticipantsParams] = deriveCodecWithDefaults

  enum Field {

    case Name, Email, Group, Role, RemovedMember,
      VisitCount, AccessCount, TotalTimeSpent,
      LastAction, Status, TermsOfAccess

  }

  object Field {

    given Codec[Field] = deriveEnumCodec

    def getName(field: Field): String = {
      field match {
        case Field.Name           => "Name"
        case Field.Email          => "Email"
        case Field.Group          => "Group"
        case Field.Role           => "Role"
        case Field.Status         => "Status"
        case Field.TermsOfAccess  => "Terms of Access"
        case Field.RemovedMember  => "Is removed"
        case Field.VisitCount     => "Visits"
        case Field.AccessCount    => "Access"
        case Field.TotalTimeSpent => "Total time spent"
        case Field.LastAction     => "Last action"
      }
    }

  }

}

final case class DataRoomExportPermissionsParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  fileIds: Set[FileId],
  target: DataRoomExportPermissionsParams.ExportPermissionTarget
) extends DataRoomRoleCheckParams
    with DataRoomInvitedOrJoinedMembersCheckParams
    with DataRoomGroupCheckParams
    with DataRoomAssetCheckParams derives CirceCodec.WithDefaults {

  override def folderIds = Set.empty

  override def userIds = target match {
    case DataRoomExportPermissionsParams.ExportPermissionTarget.Users(userIds) => userIds
    case _: DataRoomExportPermissionsParams.ExportPermissionTarget.Groups      => Set.empty
  }

  override def groupIds = target match {
    case _: DataRoomExportPermissionsParams.ExportPermissionTarget.Users         => Set.empty
    case DataRoomExportPermissionsParams.ExportPermissionTarget.Groups(groupIds) => groupIds
  }

}

object DataRoomExportPermissionsParams {

  sealed trait ExportPermissionTarget derives CirceCodec.WithDefaultsAndTypeName

  object ExportPermissionTarget {
    final case class Users(userIds: Set[UserId]) extends ExportPermissionTarget

    final case class Groups(groupIds: Set[DataRoomGroupId]) extends ExportPermissionTarget
  }

}

final case class DataRoomExportPermissionsResponse(
  url: String
)

object DataRoomExportPermissionsResponse {
  given Codec.AsObject[DataRoomExportPermissionsResponse] = deriveCodecWithDefaults
}

/** Export the audit log to a CSV file.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param queryTimeRange
  *   The time range of queried activities.
  * @param timezoneOffsetInMinutes
  *   Actor's timezone.
  */
final case class ExportFileActivitiesParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  activityTypes: Set[DataRoomFileActivityType],
  timezoneOffsetInMinutes: Int,
  actorOpt: Option[UserId] = None,
  timeRangeOpt: Option[(Instant, Instant)] = None
) extends DataRoomRoleCheckParams

object ExportFileActivitiesParams {
  given Codec.AsObject[ExportFileActivitiesParams] = deriveCodecWithDefaults
}

/** Response from exporting audit log.
  *
  * @param url
  *   URL of the CSV file.
  */
final case class ExportFileActivitiesResponse(url: String)

object ExportFileActivitiesResponse {
  given Codec.AsObject[ExportFileActivitiesResponse] = deriveCodecWithDefaults
}

/** Export data room activity log
  *
  * @param dataRoomWorkflowId
  *   Id of target data room
  * @param queryTimeRange
  *   Filter by time range
  * @param activityType
  *   Filter by activity type
  * @param queryUserRange
  *   Filter by user range, value can be all users, user role or a specific user
  * @param queryUserId
  *   When queryUserRange is ByUserId, queryUserId is defined
  */
final case class ExportDataRoomActivitiesParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  queryTimeRange: QueryTimeRange,
  activityType: DataRoomActivityType,
  queryUserRange: QueryUserRange,
  queryUserId: Option[UserId],
  timezoneOffsetInMinutes: Int
) extends DataRoomRoleCheckParams

object ExportDataRoomActivitiesParams {
  given Codec.AsObject[ExportDataRoomActivitiesParams] = deriveCodecWithDefaults
}

/** Response of exporting data room activity log
  *
  * @param url
  *   URL of exported CSV file
  */
final case class ExportDataRoomActivitiesResponse(
  url: String
)

object ExportDataRoomActivitiesResponse {
  given Codec.AsObject[ExportDataRoomActivitiesResponse] = deriveCodecWithDefaults
}

/** Create a new .url file in a folder.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param parentFolder
  *   Id of the parent folder.
  * @param url
  *   Destination URL.
  * @param name
  *   Displayed name and also the .url file name.
  */
final case class CreateShortcutParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  parentFolder: FolderId,
  url: String,
  name: String
) extends DataRoomAssetCheckParams {
  override def folderIds = Set(parentFolder)

  override def fileIds = Set.empty
}

object CreateShortcutParams {
  given Codec.AsObject[CreateShortcutParams] = deriveCodecWithDefaults
}

/** Get the destination from a .url file.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param fileId
  *   Id of the file.
  */
final case class ExtractShortcutParams(dataRoomWorkflowId: DataRoomWorkflowId, fileId: FileId, includeDeleted: Boolean)
    extends DataRoomAssetCheckParams {
  override def fileIds = Set(fileId)

  override def folderIds = Set.empty
}

object ExtractShortcutParams {
  given Codec.AsObject[ExtractShortcutParams] = deriveCodecWithDefaults
}

/** Record page view events.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param fileId
  *   Id of the viewed file.
  * @param versionIndexOpt
  *   Version of the viewed file, leave it as None the use the current version
  * @param session
  *   Timestamp of the session, marked by the time storage id was retrieved to generate URL.
  * @param interval
  *   Page view events.
  */
final case class RecordPageViewParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  fileId: FileId,
  versionIndexOpt: Option[Int],
  session: Instant,
  interval: PageViewInterval
) extends DataRoomAssetCheckParams {
  override def fileIds = Set(fileId)

  override def folderIds = Set.empty
}

object RecordPageViewParams {
  given Codec.AsObject[RecordPageViewParams] = deriveCodecWithDefaults
}

/** Response from extracting a .url file.
  *
  * @param url
  *   Destination URL.
  */
final case class ExtractShortcutResponse(url: String)

object ExtractShortcutResponse {
  given Codec.AsObject[ExtractShortcutResponse] = deriveCodecWithDefaults
}

/** Response from modifying data room settings.
  *
  * @param toaFileIdOpt
  *   Id of newly uploaded terms of access, if any.
  */
final case class ModifyDataRoomGeneralSettingsResponse(toaFileIdOpt: Option[FileId])

object ModifyDataRoomGeneralSettingsResponse {
  given Codec.AsObject[ModifyDataRoomGeneralSettingsResponse] = deriveCodecWithDefaults
}

/** Response from creating new data room.
  *
  * @param dataRoomWorkflowId
  *   Id of the newly created data room.
  */
final case class CreateDataRoomResponse(dataRoomWorkflowId: DataRoomWorkflowId)

object CreateDataRoomResponse {
  given Codec.AsObject[CreateDataRoomResponse] = deriveCodecWithDefaults
}

/** Response from uploading new files.
  *
  * @param fileIds
  *   Ids of the newly uploaded files.
  */
final case class UploadDataRoomFileResponse(fileIds: Seq[FileId])

object UploadDataRoomFileResponse {
  given Codec.AsObject[UploadDataRoomFileResponse] = deriveCodecWithDefaults
}

/** Response from view/download APIs.
  *
  * @param url
  *   URL
  * @param sessionOpt
  *   Optionally a timestamp of when the storage id was retrieved.
  */
final case class GetUrlResponse(
  url: String,
  sessionOpt: Option[Instant],
  extensionOpt: Option[String] = None
)

object GetUrlResponse {
  given Codec.AsObject[GetUrlResponse] = deriveCodecWithDefaults
}

final case class DataRoomGetDownloadUrlMetadata(
  numberOfSuccessfulFiles: Int,
  filesFailedToGenerateWatermark: Seq[String] // file names
)

object DataRoomGetDownloadUrlMetadata {
  given Codec.AsObject[DataRoomGetDownloadUrlMetadata] = deriveCodecWithDefaults
}

/** Response from exporting participants.
  *
  * @param url
  *   URL of the CSV file.
  */
final case class DataRoomExportParticipantsResponse(url: String)

object DataRoomExportParticipantsResponse {
  given Codec.AsObject[DataRoomExportParticipantsResponse] = deriveCodecWithDefaults
}

/** Response from get all file versions.
  *
  * @param currentVersion
  *   Current version of the file
  * @param fileVersions
  *   List containing all versions of a file
  */
final case class GetAllFileVersionsResponse(
  currentVersion: Int,
  fileVersions: List[FileVersionInfo]
)

object GetAllFileVersionsResponse {

  final case class FileVersionInfo(
    versionIndex: Int,
    userLocalFileName: Option[String],
    createdAt: Option[Instant],
    createdBy: UserId,
    createdByFullName: String,
    size: Long
  )

  given Codec.AsObject[FileVersionInfo] = deriveCodecWithDefaults

  given Codec.AsObject[GetAllFileVersionsResponse] = deriveCodecWithDefaults
}

final case class DataRoomEmptyParams() extends EmptyEndpointValidationParams

object DataRoomEmptyParams {
  given Codec.AsObject[DataRoomEmptyParams] = deriveCodecWithDefaults
}

final case class DataRoomEmptyResponse() derives CanEqual

object DataRoomEmptyResponse {
  given Codec.AsObject[DataRoomEmptyResponse] = deriveCodecWithDefaults
}

/** Move files and folders to another folder.
  *
  * @param folderIds
  *   Folders to be moved.
  * @param fileIds
  *   Files to be moved.
  */
final case class MoveCopyDataRoomFileParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  folderIds: Seq[FolderId],
  fileIds: Seq[FileId],
  includeDeleted: Boolean = false
)

object MoveCopyDataRoomFileParams {
  given Codec.AsObject[MoveCopyDataRoomFileParams] = deriveCodecWithDefaults
}

/** Create a new link invitation.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param data
  *   Link invitation settings.
  * @param password
  *   An optional password.
  */
final case class CreateDataRoomLinkInvitationParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  data: DataRoomLinkInvitationParamsData,
  password: Option[String],
  enableEnterpriseLogin: Boolean
) extends DataRoomRoleCheckParams

object CreateDataRoomLinkInvitationParams {
  given Codec.AsObject[CreateDataRoomLinkInvitationParams] = deriveCodecWithDefaults
}

/** Response from creating new link invitation.
  *
  * @param linkId
  *   Id of the newly created link.
  */
final case class CreateDataRoomLinkInvitationResponse(linkId: ProtectedLinkId)

object CreateDataRoomLinkInvitationResponse {
  given Codec.AsObject[CreateDataRoomLinkInvitationResponse] = deriveCodecWithDefaults
}

/** Modify an existing link invitation.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param linkId
  *   Id of the modified link invitation.
  * @param data
  *   New setting of the link invitation
  * @param password
  *   None means no password change, otherwise the password change.
  */
final case class ModifyDataRoomLinkInvitationParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  linkId: ProtectedLinkId,
  data: DataRoomLinkInvitationParamsData,
  password: Option[ModifyDataRoomLinkInvitationParams.PasswordChange],
  enableEnterpriseLoginChange: Option[Boolean]
) extends DataRoomRoleCheckParams

object ModifyDataRoomLinkInvitationParams {

  given Codec.AsObject[ModifyDataRoomLinkInvitationParams] = deriveCodecWithDefaults

  /** @param password
    *   None means password is turned off, otherwise the new password.
    */
  final case class PasswordChange(password: Option[String])

  object PasswordChange {
    given Codec.AsObject[PasswordChange] = deriveCodecWithDefaults
  }

}

/** Join a data room from link invitation.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param linkId
  *   Id of the link invitation.
  * @param toaFileIdOpt
  *   The terms of access that actor also accepts.
  */
final case class JoinDataRoomViaLinkInvitationParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  linkId: ProtectedLinkId,
  toaFileIdOpt: Option[FileId]
) extends DataRoomEmptyEndpointValidationParams

object JoinDataRoomViaLinkInvitationParams {
  given Codec.AsObject[JoinDataRoomViaLinkInvitationParams] = deriveCodecWithDefaults
}

/** Query the status of the link invitation.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param linkId
  *   Id of the link invitation.
  */
final case class GetDataRoomLinkInvitationInfoParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  linkId: ProtectedLinkId
) extends DataRoomEmptyEndpointValidationParams

object GetDataRoomLinkInvitationInfoParams {
  given Codec.AsObject[GetDataRoomLinkInvitationInfoParams] = deriveCodecWithDefaults
}

/** The status of a link invitation
  *
  * @param name
  *   The display name of the link
  * @param toaFileIdOpt
  *   Terms of access that user also needs to accept in order to join
  * @param toaFileIdNameOpt
  *   Name of the terms of access
  * @param hasJoined
  *   Whether the user has joined. If yes, frontend may redirect user to the data room.
  * @param isSeatLimitReached
  *   Whether the seat limit is reached. If yes, user may not join, although all checks are passed.
  */
final case class GetDataRoomLinkInvitationInfoResponse(
  name: String,
  toaFileIdOpt: Option[FileId],
  toaFileIdNameOpt: Option[String],
  hasJoined: Boolean,
  isSeatLimitReached: Boolean
)

object GetDataRoomLinkInvitationInfoResponse {
  given Codec.AsObject[GetDataRoomLinkInvitationInfoResponse] = deriveCodecWithDefaults
}

/** Delete an existing link invitation.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param linkId
  *   Id of the link invitation.
  */
final case class DeleteDataRoomLinkInvitationParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  linkId: ProtectedLinkId
) extends DataRoomRoleCheckParams

object DeleteDataRoomLinkInvitationParams {
  given Codec.AsObject[DeleteDataRoomLinkInvitationParams] = deriveCodecWithDefaults
}

final case class DataRoomWhiteLabelCustomValue(
  key: String,
  value: String
)

object DataRoomWhiteLabelCustomValue {
  given Codec.AsObject[DataRoomWhiteLabelCustomValue] = deriveCodecWithDefaults
}

/** Whitelabel setting of a data room.
  *
  * @param isEnabled
  *   Whether whitelabel is active.
  * @param longLogoUrl
  *   URL of the logo.
  * @param customValues
  *   Map of layout and color customization.
  * @param dataRoomIconUrl
  *   URL of data room icon
  */
final case class DataRoomWhiteLabelData(
  isEnabled: Boolean,
  longLogoUrl: Option[String],
  longLogoFileNameOpt: Option[String],
  customValues: Seq[DataRoomWhiteLabelCustomValue],
  dataRoomIconUrl: Option[String]
) {

  lazy val customValuesMap: Map[String, String] = customValues.map(kv => kv.key -> kv.value).toMap

  def enabledLongLogoUrl: Option[String] = longLogoUrl.filter(_ => isEnabled)

  def enabledCustomValues: Map[String, String] =
    if (isEnabled) {
      customValues.map(kv => (kv.key, kv.value)).toMap
    } else {
      Map.empty
    }

  def enabledDataRoomIconUrl: Option[String] = dataRoomIconUrl.filter(_ => isEnabled)
}

final case class AuthenticationWhiteLabelCustomValue(
  key: String,
  value: String
)

object AuthenticationWhiteLabelCustomValue {
  given Codec.AsObject[AuthenticationWhiteLabelCustomValue] = deriveCodecWithDefaults
}

final case class AuthenticationWhiteLabelData(
  isEnabled: Boolean,
  customValues: Seq[AuthenticationWhiteLabelCustomValue]
) {
  lazy val customValuesMap: Map[String, String] = customValues.map(kv => kv.key -> kv.value).toMap
}

object AuthenticationWhiteLabelData {
  given Codec.AsObject[AuthenticationWhiteLabelData] = deriveCodecWithDefaults
}

object DataRoomWhiteLabelData {

  given Codec.AsObject[DataRoomWhiteLabelData] = deriveCodecWithDefaults

  val empty: DataRoomWhiteLabelData = DataRoomWhiteLabelData(
    isEnabled = false,
    longLogoUrl = None,
    customValues = Seq.empty,
    dataRoomIconUrl = None,
    longLogoFileNameOpt = None
  )

}

final case class UploadDataRoomWhiteLabelFilesParams(
  dataRoomWorkflowId: DataRoomWorkflowId
) extends FileUploadParams.WithEncoder[UploadDataRoomWhiteLabelFilesParams]
    with DataRoomRoleCheckParams
    with DataRoomPremiumPlanCheckParams
    with DataRoomPortalPermissionCheckParams

object UploadDataRoomWhiteLabelFilesParams
    extends FileUploadParams.Companion[UploadDataRoomWhiteLabelFilesParams]("uploadDataRoomWhiteLabelFiles") {
  given Codec.AsObject[UploadDataRoomWhiteLabelFilesParams] = deriveCodecWithDefaults
}

final case class UploadDataRoomWhiteLabelFilesResponse(
  logoStorageIdOpt: Option[DocumentStorageId],
  iconStorageIdOpt: Option[DocumentStorageId]
)

object UploadDataRoomWhiteLabelFilesResponse {
  given Codec.AsObject[UploadDataRoomWhiteLabelFilesResponse] = deriveCodecWithDefaults
}

final case class GetDataRoomWhiteLabelParams(
  dataRoomWorkflowId: DataRoomWorkflowId
) extends DataRoomPortalPermissionCheckParams derives CanEqual

object GetDataRoomWhiteLabelParams {
  given Codec.AsObject[GetDataRoomWhiteLabelParams] = deriveCodecWithDefaults
}

final case class GetDataRoomWhiteLabelResponse(
  dataRoomWhiteLabelData: DataRoomWhiteLabelData,
  authenticationWhiteLabelData: AuthenticationWhiteLabelData
)

object GetDataRoomWhiteLabelResponse {
  given Codec.AsObject[GetDataRoomWhiteLabelResponse] = deriveCodecWithDefaults
}

/** Update data room whitelabel
  *
  * @param dataRoomWorkflowId
  *   Id of target data room.
  * @param customValues
  *   New customization map of color and layout.
  * @param changeDataRoomLogo
  *   Whether a new data room logo is uploaded.
  * @param saveAsEntityDefault
  *   Whether this setting should be reused in new data room.
  */
final case class UpdateDataRoomWhiteLabelParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  customValues: Map[String, String],
  isEnabledChanged: Option[Boolean] = None,
  logoUpdateOpt: Option[WhiteLabelFileUpdate],
  iconUpdateOpt: Option[WhiteLabelFileUpdate],
  saveAsEntityDefault: Boolean = false,
  isAuthenticationWhiteLabelEnabled: Boolean = false,
  authenticationCustomValues: Map[String, String] = Map()
) extends DataRoomRoleCheckParams
    with DataRoomPremiumPlanCheckParams
    with DataRoomPortalPermissionCheckParams

object UpdateDataRoomWhiteLabelParams {
  given Codec.AsObject[UpdateDataRoomWhiteLabelParams] = deriveCodecWithDefaults

  final case class WhiteLabelFileUpdate(
    storageIdOpt: Option[DocumentStorageId] = None,
    fileNameOpt: Option[String] = None
  )

  object WhiteLabelFileUpdate {
    given Codec.AsObject[WhiteLabelFileUpdate] = deriveCodecWithDefaults
  }

}

final case class GetDataRoomNotificationSettingsParams(
  dataRoomWorkflowId: DataRoomWorkflowId
)

object GetDataRoomNotificationSettingsParams {
  given Codec.AsObject[GetDataRoomNotificationSettingsParams] = deriveCodecWithDefaults
}

final case class GetDataRoomNotificationSettingsResponse(
  isAdmin: Boolean,
  invitationAcceptedEmail: InvitationAcceptedEmailNotification,
  newFileNotification: NewFileNotification,
  newFileNotificationFrequency: NotificationFrequency
)

object GetDataRoomNotificationSettingsResponse {
  given Codec.AsObject[GetDataRoomNotificationSettingsResponse] = deriveCodecWithDefaults
}

/** Set data room wide notification preferences.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param invitationAcceptedEmailNotification
  *   Setting for invitation accepted email.
  * @param newFileNotification
  *   Setting for new file upload digest email
  */
final case class SetDataRoomNotificationSettingsParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  invitationAcceptedEmailNotification: InvitationAcceptedEmailNotification,
  newFileNotification: NewFileNotification,
  newFileNotificationFrequency: NotificationFrequency
) extends DataRoomJoinedUserCheckParams

object SetDataRoomNotificationSettingsParams {
  given Codec.AsObject[SetDataRoomNotificationSettingsParams] = deriveCodecWithDefaults
}

/** Update data room notification configs.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param newFileNotificationConfigOpt
  *   Config for new file upload digest email, None means no update
  */
final case class UpdateDataRoomNotificationConfigsParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  newFileNotificationConfigOpt: Option[NewFileNotificationConfig]
)

object UpdateDataRoomNotificationConfigsParams {
  given Codec.AsObject[UpdateDataRoomNotificationConfigsParams] = deriveCodecWithDefaults
}

/** Whitelist users from terms of access requirements. These users are no longer required to accept terms of access.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param whitelistedUsers
  *   Set of whitelisted users.
  */
final case class SetDataRoomTermsOfAccessWhitelistedUsersParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  whitelistedUsers: Set[UserId]
) extends DataRoomRoleCheckParams
    with DataRoomInvitedOrJoinedMembersCheckParams {

  override def userIds = whitelistedUsers

}

object SetDataRoomTermsOfAccessWhitelistedUsersParams {
  given Codec.AsObject[SetDataRoomTermsOfAccessWhitelistedUsersParams] = deriveCodecWithDefaults
}

/** Get link invitation display name.
  *
  * @param linkId
  *   Id of the link invitation.
  */
final case class GetLinkNameParams(
  linkId: ProtectedLinkId
)

object GetLinkNameParams {
  given Codec.AsObject[GetLinkNameParams] = deriveCodecWithDefaults
}

/** Response from getting link invitation name.
  *
  * @param linkName
  *   Name of the link.
  */
final case class GetLinkNameResponse(
  linkName: String
)

object GetLinkNameResponse {
  given Codec.AsObject[GetLinkNameResponse] = deriveCodecWithDefaults
}

/** Request access to join a data room.
  *
  * @param linkId
  *   Id of the link invitation.
  * @param email
  *   Email address input by user.
  */
final case class DataRoomAccessRequest(
  linkId: ProtectedLinkId,
  email: String
)

object DataRoomAccessRequest {
  given Codec.AsObject[DataRoomAccessRequest] = deriveCodecWithDefaults
}

/** Approve to invite users to join the data room from their requests.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param requests
  *   Access requests.
  */
final case class ApproveAccessRequestsParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  requests: Seq[DataRoomAccessRequest]
) extends DataRoomRoleCheckParams

object ApproveAccessRequestsParams {
  given Codec.AsObject[ApproveAccessRequestsParams] = deriveCodecWithDefaults
}

/** Decline requests to join data room.
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param requests
  *   Access requests.
  */
final case class DeclineAccessRequestsParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  requests: Seq[DataRoomAccessRequest],
  hasNotifyEmail: Boolean
) extends DataRoomRoleCheckParams

object DeclineAccessRequestsParams {
  given Codec.AsObject[DeclineAccessRequestsParams] = deriveCodecWithDefaults
}

final case class GetAllDataRoomEntitiesParams() extends DataRoomPortalPermissionCheckParams

object GetAllDataRoomEntitiesParams {
  given Codec.AsObject[GetAllDataRoomEntitiesParams] = deriveCodecWithDefaults
}

/** Get all entities with data room plan.
  *
  * @param entities
  *   Map from entity to entity name and plan.
  */
final case class GetAllDataRoomEntitiesResponse(
  entities: Map[EntityId, GetAllDataRoomEntitiesResponse.DataRoomEntity]
)

object GetAllDataRoomEntitiesResponse {

  given Codec.AsObject[GetAllDataRoomEntitiesResponse] = deriveCodecWithDefaults

  final case class DataRoomEntity(
    entityName: String,
    dataRoomPlan: DataRoomPlan,
    isAnduinInternal: Boolean
  )

  object DataRoomEntity {
    given Codec.AsObject[DataRoomEntity] = deriveCodecWithDefaults
  }

}

final case class GetDataRoomParticipantsParams(
  dataRoomWorkflowId: DataRoomWorkflowId
) extends DataRoomPortalPermissionCheckParams

object GetDataRoomParticipantsParams {
  given Codec.AsObject[GetDataRoomParticipantsParams] = deriveCodecWithDefaults
}

final case class GetDataRoomParticipantsResponse(
  participants: Map[UserId, (DataRoomRole, UserInfo)]
)

object GetDataRoomParticipantsResponse {
  given Codec.AsObject[GetDataRoomParticipantsResponse] = deriveCodecWithDefaults
}

final case class GetDataRoomActivityLogParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  fromTimestamp: Option[Instant] = None,
  toTimestamp: Option[Instant] = None,
  activityActorId: Option[UserId] = None,
  activityActorRole: Option[String] = None,
  activityTypes: Set[DataRoomActivityType] = Set(DataRoomActivityType.AllActivities)
) extends DataRoomRoleCheckParams
    with DataRoomPremiumPlanCheckParams

object GetDataRoomActivityLogParams {

  given Codec.AsObject[GetDataRoomActivityLogParams] = deriveCodecWithDefaults

  def fromQueryRange(
    dataRoomWorkflowId: DataRoomWorkflowId,
    queryTimeRange: QueryTimeRange,
    activityType: DataRoomActivityType,
    queryUserRange: QueryUserRange,
    queryUserId: Option[UserId]
  ): GetDataRoomActivityLogParams = {

    val (fromTimestamp, toTimestamp) = QueryTimeRange.toTimestampRange(queryTimeRange)

    val (actorId, actorRole) = QueryUserRange.toUserParams(queryUserRange, queryUserId)

    GetDataRoomActivityLogParams(
      dataRoomWorkflowId,
      fromTimestamp = fromTimestamp,
      toTimestamp = toTimestamp,
      activityTypes = Set(activityType),
      activityActorId = actorId,
      activityActorRole = actorRole
    )
  }

}

final case class FileInfo(
  fileName: String,
  isDeleted: Boolean
)

object FileInfo {
  given Codec.AsObject[FileInfo] = deriveCodecWithDefaults
}

final case class GetDataRoomActivityLogResponse(
  activities: Seq[DataRoomActivity],
  fileMap: Map[FileId, String]
)

object GetDataRoomActivityLogResponse {
  given Codec.AsObject[GetDataRoomActivityLogResponse] = deriveCodecWithDefaults
}

final case class GetFileActivityLogParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  activityTypes: Set[DataRoomFileActivityType],
  actorOpt: Option[UserId] = None,
  timeRangeOpt: Option[(Instant, Instant)] = None,
  continuationOpt: Option[Array[Byte]] = None,
  limitOpt: Option[Int] = None
) extends DataRoomRoleCheckParams
    with DataRoomPremiumPlanCheckParams derives CanEqual

object GetFileActivityLogParams {
  given Codec.AsObject[GetFileActivityLogParams] = deriveCodecWithDefaults
}

final case class GetFileActivityLogResponse(
  activities: Seq[DataRoomFileActivityInfo],
  fileMap: Map[FileId, FileInfo],
  continuationOpt: Option[Array[Byte]]
)

object GetFileActivityLogResponse {
  given Codec.AsObject[GetFileActivityLogResponse] = deriveCodecWithDefaults
}

sealed trait DataRoomFileFolderActivityInfo extends DataRoomGenericActivityInfo derives CanEqual {
  def activityName: String

  def actor: UserId

  def actorIp: Option[String]

  def timestamp: Option[Instant]

  def activityType: DataRoomFileActivityType

  def description: String

  def fileIdOpt: Option[FileId]
}

object DataRoomFileFolderActivityInfo {
  given Codec.AsObject[DataRoomFileFolderActivityInfo] = deriveCodecWithDefaults

  final case class DataRoomFileActivityInfo(
    fileId: FileId,
    versionIndex: Int,
    actor: UserId,
    timestamp: Option[Instant],
    actorIp: Option[String],
    activityType: DataRoomFileActivityType,
    activityName: String,
    description: String,
    originalFileIdOpt: Option[FileId],
    newFileNameOpt: Option[String]
  ) extends DataRoomFileFolderActivityInfo {
    override def fileIdOpt: Option[FileId] = Some(fileId)
  }

  object DataRoomFileActivityInfo {
    given Codec.AsObject[DataRoomFileActivityInfo] = deriveCodecWithDefaults
  }

}

final case class FolderBasicInfo(
  folderName: String,
  isDeleted: Boolean
)

object FolderBasicInfo {
  given Codec.AsObject[FolderBasicInfo] = deriveCodecWithDefaults
}

/** Get optional thumbnail URL. Can be None if unable to generate a thumbnail
  *
  * @param dataRoomWorkflowId
  *   Id of the target data room.
  * @param fileId
  *   Id of the target file.
  */
final case class GetThumbnailUrlParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  fileId: FileId
) extends DataRoomAssetCheckParams {
  override def fileIds = Set(fileId)

  override def folderIds = Set.empty
}

object GetThumbnailUrlParams {
  given Codec.AsObject[GetThumbnailUrlParams] = deriveCodecWithDefaults
}

/** Response from getting optional thumbnail URL. Can be None if unable to generate a thumbnail
  *
  * @param urlOpt
  *   Optional thumbnail URL.
  */
final case class GetThumbnailUrlResponse(
  urlOpt: Option[String]
)

object GetThumbnailUrlResponse {
  given Codec.AsObject[GetThumbnailUrlResponse] = deriveCodecWithDefaults
}

final case class ModifyDataRoomAssetPermissionParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  updatedUserMap: Map[UserId, AssetPermissionChanges],
  updatedGroupMap: Map[DataRoomGroupId, AssetPermissionChanges]
) extends DataRoomJoinedUserCheckParams
    with DataRoomInvitedOrJoinedMembersCheckParams
    with DataRoomGroupCheckParams
    with DataRoomAssetCheckParams {

  override def userIds = updatedUserMap.keySet

  override def groupIds = updatedGroupMap.keySet

  override def folderIds = {
    updatedUserMap.values.flatMap(p => p.folderPermissions.keySet ++ p.recursivePermissions.keySet).toSet ++
      updatedGroupMap.values.flatMap(p => p.folderPermissions.keySet ++ p.recursivePermissions.keySet).toSet
  }

  override def fileIds = {
    updatedUserMap.values.flatMap(_.filePermissions.keySet).toSet ++
      updatedGroupMap.values.flatMap(_.filePermissions.keySet).toSet
  }

}

object ModifyDataRoomAssetPermissionParams {
  given Codec.AsObject[ModifyDataRoomAssetPermissionParams] = deriveCodecWithDefaults
}

/** Data needed to render email template
  *
  * @param invitee
  *   Invitee user id, for [Invitee Name] and [Invitee Email]
  * @param invitedAt
  *   Sent invitation timestamp, for [Invitation Date]
  */
final case class GetEmailTemplateRenderData(
  inviter: Option[UserId] = None,
  invitee: Option[UserId] = None,
  invitedAt: Option[Instant] = None
)

object GetEmailTemplateRenderData {
  given Codec.AsObject[GetEmailTemplateRenderData] = deriveCodecWithDefaults
}

/** Return default/customized email template of data room
  *
  * @param isRendered
  *   `false`` to return raw email template (no pre-filled variables), which is displayed in Edit email templates
  *   settings. `true` to return rendered email template, users can preview these rendered templates when sending
  *   invitation/reminder.
  */
final case class GetEmailTemplateParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  templateType: DataRoomEmailTemplateType,
  isRendered: Boolean = false,
  renderDataOpt: Option[GetEmailTemplateRenderData] = None
) extends DataRoomJoinedUserCheckParams derives CanEqual

object GetEmailTemplateParams {
  given Codec.AsObject[GetEmailTemplateParams] = deriveCodecWithDefaults
}

final case class GetEmailTemplateResponse(
  template: DataRoomEditableEmailTemplate
)

object GetEmailTemplateResponse {
  given Codec.AsObject[GetEmailTemplateResponse] = deriveCodecWithDefaults
}

final case class UpdateEmailTemplateParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  template: DataRoomEditableEmailTemplate
) extends DataRoomRoleCheckParams

object UpdateEmailTemplateParams {
  given Codec.AsObject[UpdateEmailTemplateParams] = deriveCodecWithDefaults
}

final case class GetDataRoomEmailSenderDetailsParams(
  dataRoomWorkflowId: DataRoomWorkflowId
) extends DataRoomRoleCheckParams

object GetDataRoomEmailSenderDetailsParams {
  given Codec.AsObject[GetDataRoomEmailSenderDetailsParams] = deriveCodecWithDefaults
}

final case class GetDataRoomEmailSenderDetailsResponse(
  customSenderDisabled: Boolean,
  emailAddressOpt: Option[EmailAddress]
)

object GetDataRoomEmailSenderDetailsResponse {
  given Codec.AsObject[GetDataRoomEmailSenderDetailsResponse] = deriveCodecWithDefaults
}

final case class UpdateDataRoomEmailConfigsParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  customSenderDisabledOpt: Option[Boolean],
  invitationEmailDisabledOpt: Option[Boolean]
) extends DataRoomRoleCheckParams

object UpdateDataRoomEmailConfigsParams {
  given Codec.AsObject[UpdateDataRoomEmailConfigsParams] = deriveCodecWithDefaults
}

final case class UpdateDataRoomWatermarkExceptionFilesParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  addWatermarkExceptionFiles: Set[FileId],
  removeWatermarkExceptionFiles: Set[FileId]
) extends DataRoomRoleCheckParams

object UpdateDataRoomWatermarkExceptionFilesParams {
  given Codec.AsObject[UpdateDataRoomWatermarkExceptionFilesParams] = deriveCodecWithDefaults
}

final case class SetBillingFolderParams(
  folderId: FolderId
) extends DataRoomPortalPermissionCheckParams

object SetBillingFolderParams {
  given Codec.AsObject[SetBillingFolderParams] = deriveCodecWithDefaults
}

final case class GetBillingFolderParams() extends DataRoomPortalPermissionCheckParams

object GetBillingFolderParams {
  given Codec.AsObject[GetBillingFolderParams] = deriveCodecWithDefaults
}

final case class GetBillingFolderResponse(folderId: Option[FolderId])

object GetBillingFolderResponse {
  given Codec.AsObject[GetBillingFolderResponse] = deriveCodecWithDefaults
}

final case class SyncBillingReportParams(
  lastMonthOnly: Boolean
) extends DataRoomPortalPermissionCheckParams

object SyncBillingReportParams {
  given Codec.AsObject[SyncBillingReportParams] = deriveCodecWithDefaults
}

final case class SyncBillingReportResponse(
  fileId: FileId
)

object SyncBillingReportResponse {
  given Codec.AsObject[SyncBillingReportResponse] = deriveCodecWithDefaults
}

final case class SetDataRoomPointsOfContactParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  contactUserIds: Set[UserId]
) extends DataRoomRoleCheckParams

object SetDataRoomPointsOfContactParams {
  given Codec.AsObject[SetDataRoomPointsOfContactParams] = deriveCodecWithDefaults
}

final case class UpdateDataRoomTermsOfAccessCopyParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  title: String,
  body: String,
  cta: String
) extends DataRoomPortalPermissionCheckParams

object UpdateDataRoomTermsOfAccessCopyParams {
  given Codec.AsObject[UpdateDataRoomTermsOfAccessCopyParams] = deriveCodecWithDefaults
}

final case class GetSharableLinkConfigParams(
  dataRoomWorkflowId: DataRoomWorkflowId
) extends DataRoomRoleCheckParams

object GetSharableLinkConfigParams {
  given Codec.AsObject[GetSharableLinkConfigParams] = deriveCodecWithDefaults
}

final case class GetSharableLinkConfigResponse(
  isEnterpriseLoginEnabled: Boolean,
  isSharableLinkEnabled: Boolean,
  ssoProviderName: Option[String]
)

object GetSharableLinkConfigResponse {
  given Codec.AsObject[GetSharableLinkConfigResponse] = deriveCodecWithDefaults
}

sealed trait DataRoomSearchResult derives CanEqual {
  def itemId: RadixId

  def name: String

  def nameWithHighlights: TextWithHighlights

  def relevanceScore: Double

  def createdUserInfo: UserInfo

  def lastModifiedTime: Option[Instant]
}

object DataRoomSearchResult {
  given Codec.AsObject[DataRoomSearchResult] = deriveCodecWithDefaultsAndTypename
}

final case class DataRoomFileSearchResult(
  itemId: FileId,
  nameWithHighlights: TextWithHighlights,
  contentSnippetOpt: Option[TextWithHighlights],
  pageIndex: Option[Int],
  parentFolderName: String,
  createdUserInfo: UserInfo,
  lastModifiedTime: Option[Instant],
  relevanceScore: Double
) extends DataRoomSearchResult {
  def name: String = nameWithHighlights.content
}

final case class DataRoomFolderSearchResult(
  itemId: FolderId,
  nameWithHighlights: TextWithHighlights,
  createdUserInfo: UserInfo,
  lastModifiedTime: Option[Instant],
  relevanceScore: Double
) extends DataRoomSearchResult {
  def name: String = nameWithHighlights.content
}

final case class QuickSearchDataRoomParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  query: String
) extends DataRoomJoinedUserCheckParams

object QuickSearchDataRoomParams {
  given Codec.AsObject[QuickSearchDataRoomParams] = deriveCodecWithDefaults
}

final case class QuickSearchDataRoomResponse(
  searchId: DataRoomSearchId,
  results: List[DataRoomSearchResult]
)

object QuickSearchDataRoomResponse {
  given Codec.AsObject[QuickSearchDataRoomResponse] = deriveCodecWithDefaults
}

final case class FullSearchDataRoomParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  isFileSearch: Boolean,
  query: String,
  limit: Int
) extends DataRoomJoinedUserCheckParams

object FullSearchDataRoomParams {
  given Codec.AsObject[FullSearchDataRoomParams] = deriveCodecWithDefaults
}

final case class FullSearchDataRoomResponse(
  searchId: DataRoomSearchId,
  results: List[DataRoomSearchResult]
)

object FullSearchDataRoomResponse {
  given Codec.AsObject[FullSearchDataRoomResponse] = deriveCodecWithDefaults
}

final case class RestoreDataRoomFilesParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  fileIds: Set[FileId]
) extends DataRoomRoleCheckParams
    with DataRoomAssetCheckParams {
  override def folderIds = Set.empty
}

object RestoreDataRoomFilesParams {
  given Codec.AsObject[RestoreDataRoomFilesParams] = deriveCodecWithDefaults
}

final case class RestoreDataRoomFilesResponse(
  restoredFiles: Set[FileId]
)

object RestoreDataRoomFilesResponse {
  given Codec.AsObject[RestoreDataRoomFilesResponse] = deriveCodecWithDefaults
}

final case class AddPortalUserToDataRoomParams(
  dataRoomWorkflowId: DataRoomWorkflowId
) extends DataRoomPortalPermissionCheckParams

object AddPortalUserToDataRoomParams {
  given Codec.AsObject[AddPortalUserToDataRoomParams] = deriveCodecWithDefaults
}

final case class RemovePortalUserFromDataRoomParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  doNotNotifyByEmail: Boolean = false
) extends DataRoomPortalPermissionCheckParams

object RemovePortalUserFromDataRoomParams {
  given Codec.AsObject[RemovePortalUserFromDataRoomParams] = deriveCodecWithDefaults
}

final case class InternalStartDataRoomSimulatorResponse(
  dataRoomWorkflowId: DataRoomWorkflowId
)

object InternalStartDataRoomSimulatorResponse {
  given Codec.AsObject[InternalStartDataRoomSimulatorResponse] = deriveCodecWithDefaults
}

final case class StartDataRoomSimulatorBackdoorParams(
  email: String,
  token: String
)

object StartDataRoomSimulatorBackdoorParams {
  given Codec.AsObject[StartDataRoomSimulatorBackdoorParams] = deriveCodecWithDefaults
}

final case class StartDataRoomSimulatorBackdoorResponse(
  dataRoomWorkflowId: DataRoomWorkflowId,
  link: String
)

object StartDataRoomSimulatorBackdoorResponse {
  given Codec.AsObject[StartDataRoomSimulatorBackdoorResponse] = deriveCodecWithDefaults
}

final case class DataRoomSimulatorSession(
  dataRoomWorkflowId: DataRoomWorkflowId,
  dataRoomName: String,
  createdAt: Option[Instant],
  progress: DataRoomSimulatorProgress,
  logoUrl: String,
  percentageProgress: Int
)

object DataRoomSimulatorSession {
  given Codec.AsObject[DataRoomSimulatorSession] = deriveCodecWithDefaults
}

final case class GetDataRoomSimulatorDashboardResponse(
  mySessions: List[DataRoomSimulatorSession]
)

object GetDataRoomSimulatorDashboardResponse {
  given Codec.AsObject[GetDataRoomSimulatorDashboardResponse] = deriveCodecWithDefaults
}

final case class DataRoomSingleFileVersionDownloadRequest(
  dataRoomWorkflowId: DataRoomWorkflowId,
  request: SingleFileVersionDownloadRequest
) extends DataRoomAssetCheckParams {
  override def fileIds = Set(request.fileId)

  override def folderIds = Set.empty
}

object DataRoomSingleFileVersionDownloadRequest {
  given Codec.AsObject[DataRoomSingleFileVersionDownloadRequest] = deriveCodecWithDefaults
}

final case class DataRoomUserActivitySession(
  session: Instant,
  activityType: DmsTrackingActivityType,
  durationOpt: Option[Duration],
  userId: UserId,
  versionIndex: Int
)

object DataRoomUserActivitySession {
  given Codec.AsObject[DataRoomUserActivitySession] = deriveCodecWithDefaults
}

final case class DataRoomFileInsight(
  fileId: FileId,
  viewCount: Long,
  downloadCount: Long,
  lastView: Option[Instant],
  lastDownload: Option[Instant],
  viewDuration: Option[Duration],
  fileName: String,
  isDeleted: Boolean,
  lastSessions: List[DataRoomUserActivitySession]
)

given Codec.AsObject[DataRoomFileInsight] = deriveCodecWithDefaults

final case class DataRoomGetFileWithUsersInsightsParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  fileId: FileId,
  startOpt: Option[Instant],
  endOpt: Option[Instant],
  expandedUsers: Set[UserId]
) extends DataRoomAssetCheckParams
    with DataRoomRoleCheckParams
    with DataRoomPremiumPlanCheckParams derives CanEqual {
  override def fileIds: Set[FileId] = Set(fileId)

  override def folderIds: Set[FolderId] = Set.empty
}

object DataRoomGetFileWithUsersInsightsParams {

  given Codec.AsObject[DataRoomGetFileWithUsersInsightsParams] = deriveCodecWithDefaults
}

final case class DataRoomGetFileWithUsersInsightsResponse(
  usersInsight: List[DataRoomGetFileWithUsersInsightsResponse.UserInsight]
)

object DataRoomGetFileWithUsersInsightsResponse {

  final case class UserInsight(
    userId: UserId,
    viewCount: Long,
    downloadCount: Long,
    lastView: Option[Instant],
    lastDownload: Option[Instant],
    viewDuration: Option[Duration],
    lastSessions: List[DataRoomUserActivitySession]
  )

  object UserInsight {
    given Codec.AsObject[UserInsight] = deriveCodecWithDefaults
  }

  given Codec.AsObject[DataRoomGetFileWithUsersInsightsResponse] = deriveCodecWithDefaults

}

final case class DataRoomGetUserWithFilesInsightsParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  userId: UserId,
  startOpt: Option[Instant],
  endOpt: Option[Instant],
  expandedFiles: Set[FileId]
) extends DataRoomRoleCheckParams
    with DataRoomPremiumPlanCheckParams
    with DataRoomInvitedOrJoinedMembersCheckParams
    with DataRoomAssetCheckParams {
  override def userIds: Set[UserId] = Set(userId)

  override def fileIds: Set[FileId] = expandedFiles

  override def folderIds: Set[FolderId] = Set.empty

}

object DataRoomGetUserWithFilesInsightsParams {
  given Codec.AsObject[DataRoomGetUserWithFilesInsightsParams] = deriveCodecWithDefaults
}

final case class DataRoomGetGroupWithFilesInsightsParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  groupId: DataRoomGroupId,
  startOpt: Option[Instant],
  endOpt: Option[Instant],
  expandedFiles: Set[FileId]
) extends DataRoomRoleCheckParams
    with DataRoomPremiumPlanCheckParams
    with DataRoomGroupCheckParams
    with DataRoomAssetCheckParams {
  override def groupIds: Set[DataRoomGroupId] = Set(groupId)

  override def fileIds: Set[FileId] = expandedFiles

  override def folderIds: Set[FolderId] = Set.empty

}

object DataRoomGetGroupWithFilesInsightsParams {
  given Codec.AsObject[DataRoomGetGroupWithFilesInsightsParams] = deriveCodecWithDefaults
}

final case class DataRoomGetParticipantsWithFilesInsightsResponse(
  filesInsight: List[DataRoomFileInsight]
)

object DataRoomGetParticipantsWithFilesInsightsResponse {
  given Codec.AsObject[DataRoomGetParticipantsWithFilesInsightsResponse] = deriveCodecWithDefaults
}

final case class DataRoomGetInsightsFileTimelineParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  queryTimeRange: QueryTimeRange,
  zoneId: ZoneId,
  fileId: FileId,
  versionIndex: Int
) extends DataRoomAssetCheckParams
    with DataRoomRoleCheckParams
    with DataRoomPremiumPlanCheckParams derives CanEqual {
  override def fileIds: Set[FileId] = Set(fileId)

  override def folderIds: Set[FolderId] = Set.empty

}

object DataRoomGetInsightsFileTimelineParams {
  given Codec.AsObject[DataRoomGetInsightsFileTimelineParams] = deriveCodecWithDefaults
}

final case class DataRoomGetInsightsFileTimelineResponse(
  viewDownloadPerDay: Map[Instant, DataRoomGetInsightsFileTimelineResponse.ViewDownloadCount]
)

object DataRoomGetInsightsFileTimelineResponse {

  final case class ViewDownloadCount(
    viewCount: Long,
    downloadCount: Long
  )

  object ViewDownloadCount {
    given Codec.AsObject[ViewDownloadCount] = deriveCodecWithDefaults
  }

  given Codec.AsObject[DataRoomGetInsightsFileTimelineResponse] = deriveCodecWithDefaults
}

sealed trait DataRoomGetInsightsParticipantsTimelineParams
    extends DataRoomRoleCheckParams
    with DataRoomPremiumPlanCheckParams {
  def dataRoomWorkflowId: DataRoomWorkflowId

  def rankingCriteria: TopActiveRankingCriteria

  def queryTimeRange: QueryTimeRange

  def zoneId: ZoneId
}

final case class DataRoomGetInsightsUserTimelineParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  rankingCriteria: TopActiveRankingCriteria,
  queryTimeRange: QueryTimeRange,
  zoneId: ZoneId,
  userId: UserId
) extends DataRoomGetInsightsParticipantsTimelineParams
    with DataRoomInvitedOrJoinedMembersCheckParams {
  override def userIds: Set[UserId] = Set(userId)

}

object DataRoomGetInsightsUserTimelineParams {
  given Codec.AsObject[DataRoomGetInsightsUserTimelineParams] = deriveCodecWithDefaults
}

final case class DataRoomGetInsightsGroupTimelineParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  rankingCriteria: TopActiveRankingCriteria,
  queryTimeRange: QueryTimeRange,
  zoneId: ZoneId,
  groupId: DataRoomGroupId
) extends DataRoomGetInsightsParticipantsTimelineParams
    with DataRoomGroupCheckParams {
  override def groupIds: Set[DataRoomGroupId] = Set(groupId)
}

object DataRoomGetInsightsGroupTimelineParams {
  given Codec.AsObject[DataRoomGetInsightsGroupTimelineParams] = deriveCodecWithDefaults
}

final case class DataRoomInsightsParticipantsTimeline(
  viewDownloadPerDay: Map[Instant, Long],
  spentTimePerDay: Map[Instant, Duration],
  cumulativeDistinctFileCountPerDay: Map[Instant, Long]
)

object DataRoomInsightsParticipantsTimeline {
  given Codec.AsObject[DataRoomInsightsParticipantsTimeline] = deriveCodecWithDefaults
}

final case class DataRoomGetInsightsParticipantsTimelineResponse(
  totalFileCount: Long,
  timeline: DataRoomInsightsParticipantsTimeline
)

object DataRoomGetInsightsParticipantsTimelineResponse {
  given Codec.AsObject[DataRoomGetInsightsParticipantsTimelineResponse] = deriveCodecWithDefaults
}

final case class DataRoomGetTopActiveParticipantsParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  rankingCriteria: TopActiveRankingCriteria,
  queryTimeRange: QueryTimeRange,
  zoneId: ZoneId
) extends DataRoomRoleCheckParams
    with DataRoomPremiumPlanCheckParams
    with DataRoomGetInsightsParticipantsTimelineParams

object DataRoomGetTopActiveParticipantsParams {
  given Codec.AsObject[DataRoomGetTopActiveParticipantsParams] = deriveCodecWithDefaults
}

final case class DataRoomAggregation(
  dataRoomVisitCount: Long,
  activity: DataRoomAggregation.Activity,
  access: DataRoomAggregation.Access,
  viewDuration: Duration
)

object DataRoomAggregation {

  final case class Activity(
    viewCount: Long,
    downloadCount: Long
  )

  final case class Access(
    prevDistinctFileCount: Long,
    distinctFileCount: Long
  )

  object Activity {
    given Codec.AsObject[Activity] = deriveCodecWithDefaults
  }

  object Access {
    given Codec.AsObject[Access] = deriveCodecWithDefaults
  }

  given Codec.AsObject[DataRoomAggregation] = deriveCodecWithDefaults

}

final case class DataRoomGetTopActiveUsersResponse(
  totalFileCount: Long,
  users: List[DataRoomGetTopActiveUsersResponse.DataRoomUserAggregation]
)

object DataRoomGetTopActiveUsersResponse {

  final case class DataRoomUserAggregation(
    userId: UserId,
    aggregation: DataRoomAggregation,
    timeline: DataRoomInsightsParticipantsTimeline
  )

  object DataRoomUserAggregation {
    given Codec.AsObject[DataRoomUserAggregation] = deriveCodecWithDefaults
  }

  given Codec.AsObject[DataRoomGetTopActiveUsersResponse] = deriveCodecWithDefaults
}

final case class DataRoomGetTopActiveGroupsResponse(
  totalFileCount: Long,
  groups: List[DataRoomGetTopActiveGroupsResponse.DataRoomGroupAggregation]
)

object DataRoomGetTopActiveGroupsResponse {

  final case class DataRoomGroupAggregation(
    groupId: DataRoomGroupId,
    aggregation: DataRoomAggregation,
    timeline: DataRoomInsightsParticipantsTimeline
  )

  object DataRoomGroupAggregation {
    given Codec.AsObject[DataRoomGroupAggregation] = deriveCodecWithDefaults
  }

  given Codec.AsObject[DataRoomGetTopActiveGroupsResponse] = deriveCodecWithDefaults
}

final case class DataRoomGetTopActiveFilesParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  rankingCriteria: TopActiveFilesRankingCriteria,
  queryTimeRange: QueryTimeRange,
  zoneId: ZoneId
) extends DataRoomRoleCheckParams
    with DataRoomPremiumPlanCheckParams

object DataRoomGetTopActiveFilesParams {
  given Codec.AsObject[DataRoomGetTopActiveFilesParams] = deriveCodecWithDefaults
}

final case class DataRoomGetTopActiveFilesResponse(
  files: List[DataRoomGetTopActiveFilesResponse.DataRoomFileAggregation]
)

object DataRoomGetTopActiveFilesResponse {

  final case class DataRoomFileAggregation(
    fileId: FileId,
    activity: DataRoomAggregation.Activity,
    viewDuration: Duration,
    fileName: String
  )

  object DataRoomFileAggregation {
    given Codec.AsObject[DataRoomFileAggregation] = deriveCodecWithDefaults
  }

  given Codec.AsObject[DataRoomGetTopActiveFilesResponse] = deriveCodecWithDefaults

}

final case class DataRoomGetInsightsNewestParticipantsParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  userIds: Set[UserId],
  zoneId: ZoneId
) extends DataRoomRoleCheckParams
    with DataRoomInvitedOrJoinedMembersCheckParams
    with DataRoomPremiumPlanCheckParams

object DataRoomGetInsightsNewestParticipantsParams {
  given Codec.AsObject[DataRoomGetInsightsNewestParticipantsParams] = deriveCodecWithDefaults
}

final case class DataRoomGetInsightsNewestParticipantsResponse(
  totalFileCount: Long,
  users: Map[UserId, DataRoomGetInsightsNewestParticipantsResponse.ParticipantData]
)

object DataRoomGetInsightsNewestParticipantsResponse {

  final case class ParticipantData(
    distinctFileCount: Long
  )

  object ParticipantData {
    given Codec.AsObject[ParticipantData] = deriveCodecWithDefaults
  }

  given Codec.AsObject[DataRoomGetInsightsNewestParticipantsResponse] = deriveCodecWithDefaults
}

final case class DataRoomGetUserSummaryInsightsParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  userId: UserId,
  startOpt: Option[Instant] = None,
  endOpt: Option[Instant] = None
) extends DataRoomRoleCheckParams
    with DataRoomPremiumPlanCheckParams
    with DataRoomInvitedOrJoinedMembersCheckParams {
  override def userIds: Set[UserId] = Set(userId)
}

object DataRoomGetUserSummaryInsightsParams {
  given Codec.AsObject[DataRoomGetUserSummaryInsightsParams] = deriveCodecWithDefaults
}

final case class DataRoomGetGroupSummaryInsightsParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  groupId: DataRoomGroupId,
  startOpt: Option[Instant] = None,
  endOpt: Option[Instant] = None
) extends DataRoomRoleCheckParams
    with DataRoomPremiumPlanCheckParams
    with DataRoomGroupCheckParams {
  override def groupIds: Set[DataRoomGroupId] = Set(groupId)
}

object DataRoomGetGroupSummaryInsightsParams {
  given Codec.AsObject[DataRoomGetGroupSummaryInsightsParams] = deriveCodecWithDefaults
}

final case class DataRoomGetParticipantsSummaryInsightsResponse(
  totalFileCount: Long,
  visitCount: Long,
  lastVisitOpt: Option[Instant],
  accessedFileCount: Long,
  timeSpent: Duration
)

object DataRoomGetParticipantsSummaryInsightsResponse {
  given Codec.AsObject[DataRoomGetParticipantsSummaryInsightsResponse] = deriveCodecWithDefaults
}

final case class DataRoomGetFileSummaryInsightsParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  fileId: FileId,
  startOpt: Option[Instant] = None,
  endOpt: Option[Instant] = None
) extends DataRoomRoleCheckParams
    with DataRoomPremiumPlanCheckParams
    with DataRoomAssetCheckParams {
  override def fileIds: Set[FileId] = Set(fileId)

  override def folderIds: Set[FolderId] = Set.empty

}

object DataRoomGetFileSummaryInsightsParams {
  given Codec.AsObject[DataRoomGetFileSummaryInsightsParams] = deriveCodecWithDefaults
}

final case class DataRoomGetFileSummaryInsightsResponse(
  fileSummaryInsight: List[DataRoomGetFileSummaryInsightsResponse.FileVersionSummaryInsights]
)

object DataRoomGetFileSummaryInsightsResponse {

  final case class FileVersionSummaryInsights(
    versionIndex: Int,
    uniqueViews: Long,
    uniqueDownloads: Long,
    seenBy: Long,
    totalViewDuration: Duration
  )

  object FileVersionSummaryInsights {
    given Codec.AsObject[FileVersionSummaryInsights] = deriveCodecWithDefaults
  }

  given Codec.AsObject[DataRoomGetFileSummaryInsightsResponse] = deriveCodecWithDefaults
}

final case class DataRoomGetFilePageTotalViewTimeParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  fileId: FileId,
  versionIndex: Int,
  userIdsOpt: Option[Seq[UserId]]
) extends DataRoomRoleCheckParams
    with DataRoomAssetCheckParams
    with DataRoomPremiumPlanCheckParams
    with DataRoomInvitedOrJoinedMembersCheckParams {
  override def userIds: Set[UserId] = userIdsOpt.map(_.toSet).getOrElse(Set.empty)

  override def fileIds: Set[FileId] = Set(fileId)

  override def folderIds: Set[FolderId] = Set.empty

}

object DataRoomGetFilePageTotalViewTimeParams {
  given Codec.AsObject[DataRoomGetFilePageTotalViewTimeParams] = deriveCodecWithDefaults
}

final case class DataRoomGetFilePageTotalViewTimeResponse(
  pageViewTimeMap: Map[Int, Duration]
)

object DataRoomGetFilePageTotalViewTimeResponse {
  given Codec.AsObject[DataRoomGetFilePageTotalViewTimeResponse] = deriveCodecWithDefaults
}

final case class DataRoomGetFilePageUserViewPercentageParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  fileId: FileId,
  versionIndex: Int
) extends DataRoomRoleCheckParams
    with DataRoomAssetCheckParams
    with DataRoomPremiumPlanCheckParams {
  override def fileIds: Set[FileId] = Set(fileId)

  override def folderIds: Set[FolderId] = Set.empty

}

object DataRoomGetFilePageUserViewPercentageParams {
  given Codec.AsObject[DataRoomGetFilePageUserViewPercentageParams] = deriveCodecWithDefaults
}

final case class DataRoomGetFilePageUserViewPercentageResponse(
  pageUserViewPercentage: Map[Int, Double]
)

object DataRoomGetFilePageUserViewPercentageResponse {
  given Codec.AsObject[DataRoomGetFilePageUserViewPercentageResponse] = deriveCodecWithDefaults
}

final case class DataRoomGetParticipantsInsightsParams(
  dataRoomWorkflowId: DataRoomWorkflowId
) extends DataRoomRoleCheckParams
    with DataRoomPremiumPlanCheckParams

object DataRoomGetParticipantsInsightsParams {
  given Codec.AsObject[DataRoomGetParticipantsInsightsParams] = deriveCodecWithDefaults
}

final case class DataRoomGetUsersInsightsResponse(
  totalFileCount: Long,
  users: List[DataRoomGetUsersInsightsResponse.UserInsights]
)

object DataRoomGetUsersInsightsResponse {

  final case class UserInsights(
    userId: UserId,
    accessedFileCount: Long,
    visitCount: Long,
    lastView: Option[Instant],
    lastDownload: Option[Instant],
    viewDuration: Option[Duration]
  )

  object UserInsights {
    given Codec.AsObject[UserInsights] = deriveCodecWithDefaults
  }

  given Codec.AsObject[DataRoomGetUsersInsightsResponse] = deriveCodecWithDefaults

}

final case class DataRoomGetGroupsInsightsResponse(
  totalFileCount: Long,
  groups: List[DataRoomGetGroupsInsightsResponse.GroupInsights]
)

object DataRoomGetGroupsInsightsResponse {

  final case class GroupInsights(
    groupId: DataRoomGroupId,
    accessedFileCount: Long,
    visitCount: Long,
    lastView: Option[Instant],
    lastDownload: Option[Instant],
    viewDuration: Option[Duration]
  )

  object GroupInsights {
    given Codec.AsObject[GroupInsights] = deriveCodecWithDefaults
  }

  given Codec.AsObject[DataRoomGetGroupsInsightsResponse] = deriveCodecWithDefaults
}

final case class DataRoomGetFilesInsightsParams(
  dataRoomWorkflowId: DataRoomWorkflowId
) extends DataRoomRoleCheckParams
    with DataRoomPremiumPlanCheckParams

object DataRoomGetFilesInsightsParams {
  given Codec.AsObject[DataRoomGetFilesInsightsParams] = deriveCodecWithDefaults
}

final case class DataRoomGetFilesInsightsResponse(
  files: List[DataRoomGetFilesInsightsResponse.FileInsights]
)

object DataRoomGetFilesInsightsResponse {

  final case class FileInsights(
    fileId: FileId,
    fileName: String,
    seenBy: Long,
    viewCount: Long,
    downloadCount: Long,
    totalViewDuration: Duration,
    isDeleted: Boolean
  )

  object FileInsights {
    given Codec.AsObject[FileInsights] = deriveCodecWithDefaults
  }

  given Codec.AsObject[DataRoomGetFilesInsightsResponse] = deriveCodecWithDefaults

}

final case class GetToaCustomizationParams(
  dataRoomWorkflowId: DataRoomWorkflowId
) extends DataRoomEmptyEndpointValidationParams derives CanEqual

object GetToaCustomizationParams {
  given Codec.AsObject[GetToaCustomizationParams] = deriveCodecWithDefaults
}

final case class GetToaCustomizationResponse(
  title: String,
  body: String,
  cta: String
)

object GetToaCustomizationResponse {
  given Codec.AsObject[GetToaCustomizationResponse] = deriveCodecWithDefaults
}

final case class GetDataRoomFileFolderPermissionParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  fileOrFolderId: Either[FileId, FolderId]
) extends DataRoomJoinedUserCheckParams
    with DataRoomAssetCheckParams derives CanEqual {

  override def fileIds: Set[FileId] = fileOrFolderId.fold(
    fileId => Set(fileId),
    _ => Set.empty
  )

  override def folderIds: Set[FolderId] = fileOrFolderId.fold(
    _ => Set.empty,
    folderId => Set(folderId)
  )

}

object GetDataRoomFileFolderPermissionParams {
  given Codec.AsObject[GetDataRoomFileFolderPermissionParams] = deriveCodecWithDefaults
}

final case class GetDataRoomFileFolderPermissionResponse(
  permissionDetail: DataRoomFileFolderPermissionDetail
) derives CanEqual

object GetDataRoomFileFolderPermissionResponse {
  given Codec.AsObject[GetDataRoomFileFolderPermissionResponse] = deriveCodecWithDefaults
}

final case class GetDataRoomDashboardParams(
  isArchived: Boolean,
  includeInvited: Boolean
) extends EmptyEndpointValidationParams derives CanEqual

object GetDataRoomDashboardParams {
  given Codec.AsObject[GetDataRoomDashboardParams] = deriveCodecWithDefaults
}

final case class GetDataRoomDashboardResponse(
  dataRoomDashboard: Seq[DataRoomDashboardData],
  pendingInvitationCount: Int,
  participatedEntities: Seq[DataRoomEntityModel],
  participatedOrgBillingModels: Seq[DataRoomOrgBillingModel],
  dataRoomUserInfo: DataRoomUserData.UserInfo
) derives CanEqual

object GetDataRoomDashboardResponse {
  given Codec.AsObject[GetDataRoomDashboardResponse] = deriveCodecWithDefaults
}

final case class DataRoomDashboardWhitelabel(
  environmentDashboardWhitelabel: DashboardWhitelabelData
) derives CanEqual

object DataRoomDashboardWhitelabel {
  given Codec.AsObject[DataRoomDashboardWhitelabel] = deriveCodecWithDefaults
}

final case class GetDataRoomActiveInOrgParams(
  includeInvited: Boolean
) extends EmptyEndpointValidationParams derives CanEqual

object GetDataRoomActiveInOrgParams {
  given Codec.AsObject[GetDataRoomActiveInOrgParams] = deriveCodecWithDefaults
}

final case class GetDataRoomActiveInOrgResponse(
  dataRooms: Seq[DataRoomActiveInOrg]
) derives CanEqual

object GetDataRoomActiveInOrgResponse {

  final case class DataRoomActiveInOrg(
    workflowId: DataRoomWorkflowId,
    name: String,
    dataRoomPlan: DataRoomPlan,
    dataRoomIconUrl: Option[String],
    userInvitedOpt: Option[InvitedUserData],
    isShowingHomePage: Boolean
  )

  given Codec.AsObject[GetDataRoomActiveInOrgResponse] = deriveCodecWithDefaults

  given Codec.AsObject[DataRoomActiveInOrg] = deriveCodecWithDefaults

}

final case class GetDataRoomDetailParams(
  dataRoomWorkflowId: DataRoomWorkflowId
) extends EmptyEndpointValidationParams derives CanEqual

object GetDataRoomDetailParams {
  given Codec.AsObject[GetDataRoomDetailParams] = deriveCodecWithDefaults
}

final case class GetDataRoomDetailResponse(
  dataRoomDetail: DataRoomData
) derives CanEqual

object GetDataRoomDetailResponse {
  given Codec.AsObject[GetDataRoomDetailResponse] = deriveCodecWithDefaults
}

final case class DataRoomCountForRegion(
  region: MultiRegionCode,
  state: DataRoomParticipantState,
  count: Long
) derives CanEqual

object DataRoomCountForRegion {
  given Codec.AsObject[DataRoomCountForRegion] = deriveCodecWithDefaults
}

final case class GetDataRoomCountForOtherRegionsResponse(
  regions: List[DataRoomCountForRegion],
  links: Map[MultiRegionCode, String]
)

object GetDataRoomCountForOtherRegionsResponse {
  given Codec.AsObject[GetDataRoomCountForOtherRegionsResponse] = deriveCodecWithDefaults
}

final case class EnableDataRoomWebhookParams(
  dataRoomWorkflowId: DataRoomWorkflowId,
  enableWebhook: Boolean
) extends DataRoomPortalPermissionCheckParams derives CirceCodec.WithDefaults

final case class BindToEnvironmentParams(
  dataRoomWorkflowIds: Set[DataRoomWorkflowId],
  environmentId: EnvironmentId
) extends DataRoomPortalPermissionCheckParams derives CirceCodec.WithDefaults

final case class GetEnvironmentDataRoomsParams(
  environmentId: EnvironmentId
) extends DataRoomPortalPermissionCheckParams derives CirceCodec.WithDefaults

final case class GetEnvironmentDataRoomsResponse(
  dataRooms: List[GetEnvironmentDataRoomsResponse.DataRoomBasicInfo],
  entityNameMap: Map[EntityId, String]
) derives CirceCodec.WithDefaults

object GetEnvironmentDataRoomsResponse {

  final case class DataRoomBasicInfo(
    dataRoomWorkflowId: DataRoomWorkflowId,
    entityId: EntityId,
    name: String,
    createdAt: Option[Instant]
  ) derives CirceCodec.WithDefaults

}

final case class RemoveEnvironmentBindingParams(
  environmentId: EnvironmentId,
  dataRoomWorkflowId: DataRoomWorkflowId
) extends DataRoomPortalPermissionCheckParams derives CirceCodec.WithDefaults

final case class StartMarketingDataRoomResponse(
  dataRoomWorkflowId: DataRoomWorkflowId,
  dataRoomName: String,
  shareableLinkId: ProtectedLinkId
)

final case class DataRoomSearchIndexStateInfo(
  entityId: EntityId,
  dataRoomWorkflowId: DataRoomWorkflowId,
  dataRoomName: String,
  showSearch: Boolean,
  searchIndexState: SearchIndexState
) derives CirceCodec.WithDefaults

final case class GetAllDataRoomsSearchIndexStateParams() extends DataRoomPortalPermissionCheckParams
    derives CirceCodec.WithDefaults

final case class GetAllDataRoomsSearchIndexStateResponse(
  data: List[DataRoomSearchIndexStateInfo]
) derives CirceCodec.WithDefaults

final case class ReindexDataRoomsParams(
  indexFailedOnly: Boolean
) extends DataRoomPortalPermissionCheckParams derives CirceCodec.WithDefaults

final case class GetAllDataRoomMigrationStatusParams(
  migrationType: MigrationType
) extends DataRoomPortalPermissionCheckParams derives CirceCodec.WithDefaults

final case class GetAllDataRoomMigrationStatusResponse(
  dataRooms: List[DataRoomMigrationInfo]
) derives CirceCodec.WithDefaults

object GetAllDataRoomMigrationStatusResponse {

  final case class DataRoomMigrationInfo(
    dataRoomWorkflowId: DataRoomWorkflowId,
    migrationStatus: MigrationStatus,
    lastUpdatedOpt: Option[Instant]
  ) derives CirceCodec.WithDefaults

}
