package build.modules.dataroom

import build_.build.util_.*
import build.modules.{bifrost, greylin, heimdall, brienne, integplatform}
import build.platform.stargazerCore

import mill.*
import mill.scalalib.*
import anduin.build.*
import anduin.mill.*

object `package` extends Module {

  object dataroomModel extends StargazerModelCrossPlatformModule {

    object jvm extends JvmModelModule with SharedModule {

      override def moduleDeps = super.moduleDeps ++ Seq(build.gondor.gondorModel.jvm)

    }

    object js extends JsModelModule with SharedModule {

      override def moduleDeps = super.moduleDeps ++ Seq(build.gondor.gondorModel.js)

    }

    trait SharedModule extends ScalaModule {
      override def scalacOptions = super.scalacOptions() ++ Seq("-Xmax-inlines:128")
    }

  }

  object dataroomCore extends AnduinCrossPlatformModule {

    object jvm extends JvmModule with SharedModule {

      override def moduleDeps =
        super.moduleDeps ++ Seq(
          build.gondor.gondorCore.jvm,
          dataroomModel.jvm,
          brienne.brienneModel,
          integplatform.integplatformModel.jvm
        )

    }

    object js extends JsModule with SharedModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        build.gondor.gondorCore.js,
        dataroomModel.js,
        integplatform.integplatformModel.js
      )

    }

    trait SharedModule extends ScalaModule {
      override def scalacOptions = super.scalacOptions() ++ Seq("-Xmax-inlines:128")
    }

  }

  object dataroomIntegration extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {
      override def moduleDeps = super.moduleDeps ++ Seq(dataroomCore.jvm, integplatform.integplatformModel.jvm)
    }

    object js extends JsModule {
      override def moduleDeps = super.moduleDeps ++ Seq(dataroomCore.js, build.gondor.gondorModel.js)
    }

  }

  object dataroom extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        dataroomIntegration.jvm,
        build.gondor.gondorCore.jvm,
        greylin.greylinCore,
        bifrost.jvm,
        brienne.brienneCore.jvm,
        integplatform.integplatformCore.jvm
      )

      object it extends AnduinZioTests with AnduinIntegTests {
        override def moduleDeps = super.moduleDeps ++ Seq(build.gondor.gondorTest, heimdall.heimdall.jvm)
      }

    }

    object js extends JsModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        build.gondor.gondorCore.js,
        bifrost.js,
        dataroomCore.js
      )

    }

  }

  object dataroomApp extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {
      override def moduleDeps = super.moduleDeps ++ Seq(dataroom.jvm)
    }

    object js extends JsModule with AnduinWebClientModule {

      override def webModule = AnduinWebModules.DataRoom

      override def moduleDeps = super.moduleDeps ++ Seq(dataroom.js)

      override def mainClass = Some("anduin.dataroom.client.DataRoomMainApp")
    }

  }

}
