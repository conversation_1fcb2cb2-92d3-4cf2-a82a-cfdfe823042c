package build.modules.narya

import build_.modules.narya.dependency_.Narya
import build_.build.util_.*
import build.modules.{rohan, heimdall, fundsub, signature, graphql}
import build.platform.{stargazer, webModules}

import mill.*
import mill.scalalib.*
import anduin.build.*
import anduin.mill.*
import anduin.mill.jsdeps.*

object `package` extends AnduinCrossPlatformModule {

  object jvm extends JvmModule with SharedModule {
    override def moduleDeps = super.moduleDeps ++ Seq(rohan.rohan)
  }

  object js extends JsModule with SharedModule with AnduinWebClientModule {
    override def webModule = AnduinWebModules.Narya
    override def mainClass = Some("anduin.narya.client.NaryaMainApp")
    override def jsDeps = super.jsDeps() ++ JsDeps(Narya.npmDeps*)

    override def moduleDeps = super.moduleDeps ++ Seq(
      webModules,
      heimdall.heimdall.js,
      stargazer.js,
      build.gondor.gondorCore.js,
      fundsub.fundsubCore.js,
      signature.signatureApp.js,
      graphql.js
    )

  }

  //      trait SharedModule extends AnduinGraphQLGenModule {
  //        override def graphqlSources = Task.Sources(moduleDir / os.up / "shared" / "graphql")
  //        override def graphqlOutputPackage = "anduin.narya.operation"
  //        override def graphqlBaseTrait = "NaryaQuery"
  //        override def mvnDeps = super.mvnDeps() ++ Narya.sharedDeps
  //      }

  trait SharedModule extends ScalaModule {
    override def mvnDeps = super.mvnDeps() ++ Narya.sharedDeps
  }

}
