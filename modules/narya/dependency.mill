package build.modules.narya

import anduin.build.AnduinVersions
import mill.scalalib.*

object Narya {

  lazy val sharedDeps = Seq(
    mvn"com.lihaoyi::geny::${AnduinVersions.geny}",
    mvn"com.lihaoyi::scalatags::${AnduinVersions.scalatags}"
      .exclude("org.scala-js" -> AnduinVersions.j2s("scalajs-dom"))
      .exclude("org.scala-js" -> AnduinVersions.j2sjs("scalajs-dom"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("sourcecode"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("sourcecode"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("geny"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("geny"))
  )

  lazy val npmDeps = Seq(
    "fontfaceobserver" -> AnduinVersions.npm.fontfaceobserver
  )

}