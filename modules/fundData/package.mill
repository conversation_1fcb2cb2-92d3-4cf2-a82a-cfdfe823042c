package build.modules.fundData

import build_.build.util_.*
import build.modules.{bifrost, brienne, dataroom, fundsub, gaia, heimdall, integplatform, investorProfile, signature, sa}
import build.platform.{stargazer, stargazerCore}
import build_.platform.package_.stargazerTest
import mill.*
import anduin.build.*
import anduin.mill.*

object `package` extends Module {

  object fundDataModel extends StargazerModelCrossPlatformModule {

    object jvm extends JvmModelModule {

      override def moduleDeps =
        super.moduleDeps ++ Seq(
          gaia.gaiaModel.jvm,
          stargazer.jvm,
          build.gondor.gondorModel.jvm
        )

    }

    object js extends JsModelModule {
      override def moduleDeps = super.moduleDeps ++ Seq(build.gondor.gondorModel.js, stargazerCore.js)
    }

  }

  object fundDataCore extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        fundDataModel.jvm,
        heimdall.heimdallCore.jvm,
        gaia.gaia.jvm,
        build.gondor.gondorCore.jvm,
        integplatform.integplatformModel.jvm
      )

    }

    object js extends JsModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        fundDataModel.js,
        heimdall.heimdall.js,
        gaia.gaia.js,
        build.gondor.gondorCore.js,
        bifrost.js,
        integplatform.integplatformModel.js
      )

    }

  }

  object fundData extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        fundDataCore.jvm,
        fundsub.fundsub.jvm,
        dataroom.dataroom.jvm,
        signature.signature.jvm,
        build.gondor.gondorCore.jvm,
        stargazer.jvm,
        investorProfile.investorProfileCore.jvm,
        integplatform.integplatformCore.jvm,
        brienne.brienneCore.jvm,
        sa.sa.jvm
      )

      object it extends AnduinZioTests with AnduinIntegTests {
        override def moduleDeps = super.moduleDeps ++ Seq(fundsub.fundsubTest)
      }

    }

    object js extends JsModule {

      override def scalacOptions: T[Seq[String]] = {
        super.scalacOptions() ++ Seq("-explain-cyclic")
      }

      override def moduleDeps = super.moduleDeps ++ Seq(
        build.gondor.gondorCore.js,
        stargazer.js,
        fundDataCore.js,
        fundsub.fundsubCore.js,
        integplatform.integplatformCore.js
      )

      object test extends AnduinScalaJSTests {
        override def moduleDeps = super.moduleDeps ++ Seq(stargazerTest.js)
      }

    }

  }

  object fundDataApp extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {
      override def moduleDeps = super.moduleDeps ++ Seq(fundData.jvm)
    }

    object js extends JsModule with AnduinWebClientModule {
      override def webModule = AnduinWebModules.FundData

      override def moduleDeps = super.moduleDeps ++ Seq(fundData.js)

      override def mainClass = Some("anduin.funddata.client.FundDataMainApp")
    }

  }

}
