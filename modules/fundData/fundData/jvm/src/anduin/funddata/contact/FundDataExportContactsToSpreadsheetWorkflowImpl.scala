// Copyright (C) 2014-2025 Anduin Transactions Inc

package anduin.funddata.contact

import java.time.Duration

import io.circe.Json
import io.circe.syntax.EncoderOps
import zio.{Task, ZIO}

import anduin.batchaction.*
import anduin.batchaction.endpoint.{BatchActionInfo, BatchActionItemStatusInfo}
import anduin.dms.DmsFeature.Public
import anduin.dms.service.FileService
import anduin.dms.tracking.DmsTrackingActivityType
import anduin.funddata.endpoint.contact.{
  BatchExportContactsResp,
  ExportContactsBatchItem,
  ExportContactsBatchItemResp,
  ExportContactsPostExecuteParams
}
import anduin.model.common.user.UserId
import anduin.serverless.functions.ExportCsvFilesToExcelServerless
import anduin.serverless.models.CsvToExcel.{ExportCsvFilesToExcelRequest, SpreadsheetExportItem}
import anduin.serverless.utils.ServerlessUtils
import anduin.service.GeneralServiceException
import anduin.storageservice.common.FileContentOrigin
import anduin.workflow.TemporalWorkflowService
import com.anduin.stargazer.service.api.FileDownloadService
import com.anduin.stargazer.service.file.BatchDownloadRequest
import com.anduin.stargazer.service.utils.ZIOUtils

@zio.temporal.workflowInterface
trait FundDataExportContactsToSpreadsheetWorkflow extends BatchActionWorkflow {}

class FundDataExportContactsToSpreadsheetWorkflowImpl
    extends FundDataExportContactsToSpreadsheetWorkflow
    with BatchActionWorkflowImpl[
      FundDataExportContactsToSpreadsheetItemWorkflow,
      FundDataExportContactsToSpreadsheetActivities
    ] {
  override val activityStartToCloseTimeout: Duration = Duration.ofMinutes(10)
}

object FundDataExportContactsToSpreadsheetWorkflowImpl
    extends BatchActionWorkflowImplCompanion[
      FundDataExportContactsToSpreadsheetWorkflow,
      FundDataExportContactsToSpreadsheetWorkflowImpl
    ] {}

@zio.temporal.workflowInterface
trait FundDataExportContactsToSpreadsheetItemWorkflow extends BatchActionItemWorkflow {}

class FundDataExportContactsToSpreadsheetWorkflowItemImpl
    extends FundDataExportContactsToSpreadsheetItemWorkflow
    with BatchActionItemWorkflowImpl[FundDataExportContactsToSpreadsheetActivities] {}

object FundDataExportContactsToSpreadsheetWorkflowItemImpl
    extends BatchActionItemWorkflowImplCompanion[
      FundDataExportContactsToSpreadsheetItemWorkflow,
      FundDataExportContactsToSpreadsheetWorkflowItemImpl
    ] {}

@zio.temporal.activityInterface(namePrefix = "FundDataExportContactsToSpreadsheet")
trait FundDataExportContactsToSpreadsheetActivities extends BatchActionActivities {}

case class FundDataExportContactsToSpreadsheetActivitiesImpl(
  fundDataContactExportService: FundDataContactExportService,
  fileService: FileService,
  fileDownloadService: FileDownloadService,
  exportCsvFilesToExcelServerless: ExportCsvFilesToExcelServerless,
  override val batchActionService: BatchActionService
)(
  using override val temporalWorkflowService: TemporalWorkflowService
) extends FundDataExportContactsToSpreadsheetActivities
    with BatchActionActivitiesImpl {

  override def processItem(actionType: BatchActionType, data: Json, actor: UserId): Task[Option[Json]] = {
    for {
      batchItem <- ZIOUtils.fromOption(
        data.as[ExportContactsBatchItem].toOption,
        GeneralServiceException(s"Failed to decode $data")
      )
      resp <- fundDataContactExportService.exportContactsBatchInternal(batchItem, actor)
    } yield Option(resp.asJson)
  }

  override def processPostExecute(data: BatchActionInfo): Task[Option[Json]] = {
    for {
      batchItemResps <- ZIO.attempt {
        data.items.flatMap { batchActionItemInfo =>
          batchActionItemInfo.status match {
            case BatchActionItemStatusInfo.Succeeded(_, dataOpt) =>
              dataOpt.fold(Option.empty[ExportContactsBatchItemResp])(inputData =>
                inputData.as[ExportContactsBatchItemResp].toOption
              )
            case _ => Option.empty[ExportContactsBatchItemResp]
          }
        }
      }
      postExecuteParams <- ZIOUtils.fromOption(
        data.postExecuteData.flatMap(_.as[ExportContactsPostExecuteParams].toOption),
        GeneralServiceException(
          s"Failed to decode export investment entities post execute params for ${data.batchActionId}"
        )
      )

      exportItems = prepareContactData(batchItemResps)

      bucket = fileService.s3Service.s3Config.bucket
      folderId <- fileService.createUserTemporaryFolderIfNeeded(actor = data.createdBy)
      outputStorageId = fileService.s3Service.generateRandomStorageId(
        prefixIdOpt = Some(folderId),
        fileName = postExecuteParams.fileName
      )

      _ <- exportCsvFilesToExcelServerless.exportCsvFilesToExcel(
        ExportCsvFilesToExcelRequest(
          exportItems = exportItems,
          outputStorageId = outputStorageId.id,
          s3Access = ServerlessUtils.getS3Access(bucket)
        )
      )
      outputFileId <- fileService.uploadFile(
        parentFolderId = folderId,
        fileName = postExecuteParams.fileName,
        content = FileContentOrigin.FromStorageId(outputStorageId, fileService.s3Service.s3Config.bucket),
        uploader = data.createdBy
      )
      resp <- fileDownloadService.getBatchDownloadData(
        actor = data.createdBy,
        request = BatchDownloadRequest(
          "",
          Seq(),
          Seq(outputFileId),
          DmsTrackingActivityType.Download
        ),
        httpContext = None
      )
    } yield Option(
      BatchExportContactsResp(
        fileUrl = resp.url
      ).asJson
    )
  }

  private def prepareContactData(batchItemResps: Seq[ExportContactsBatchItemResp]) = {

    val contactInfoExportItem = SpreadsheetExportItem(
      headers = Left(
        List(
          List(
            "Email",
            "Prefix",
            "First name",
            "Last name",
            "Suffix",
            "Custom ID",
            "Company",
            "Title",
            "Phone",
            "Number and street",
            "City",
            "State",
            "Country",
            "Zip code",
            "Note"
          )
        )
      ),
      rawDataStorageIds = batchItemResps.map(_.contactInfoFileStorageId.id).toList,
      sheetName = "Contact information"
    )

    val contactRelationshipsExportItem = SpreadsheetExportItem(
      headers = Left(
        List(
          List(
            "Email",
            "Client",
            "Investment Entity"
          )
        )
      ),
      rawDataStorageIds = batchItemResps.map(_.contactRelationshipsFileStorageId.id).toList,
      sheetName = "Contact relationships"
    )
    val ieLevelCommunicationSettingExportItem = SpreadsheetExportItem(
      headers = Left(
        List(
          List(
            "Email",
            "Investment Entity",
            "Profile access",
            "Invitation emails",
            "Document requests"
          )
        )
      ),
      rawDataStorageIds = batchItemResps.map(_.ieLevelCommunicationSettingFileStorageId.id).toList,
      sheetName = "IE-Level Communication Settings"
    )
    val contactCommunicationMatrixExportItem = SpreadsheetExportItem(
      headers = Left(
        List(
          List(
            "Email",
            "Investment Entity",
            "Fund Legal Entity"
          )
            ++ batchItemResps.headOption
              .fold(List.empty[String])(_.firmCommTypeNames)
        )
      ),
      rawDataStorageIds = batchItemResps.map(_.contactCommunicationMatrixFileStorageId.id).toList,
      sheetName = "Fund Communication Matrices"
    )
    List(
      contactInfoExportItem,
      contactRelationshipsExportItem,
      ieLevelCommunicationSettingExportItem,
      contactCommunicationMatrixExportItem
    )
  }

}
