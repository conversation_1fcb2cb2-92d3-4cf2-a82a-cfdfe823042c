// Copyright (C) 2014-2025 Anduin Transactions Inc

package anduin.funddata.contact

import java.time.Instant

import io.circe.syntax.EncoderOps
import sttp.model.MediaType
import zio.temporal.workflow.ZWorkflowStub
import zio.{Task, ZIO}

import anduin.batchaction.{BatchActionFrontendTracking, BatchActionService, BatchActionType}
import anduin.dms.DmsFeature.Public
import anduin.dms.service.FileService
import anduin.dms.tracking.DmsTrackingActivityType
import anduin.documentcontent.csv.CsvUtils
import anduin.fdb.record.{DefaultCluster, FDBRecordDatabase}
import anduin.funddata.communication.{FundDataFirmCommunicationService, FundDataFundLegalEntityCommunicationService}
import anduin.funddata.contact.note.FundDataContactNoteService
import anduin.funddata.endpoint.contact.*
import anduin.funddata.endpoint.contact.FundDataContact.ProfileAccessType
import anduin.funddata.error.FundDataError.FundDataValidationError
import anduin.funddata.firm.FundDataFirmStoreOperations
import anduin.funddata.fund.fundlegalentity.FundLegalEntityOperations
import anduin.funddata.investmententity.InvestmentEntityStoreOperations
import anduin.funddata.investor.FundDataInvestorStoreOperations
import anduin.funddata.note.FundDataNoteUtils
import anduin.funddata.permission.FundDataPermissionService
import anduin.funddata.validation.FundDataValidationUtils
import anduin.id.batchaction.BatchActionId
import anduin.model.common.user.UserId
import anduin.storageservice.common.FileContentOrigin
import anduin.temporal.TemporalEnvironment
import anduin.user.UserService
import anduin.utils.DateTimeUtils
import com.anduin.stargazer.service.api.FileDownloadService
import com.anduin.stargazer.service.utils.ZIOUtils

final case class FundDataContactExportService(
  fundDataPermissionService: FundDataPermissionService,
  fundDataContactService: FundDataContactService,
  fundDataFirmCommunicationService: FundDataFirmCommunicationService,
  fundDataFundLegalEntityCommunicationService: FundDataFundLegalEntityCommunicationService,
  fileService: FileService,
  fundDataContactNoteService: FundDataContactNoteService,
  batchActionService: BatchActionService,
  temporalEnvironment: TemporalEnvironment,
  fileDownloadService: FileDownloadService,
  userService: UserService
) {

  private[funddata] def exportContactsBatchInternal(
    params: ExportContactsBatchItem,
    actor: UserId
  ): Task[ExportContactsBatchItemResp] = {
    val firmId = params.firmId
    val contactIds = params.contactIds
    for {
      _ <- ZIO.logInfo(s"$actor export ${params.contactIds.size} contacts data for batch")
      contacts <- fundDataContactService
        .getFirmContactsInfo(
          params = GetFirmContactsInfoParams(
            firmId = firmId,
            filterByContacts = Some(contactIds)
          ),
          actor = actor
        )
        .map(_.data)

      refinedContacts <- ZIO.foreach(contacts) { contact =>
        for {
          contactRelations <- fundDataContactService.getContactRelation(
            params = GetContactRelationParams(
              firmId = firmId,
              contactId = contact.contactId
            ),
            actor = actor
          )
          contactMatrix <- fundDataContactService
            .getContactMatrix(
              params = GetContactMatrixParams(
                firmId = firmId,
                contactId = contact.contactId
              ),
              actor = actor
            )
            .map(_.contactMatrixValue)
        } yield (
          contact = contact,
          contactInvestmentEntityPermission = contactRelations.investmentEntityRelations,
          contactClientRelations = (contactRelations.contactRelationInfos.investors
            .map {
              _.investorId -> None
            } ++
            contactRelations.contactRelationInfos.investmentEntities.map { relation =>
              relation.investmentEntityId.parent -> Some(relation.investmentEntityId)
            })
            .groupMap(_._1)(_._2)
            .flatMap { case (investorId, investmentEntityIds) =>
              investmentEntityIds.distinct match {
                case None :: Nil =>
                  Seq(
                    (
                      clientId = investorId,
                      investmentEntityId = None
                    )
                  )
                case investmentEntityIds =>
                  investmentEntityIds.flatten.map { investmentEntityIdOpt =>
                    (
                      clientId = investorId,
                      investmentEntityId = Option(investmentEntityIdOpt)
                    )
                  }
              }

            }
            .toSeq,
          contactMatrix = contactMatrix
        )
      }

      clientNameMap <- FundDataInvestorStoreOperations
        .transact(
          _.getInvestors(refinedContacts.flatMap(_.contactClientRelations.map(_.clientId)).distinct)
        )
        .map(_.map { investor =>
          investor.investorId -> investor.name
        }.toMap)

      investmentEntityNameMap <- InvestmentEntityStoreOperations
        .transact(
          _.gets(refinedContacts.flatMap(_.contactClientRelations.flatMap(_.investmentEntityId)).distinct)
        )
        .map(_.map { investmentEntity =>
          investmentEntity.investmentEntityId -> investmentEntity.name
        }.toMap)

      fundLegalEntityNameMap <- FundLegalEntityOperations
        .transact(_.gets(refinedContacts.flatMap(_.contactMatrix.map(_.contactMatrixKey.fundLegalEntityId)).distinct))
        .map(_.map { fundLegalEntity =>
          fundLegalEntity.id -> fundLegalEntity.name
        }.toMap)

      contactsNoteMap <- fundDataContactNoteService.getContactsNote(
        firmId = firmId,
        contactIds = contactIds
      )

      // Contact Information Data:
      contactInformationRawData = refinedContacts.map { case (contact, _, _, _) =>
        List(
          contact.contactInfo.email,
          contact.contactInfo.prefix,
          contact.contactInfo.firstName,
          contact.contactInfo.lastName,
          contact.contactInfo.suffix,
          contact.contactInfo.customId,
          contact.contactInfo.company,
          contact.contactInfo.title,
          contact.contactInfo.phone,
          contact.contactInfo.numberAndStreet,
          contact.contactInfo.city,
          contact.contactInfo.state,
          contact.contactInfo.country,
          contact.contactInfo.zipCode,
          contactsNoteMap.getOrElse(contact.contactId, None).fold("") { note =>
            FundDataNoteUtils.getTextFromNoteContent(note.content)
          }
        )
      }

      firmCommunicationTypes <- fundDataFirmCommunicationService
        .getFirmCommunicationsUnsafe(firmId)
        .map(_.sortBy(_.name))

      fleCommunicationTypeMap <- ZIO
        .foreach(
          refinedContacts.flatMap(_.contactMatrix.map(_.contactMatrixKey.fundLegalEntityId)).distinct
        ) { fundLegalEntityId =>
          fundDataFundLegalEntityCommunicationService
            .getFundLegalEntityCommunicationTypeIdsUnsafe(fundLegalEntityId)
            .map(fundLegalEntityId -> _)
        }
        .map(_.toMap)

      // Contact Relationships Data:
      contactRelationshipsRawData = refinedContacts.flatMap { case (contact, _, contactClientRelations, _) =>
        contactClientRelations.map { relation =>
          List(
            contact.contactInfo.email,
            clientNameMap.getOrElse(relation.clientId, ""),
            relation.investmentEntityId.flatMap(investmentEntityNameMap.get).getOrElse("")
          )
        }
      }

      // Contact IE Level Communication Setting Data
      ieLevelCommunicationSettingRawData = refinedContacts.flatMap {
        case (contact, contactInvestmentEntityPermission, _, _) =>
          contactInvestmentEntityPermission.map { permission =>
            List(
              contact.contactInfo.email,
              investmentEntityNameMap.getOrElse(permission.investmentEntityId, ""),
              permission.profileAccessType match {
                case ProfileAccessType.View     => "View"
                case ProfileAccessType.NoAccess => "No access"
              },
              permission.fundSubCommunicationType match {
                case FundDataContact.FundSubCommunicationType.MainInvestor => "Yes"
                case FundDataContact.FundSubCommunicationType.Empty        => "No"
              },
              permission.documentRequestCommunicationType match {
                case FundDataContact.DocumentRequestCommunicationType.NonReceiver => "No"
                case FundDataContact.DocumentRequestCommunicationType.Receiver    => "Yes"
              }
            )
          }
      }

      // Communication matrix map Data
      contactCommunicationMatrixRawData = refinedContacts.flatMap { case (contact, _, _, contactMatrices) =>
        contactMatrices
          .groupMap { matrix =>
            (
              investmentEntityId = matrix.contactMatrixKey.investmentEntityId,
              fundLegalEntityId = matrix.contactMatrixKey.fundLegalEntityId
            )
          } { matrix =>
            matrix.contactMatrixKey.communicationTypeId -> matrix.communicationRole
          }
          .view
          .mapValues(_.toMap)
          .toSeq
          .map { case ((investmentEntityId, fundLegalEntityId), communicationRoleMap) =>
            val fleCommunicationTypeIds = fleCommunicationTypeMap.getOrElse(fundLegalEntityId, Set.empty)
            List(
              contact.contactInfo.email,
              investmentEntityNameMap.getOrElse(investmentEntityId, ""),
              fundLegalEntityNameMap.getOrElse(fundLegalEntityId, "")
            ) ++ firmCommunicationTypes.map { communicationType =>
              if (fleCommunicationTypeIds.contains(communicationType.communicationTypeId)) {
                communicationRoleMap
                  .get(communicationType.communicationTypeId)
                  .fold(
                    "Unassigned"
                  ) {
                    case FundDataContact.CommunicationRole.Primary   => "Primary"
                    case FundDataContact.CommunicationRole.Secondary => "Secondary"
                  }
              } else {
                "N/A"
              }
            }
          }
      }

      folderId <- fileService.createUserTemporaryFolderIfNeeded(actor = actor)
      documentStorageIds <- ZIOUtils.foreachParN(4)(
        List(
          contactInformationRawData,
          contactRelationshipsRawData,
          ieLevelCommunicationSettingRawData,
          contactCommunicationMatrixRawData
        )
      ) { rawData =>
        for {
          resultSource <- CsvUtils.createCsvTask(rawData)
          fileId <- fileService.uploadFile(
            folderId,
            s"export-contact-temp.csv",
            FileContentOrigin.FromSource(
              resultSource,
              MediaType.TextCsv
            ),
            actor
          )
          documentStorageId <- fileService.getFileStorageId(
            actor = actor,
            fileId = fileId,
            purpose = DmsTrackingActivityType.Internal,
            httpContextOpt = None
          )
        } yield documentStorageId
      }
    } yield ExportContactsBatchItemResp(
      documentStorageIds.head,
      documentStorageIds(1),
      documentStorageIds(2),
      documentStorageIds(3),
      firmCommunicationTypes.map(_.name)
    )
  }

  def batchExportContacts(
    params: BatchExportContactsParams,
    actor: UserId
  ): Task[BatchActionId] = {
    for {
      _ <- ZIO.logInfo(s"$actor export ${params.contactIds.size} contacts data to spreadsheet")
      _ <- fundDataPermissionService.validateUserHasRole(params.firmId, actor)

      _ <- FundDataValidationUtils
        .minSize("contactIds", params.contactIds.toList)
        .toZIO
        .mapError(FundDataValidationError(_))

      contactIds = params.contactIds.sliding(100, 100).toList

      batchItems = contactIds.map { contactIds =>
        ExportContactsBatchItem(
          firmId = params.firmId,
          contactIds = contactIds
        )
      }

      userZoneId <- userService.getUserTimeZone(actor).map(_.getOrElse(DateTimeUtils.defaultTimezone))
      formattedDateTime = DateTimeUtils.formatInstant(
        Instant.now,
        DateTimeUtils.DefaultDateFormatter
      )(
        using userZoneId
      )
      firmModel <- FDBRecordDatabase.transact(FundDataFirmStoreOperations.Production)(_.get(params.firmId))
      fileName = firmModel.name + "- Contact data export - " + formattedDateTime + ".xlsx"

      postExecuteParams = ExportContactsPostExecuteParams(
        firmId = params.firmId,
        fileName = fileName
      )

      batchActionId <- batchActionService.startBatchActionInternal(
        params.firmId,
        actor,
        actionType = BatchActionType.FundDataExportContactsToSpreadsheet,
        batchActionItemsData = batchItems.map(_.asJson),
        postExecuteDataOpt = Some(postExecuteParams.asJson),
        frontendTracking = BatchActionFrontendTracking.ACTOR_TRACKING,
        startWorkflow = workflowParams => {
          FundDataExportContactsToSpreadsheetWorkflowImpl.instance
            .getWorkflowStub()
            .provideEnvironment(temporalEnvironment.workflowClient)
            .flatMap(workflowStub => ZWorkflowStub.start(workflowStub.execute(workflowParams)))
        }
      )
    } yield batchActionId
  }

}
