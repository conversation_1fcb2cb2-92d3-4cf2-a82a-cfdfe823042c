//  Copyright (C) 2014-2024 Anduin Transactions Inc.

package anduin.gondor.server.funddata.contact

import sttp.tapir.server.armeria.zio.ArmeriaZioServerInterpreter

import anduin.asyncapiv2.AsyncApiService
import anduin.funddata.contact.note.FundDataContactNoteService
import anduin.funddata.contact.{
  FundDataClientContactService,
  FundDataContactExportService,
  FundDataContactService,
  FundDataInvestmentEntityContactServiceV2
}
import anduin.funddata.endpoint.contact.FundDataContactEndpoints.*
import anduin.funddata.endpoint.contact.note.FundDataContactNoteEndpoints.*
import anduin.funddata.permission.{FundDataEndpointServer, FundDataPermissionService}
import anduin.tapir.server.AsyncAuthenticatedValidationEndpointServer
import anduin.tapir.server.EndpointServer.{AsyncTapirServerService, TapirServerService}
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.authorization.AuthorizationService

final case class FundDataContactEndpointServer(
  protected val backendConfig: GondorBackendConfig,
  fundDataPermissionService: FundDataPermissionService,
  fundDataContactService: FundDataContactService,
  fundDataContactExportService: FundDataContactExportService,
  fundDataContactNoteService: FundDataContactNoteService,
  fundDataClientContactService: FundDataClientContactService,
  fundDataInvestmentEntityContactService: FundDataInvestmentEntityContactServiceV2,
  override protected val interpreter: ArmeriaZioServerInterpreter[Any],
  override protected val asyncApiService: AsyncApiService
)(
  using val authorizationService: AuthorizationService
) extends AsyncAuthenticatedValidationEndpointServer
    with FundDataEndpointServer {

  private[server] val createContactService =
    validateRouteCatchError(
      createContact,
      validateCanManageFirmContact && validateCanEditInvestmentEntity && validateCanEditClient && validateInvestmentEntityFirmRelation
    ) { (params, ctx) =>
      fundDataContactService.createContact(params, ctx.actor.userId)
    }

  private[server] val checkDuplicatedContactInfoService =
    validateRouteCatchError(checkDuplicatedContactInfo, validateCanManageFirmContact) { (params, ctx) =>
      fundDataContactService.checkDuplicatedContactInfo(params, ctx.actor.userId)
    }

  private[server] val batchCheckDuplicatedContactsService =
    validateRouteCatchError(batchCheckDuplicatedContacts, validateCanManageFirmContact) { (params, ctx) =>
      fundDataContactService.batchCheckDuplicatedContacts(params, ctx.actor.userId)
    }

  private[server] val batchCreateContactsService =
    validateAsyncRouteCatchError(batchCreateContacts, validateCanManageFirmContact) { (params, ctx) =>
      fundDataContactService.batchCreateContacts(params, ctx.actor.userId)
    }

  private[server] val batchCreateContactRelationsService =
    validateAsyncRouteCatchError(
      batchCreateContactRelations,
      validateCanManageFirmContact && validateFirmAndClientsRelation && validateCanEditClient
    ) { (params, ctx) =>
      fundDataContactService.batchCreateContactRelations(params, ctx.actor.userId)
    }

  private[server] val batchCreateContactMatricesService =
    validateAsyncRouteCatchError(
      batchCreateContactMatrices,
      validateCanManageFirmContact && validateInvestmentEntityFirmRelation && validateCanEditInvestmentEntity
    ) { (params, ctx) =>
      fundDataContactService.batchCreateContactMatrices(params, ctx.actor.userId)
    }

  private[server] val editContactService =
    validateRouteCatchError(editContactInfo, validateCanManageFirmContact) { (params, ctx) =>
      fundDataContactService.editContact(params, ctx.actor.userId)
    }

  private[server] val getFirmContactsService =
    validateRouteCatchError(getFirmContacts, validateCanViewFirmContact) { (params, ctx) =>
      fundDataContactService.getFirmContacts(params, ctx.actor.userId)
    }

  private[server] val getFirmContactAdditionalInfosService =
    validateRouteCatchError(getFirmContactAdditionalInfos, validateCanViewFirmContact && validateFirmAndContactsRelation) {
      (params, ctx) =>
        fundDataContactService.getFirmContactAdditionalInfos(
          params,
          ctx.actor.userId
        )
    }

  private[server] val getContactService =
    validateRouteCatchError(getContact, validateCanViewFirmContact) { (params, ctx) =>
      fundDataContactService.getContact(params, ctx.actor.userId)
    }

  private[server] val getFirmContactsInfoService =
    validateRouteCatchError(getFirmContactsInfo, validateCanViewFirmContact) { (params, ctx) =>
      fundDataContactService.getFirmContactsInfo(
        params,
        ctx.actor.userId
      )
    }

  private[server] val getInvestmentEntityContactsInfoService =
    validateRouteCatchError(
      getInvestmentEntityContactsInfo,
      validateCanViewFirmContact && validateCanViewInvestmentEntity && validateInvestmentEntityFirmRelation
    ) { (params, ctx) =>
      fundDataInvestmentEntityContactService.getInvestmentEntityContactsInfo(
        params,
        ctx.actor.userId
      )
    }

  private[server] val getInvestmentEntitiesContactsInfoService =
    validateRouteCatchError(
      getInvestmentEntitiesContactsInfo,
      validateCanViewFirmContact && validateCanViewInvestmentEntity && validateInvestmentEntityFirmRelation
    ) { (params, ctx) =>
      fundDataInvestmentEntityContactService.getInvestmentEntitiesContactsInfo(
        params,
        ctx.actor.userId
      )
    }

  private[server] val deleteContactService =
    validateAsyncRouteCatchError(deleteContact, validateCanDeleteFirmContact) { (params, ctx) =>
      fundDataContactService.deleteContact(params, ctx.actor.userId)
    }

  private val generalContactServices = List(
    createContactService,
    editContactService,
    getFirmContactsService,
    getFirmContactAdditionalInfosService,
    getContactService,
    getFirmContactsInfoService,
    getInvestmentEntityContactsInfoService,
    getInvestmentEntitiesContactsInfoService,
    checkDuplicatedContactInfoService,
    batchCheckDuplicatedContactsService
  )

  private[server] val getContactAccessService =
    validateRouteCatchError(getContactAccess, validateCanViewFirmContact) { (params, ctx) =>
      fundDataContactService.getContactAccess(params, ctx.actor.userId)
    }

  private val contactAccessServices = List(getContactAccessService)

  private[server] val assignClientsAndInvestmentEntitiesService =
    validateRouteCatchError(
      assignClientsAndInvestmentEntities,
      validateCanManageFirmContact && validateCanEditClient && validateCanEditInvestmentEntity && validateInvestmentEntityFirmRelation
    ) { (params, ctx) =>
      fundDataContactService.assignClientsAndInvestmentEntities(params, ctx.actor.userId)
    }

  private[server] val getContactRelationService =
    validateRouteCatchError(getContactRelation, validateCanViewFirmContact) { (params, ctx) =>
      fundDataContactService.getContactRelation(params, ctx.actor.userId)
    }

  private val contactRelationServices = List(assignClientsAndInvestmentEntitiesService, getContactRelationService)

  private[server] val getContactMatrixService =
    validateRouteCatchError(getContactMatrix, validateCanViewFirmContact) { (params, ctx) =>
      fundDataContactService.getContactMatrix(
        params,
        ctx.actor.userId
      )
    }

  private[server] val updateContactMatrixService =
    validateRouteCatchError(
      updateContactMatrix,
      validateCanManageFirmContact && validateCanEditClient && validateCanEditInvestmentEntity && validateInvestmentEntityFirmRelation
        && validateCanManageFundLegalEntity
    ) { (params, ctx) =>
      fundDataContactService.updateContactMatrix(
        params,
        ctx.actor.userId
      )
    }

  private[server] val getContactMatrixByInvestmentEntityService =
    validateRouteCatchError(
      getContactMatrixByInvestmentEntity,
      validateCanManageFirmContact && validateCanViewInvestmentEntity
    ) { (params, ctx) =>
      fundDataInvestmentEntityContactService.getContactMatrixByInvestmentEntity(
        params,
        ctx.actor.userId
      )
    }

  private[server] val batchRemoveContactMatrixService =
    validateRouteCatchError(
      batchRemoveContactMatrix,
      validateCanManageFirmContact && validateCanEditInvestmentEntity && validateInvestmentEntityFirmRelation && validateCanManageFundLegalEntity
    ) { (params, ctx) =>
      fundDataContactService.batchRemoveContactMatrix(
        params,
        ctx.actor.userId
      )
    }

  private val contactMatrixServices = List(
    getContactMatrixService,
    updateContactMatrixService,
    getContactMatrixByInvestmentEntityService,
    batchRemoveContactMatrixService
  )

  private[server] val exportContactsToSpreadsheetService =
    validateRouteCatchError(batchExportContacts, validateCanViewFirmContact) { (params, ctx) =>
      fundDataContactExportService.batchExportContacts(params, ctx.actor.userId)
    }

  private val contactExportServices = List(exportContactsToSpreadsheetService)

  /** Contact for client
    */
  private[server] val getClientContactsService =
    validateRouteCatchError(getClientContacts, validateCanViewClient) { (params, ctx) =>
      fundDataClientContactService.getClientContacts(params, ctx.actor.userId)
    }

  private[server] val assignContactsToClientService =
    validateAsyncRouteCatchError(assignContactsToClient, validateCanEditClient && validateCanManageFirmContact) {
      (params, ctx) =>
        fundDataClientContactService.assignContactsToClient(params, ctx.actor.userId)
    }

  private[server] val unassignContactsFromClientService =
    validateAsyncRouteCatchError(unassignContactsFromClient, validateCanEditClient && validateCanManageFirmContact) {
      (params, ctx) =>
        fundDataClientContactService.unassignContactsFromClient(params, ctx.actor.userId)
    }

  private[server] val updateContactsPermissionToClientService =
    validateAsyncRouteCatchError(
      updateContactsPermissionToClient,
      validateCanEditClient && validateCanManageFirmContact
    ) { (params, ctx) =>
      fundDataClientContactService.updateContactsPermissionToClient(params, ctx.actor.userId)
    }

  private[server] val updateInvestmentEntityContactsService =
    validateAsyncRouteCatchError(
      updateInvestmentEntityContacts,
      validateCanEditInvestmentEntity && validateCanManageFirmContact
    ) { (params, ctx) =>
      fundDataInvestmentEntityContactService.updateInvestmentEntityContacts(params, ctx.actor.userId)
    }

  private val clientContactServices = List(
    getClientContactsService
  )

  private[server] val getContactNoteService =
    validateRouteCatchError(getContactNote, validateCanViewFirmContact) { (params, ctx) =>
      fundDataContactNoteService.getContactNote(params, ctx.actor.userId)
    }

  private[server] val getContactNotesService =
    validateRouteCatchError(getContactNotes, validateCanViewFirmContact && validateFirmAndContactsRelation) {
      (params, ctx) =>
        fundDataContactNoteService.getContactNotes(params, ctx.actor.userId)
    }

  private[server] val updateContactNoteService =
    validateRouteCatchError(updateContactNote, validateCanManageFirmContact) { (params, ctx) =>
      fundDataContactNoteService.updateContactNote(params, ctx.actor.userId)
    }

  private[server] val deleteContactNoteService =
    validateRouteCatchError(deleteContactNote, validateCanManageFirmContact) { (params, ctx) =>
      fundDataContactNoteService.deleteContactNote(params, ctx.actor.userId)
    }

  private val contactNoteServices = List(
    getContactNoteService,
    getContactNotesService,
    updateContactNoteService,
    deleteContactNoteService
  )

  val services: List[TapirServerService] =
    List(
      generalContactServices,
      contactAccessServices,
      contactRelationServices,
      contactMatrixServices,
      contactExportServices,
      clientContactServices,
      contactNoteServices
    ).flatten

  val asyncServices: List[AsyncTapirServerService] = List(
    deleteContactService,
    assignContactsToClientService,
    updateContactsPermissionToClientService,
    unassignContactsFromClientService,
    batchCreateContactsService,
    batchCreateContactRelationsService,
    batchCreateContactMatricesService,
    updateInvestmentEntityContactsService
  )

}
