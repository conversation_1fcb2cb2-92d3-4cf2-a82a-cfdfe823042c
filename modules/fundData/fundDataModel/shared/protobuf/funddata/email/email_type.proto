syntax = "proto3";

package anduin.protobuf.funddata.email.type;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.funddata.email.type"
  single_file: true
};

enum FundDataEmailType {
  reserved 14;
  InviteMember = 0;
  RemindMemberInvitation = 1;
  RemoveMember = 2;
  LeaveFundData = 3;
  NotifyDocumentExpiration = 4;
  NotifyAssessmentDueDate = 5;
  AutoSyncDowngradeAdmin = 6;
  NotifyRequestSubmitted = 7;
  NotifyRequestSentToRecipients = 8;
  NotifyRequestMarkedAsAllNotApplicable = 9;
  NotifyRequestCanceled = 10;
  NotifyRequestSentToCollaborators = 11;
  NotifyRequestRemoveCollaborator = 12;
  NotifyIEProfileConflict = 13;
  RemoveGuest = 15;
  NotifyGuest = 16;
  DocDistributionNotification = 17;
  CustomEmail = 18;
  NotifyLandingPageLimitExceeded = 19;
}