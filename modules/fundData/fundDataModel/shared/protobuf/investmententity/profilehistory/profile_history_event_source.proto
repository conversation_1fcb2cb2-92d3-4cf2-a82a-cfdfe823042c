syntax = "proto3";

package anduin.protobuf.funddata.investmententity.profilehistory.eventsource;

import "scalapb/scalapb.proto";


option (scalapb.options) = {
  package_name: "anduin.protobuf.funddata.investmententity.profilehistory.eventsource"
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.id.funddata.FundDataInvestmentEntityId"
  single_file: true
};


message ProfileHistoryEventSource {
  oneof sealed_value {
    ManualEdit manual_edit = 1;
    ImportFromSubscription import_from_subscription = 2;
    ImportFromSpreadSheet import_from_spreadsheet = 3;
    AutoSyncFromSubscription auto_sync_from_subscription = 4;
    ManualSyncFromSubscription manual_sync_from_subscription = 5;
    ThroughPublicAPI through_public_api = 6;
    ThroughMergeInvestmentEntity merge_investment_entity = 7;
  }
}


message ManualEdit {}

message ImportFromSubscription {
  string fund_name = 1;
  string fund_sub_lp_id = 3 [(scalapb.field).type = "FundSubLpId"];
}

message ImportFromSpreadSheet {}

message AutoSyncFromSubscription {
  string fund_name = 1;
  string fund_sub_lp_id = 3 [(scalapb.field).type = "FundSubLpId"];
}

message ManualSyncFromSubscription {
  string fund_name = 1;
  string fund_sub_lp_id = 3 [(scalapb.field).type = "FundSubLpId"];
}

message ThroughPublicAPI {}

message ThroughMergeInvestmentEntity {
  string src_investment_entity_id = 1 [(scalapb.field).type = "FundDataInvestmentEntityId"];
  string src_investment_entity_name = 2;
}