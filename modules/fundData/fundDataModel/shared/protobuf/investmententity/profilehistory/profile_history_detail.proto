syntax = "proto3";

package anduin.protobuf.funddata.investmententity.profilehistory.detail;

import "scalapb/scalapb.proto";
import "google/protobuf/struct.proto";


option (scalapb.options) = {
  package_name: "anduin.protobuf.funddata.investmententity.profilehistory.detail"
  import: "io.circe.Json"
  import: "anduin.model.codec.ProtoCodecs.given"
  single_file: true
};


message ProfileHistoryEventDetail {
  oneof sealed_value {
    ProfileClear profile_clear = 1;
    FieldSet field_set = 2;
    FieldUpdate field_update = 3;
    FieldClear field_clear = 4;
  }
}

message ProfileClear {}

message FieldSet {
  google.protobuf.Value to_value = 1 [(scalapb.field).type = "Json"];
  google.protobuf.Value to_value_formatted_text = 2 [(scalapb.field).type = "Json"];
}

message FieldUpdate {
  google.protobuf.Value from_value = 1 [(scalapb.field).type = "Json"];
  google.protobuf.Value from_value_formatted_text = 2 [(scalapb.field).type = "Json"];
  google.protobuf.Value to_value = 3 [(scalapb.field).type = "Json"];
  google.protobuf.Value to_value_formatted_text = 4 [(scalapb.field).type = "Json"];
}

message FieldClear {
  google.protobuf.Value from_value = 1 [(scalapb.field).type = "Json"];
  google.protobuf.Value from_value_formatted_text = 2 [(scalapb.field).type = "Json"];
}