// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.client

import zio.Task

import anduin.funddata.endpoint.contact.*
import anduin.funddata.endpoint.contact.note.*
import anduin.funddata.endpoint.note.FundDataNote
import anduin.id.batchaction.BatchActionId
import anduin.id.funddata.note.FundDataNoteId
import anduin.service.GeneralServiceException
import anduin.tapir.client.{AsyncEndpointClient, AuthenticatedEndpointClient}

object FundDataContactEndpointClient extends AuthenticatedEndpointClient with AsyncEndpointClient {

  val createContact: CreateContactParams => Task[Either[GeneralServiceException, CreateContactResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.createContact)

  val checkDuplicatedContactInfo
    : CheckDuplicatedContactInfoParams => Task[Either[GeneralServiceException, CheckDuplicatedContactInfoResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.checkDuplicatedContactInfo)

  val batchCheckDuplicatedContacts: BatchCheckDuplicatedContactsParams => Task[
    Either[GeneralServiceException, BatchCheckDuplicatedContactsResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.batchCheckDuplicatedContacts)

  val batchCreateContacts: BatchCreateContactsParams => Task[
    Either[GeneralServiceException, BatchCreateContactsResponse]
  ] =
    toSyncClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.batchCreateContacts)

  val batchCreateContactRelations: BatchCreateContactRelationsParams => Task[
    Either[GeneralServiceException, BatchCreateContactRelationsResponse]
  ] =
    toSyncClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.batchCreateContactRelations)

  val batchCreateContactMatrices: BatchCreateContactMatricesParams => Task[
    Either[GeneralServiceException, BatchCreateContactMatricesResponse]
  ] =
    toSyncClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.batchCreateContactMatrices)

  val editContactInfo: EditContactParams => Task[Either[GeneralServiceException, EditContactResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.editContactInfo)

  val getFirmContacts: GetFirmContactsParams => Task[Either[GeneralServiceException, GetFirmContactsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.getFirmContacts)

  val getContact: GetContactParams => Task[Either[GeneralServiceException, GetContactResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.getContact)

  val deleteContact: DeleteContactParams => Task[Either[GeneralServiceException, DeleteContactResponse]] =
    toSyncClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.deleteContact)

  val getContactAccess: GetContactAccessParams => Task[Either[GeneralServiceException, GetContactAccessResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.getContactAccess)

  val assignClientsAndInvestmentEntities: AssignClientAndInvestmentEntitiesParams => Task[
    Either[GeneralServiceException, AssignClientAndInvestmentEntitiesResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.assignClientsAndInvestmentEntities)

  val getContactRelation: GetContactRelationParams => Task[
    Either[GeneralServiceException, GetContactRelationResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.getContactRelation)

  val getContactMatrix: GetContactMatrixParams => Task[Either[GeneralServiceException, GetContactMatrixResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.getContactMatrix)

  val updateContactMatrix
    : UpdateContactMatrixParams => Task[Either[GeneralServiceException, UpdateContactMatrixResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.updateContactMatrix)

  val batchRemoveContactMatrix: BatchRemoveContactMatrixParams => Task[
    Either[GeneralServiceException, BatchRemoveContactMatrixResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.batchRemoveContactMatrix)

  val getFirmContactsInfo
    : GetFirmContactsInfoParams => Task[Either[GeneralServiceException, GetFirmContactsInfoResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.getFirmContactsInfo)

  val getFirmContactAdditionalInfos: GetFirmContactAdditionalInfosParams => Task[
    Either[GeneralServiceException, GetFirmContactAdditionalInfosResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.getFirmContactAdditionalInfos)

  val getInvestmentEntityContactsInfo: GetInvestmentEntityContactsInfoParams => Task[
    Either[GeneralServiceException, GetInvestmentEntityContactsInfoResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.getInvestmentEntityContactsInfo)

  val getInvestmentEntitiesContactsInfo: GetInvestmentEntitiesContactsInfoParams => Task[
    Either[GeneralServiceException, GetInvestmentEntitiesContactsInfoResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.getInvestmentEntitiesContactsInfo)

  val getContactMatrixByInvestmentEntity: GetContactMatrixByInvestmentEntityParams => Task[
    Either[GeneralServiceException, GetContactMatrixByInvestmentEntityResponse]
  ] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.getContactMatrixByInvestmentEntity)

  val batchExportContacts: BatchExportContactsParams => Task[Either[GeneralServiceException, BatchActionId]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.batchExportContacts)

  /** Contact for Client
    */

  val getClientContacts: GetClientContactsParams => Task[Either[GeneralServiceException, GetClientContactsResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.getClientContacts)

  val assignContactsToClient
    : AssignContactsToClientParams => Task[Either[GeneralServiceException, AssignContactsToClientResponse]] =
    toSyncClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.assignContactsToClient)

  val unassignContactsFromClient
    : UnassignContactsFromClientParams => Task[Either[GeneralServiceException, UnassignContactsFromClientResponse]] =
    toSyncClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.unassignContactsFromClient)

  val updateContactsPermissionToClient
    : UpdateContactsPermissionToClientParams => Task[Either[GeneralServiceException, Unit]] =
    toSyncClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.updateContactsPermissionToClient)

  val updateInvestmentEntityContacts: UpdateInvestmentEntityContactsParams => Task[
    Either[GeneralServiceException, UpdateInvestmentEntityContactsResponse]
  ] =
    toSyncClientThrowDecodeAndSecurityFailures(FundDataContactEndpoints.updateInvestmentEntityContacts)

  /** Note
    */

  val getContactNote: GetContactNoteParams => Task[Either[GeneralServiceException, Option[FundDataNote]]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactNoteEndpoints.getContactNote)

  val getContactNotes: GetContactNotesParams => Task[Either[GeneralServiceException, GetContactNotesResponse]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactNoteEndpoints.getContactNotes)

  val updateContactNote: UpdateContactNoteParams => Task[Either[GeneralServiceException, FundDataNoteId]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactNoteEndpoints.updateContactNote)

  val deleteContactNote: DeleteContactNoteParams => Task[Either[GeneralServiceException, Unit]] =
    toClientThrowDecodeAndSecurityFailures(FundDataContactNoteEndpoints.deleteContactNote)

}
