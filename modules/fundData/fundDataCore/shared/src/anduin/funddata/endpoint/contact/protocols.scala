// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.endpoint.contact

import anduin.circe.generic.semiauto.CirceCodec
import anduin.funddata.endpoint.contact.BatchCreateContactRelationsParams.InvestmentEntitiesInClient
import anduin.funddata.endpoint.contact.FundDataContact.{
  ContactQueryParams,
  InvestmentEntityRelation,
  InvestmentEntityRelationUpdate
}
import anduin.funddata.endpoint.landingpage.{LandingPageType, LandingPageUserPageType}
import anduin.funddata.endpoint.permission.*
import anduin.funddata.endpoint.{SortOrder, UpdateProperty}
import anduin.id.batchaction.BatchActionId
import anduin.id.communication.CommunicationTypeId
import anduin.id.contact.ContactId
import anduin.model.codec.MapCodecs.given
import anduin.id.funddata.*
import anduin.id.funddata.fund.FundLegalEntityId
import anduin.id.fundsub.FundSubId
import anduin.id.tag.TagItemId
import anduin.model.common.user.UserInfo
import anduin.model.document.DocumentStorageId
import anduin.model.id.FileId
import anduin.model.id.stage.DataRoomWorkflowId
import anduin.utils.endpoint.{
  DashboardPaginationData,
  GetDashboardAdditionalDataResponse,
  GetDashboardDataParams,
  GetDashboardDataResponse
}

object FundDataContact {

  final case class ContactInfo(
    firstName: String,
    lastName: String,
    email: String,
    customId: String = "",
    phone: String = "",
    company: String = "",
    title: String = "",
    country: String = "",
    numberAndStreet: String = "",
    city: String = "",
    state: String = "",
    zipCode: String = "",
    prefix: String = "",
    suffix: String = ""
  ) derives CanEqual,
        CirceCodec.WithDefaultsAndTypeName { self =>
    val fullName: String = s"${firstName.trim} ${lastName.trim} ${suffix.trim}".trim.replaceAll("\\s{2,}", " ")

    val fullNameWithPrefixAndSuffix: String = {
      val prefix = if (self.prefix.nonEmpty) s"(${self.prefix})" else ""
      s"$fullName $prefix".trim.replaceAll("\\s{2,}", " ")
    }

    val address: String = {
      val address = s"${numberAndStreet.trim}, ${city.trim} ${state.trim} ${zipCode.trim}".trim
      if (address == ",") "" else address
    }

    def trimAll: ContactInfo =
      ContactInfo(
        firstName.trim,
        lastName.trim,
        email.trim,
        customId.trim,
        phone.trim.replaceAll("\\s{2,}", " "),
        company.trim,
        title.trim,
        country.trim,
        numberAndStreet.trim,
        city.trim,
        state.trim,
        zipCode.trim,
        prefix.trim,
        suffix.trim
      )

  }

  object ContactInfo {

    def fromUserInfo(userInfo: UserInfo): ContactInfo = {
      ContactInfo(
        firstName = userInfo.firstName,
        lastName = userInfo.lastName,
        email = userInfo.emailAddressStr,
        phone = userInfo.phone,
        title = userInfo.jobTitle,
        country = userInfo.country,
        numberAndStreet = userInfo.address,
        city = userInfo.city,
        state = userInfo.state
      )
    }

  }

  final case class InvestmentEntityContactMatrixValueUpdate(
    fundLegalEntityId: FundLegalEntityId,
    communicationTypeId: CommunicationTypeId,
    communicationRoleUpdate: UpdateProperty[CommunicationRole] = UpdateProperty()
  ) derives CirceCodec.WithDefaultsAndTypeName,
        CanEqual

  final case class InvestmentEntityRelationWithMatrixUpdate(
    investmentEntityId: FundDataInvestmentEntityId,
    fundSubCommunicationTypeUpdate: UpdateProperty[FundSubCommunicationType] = UpdateProperty(),
    documentRequestCommunicationTypeUpdate: UpdateProperty[DocumentRequestCommunicationType] = UpdateProperty(),
    profileAccessTypeUpdate: UpdateProperty[ProfileAccessType] = UpdateProperty(),
    contactMatrixValueUpdates: UpdateProperty[List[InvestmentEntityContactMatrixValueUpdate]] = UpdateProperty()
  ) derives CirceCodec.WithDefaultsAndTypeName,
        CanEqual

  final case class ContactWithMatrixUpdate(
    contactInfo: ContactInfo,
    investmentEntityRelations: List[InvestmentEntityRelationWithMatrixUpdate]
  ) derives CirceCodec.WithDefaultsAndTypeName,
        CanEqual

  final case class Contact(
    contactId: ContactId,
    contactInfo: ContactInfo
  ) derives CirceCodec.WithDefaultsAndTypeName,
        CanEqual {

    def toInvestmentEntityContact(investmentEntityId: FundDataInvestmentEntityId): InvestmentEntityContact =
      InvestmentEntityContact(
        contactId = contactId,
        contactInfo = contactInfo,
        clientRelation = ClientRelation(investmentEntityId.investorId)
      )

  }

  final case class ClientContact(
    contactId: ContactId,
    contactInfo: ContactInfo,
    clientRelation: ClientRelation,
    investmentEntityRelations: List[InvestmentEntityRelation]
  ) derives CirceCodec.WithDefaultsAndTypeName,
        CanEqual

  final case class InvestmentEntityContact(
    contactId: ContactId,
    contactInfo: ContactInfo,
    fundSubCommunicationType: FundSubCommunicationType = FundSubCommunicationType.Empty,
    documentRequestCommunicationType: DocumentRequestCommunicationType = DocumentRequestCommunicationType.NonReceiver,
    profileAccessType: ProfileAccessType = ProfileAccessType.NoAccess,
    clientRelation: ClientRelation
  ) derives CirceCodec.WithDefaultsAndTypeName,
        CanEqual {
    def toContact: Contact = Contact(contactId, contactInfo)
  }

  final case class ContactQueryParams(
    searchText: String = "",
    filterByClientTags: Set[Option[TagItemId]] = Set.empty,
    filterByClientGroups: Set[FundDataClientGroupId] = Set.empty,
    filterByClients: Set[Option[FundDataInvestorId]] = Set.empty,
    filterByFundSubCommunicationType: Set[FundSubCommunicationType] = Set.empty,
    filterByDocumentRequestCommunicationType: Set[DocumentRequestCommunicationType] = Set.empty,
    filterByFundLegalEntities: Set[Option[FundLegalEntityId]] = Set.empty,
    filterByInvestmentEntities: Set[Option[FundDataInvestmentEntityId]] = Set.empty,
    filterByCommunicationTypes: Set[Option[CommunicationTypeId]] = Set.empty,
    filterByContacts: Option[Set[ContactId]] = None, // None if not filter by contacts
    filterByDataRoomAccess: Set[Option[FundDataDataRoomId]] = Set.empty,
    filterByFundSubAccess: Set[Option[FundDataFundId]] = Set.empty,
    filterByLandingPageAccess: Set[Option[LandingPageUserPageType]] = Set.empty,
    offset: Int = 0,
    limit: Int = 20,
    sortBy: ContactQueryParams.SortBy = ContactQueryParams.SortBy.Name,
    sortOrder: SortOrder = SortOrder.Ascending
  ) derives CirceCodec.WithDefaultsAndTypeName

  object ContactQueryParams {
    sealed trait SortBy derives CanEqual, CirceCodec.WithDefaultsAndTypeName

    object SortBy {
      case object Name extends SortBy derives CirceCodec.WithDefaultsAndTypeName

      case object Phone extends SortBy derives CirceCodec.WithDefaultsAndTypeName

      case object Company extends SortBy derives CirceCodec.WithDefaultsAndTypeName

      case object Title extends SortBy derives CirceCodec.WithDefaultsAndTypeName

      case object Address extends SortBy derives CirceCodec.WithDefaultsAndTypeName

    }

  }

  final case class InvestorInfo(investorId: FundDataInvestorId, name: String, customId: String)
      derives CirceCodec.WithDefaultsAndTypeName

  enum FundSubCommunicationType extends Enum[FundSubCommunicationType] derives CirceCodec.Enum, CanEqual {
    case MainInvestor
    case Empty
  }

  enum DocumentRequestCommunicationType extends Enum[DocumentRequestCommunicationType] derives CirceCodec.Enum, CanEqual {
    case NonReceiver
    case Receiver
  }

  enum ProfileAccessType extends Enum[ProfileAccessType] derives CirceCodec.Enum {
    case NoAccess
    case View
  }

  final case class InvestmentEntityInfo(
    investmentEntityId: FundDataInvestmentEntityId,
    name: String,
    customId: String,
    jurisdictionType: Option[TagItemId],
    investorType: Option[TagItemId],
    riskAssessment: Option[TagItemId]
  ) derives CirceCodec.WithDefaultsAndTypeName

  final case class ContactRelationInfo(
    investors: List[InvestorInfo],
    investmentEntities: List[InvestmentEntityInfo]
  ) derives CirceCodec.WithDefaultsAndTypeName

  final case class ClientRelation(
    clientId: FundDataInvestorId,
    fundSubCommunicationTypeOpt: Option[FundSubCommunicationType] = None,
    documentRequestCommunicationTypeOpt: Option[DocumentRequestCommunicationType] = None
  ) derives CirceCodec.WithDefaultsAndTypeName

  final case class InvestmentEntityRelation(
    investmentEntityId: FundDataInvestmentEntityId,
    fundSubCommunicationType: FundSubCommunicationType = FundSubCommunicationType.Empty,
    documentRequestCommunicationType: DocumentRequestCommunicationType = DocumentRequestCommunicationType.NonReceiver,
    profileAccessType: ProfileAccessType = ProfileAccessType.NoAccess
  ) derives CirceCodec.WithDefaultsAndTypeName,
        CanEqual

  final case class ContactRelation(
    contactId: ContactId,
    fundSubCommunicationType: FundSubCommunicationType = FundSubCommunicationType.Empty,
    documentRequestCommunicationType: DocumentRequestCommunicationType = DocumentRequestCommunicationType.NonReceiver,
    profileAccessType: ProfileAccessType = ProfileAccessType.NoAccess
  ) derives CirceCodec.WithDefaultsAndTypeName,
        CanEqual

  final case class InvestmentEntityRelationUpdate(
    investmentEntityId: FundDataInvestmentEntityId,
    fundSubCommunicationTypeUpdate: UpdateProperty[FundSubCommunicationType],
    documentRequestCommunicationTypeUpdate: UpdateProperty[DocumentRequestCommunicationType],
    profileAccessTypeUpdate: UpdateProperty[ProfileAccessType]
  ) derives CirceCodec.WithDefaultsAndTypeName

  sealed trait ContactAccessInfo derives CirceCodec.WithDefaults

  final case class FundSubscriptionInfo(
    id: FundSubId,
    name: String,
    canAccess: Boolean
  ) extends ContactAccessInfo derives CirceCodec.WithDefaultsAndTypeName

  final case class DataRoomInfo(
    id: DataRoomWorkflowId,
    name: String,
    canAccess: Boolean
  ) extends ContactAccessInfo derives CirceCodec.WithDefaultsAndTypeName

  final case class LandingPageInfo(
    landingPageType: LandingPageType,
    pageName: String,
    customUrl: Option[String]
  ) extends ContactAccessInfo derives CirceCodec.WithDefaultsAndTypeName

  final case class PortalInfo(portalName: String) extends ContactAccessInfo derives CirceCodec.WithDefaultsAndTypeName

  final case class ContactAccess(
    contactEmail: String,
    fundSubscriptions: List[FundSubscriptionInfo],
    dataRooms: List[DataRoomInfo],
    landingPages: List[LandingPageInfo],
    portals: List[PortalInfo]
  ) derives CirceCodec.WithDefaultsAndTypeName {
    def isAllEmpty: Boolean = fundSubscriptions.isEmpty && dataRooms.isEmpty && landingPages.isEmpty && portals.isEmpty
  }

  object ContactAccess {
    val Empty: ContactAccess = ContactAccess("", Nil, Nil, Nil, Nil)
  }

  final case class ContactAndRelationIds(
    contact: Contact,
    investmentEntityRelations: List[InvestmentEntityRelation],
    investorIds: List[FundDataInvestorId]
  ) derives CirceCodec.WithDefaultsAndTypeName {
    lazy val investmentEntityIds: List[FundDataInvestmentEntityId] = investmentEntityRelations.map(_.investmentEntityId)
  }

  enum CommunicationRole derives CirceCodec.Enum {
    case Primary
    case Secondary
  }

  final case class ContactMatrixKey(
    investmentEntityId: FundDataInvestmentEntityId,
    fundLegalEntityId: FundLegalEntityId,
    communicationTypeId: CommunicationTypeId
  ) derives CirceCodec.WithDefaultsAndTypeName,
        CanEqual

  final case class ContactMatrixValue(
    contactMatrixKey: ContactMatrixKey,
    communicationRole: CommunicationRole
  ) derives CirceCodec.WithDefaultsAndTypeName,
        CanEqual

  final case class ContactMatrixValueUpdate(
    contactMatrixKey: ContactMatrixKey,
    communicationRoleUpdate: UpdateProperty[CommunicationRole]
  ) derives CirceCodec.WithDefaultsAndTypeName

  final case class ContactMatrixKeyByInvestmentEntity(
    fundLegalEntityId: FundLegalEntityId,
    communicationTypeId: CommunicationTypeId,
    contactId: ContactId
  ) derives CirceCodec.WithDefaultsAndTypeName,
        CanEqual

  final case class ContactMatrixValueByInvestmentEntity(
    contactMatrixKey: ContactMatrixKeyByInvestmentEntity,
    communicationRole: CommunicationRole
  ) derives CirceCodec.WithDefaultsAndTypeName,
        CanEqual

  final case class ContactAdditionalInfo(
    contactId: ContactId,
    investmentEntityRelations: List[FundDataContact.InvestmentEntityRelation],
    investorIds: List[FundDataInvestorId],
    contactCommunicationMatrix: List[FundDataContact.ContactMatrixValue],
    contactAccesses: FundDataContact.ContactAccess
  ) derives CirceCodec.WithDefaultsAndTypeName,
        CanEqual

}

final case class CreateContactParams(
  firmId: FundDataFirmId,
  createdContacts: List[FundDataContact.ContactInfo],
  clientIdsList: List[List[FundDataInvestorId]],
  investmentEntityIdsList: List[List[FundDataInvestmentEntityId]]
) extends FundDataFirmValidationParams
    with FundDataInvestmentEntitiesValidationParams
    with FundDataInvestorsValidationParams
    with FundDataInvestmentEntitiesWithinFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName {

  lazy val investorIds: List[FundDataInvestorId] = clientIdsList.flatten.distinct
  lazy val investmentEntityIds: List[FundDataInvestmentEntityId] = investmentEntityIdsList.flatten.distinct
}

final case class CreateContactResponse(contactIdMap: Map[String, ContactId]) derives CirceCodec.WithDefaultsAndTypeName

final case class BatchCreateContactsParams(
  firmId: FundDataFirmId,
  contacts: List[BatchCreateContactsParams.BatchCreateContactsItem]
) extends FundDataFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName

object BatchCreateContactsParams {

  enum BatchCreateActionType derives CirceCodec.Enum, CanEqual {
    case CreateNew
    case UpdateWithNewData
    case UpdateOnMissingData
  }

  final case class BatchCreateContactsItem(
    firmId: FundDataFirmId,
    contactInfo: FundDataContact.ContactInfo,
    notes: String,
    actionType: BatchCreateActionType
  ) derives CirceCodec.WithDefaultsAndTypeName

}

final case class BatchCreateContactsResponse(
  batchActionId: BatchActionId
) derives CirceCodec.WithDefaultsAndTypeName

final case class BatchCreateContactMatricesParams(
  firmId: FundDataFirmId,
  contacts: List[BatchCreateContactMatricesParams.BatchCreateContactMatricesItem]
) extends FundDataInvestmentEntitiesWithinFirmValidationParams
    with FundLegalEntitiesValidationParams derives CirceCodec.WithDefaultsAndTypeName {

  override def investmentEntityIds: List[FundDataInvestmentEntityId] = contacts.flatMap { contact =>
    contact.investmentEntityRelations.map(_.investmentEntityId) ++
      contact.contactMatricesPerEntityAndFund.map(_.investmentEntityId)
  }.distinct

  override def fundLegalEntityIds: List[FundLegalEntityId] = contacts.flatMap {
    _.contactMatricesPerEntityAndFund.map(_.fundLegalEntityId)
  }.distinct

}

object BatchCreateContactMatricesParams {

  sealed trait ContactSource derives CirceCodec.WithDefaults, CanEqual

  object ContactSource {
    final case class ExistingContact(contactId: ContactId) extends ContactSource derives CirceCodec.WithDefaults

    final case class NewContact(email: String, firstName: String, lastName: String) extends ContactSource
        derives CirceCodec.WithDefaults

  }

  final case class ContactMatricesPerEntityAndFund(
    investmentEntityId: FundDataInvestmentEntityId,
    fundLegalEntityId: FundLegalEntityId,
    commTypesAndRoles: List[(CommunicationTypeId, FundDataContact.CommunicationRole)]
  ) derives CirceCodec.WithDefaultsAndTypeName

  final case class BatchCreateContactMatricesItem(
    contactSource: ContactSource,
    investmentEntityRelations: List[FundDataContact.InvestmentEntityRelation] = List.empty,
    contactMatricesPerEntityAndFund: List[ContactMatricesPerEntityAndFund] = List.empty
  ) derives CirceCodec.WithDefaultsAndTypeName

  final case class BatchCreateContactMatricesItemData(
    firmId: FundDataFirmId,
    contactIdOpt: Option[ContactId], // if None, create new contact
    email: String,
    firstName: String,
    lastName: String,
    updateInvestmentEntityRelations: List[BatchCreateContactMatricesItemData.UpdateSingleInvestmentEntityRelation],
    updateContactMatrices: List[BatchCreateContactMatricesItemData.UpdateSingleContactMatrix]
  ) derives CirceCodec.WithDefaults,
        CanEqual {
    def fullName: String = firstName + " " + lastName
  }

  object BatchCreateContactMatricesItemData {

    final case class UpdateSingleInvestmentEntityRelation(
      investmentEntityRelations: FundDataContact.InvestmentEntityRelation
    ) derives CirceCodec.WithDefaultsAndTypeName

    final case class UpdateSingleContactMatrix(
      contactMatrixKey: FundDataContact.ContactMatrixKey,
      communicationRoleOpt: Option[FundDataContact.CommunicationRole] // if None, remove current matrix
    ) derives CirceCodec.WithDefaultsAndTypeName

  }

}

final case class BatchCreateContactMatricesResponse(
  batchActionId: BatchActionId
) derives CirceCodec.WithDefaultsAndTypeName

final case class BatchCreateContactRelationsParams(
  firmId: FundDataFirmId,
  contacts: List[BatchCreateContactRelationsParams.BatchCreateContactRelationsItem]
) extends FundDataInvestorsWithinFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName {

  override def investorIds: List[FundDataInvestorId] = contacts.flatMap {
    _.relations.flatMap { contact =>
      contact.clientId +: (contact.investmentEntitiesInClient match {
        case InvestmentEntitiesInClient.AllInvestmentEntities                           => Seq.empty
        case InvestmentEntitiesInClient.SpecificInvestmentEntities(investmentEntityIds) =>
          investmentEntityIds.map(_.parent)
      })
    }
  }.distinct

}

object BatchCreateContactRelationsParams {
  sealed trait ContactSource derives CirceCodec.WithDefaults, CanEqual

  object ContactSource {
    final case class ExistingContact(contactId: ContactId) extends ContactSource derives CirceCodec.WithDefaults

    final case class NewContact(email: String, firstName: String, lastName: String) extends ContactSource
        derives CirceCodec.WithDefaults

  }

  sealed trait InvestmentEntitiesInClient derives CanEqual, CirceCodec.WithDefaultsAndTypeName

  object InvestmentEntitiesInClient {
    case object AllInvestmentEntities extends InvestmentEntitiesInClient derives CirceCodec.WithDefaultsAndTypeName

    final case class SpecificInvestmentEntities(investmentEntityIds: List[FundDataInvestmentEntityId])
        extends InvestmentEntitiesInClient derives CirceCodec.WithDefaultsAndTypeName

  }

  final case class ClientAndInvestmentEntityRelation(
    clientId: FundDataInvestorId,
    investmentEntitiesInClient: InvestmentEntitiesInClient
  ) derives CirceCodec.WithDefaultsAndTypeName

  final case class BatchCreateContactRelationsItem(
    contactSource: ContactSource,
    relations: List[ClientAndInvestmentEntityRelation]
  ) derives CirceCodec.WithDefaultsAndTypeName

  final case class BatchCreateContactRelationsItemData(
    firmId: FundDataFirmId,
    contactIdOpt: Option[ContactId], // if None, create new contact
    email: String,
    firstName: String,
    lastName: String,
    relations: List[ClientAndInvestmentEntityRelation]
  ) derives CirceCodec.WithDefaultsAndTypeName {
    lazy val fullName: String = firstName + " " + lastName
  }

}

final case class BatchCreateContactRelationsResponse(
  batchActionId: BatchActionId
) derives CirceCodec.WithDefaultsAndTypeName

final case class BatchCheckDuplicatedContactsParams(
  firmId: FundDataFirmId,
  contacts: List[BatchCheckDuplicatedContactsParams.BatchCheckDuplicatedContactsItem]
) extends FundDataFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName

object BatchCheckDuplicatedContactsParams {

  final case class BatchCheckDuplicatedContactsItem(
    contactInfo: FundDataContact.ContactInfo,
    notes: String
  ) derives CirceCodec.WithDefaultsAndTypeName

}

final case class BatchCheckDuplicatedContactsResponse(
  existingContacts: List[BatchCheckDuplicatedContactsResponse.ContactBasicInfo]
) derives CirceCodec.WithDefaultsAndTypeName

object BatchCheckDuplicatedContactsResponse {

  final case class ContactBasicInfo(
    contactId: ContactId,
    firstName: String,
    lastName: String,
    email: String
  ) derives CirceCodec.WithDefaultsAndTypeName

}

final case class CheckDuplicatedContactInfoParams(firmId: FundDataFirmId, emails: Set[String], customIds: Set[String])
    extends FundDataFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName

final case class CheckDuplicatedContactInfoResponse(duplicatedEmails: Set[String], duplicatedCustomIds: Set[String])
    derives CirceCodec.WithDefaultsAndTypeName

final case class GetContactParams(firmId: FundDataFirmId, contactId: ContactId) extends FundDataFirmValidationParams
    derives CirceCodec.WithDefaultsAndTypeName

final case class GetContactResponse(contact: FundDataContact.Contact) derives CirceCodec.WithDefaultsAndTypeName

final case class GetFirmContactsParams(
  firmId: FundDataFirmId,
  mode: GetFirmContactsParams.Mode = GetFirmContactsParams.Mode.AllContacts,
  queryParams: ContactQueryParams = ContactQueryParams()
) extends FundDataFirmValidationParams,
      GetDashboardDataParams[ContactId, FundDataContact.Contact] derives CirceCodec.WithDefaultsAndTypeName

object GetFirmContactsParams {

  sealed trait Mode derives CanEqual, CirceCodec.WithDefaultsAndTypeName

  object Mode {
    case object AllContacts extends Mode
    case class SpecificInvestmentEntities(investmentEntityIds: Set[FundDataInvestmentEntityId]) extends Mode

    case class SpecificInvestmentEntitiesInFundLegalEntities(
      fundLegalEntityIds: Set[FundLegalEntityId],
      investmentEntityIds: Set[FundDataInvestmentEntityId],
      communicationTypeIds: Set[CommunicationTypeId]
    ) extends Mode

  }

}

final case class GetFirmContactsResponse(
  data: List[(ContactId, FundDataContact.Contact)],
  filterKeys: Set[ContactId],
  pagination: DashboardPaginationData
) extends GetDashboardDataResponse[ContactId, FundDataContact.Contact] derives CirceCodec.WithDefaultsAndTypeName

final case class GetFirmContactAdditionalInfosParams(
  firmId: FundDataFirmId,
  contactIds: List[ContactId],
  mode: GetFirmContactsParams.Mode = GetFirmContactsParams.Mode.AllContacts,
  skippedInformation: List[GetFirmContactAdditionalInfosParams.SkippedInformation]
) extends FundDataFirmValidationParams,
      FundDataContactsWithinFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName

object GetFirmContactAdditionalInfosParams {

  enum SkippedInformation derives CirceCodec.Enum, CanEqual {
    case ContactMatrix
    case ContactAccess
  }

}

final case class GetFirmContactAdditionalInfosResponse(
  contactAdditionalInfos: List[FundDataContact.ContactAdditionalInfo],
  investors: List[FundDataContact.InvestorInfo],
  investmentEntities: List[FundDataContact.InvestmentEntityInfo]
) extends GetDashboardAdditionalDataResponse[ContactId, FundDataContact.ContactAdditionalInfo]
    derives CirceCodec.WithDefaultsAndTypeName {

  override def data: List[(ContactId, FundDataContact.ContactAdditionalInfo)] =
    contactAdditionalInfos.map(contactAdditionalInfo => (contactAdditionalInfo.contactId, contactAdditionalInfo))

}

final case class GetFirmContactsInfoParams(
  firmId: FundDataFirmId,
  filterByContacts: Option[Set[ContactId]] = None
) extends FundDataFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName

final case class GetFirmContactsInfoResponse(data: List[FundDataContact.Contact])
    derives CirceCodec.WithDefaultsAndTypeName

final case class GetInvestmentEntityContactsInfoParams(investmentEntityId: FundDataInvestmentEntityId)
    extends FundDataFirmValidationParams
    with FundDataInvestmentEntitiesValidationParams
    with FundDataInvestmentEntitiesWithinFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName {
  override def firmId: FundDataFirmId = investmentEntityId.firmId
  override def investmentEntityIds: List[FundDataInvestmentEntityId] = List(investmentEntityId)
}

final case class GetInvestmentEntityContactsInfoResponse(
  data: List[FundDataContact.InvestmentEntityContact]
) derives CirceCodec.WithDefaultsAndTypeName

final case class GetInvestmentEntitiesContactsInfoParams(
  investmentEntityIds: List[FundDataInvestmentEntityId]
) extends FundDataFirmValidationParams
    with FundDataInvestmentEntitiesValidationParams
    with FundDataInvestmentEntitiesWithinFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName {
  override def firmId: FundDataFirmId = investmentEntityIds.headOption.fold(FundDataFirmId.defaultValue.get)(_.firmId)
}

final case class GetInvestmentEntitiesContactsInfoResponse(
  data: Map[FundDataInvestmentEntityId, List[FundDataContact.InvestmentEntityContact]]
) derives CirceCodec.WithDefaultsAndTypeName

final case class EditContactParams(
  firmId: FundDataFirmId,
  contactId: ContactId,
  firstNameUpdate: UpdateProperty[String] = UpdateProperty(),
  lastNameUpdate: UpdateProperty[String] = UpdateProperty(),
  customIdUpdate: UpdateProperty[String] = UpdateProperty(),
  phoneUpdate: UpdateProperty[String] = UpdateProperty(),
  companyUpdate: UpdateProperty[String] = UpdateProperty(),
  titleUpdate: UpdateProperty[String] = UpdateProperty(),
  countryUpdate: UpdateProperty[String] = UpdateProperty(),
  numberAndStreetUpdate: UpdateProperty[String] = UpdateProperty(),
  cityUpdate: UpdateProperty[String] = UpdateProperty(),
  stateUpdate: UpdateProperty[String] = UpdateProperty(),
  zipCodeUpdate: UpdateProperty[String] = UpdateProperty(),
  prefixUpdate: UpdateProperty[String] = UpdateProperty(),
  suffixUpdate: UpdateProperty[String] = UpdateProperty()
) extends FundDataFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName

final case class EditContactResponse() derives CirceCodec.WithDefaultsAndTypeName

final case class DeleteContactParams(firmId: FundDataFirmId, contactIds: Set[ContactId])
    extends FundDataFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName

final case class DeleteContactResponse() derives CirceCodec.WithDefaultsAndTypeName

// Contact Access
final case class GetContactAccessParams(firmId: FundDataFirmId, contactId: ContactId)
    extends FundDataFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName

final case class GetContactAccessResponse(contactAccess: FundDataContact.ContactAccess)
    derives CirceCodec.WithDefaultsAndTypeName

// Contact IE/Clients Relations

final case class AssignClientAndInvestmentEntitiesParams(
  firmId: FundDataFirmId,
  contactId: ContactId,
  toAddClients: Set[FundDataInvestorId] = Set.empty,
  toRemoveClients: Set[FundDataInvestorId] = Set.empty,
  toAddInvestmentEntities: List[InvestmentEntityRelation] = List.empty,
  toUpdateInvestmentEntities: List[InvestmentEntityRelationUpdate] = List.empty,
  toRemoveInvestmentEntities: Set[FundDataInvestmentEntityId] = Set.empty
) extends FundDataFirmValidationParams
    with FundDataInvestorsValidationParams
    with FundDataInvestmentEntitiesValidationParams
    with FundDataInvestmentEntitiesWithinFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName {

  override def investorIds: List[FundDataInvestorId] =
    (toAddClients ++ toRemoveClients ++ investmentEntityIds.map(_.parent)).toList

  override def investmentEntityIds: List[FundDataInvestmentEntityId] =
    (toAddInvestmentEntities.map(_.investmentEntityId) ++ toUpdateInvestmentEntities.map(
      _.investmentEntityId
    ) ++ toRemoveInvestmentEntities.toList).distinct

}

final case class AssignClientAndInvestmentEntitiesResponse() derives CirceCodec.WithDefaultsAndTypeName

final case class GetContactRelationParams(firmId: FundDataFirmId, contactId: ContactId)
    extends FundDataFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName

final case class GetContactRelationResponse(
  investmentEntityRelations: List[InvestmentEntityRelation],
  contactRelationInfos: FundDataContact.ContactRelationInfo
) derives CirceCodec.WithDefaultsAndTypeName

final case class GetInvestmentEntityRelationParams() derives CirceCodec.WithDefaultsAndTypeName

final case class GetInvestmentEntityRelationResponse() derives CirceCodec.WithDefaultsAndTypeName
// Communication Matrix

final case class GetContactMatrixParams(firmId: FundDataFirmId, contactId: ContactId)
    extends FundDataFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName

final case class GetContactMatrixResponse(
  contactMatrixValue: List[FundDataContact.ContactMatrixValue]
) derives CirceCodec.WithDefaultsAndTypeName

final case class UpdateContactMatrixParams(
  firmId: FundDataFirmId,
  contactId: ContactId,
  toAddInvestmentEntities: List[FundDataContact.InvestmentEntityRelation] = List.empty,
  toUpdateInvestmentEntities: List[FundDataContact.InvestmentEntityRelationUpdate] = List.empty,
  toRemoveInvestmentEntities: Set[FundDataInvestmentEntityId] = Set.empty,
  toAddMatrixValues: List[FundDataContact.ContactMatrixValue] = List.empty,
  toUpdateMatrixValues: List[FundDataContact.ContactMatrixValueUpdate] = List.empty,
  toRemoveMatrixValues: Set[FundDataContact.ContactMatrixKey] = Set.empty
) extends FundDataFirmValidationParams
    with FundDataInvestorsValidationParams
    with FundDataInvestmentEntitiesValidationParams
    with FundDataInvestmentEntitiesWithinFirmValidationParams
    with FundLegalEntitiesValidationParams derives CirceCodec.WithDefaultsAndTypeName {

  override def investmentEntityIds: List[FundDataInvestmentEntityId] =
    (toAddInvestmentEntities.map(_.investmentEntityId) ++
      toUpdateInvestmentEntities.map(_.investmentEntityId) ++
      toRemoveInvestmentEntities.toList ++
      toAddMatrixValues.map(_.contactMatrixKey.investmentEntityId) ++
      toUpdateMatrixValues.map(_.contactMatrixKey.investmentEntityId) ++
      toRemoveMatrixValues.map(_.investmentEntityId).toList).distinct

  override def investorIds: List[FundDataInvestorId] =
    investmentEntityIds.map(_.parent).distinct

  override def fundLegalEntityIds: List[FundLegalEntityId] =
    (toAddMatrixValues.map(_.contactMatrixKey.fundLegalEntityId) ++
      toUpdateMatrixValues.map(_.contactMatrixKey.fundLegalEntityId) ++
      toRemoveMatrixValues.map(_.fundLegalEntityId).toList).distinct

}

final case class UpdateContactMatrixResponse() derives CirceCodec.WithDefaultsAndTypeName

final case class GetContactMatrixByInvestmentEntityParams(investmentEntityId: FundDataInvestmentEntityId)
    extends FundDataFirmValidationParams
    with FundDataInvestmentEntitiesValidationParams derives CirceCodec.WithDefaultsAndTypeName {
  lazy val firmId: FundDataFirmId = investmentEntityId.firmId
  lazy val investmentEntityIds: List[FundDataInvestmentEntityId] = List(investmentEntityId)
}

final case class GetContactMatrixByInvestmentEntityResponse(
  contactMatrixValues: List[FundDataContact.ContactMatrixValueByInvestmentEntity]
) derives CirceCodec.WithDefaultsAndTypeName

final case class ExportContactsParams(firmId: FundDataFirmId, contactIds: Set[ContactId])
    extends FundDataFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName

final case class ExportContactsResponse(fileId: FileId) derives CirceCodec.WithDefaultsAndTypeName

final case class BatchExportContactsParams(
  firmId: FundDataFirmId,
  contactIds: Set[ContactId]
) extends FundDataFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName

final case class BatchExportContactsResp(
  fileUrl: String
) derives CirceCodec.WithDefaultsAndTypeName

final case class ExportContactsBatchItem(
  firmId: FundDataFirmId,
  contactIds: Set[ContactId]
) extends FundDataFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName

final case class ExportContactsBatchItemResp(
  contactInfoFileStorageId: DocumentStorageId,
  contactRelationshipsFileStorageId: DocumentStorageId,
  ieLevelCommunicationSettingFileStorageId: DocumentStorageId,
  contactCommunicationMatrixFileStorageId: DocumentStorageId,
  firmCommTypeNames: List[String]
) derives CirceCodec.WithDefaultsAndTypeName

final case class ExportContactsPostExecuteParams(
  firmId: FundDataFirmId,
  fileName: String
) derives CirceCodec.WithDefaultsAndTypeName

/** Contact for Client
  */
final case class GetClientContactsParams(
  clientId: FundDataInvestorId
) extends FundDataInvestorsValidationParams derives CirceCodec.WithDefaultsAndTypeName {
  override def investorIds: List[FundDataInvestorId] = List(clientId)
}

final case class GetClientContactsResponse(
  data: List[FundDataContact.ClientContact],
  investmentEntitiesInfo: Map[FundDataInvestmentEntityId, FundDataContact.InvestmentEntityInfo]
) derives CirceCodec.WithDefaultsAndTypeName

final case class AssignContactsToClientParams(
  clientId: FundDataInvestorId,
  contactIds: List[ContactId]
) extends FundDataInvestorsValidationParams
    with FundDataContactsWithinFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName {
  override def investorIds: List[FundDataInvestorId] = List(clientId)
  override def firmId: FundDataFirmId = clientId.firmId
}

final case class AssignContactsToClientResponse(
  batchActionId: BatchActionId
) derives CirceCodec.WithDefaultsAndTypeName

final case class AssignContactToClientParams(
  clientId: FundDataInvestorId,
  clientName: String,
  contactId: ContactId,
  contactFullName: String,
  contactEmail: String
) derives CirceCodec.WithDefaultsAndTypeName

final case class UnassignContactsFromClientParams(
  clientId: FundDataInvestorId,
  contactIds: List[ContactId]
) extends FundDataInvestorsValidationParams
    with FundDataContactsWithinFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName {
  override def investorIds: List[FundDataInvestorId] = List(clientId)
  override def firmId: FundDataFirmId = clientId.firmId
}

final case class UnassignContactsFromClientResponse(
  batchActionId: BatchActionId
) derives CirceCodec.WithDefaultsAndTypeName

final case class UnassignContactFromClientParams(
  clientId: FundDataInvestorId,
  clientName: String,
  contactId: ContactId,
  contactFullName: String,
  contactEmail: String
) derives CirceCodec.WithDefaultsAndTypeName

final case class UpdateContactsPermissionToClientParams(
  clientId: FundDataInvestorId,
  contactsPermissionUpdate: List[UpdateContactsPermissionToClientParams.ContactPermissionUpdate]
) extends FundDataInvestorsValidationParams
    with FundDataContactsWithinFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName {
  override def investorIds: List[FundDataInvestorId] = List(clientId)

  override def firmId: FundDataFirmId = clientId.firmId

  override def contactIds: List[ContactId] = contactsPermissionUpdate.map(_.contactId)
}

object UpdateContactsPermissionToClientParams {

  final case class ContactPermissionUpdate(
    contactId: ContactId,
    fundSubCommunicationTypeUpdate: UpdateProperty[Option[FundDataContact.FundSubCommunicationType]] =
      UpdateProperty.unchanged,
    documentRequestCommunicationTypeUpdate: UpdateProperty[Option[FundDataContact.DocumentRequestCommunicationType]] =
      UpdateProperty.unchanged
  ) derives CirceCodec.WithDefaultsAndTypeName

}

final case class UpdateInvestmentEntityContactsParams(
  investmentEntityId: FundDataInvestmentEntityId,
  updateContactOperations: List[UpdateInvestmentEntityContactsParams.UpdateOperation],
  mode: UpdateInvestmentEntityContactsParams.Mode
) extends FundDataInvestmentEntitiesValidationParams
    with FundDataContactsWithinFirmValidationParams derives CirceCodec.WithDefaultsAndTypeName {
  override def investmentEntityIds: List[FundDataInvestmentEntityId] = List(investmentEntityId)
  override def firmId: FundDataFirmId = investmentEntityId.parent.parent
  override def contactIds: List[ContactId] = updateContactOperations.map(_.contactId).distinct
}

object UpdateInvestmentEntityContactsParams {

  enum Mode derives CirceCodec.Enum, CanEqual {
    case Assign
    case Update
  }

  case class UpdateOperation(
    contactId: ContactId,
    toAddInvestmentEntity: Option[FundDataContact.InvestmentEntityRelation] = None,
    toUpdateInvestmentEntity: Option[FundDataContact.InvestmentEntityRelationUpdate] = None,
    toRemoveInvestmentEntity: Option[FundDataInvestmentEntityId] = None,
    toAddMatrixValues: List[FundDataContact.ContactMatrixValue] = List.empty,
    toUpdateMatrixValues: List[FundDataContact.ContactMatrixValueUpdate] = List.empty,
    toRemoveMatrixValues: Set[FundDataContact.ContactMatrixKey] = Set.empty
  ) derives CirceCodec.WithDefaultsAndTypeName

}

final case class UpdateInvestmentEntityContactsResponse(
  batchActionId: BatchActionId
) derives CirceCodec.WithDefaultsAndTypeName

final case class UpdateInvestmentEntityContactParams(
  investmentEntityId: FundDataInvestmentEntityId,
  contactId: ContactId,
  contactFullName: String,
  contactEmail: String,
  operation: UpdateInvestmentEntityContactsParams.UpdateOperation
) derives CirceCodec.WithDefaultsAndTypeName

final case class BatchRemoveContactMatrixParams(
  firmId: FundDataFirmId,
  contactIds: List[ContactId],
  fundLegalEntityIds: List[FundLegalEntityId],
  investmentEntityIds: List[FundDataInvestmentEntityId],
  communicationTypeIds: List[CommunicationTypeId]
) extends FundDataFirmValidationParams
    with FundDataInvestmentEntitiesValidationParams
    with FundDataInvestmentEntitiesWithinFirmValidationParams
    with FundLegalEntitiesValidationParams derives CirceCodec.WithDefaultsAndTypeName

final case class BatchRemoveContactMatrixResponse() derives CirceCodec.WithDefaultsAndTypeName
