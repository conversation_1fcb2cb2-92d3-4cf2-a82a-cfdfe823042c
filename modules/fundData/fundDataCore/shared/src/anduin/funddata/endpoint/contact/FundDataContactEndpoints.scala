// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.funddata.endpoint.contact

import sttp.tapir.*

import anduin.asyncapiv2.execution.AsyncApiTemporalQueue
import anduin.id.batchaction.BatchActionId
import anduin.service.GeneralServiceException
import anduin.tapir.AsyncEndpoint.AsyncAuthenticatedEndpoint
import anduin.tapir.AuthenticatedEndpoints.BaseAuthenticatedEndpoint
import anduin.tapir.{AsyncEndpoint, AuthenticatedEndpoints}

object FundDataContactEndpoints extends AuthenticatedEndpoints with AsyncEndpoint {
  private val Path = "funddata" / "contact"

  val createContact: BaseAuthenticatedEndpoint[CreateContactParams, GeneralServiceException, CreateContactResponse] = {
    authEndpoint[CreateContactParams, GeneralServiceException, CreateContactResponse](
      Path / "createContact"
    )
  }

  val checkDuplicatedContactInfo: BaseAuthenticatedEndpoint[
    CheckDuplicatedContactInfoParams,
    GeneralServiceException,
    CheckDuplicatedContactInfoResponse
  ] = {
    authEndpoint[
      CheckDuplicatedContactInfoParams,
      GeneralServiceException,
      CheckDuplicatedContactInfoResponse
    ](
      Path / "checkDuplicatedContactInfo"
    )
  }

  val batchCheckDuplicatedContacts: BaseAuthenticatedEndpoint[
    BatchCheckDuplicatedContactsParams,
    GeneralServiceException,
    BatchCheckDuplicatedContactsResponse
  ] = {
    authEndpoint[
      BatchCheckDuplicatedContactsParams,
      GeneralServiceException,
      BatchCheckDuplicatedContactsResponse
    ](
      Path / "batchCheckDuplicatedContacts"
    )
  }

  val batchCreateContacts: AsyncAuthenticatedEndpoint[
    BatchCreateContactsParams,
    GeneralServiceException,
    BatchCreateContactsResponse
  ] = {
    asyncEndpoint[
      BatchCreateContactsParams,
      GeneralServiceException,
      BatchCreateContactsResponse
    ](
      Path / "batchCreateContacts",
      AsyncApiTemporalQueue.Heavy
    )
  }

  val batchCreateContactRelations: AsyncAuthenticatedEndpoint[
    BatchCreateContactRelationsParams,
    GeneralServiceException,
    BatchCreateContactRelationsResponse
  ] = {
    asyncEndpoint[
      BatchCreateContactRelationsParams,
      GeneralServiceException,
      BatchCreateContactRelationsResponse
    ](
      Path / "batchCreateContactRelations",
      AsyncApiTemporalQueue.Heavy
    )
  }

  val batchCreateContactMatrices: AsyncAuthenticatedEndpoint[
    BatchCreateContactMatricesParams,
    GeneralServiceException,
    BatchCreateContactMatricesResponse
  ] = {
    asyncEndpoint[
      BatchCreateContactMatricesParams,
      GeneralServiceException,
      BatchCreateContactMatricesResponse
    ](
      Path / "batchCreateContactMatrices",
      AsyncApiTemporalQueue.Heavy
    )
  }

  val editContactInfo: BaseAuthenticatedEndpoint[EditContactParams, GeneralServiceException, EditContactResponse] = {
    authEndpoint[EditContactParams, GeneralServiceException, EditContactResponse](
      Path / "editContactInfo"
    )
  }

  val getFirmContacts: BaseAuthenticatedEndpoint[
    GetFirmContactsParams,
    GeneralServiceException,
    GetFirmContactsResponse
  ] = {
    authEndpoint[
      GetFirmContactsParams,
      GeneralServiceException,
      GetFirmContactsResponse
    ](
      Path / "getFirmContacts"
    )
  }

  val getFirmContactAdditionalInfos: BaseAuthenticatedEndpoint[
    GetFirmContactAdditionalInfosParams,
    GeneralServiceException,
    GetFirmContactAdditionalInfosResponse
  ] = {
    authEndpoint[
      GetFirmContactAdditionalInfosParams,
      GeneralServiceException,
      GetFirmContactAdditionalInfosResponse
    ](
      Path / "getFirmContactAdditionalInfos"
    )
  }

  val getContact: BaseAuthenticatedEndpoint[GetContactParams, GeneralServiceException, GetContactResponse] = {
    authEndpoint[GetContactParams, GeneralServiceException, GetContactResponse](
      Path / "getContact"
    )
  }

  val getFirmContactsInfo: BaseAuthenticatedEndpoint[
    GetFirmContactsInfoParams,
    GeneralServiceException,
    GetFirmContactsInfoResponse
  ] = {
    authEndpoint[
      GetFirmContactsInfoParams,
      GeneralServiceException,
      GetFirmContactsInfoResponse
    ](
      Path / "getFirmContactsInfo"
    )
  }

  val getInvestmentEntityContactsInfo: BaseAuthenticatedEndpoint[
    GetInvestmentEntityContactsInfoParams,
    GeneralServiceException,
    GetInvestmentEntityContactsInfoResponse
  ] = {
    authEndpoint[
      GetInvestmentEntityContactsInfoParams,
      GeneralServiceException,
      GetInvestmentEntityContactsInfoResponse
    ](
      Path / "getInvestmentEntityContactsInfo"
    )
  }

  val getInvestmentEntitiesContactsInfo: BaseAuthenticatedEndpoint[
    GetInvestmentEntitiesContactsInfoParams,
    GeneralServiceException,
    GetInvestmentEntitiesContactsInfoResponse
  ] = {
    authEndpoint[
      GetInvestmentEntitiesContactsInfoParams,
      GeneralServiceException,
      GetInvestmentEntitiesContactsInfoResponse
    ](
      Path / "getInvestmentEntitiesContactsInfo"
    )
  }

  val deleteContact: AsyncAuthenticatedEndpoint[DeleteContactParams, GeneralServiceException, DeleteContactResponse] = {
    asyncEndpoint[DeleteContactParams, GeneralServiceException, DeleteContactResponse](
      Path / "deleteContact",
      AsyncApiTemporalQueue.Heavy
    )
  }

  private val PathContactAccess = Path / "access"

  val getContactAccess: BaseAuthenticatedEndpoint[
    GetContactAccessParams,
    GeneralServiceException,
    GetContactAccessResponse
  ] = {
    authEndpoint[
      GetContactAccessParams,
      GeneralServiceException,
      GetContactAccessResponse
    ](
      PathContactAccess / "getContactAccess"
    )
  }

  private val PathRelation = Path / "relation"

  val assignClientsAndInvestmentEntities: BaseAuthenticatedEndpoint[
    AssignClientAndInvestmentEntitiesParams,
    GeneralServiceException,
    AssignClientAndInvestmentEntitiesResponse
  ] = {
    authEndpoint[
      AssignClientAndInvestmentEntitiesParams,
      GeneralServiceException,
      AssignClientAndInvestmentEntitiesResponse
    ](
      PathRelation / "assignClientsAndInvestmentEntities"
    )
  }

  val getContactRelation: BaseAuthenticatedEndpoint[
    GetContactRelationParams,
    GeneralServiceException,
    GetContactRelationResponse
  ] = {
    authEndpoint[
      GetContactRelationParams,
      GeneralServiceException,
      GetContactRelationResponse
    ](
      PathRelation / "getContactRelation"
    )
  }

  private val PathContactMatrix = Path / "contact-matrix"

  val getContactMatrix: BaseAuthenticatedEndpoint[
    GetContactMatrixParams,
    GeneralServiceException,
    GetContactMatrixResponse
  ] = {
    authEndpoint[
      GetContactMatrixParams,
      GeneralServiceException,
      GetContactMatrixResponse
    ](
      PathContactMatrix / "getContactMatrix"
    )
  }

  val updateContactMatrix: BaseAuthenticatedEndpoint[
    UpdateContactMatrixParams,
    GeneralServiceException,
    UpdateContactMatrixResponse
  ] = {
    authEndpoint[
      UpdateContactMatrixParams,
      GeneralServiceException,
      UpdateContactMatrixResponse
    ](
      PathContactMatrix / "updateContactMatrix"
    )
  }

  val batchRemoveContactMatrix: BaseAuthenticatedEndpoint[
    BatchRemoveContactMatrixParams,
    GeneralServiceException,
    BatchRemoveContactMatrixResponse
  ] = {
    authEndpoint[
      BatchRemoveContactMatrixParams,
      GeneralServiceException,
      BatchRemoveContactMatrixResponse
    ](
      PathContactMatrix / "batchRemoveContactMatrix"
    )
  }

  val getContactMatrixByInvestmentEntity: BaseAuthenticatedEndpoint[
    GetContactMatrixByInvestmentEntityParams,
    GeneralServiceException,
    GetContactMatrixByInvestmentEntityResponse
  ] = {
    authEndpoint[
      GetContactMatrixByInvestmentEntityParams,
      GeneralServiceException,
      GetContactMatrixByInvestmentEntityResponse
    ](
      Path / "getContactMatrixByInvestmentEntity"
    )
  }

  val batchExportContacts: BaseAuthenticatedEndpoint[
    BatchExportContactsParams,
    GeneralServiceException,
    BatchActionId
  ] = {
    authEndpoint[
      BatchExportContactsParams,
      GeneralServiceException,
      BatchActionId
    ](
      Path / "batchExportContacts"
    )
  }

  /** Contact for Client
    */
  val getClientContacts: BaseAuthenticatedEndpoint[
    GetClientContactsParams,
    GeneralServiceException,
    GetClientContactsResponse
  ] = {
    authEndpoint[
      GetClientContactsParams,
      GeneralServiceException,
      GetClientContactsResponse
    ](
      Path / "getClientContacts"
    )
  }

  val assignContactsToClient: AsyncAuthenticatedEndpoint[
    AssignContactsToClientParams,
    GeneralServiceException,
    AssignContactsToClientResponse
  ] = {
    asyncEndpoint[
      AssignContactsToClientParams,
      GeneralServiceException,
      AssignContactsToClientResponse
    ](
      Path / "assignContactsToClient",
      AsyncApiTemporalQueue.Heavy
    )
  }

  val unassignContactsFromClient: AsyncAuthenticatedEndpoint[
    UnassignContactsFromClientParams,
    GeneralServiceException,
    UnassignContactsFromClientResponse
  ] = {
    asyncEndpoint[
      UnassignContactsFromClientParams,
      GeneralServiceException,
      UnassignContactsFromClientResponse
    ](
      Path / "unassignContactsFromClient",
      AsyncApiTemporalQueue.Heavy
    )
  }

  val updateContactsPermissionToClient: AsyncAuthenticatedEndpoint[
    UpdateContactsPermissionToClientParams,
    GeneralServiceException,
    Unit
  ] = {
    asyncEndpoint[
      UpdateContactsPermissionToClientParams,
      GeneralServiceException,
      Unit
    ](
      Path / "updateContactsPermissionToClient",
      AsyncApiTemporalQueue.Heavy
    )
  }

  val updateInvestmentEntityContacts: AsyncAuthenticatedEndpoint[
    UpdateInvestmentEntityContactsParams,
    GeneralServiceException,
    UpdateInvestmentEntityContactsResponse
  ] = {
    asyncEndpoint[
      UpdateInvestmentEntityContactsParams,
      GeneralServiceException,
      UpdateInvestmentEntityContactsResponse
    ](
      Path / "updateInvestmentEntityContacts",
      AsyncApiTemporalQueue.Heavy
    )
  }

}
