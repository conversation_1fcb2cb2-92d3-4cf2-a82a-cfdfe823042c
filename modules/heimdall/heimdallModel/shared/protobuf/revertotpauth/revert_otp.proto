syntax = "proto3";

package revertotpauth;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.revertotpauth"
  single_file: true
  import: "anduin.id.account.RevertOTPAuthenticationRequestId"
  import: "anduin.id.account.RevertOTPAuthenticationCodeId"
  import: "anduin.model.common.user.UserId"
};

message RevertOTPAuthenticationRequest {
  string revert_otp_authentication_request_id = 1 [(scalapb.field).type = "RevertOTPAuthenticationRequestId"];
  string revert_otp_authentication_code_id = 2 [(scalapb.field).type = "RevertOTPAuthenticationCodeId"];
}

message UserRevertOTPRequest {
  string userId = 1 [(scalapb.field).type = "UserId"];
  map<string, RevertOTPAuthenticationRequest> requests = 2;
}

message RecordTypeUnion {
  UserRevertOTPRequest _UserRevertOTPRequest = 1;
}