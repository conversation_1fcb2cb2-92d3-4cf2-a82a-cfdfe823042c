syntax = "proto3";

package botdetector;

import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.botdetector"
  single_file: true
  import: "java.time.Instant"
};

message BotDetectorNonceValue {
    google.protobuf.StringValue ip = 1;
    google.protobuf.StringValue userAgent = 2;
    InstantMessage createdAt = 3 [(scalapb.field).type = "Instant"];
}
