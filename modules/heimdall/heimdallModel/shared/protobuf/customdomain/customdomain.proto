syntax = "proto3";

package customdomain;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.customdomain"
  single_file: true
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.customdomain.CustomDomainId"
  import: "anduin.id.customdomain.CustomDomainOfferingId"
  import: "anduin.id.offering.GlobalOfferingId"
};

enum CustomDomainType {
  reserved 1, 2, 3;
  System = 0;
  Environment = 4;
  MultiRegion = 5;
}

message CustomDomain {
  string id = 1 [(scalapb.field).type = "CustomDomainId"];
  string domain = 2;
  CustomDomainType domainType = 3;
  string creator = 4 [(scalapb.field).type = "UserId"];
  int64 createdAt = 5;
}

message CustomDomainOfferingMapping {
  string id = 1 [(scalapb.field).type = "CustomDomainOfferingId"];
  string offeringId = 2 [(scalapb.field).type = "GlobalOfferingId"];
}

message OfferingPrimaryDomainMapping {
  string offeringId = 1 [(scalapb.field).type = "GlobalOfferingId"];
  string customDomainId = 2 [(scalapb.field).type = "CustomDomainId"];
}

message RecordTypeUnion {
  CustomDomain _CustomDomainV2 = 1;
  CustomDomainOfferingMapping _CustomDomainOfferingMapping = 2;
  OfferingPrimaryDomainMapping _OfferingPrimaryDomainMapping = 3;
}