syntax = "proto3";

package policy;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.policy"
  single_file: true
  import: "anduin.model.common.user.UserId"
};

enum AccountPolicyType {
  MFARequired = 0;
  PasswordRequired = 1;
  ShortSession = 2;
}

message AccountPolicyEnforcer {
  string id = 1;
  string name = 2;
}

message AccountPolicyData {
  AccountPolicyType policyType = 1;
  repeated AccountPolicyEnforcer enforcers = 2;
}

message AccountPolicy {
  string userId = 1 [(scalapb.field).type = "UserId"];
  repeated AccountPolicyData policies = 2;
}

message RecordTypeUnion {
  AccountPolicy _AccountPolicy = 1;
}