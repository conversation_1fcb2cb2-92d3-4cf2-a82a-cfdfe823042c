syntax = "proto3";

package otpauth;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.otpauth"
  single_file: true
  import: "anduin.id.account.OTPAuthenticationRequestId"
  import: "anduin.model.common.user.UserId"
  import: "java.time.Instant"
};



message OTPRequest {
  string requestId = 1 [(scalapb.field).type = "OTPAuthenticationRequestId"];
  InstantMessage createdAt = 2 [(scalapb.field).type = "Instant"];
}

message UserOTPRequest {
  reserved 2, 3;
  reserved "requestId", "createdAt";
  string userId = 1 [(scalapb.field).type = "UserId"];
  map<string, OTPRequest> requests = 4;
}

message RecordTypeUnion {
  UserOTPRequest _UserOTPRequest = 1;
}