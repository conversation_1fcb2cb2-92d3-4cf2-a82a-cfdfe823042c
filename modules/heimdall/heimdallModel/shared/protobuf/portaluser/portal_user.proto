syntax = "proto3";

package portaluser;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.portaluser"
  single_file: true
  import: "anduin.id.role.portal.PortalGroupId"
  import: "anduin.model.common.user.UserId"
};

message PortalUser {
  reserved 4;
  reserved "productRoleIds";
  string userId = 1 [(scalapb.field).type = "UserId"];
  string creator = 2 [(scalapb.field).type = "UserId"];
  bool isRoot = 3;
  int64 createdAt = 5;
  bool useRebac = 6;
}

message PortalGroup {
  reserved 4;
  reserved "productRoleIds";
  string groupId = 1 [(scalapb.field).type = "PortalGroupId"];
  string name = 2;
  string creator = 3 [(scalapb.field).type = "UserId"];
  int64 createdAt = 5;
  bool useRebac = 6;
}

enum PortalUserAuditEvent {
  Empty = 0;
  CreateUser = 1;
  RemoveUser = 2;
  EditUser = 3;
  CreateGroup = 4;
  RemoveGroup = 5;
  EditGroup = 6;
  AssignUserGroup = 7;
  RemoveUserGroup = 8;
  SetPermissions = 9;
  AssignAdminGroup = 10;
  RemoveAdminGroup = 11;
}

message PortalUserAuditLog {
  reserved 7;
  reserved "productRoleIds";
  string targetId = 1;
  string actor = 2 [(scalapb.field).type = "UserId"];
  PortalUserAuditEvent eventType = 3;
  int64 eventTime = 4;
  string userAgent = 5;
  string ip = 6;
  repeated string userGroupTargets = 8;
}

message RecordTypeUnion {
  PortalUser _PortalUser = 1;
  PortalGroup _PortalGroup = 2;
}
