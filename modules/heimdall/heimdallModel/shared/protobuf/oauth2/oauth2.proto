syntax = "proto3";

package oauth2;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.oauth2"
  preserve_unknown_fields: false
  flat_package: true
  single_file: true
  lenses: false
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.oauth2.Oauth2IntegrationClientId"
  import: "anduin.id.oauth2.Oauth2RefreshTokenId"
  import: "java.time.Instant"
};

message Oauth2Integration {
  string clientId = 1 [(scalapb.field).type = "Oauth2IntegrationClientId"];
  string clientSecretEncrypted = 2;
  string name = 3;
  string redirectUrl = 4;
  string creatorId = 5 [(scalapb.field).type = "UserId"];
  InstantMessage createdAt = 6 [(scalapb.field).type = "Instant"];
  InstantMessage updatedAt = 7 [(scalapb.field).type = "Instant"];
}

message Oauth2RefreshToken {
  string refreshTokenId = 1 [(scalapb.field).type = "Oauth2RefreshTokenId"];
  string clientId = 2 [(scalapb.field).type = "Oauth2IntegrationClientId"];
  string userId = 3 [(scalapb.field).type = "UserId"];
}

message RecordTypeUnion {
  Oauth2Integration _Oauth2Integration = 1;
  Oauth2RefreshToken _Oauth2RefreshToken = 2;
}
