syntax = "proto3";

package anduin.disclaimer.setting;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.disclaimer"
  single_file: true
  import: "anduin.id.disclaimer.DisclaimerId"
  import: "anduin.radix.RadixId"
};

message DisclaimerSettingClient {
  string disclaimer_id = 1 [(scalapb.field).type = "DisclaimerId"];
  string protected_resource_id = 9 [(scalapb.field).type = "RadixId"];

  string name = 2;
  string title = 8;
  string header = 3;
  string content = 4;
  string primary_button_copy = 5;

  bool show_anduin_terms = 6;
  optional string logo_url = 7;
  bool skip_existing_user_to_accept = 10;
}