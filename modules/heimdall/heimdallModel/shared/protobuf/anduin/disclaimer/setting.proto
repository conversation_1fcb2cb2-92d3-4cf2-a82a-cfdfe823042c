syntax = "proto3";

package anduin.disclaimer.setting;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.disclaimer.setting"
  single_file: true
  import: "anduin.model.common.user.UserId"
  import: "anduin.radix.RadixId"
  import: "anduin.id.disclaimer.DisclaimerId"
  import: "java.time.Instant"
};

message DisclaimerSetting {
  string protected_resource_id = 1 [(scalapb.field).type = "RadixId"];
  string disclaimer_id = 2 [(scalapb.field).type = "DisclaimerId"];

  string created_by = 3 [(scalapb.field).type = "UserId"];
  InstantMessage created_at = 4 [(scalapb.field).type = "Instant"];

  bool isEnabled = 5;
  bool skip_existing_user_to_accept = 6;
}

message RecordTypeUnion {
  DisclaimerSetting _DisclaimerSetting = 1;
}
