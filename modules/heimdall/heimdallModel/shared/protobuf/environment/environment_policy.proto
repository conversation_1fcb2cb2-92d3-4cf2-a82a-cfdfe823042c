syntax = "proto3";

package environment.policy;

import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";
import "date_time.proto";
import "environment/environment_auth.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.environment.policy"
  single_file: true
  import: "anduin.id.environment.EnvironmentId"
  import: "anduin.id.environment.EnvironmentReauthenticationPolicyId"
  import: "anduin.id.environment.EnvironmentEnforcementPolicyId"
  import: "anduin.id.account.EnterpriseLoginConfigId"
  import: "java.time.Instant"
};

// Reauthentication policy
message EnvironmentPolicyReauthenticationFallbackConfig {
  string defaultProvider = 1 [(scalapb.field).type = "Option[EnterpriseLoginConfigId]"];
  string noAccessMessageTitle = 2;
  string noAccessMessageBody = 3;
}

message EnvironmentPolicyReauthenticationGlobalConfig {
  string enforcedProvider = 1 [(scalapb.field).type = "Option[EnterpriseLoginConfigId]"];
}

message EnvironmentReauthenticationPolicy {
  string id = 1 [(scalapb.field).type = "EnvironmentReauthenticationPolicyId"];
  string environmentId = 2 [(scalapb.field).type = "EnvironmentId"];
  bool isEnabled = 3;
  InstantMessage createdAt = 4 [(scalapb.field).type = "Instant"];
  repeated string enterpriseLoginConfigIds = 5 [(scalapb.field).type = "EnterpriseLoginConfigId"];
  EnvironmentPolicyReauthenticationFallbackPolicy fallbackPolicy = 6;
  EnvironmentPolicyReauthenticationFallbackConfig fallbackConfig = 7;
  EnvironmentPolicyReauthenticationGlobalConfig globalConfig = 8;
}

// Enforcement policy
message EnvironmentAccountPolicyEnforcementConfig {
  bool enforce_mfa = 1;
  bool enforce_short_session = 2;
}

message EnvironmentEnforcementPolicy {
  string id = 1 [(scalapb.field).type = "EnvironmentEnforcementPolicyId"];
  string environmentId = 2 [(scalapb.field).type = "EnvironmentId"];
  bool isEnabled = 3;
  InstantMessage createdAt = 4 [(scalapb.field).type = "Instant"];
  EnvironmentAccountPolicyEnforcementConfig accountEnforcementConfig = 5;
}

message RecordTypeUnion {
  EnvironmentReauthenticationPolicy _EnvironmentReauthenticationPolicy = 1;
  EnvironmentEnforcementPolicy _EnvironmentEnforcementPolicy = 2;
}
