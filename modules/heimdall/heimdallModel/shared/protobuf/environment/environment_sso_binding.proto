syntax = "proto3";

package environment.sso;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.environment.sso"
  single_file: true
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.environment.EnvironmentSSOBindingId"
  import: "anduin.id.environment.EnvironmentId"
  import: "anduin.id.environment.UserEnvironmentSSOBindingId"
  import: "anduin.id.environment.EmailDomainEnvironmentSSOBindingId"
  import: "anduin.id.environment.GlobalEmailDomainEnvironmentSSOBindingId"
  import: "anduin.id.offering.GlobalOfferingId"
  import: "anduin.id.account.EnterpriseLoginConfigId"
};

message EnvironmentSSOBinding {
  string id = 1 [(scalapb.field).type = "EnvironmentSSOBindingId"];
  string environmentId = 2 [(scalapb.field).type = "EnvironmentId"];
  string offeringId = 3 [(scalapb.field).type = "GlobalOfferingId"];
  string enterpriseLoginConfigId = 4 [(scalapb.field).type = "Option[EnterpriseLoginConfigId]"];
  bool useAnduinAuth = 5;
  string appContext = 6;
}

message UserEnvironmentSSOBinding {
  string id = 1 [(scalapb.field).type = "UserEnvironmentSSOBindingId"];
  string userId = 2 [(scalapb.field).type = "UserId"];
  string ssoBindingId = 3 [(scalapb.field).type = "EnvironmentSSOBindingId"];
  string appContext = 4;
  string environmentId = 5 [(scalapb.field).type = "EnvironmentId"];
  string offeringId = 6 [(scalapb.field).type = "GlobalOfferingId"];
  map<string, string> mainRegionMappingIds = 7 [(scalapb.field).value_type = "UserEnvironmentSSOBindingId"];
}

message EmailDomainEnvironmentSSOBinding {
  string id = 1 [(scalapb.field).type = "EmailDomainEnvironmentSSOBindingId"];
  string email = 2;
  string environmentId = 3 [(scalapb.field).type = "EnvironmentId"];
  string offeringId = 4 [(scalapb.field).type = "GlobalOfferingId"];
  string ssoBindingId = 5 [(scalapb.field).type = "EnvironmentSSOBindingId"];
  string appContext = 6;
}

message GlobalEmailDomainEnvironmentSSOBinding {
  string id = 1 [(scalapb.field).type = "GlobalEmailDomainEnvironmentSSOBindingId"];
  string email = 2;
  string environmentId = 3 [(scalapb.field).type = "EnvironmentId"];
  string ssoBindingId = 4 [(scalapb.field).type = "EnvironmentSSOBindingId"];
}

message RecordTypeUnion {
  EnvironmentSSOBinding _EnvironmentSSOBinding = 1;
  UserEnvironmentSSOBinding _UserEnvironmentSSOBinding = 2;
  EmailDomainEnvironmentSSOBinding _EmailDomainEnvironmentSSOBinding = 3;
  GlobalEmailDomainEnvironmentSSOBinding _GlobalEmailDomainEnvironmentSSOBinding = 4;
}