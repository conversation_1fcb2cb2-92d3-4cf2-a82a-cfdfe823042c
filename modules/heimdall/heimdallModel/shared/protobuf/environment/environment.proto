syntax = "proto3";

package environment;

import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";
import "environment/environment_auth.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.environment"
  single_file: true
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.environment.EnvironmentId"
  import: "anduin.id.environment.EnvironmentWhitelabelId"
  import: "anduin.id.environment.EnvironmentPolicyId"
  import: "anduin.id.environment.EnvironmentEmailConfigId"
  import: "anduin.id.customdomain.CustomDomainId"
  import: "anduin.id.offering.GlobalOfferingId"
  import: "anduin.id.authwhitelabel.AuthenticationWhitelabelId"
  import: "anduin.id.theme.ThemeWhitelabelId"
  import: "anduin.dms.DocumentStorageIdMapper.given"
  import: "anduin.model.document.DocumentStorageId"
  import: "anduin.model.id.email.provider.EmailProviderId"
};

enum EnvironmentType {
  Normal = 0;
  Shadow = 1;
}

message EnvironmentData {
  string id = 1 [(scalapb.field).type = "EnvironmentId"];
  string name = 2;
  string creator = 3 [(scalapb.field).type = "UserId"];
  int64 createdAt = 4;
  string mainId = 5 [(scalapb.field).type = "Option[EnvironmentId]"];
  EnvironmentType environmentType = 6;
}

enum EnvironmentCustomDomainType {
  Fallback = 0;
  Platform = 1;
  Offering = 2;
  MultiRegion = 3;
}

enum EnvironmentCustomDomainCategory {
  Primary = 0;
  Secondary = 1;
  Tertiary = 2;
}

message EnvironmentCustomDomain {
  reserved 5;
  string customDomainId = 1 [(scalapb.field).type = "CustomDomainId"];
  string environmentId = 2 [(scalapb.field).type = "EnvironmentId"];
  EnvironmentCustomDomainType domainType = 3;
  string offeringId = 4 [(scalapb.field).type = "Option[GlobalOfferingId]"];
  EnvironmentCustomDomainCategory category = 6;
}

message EnvironmentWhitelabelCommonData {
  google.protobuf.StringValue provider_long_logo_storage_id = 1 [(scalapb.field).type = "DocumentStorageId"];
  bool hide_lp_profile = 2; // adhoc switcher to show/hide lp profile on environment dashboard
}

message EnvironmentThemeWhitelabel {
  string theme_whitelabel_id = 2 [(scalapb.field).type = "ThemeWhitelabelId"];
  repeated string offering_ids = 3 [(scalapb.field).type = "GlobalOfferingId"];
}

message EnvironmentWhitelabel {
  string id = 1 [(scalapb.field).type = "EnvironmentWhitelabelId"];
  EnvironmentWhitelabelCommonData common_data = 2;
  string authentication_whitelabel_id = 3 [(scalapb.field).type = "AuthenticationWhitelabelId"];
  map<string, string> custom_values = 4;
  EnvironmentThemeWhitelabel theme_whitelabel = 5;
}

enum EnvironmentPolicyType {
  Reauthentication = 0;
}


message EnvironmentPolicy {
  string id = 1 [(scalapb.field).type = "EnvironmentPolicyId"];
  string environmentId = 2 [(scalapb.field).type = "EnvironmentId"];
  bool isEnabled = 3;
  EnvironmentPolicyType policyType = 4;
}

message EnvironmentCustomSmtpConfig {
  bool enabled = 1;
  string provider_id = 2 [(scalapb.field).type = "Option[EmailProviderId]"];
}

message EnvironmentEmailConfig {
  string id = 1 [(scalapb.field).type = "EnvironmentEmailConfigId"];
  string environmentId = 2 [(scalapb.field).type = "EnvironmentId"];
  EnvironmentCustomSmtpConfig custom_smtp_config = 3;
}

message RecordTypeUnion {
  EnvironmentData _EnvironmentData = 1;
  EnvironmentCustomDomain _EnvironmentCustomDomain = 2;
  EnvironmentWhitelabel _EnvironmentWhitelabel = 3;
  EnvironmentPolicy _EnvironmentPolicy = 4;
  EnvironmentEmailConfig _EnvironmentEmailConfig = 5;
}