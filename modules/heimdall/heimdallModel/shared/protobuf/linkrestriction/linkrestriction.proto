syntax = "proto3";

package linkrestriction;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.linkrestriction"
  single_file: true
  import: "anduin.id.linkrestriction.LinkRestrictionId"
  import: "anduin.id.linkrestriction.LinkRestrictionListId"
  import: "java.time.Instant"
};

enum LinkRestrictionType {
  AutoLogin = 0;
}

message LinkRestriction {
  string id = 1 [(scalapb.field).type = "LinkRestrictionId"];
  LinkRestrictionType restrictionType = 2;
  string config = 3;
}

message LinkRestrictionList {
  string id = 1 [(scalapb.field).type = "LinkRestrictionListId"];
  repeated LinkRestriction restrictions = 2;
  InstantMessage createdAt = 3 [(scalapb.field).type = "Instant"];
}

message RecordTypeUnion {
  LinkRestrictionList _LinkRestrictionList = 1;
}