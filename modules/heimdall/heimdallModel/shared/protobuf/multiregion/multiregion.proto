syntax = "proto3";

package multiregion;

import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.multiregion"
  single_file: true
  import: "anduin.id.customdomain.CustomDomainId"
};

message MultiRegionCustomDomain {
  string customDomainId = 1 [(scalapb.field).type = "CustomDomainId"];
  string region = 2;
}

message RecordTypeUnion {
  MultiRegionCustomDomain _MultiRegionCustomDomain = 1;
}