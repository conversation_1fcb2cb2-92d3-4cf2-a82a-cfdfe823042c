syntax = "proto3";

package anduin.protobuf.theme;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.theme"
  single_file: true
  import: "anduin.id.theme.ThemeWhitelabelId"
};

message GrayColorSet {
  string shade_1 = 1;
  string shade_2 = 2;
  string shade_3 = 3;
  string shade_4 = 4;
  string shade_5 = 5;
  string shade_6 = 6;
  string shade_7 = 7;
  string shade_8 = 8;
  string shade_9 = 9;
}

message PurposefulColorSet {
  string shade_1 = 1;
  string shade_2 = 2;
  string shade_3 = 3;
  string shade_4 = 4;
  string shade_5 = 5;
}

message ThemeWhitelabelModel {
  string theme_whitelabel_id = 1 [(scalapb.field).type = "ThemeWhitelabelId"];
  GrayColorSet gray = 2;
  PurposefulColorSet primary = 3;
  PurposefulColorSet success = 4;
  PurposefulColorSet warning = 5;
  PurposefulColorSet danger = 6;
}

message RecordTypeUnion {
  ThemeWhitelabelModel _ThemeWhitelabelModel = 1;
}