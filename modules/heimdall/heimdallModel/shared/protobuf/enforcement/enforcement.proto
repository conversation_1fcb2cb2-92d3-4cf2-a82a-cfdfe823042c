syntax = "proto3";

package enforcement;

import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.enforcement"
  single_file: true
  import: "anduin.model.common.user.UserId"
};

enum AccountEnforcementType {
  ResetPassword = 0;
  SetupMFA = 1;
  SetPassword = 2;
}

message AccountEnforcementData {
  AccountEnforcementType enforcementType = 1;
  google.protobuf.StringValue enforcer = 2;
}

message AccountEnforcement {
  string userId = 1 [(scalapb.field).type = "UserId"];
  repeated AccountEnforcementData enforcements = 2;
}

message RecordTypeUnion {
  AccountEnforcement _AccountEnforcement = 1;
}