syntax = "proto3";

package whitelistdomain;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.whitelistdomain"
  preserve_unknown_fields: false
  flat_package: true
  single_file: true
  lenses: false
  import: "java.time.Instant"
};

message WhitelistDomain {
  string domain = 1;
  string adder = 2;
  string context = 3;
  InstantMessage createdAt = 4 [(scalapb.field).type = "Instant"];
}

message RecordTypeUnion {
  WhitelistDomain _WhitelistDomain = 1;
}
