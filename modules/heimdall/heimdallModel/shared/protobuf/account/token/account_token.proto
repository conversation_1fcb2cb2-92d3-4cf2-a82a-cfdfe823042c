syntax = "proto3";

package account.token;

import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";
import "date_time.proto";
import "environment/environment_auth.proto";

option (scalapb.options) = {
    package_name: "anduin.protobuf.account.token"
    single_file: true
    import: "anduin.model.common.user.UserId"
    import: "anduin.id.offering.GlobalOfferingId"
    import: "anduin.id.authwhitelabel.AuthenticationWhitelabelId"
    import: "anduin.id.environment.EnvironmentId"
    import: "anduin.id.account.*"
    import: "anduin.id.oauth2.*"
    import: "java.time.Instant"

    preamble: "sealed trait AccountToken extends scalapb.GeneratedMessage {"
    preamble: "  def expiredAt: Option[Instant]"
    preamble: "}"
};

enum UserSessionMfaMethod {
    None = 0;
    EmailOtp = 1;
    PhoneOtp = 2;
    AuthenticatorApp = 3;
}

message UserSession {
    option (scalapb.message).extends = "AccountToken";
    string userId = 1 [(scalapb.field).type = "UserId"];
    google.protobuf.StringValue ip = 2;
    google.protobuf.StringValue userAgent = 3;
    InstantMessage createdAt = 4 [(scalapb.field).type = "Instant"];
    InstantMessage updatedAt = 5 [(scalapb.field).type = "Instant"];
    UserSessionMfaMethod mfaMethod = 6;
    string user_session_id = 7 [(scalapb.field).type = "UserSessionId"];
    InstantMessage expired_at = 8 [(scalapb.field).type = "Instant"];
    repeated string environmentIds = 9 [(scalapb.field).type = "EnvironmentId"];
    repeated environment.EnvironmentAuthenticationAction envAuthActions = 10;
}

message UserSignup {
    option (scalapb.message).extends = "AccountToken";
    string email = 1;
    google.protobuf.StringValue redirectUrl = 2;
    InstantMessage createdAt = 3 [(scalapb.field).type = "Instant"];
    string authentication_whitelabel_id = 4 [(scalapb.field).type = "Option[AuthenticationWhitelabelId]"];
    bool zombiePassthrough = 5;
    bool remoteSignup = 6;
    string user_signup_id = 7 [(scalapb.field).type = "UserSignupId"];
    InstantMessage expired_at = 8 [(scalapb.field).type = "Instant"];
    bool protectedLinkSignup = 9;
    string global_offering_id = 10 [(scalapb.field).type = "Option[GlobalOfferingId]"];
    string environmentId = 11 [(scalapb.field).type = "Option[EnvironmentId]"];
}

message UserResetPassword {
    option (scalapb.message).extends = "AccountToken";
    string userId = 1 [(scalapb.field).type = "UserId"];
    google.protobuf.StringValue redirectUrl = 2;
    InstantMessage createdAt = 3 [(scalapb.field).type = "Instant"];
    string user_reset_password_id = 4 [(scalapb.field).type = "UserResetPasswordId"];
    InstantMessage expired_at = 5 [(scalapb.field).type = "Instant"];
}

message UserAccountRecovery {
    option (scalapb.message).extends = "AccountToken";
    string userId = 1 [(scalapb.field).type = "UserId"];
    string global_offering_id = 2 [(scalapb.field).type = "Option[GlobalOfferingId]"];
    bool needSetupMFA = 3;
    string user_account_recovery_id = 4 [(scalapb.field).type = "UserAccountRecoveryId"];
    InstantMessage expired_at = 5 [(scalapb.field).type = "Instant"];
}

message Oauth2State {
    option (scalapb.message).extends = "AccountToken";
    google.protobuf.StringValue redirectUrl = 1;
    string originUserId = 2 [(scalapb.field).type = "Option[UserId]"];
    string oauth2_state_id = 3 [(scalapb.field).type = "Oauth2StateId"];
    InstantMessage expired_at = 4 [(scalapb.field).type = "Instant"];
}

message LinkAccountData {
    option (scalapb.message).extends = "AccountToken";
    string userId = 1 [(scalapb.field).type = "UserId"];
    string identityProvider = 2;
    string identityUserId = 3;
    string identityUsername = 4;
    google.protobuf.StringValue redirectUrl = 5;
    string link_account_id = 6 [(scalapb.field).type = "LinkAccountId"];
    InstantMessage expired_at = 7 [(scalapb.field).type = "Instant"];
}

message Oauth2IntegrationState {
    option (scalapb.message).extends = "AccountToken";
    string clientId = 1 [(scalapb.field).type = "Oauth2IntegrationClientId"];
    string scope = 2;
    string redirectUrl = 3;
    string callerState = 4;
    string oauth2_integration_state_id = 5 [(scalapb.field).type = "Oauth2IntegrationStateId"];
    InstantMessage expired_at = 6 [(scalapb.field).type = "Instant"];
}

message CookieConsentConfig {
    reserved 2, 4;

    bool enabled = 1;
    string bodyText = 3;
    string consentBtnText = 5;
}

message CookieConsentAuditLog {
    google.protobuf.StringValue ip = 1;
    google.protobuf.StringValue userAgent = 2;
    InstantMessage createdAt = 3 [(scalapb.field).type = "Instant"];
    string userId = 4 [(scalapb.field).type = "Option[UserId]"];
}

message OTPAuthenticationCodeValue {
    option (scalapb.message).extends = "AccountToken";
    reserved 2;
    reserved "email";
    InstantMessage createdAt = 1 [(scalapb.field).type = "Instant"];
    string target = 3;
    string otp_authentication_code_id = 4 [(scalapb.field).type = "OTPAuthenticationCodeId"];
    InstantMessage expired_at = 5 [(scalapb.field).type = "Instant"];
}

enum RevertOTPAuthenticationCodeStatus {
    PENDING = 0;
    SUCCESS = 1;
}

message RevertOTPAuthenticationCodeValue {
    option (scalapb.message).extends = "AccountToken";    
    InstantMessage createdAt = 1 [(scalapb.field).type = "Instant"];
    string userId = 2 [(scalapb.field).type = "UserId"];
    string target = 3;
    string revert_otp_authentication_code_id = 4 [(scalapb.field).type = "RevertOTPAuthenticationCodeId"];
    string revert_otp_authentication_request_id = 5 [(scalapb.field).type = "RevertOTPAuthenticationRequestId"];
    RevertOTPAuthenticationCodeStatus status = 6;
    InstantMessage expired_at = 7 [(scalapb.field).type = "Instant"];
}

message OTPRequestAttempt {
    option (scalapb.message).extends = "AccountToken";
    int32 attempt = 1;
    string otp_authentication_request_id = 4 [(scalapb.field).type = "OTPAuthenticationRequestId"];
    InstantMessage expired_at = 3 [(scalapb.field).type = "Instant"];
}

message EnterpriseLoginProxyDataOauth2 {
    string underlyingStateId = 1 [(scalapb.field).type = "EnterpriseLoginStateId"];
    string configId = 2 [(scalapb.field).type = "EnterpriseLoginConfigId"];
    string code = 3;
}

message EnterpriseLoginProxyDataSaml {
    string underlyingStateId = 1 [(scalapb.field).type = "EnterpriseLoginStateId"];
    string configId = 2 [(scalapb.field).type = "EnterpriseLoginConfigId"];
    string samlResponse = 3;
}

message EnterpriseLoginProxyDataBox {
    oneof enterpriseLoginProxyData {
        EnterpriseLoginProxyDataOauth2 enterprise_proxy_data_oauth2 = 1;
        EnterpriseLoginProxyDataSaml enterprise_proxy_data_saml = 2;
    }
}

message EnterpriseLoginProxyData {
    option (scalapb.message).extends = "AccountToken";
    EnterpriseLoginProxyDataBox proxyDataBox = 1;
    string enterprise_login_proxy_data_id = 2 [(scalapb.field).type = "EnterpriseLoginProxyDataId"];
    InstantMessage expired_at = 3 [(scalapb.field).type = "Instant"];
}

message EnterpriseLoginTestState {
    string configId = 1 [(scalapb.field).type = "EnterpriseLoginConfigId"];
}

message EnterpriseLoginLinkState {
    string linkId = 1 [(scalapb.field).type = "EnterpriseLoginLinkId"];
}

message EnterpriseLoginUserLoginState {
    string configId = 1 [(scalapb.field).type = "EnterpriseLoginConfigId"];
    google.protobuf.StringValue redirect_url = 2;
    bool skipLinkAccount = 3;
    google.protobuf.StringValue email = 4;
    bool triggeredByEnvironment = 5;
    bool mustImpersonateConfig = 6;
}

message EnterpriseLoginProxyState {
    string underlyingStateId = 1 [(scalapb.field).type = "EnterpriseLoginStateId"];
    string configId = 2 [(scalapb.field).type = "EnterpriseLoginConfigId"];
    string fromServer = 3;
}

message EnterpriseLoginStateBox {
    oneof enterpriseLoginStates {
        EnterpriseLoginTestState enterprise_test_state = 1;
        EnterpriseLoginLinkState enterprise_link_state = 2;
        EnterpriseLoginUserLoginState enterprise_user_login_state = 3;
        EnterpriseLoginProxyState enterprise_proxy_state = 4;
    }
}

message EnterpriseLoginState {
    option (scalapb.message).extends = "AccountToken";
    EnterpriseLoginStateBox stateBox = 1;
    string enterprise_login_state_id = 2 [(scalapb.field).type = "EnterpriseLoginStateId"];
    InstantMessage expired_at = 3 [(scalapb.field).type = "Instant"];
}


message RecordTypeUnion {
    UserSession user_session = 1;
    UserSignup user_signup = 2;
    UserResetPassword user_reset_password = 3;
    UserAccountRecovery user_account_recovery = 4;
    Oauth2State oauth2_state = 5;
    LinkAccountData link_account_data = 6;
    Oauth2IntegrationState oauth2_integration_state = 7;
    OTPAuthenticationCodeValue otp_authentication_code_value = 8;
    OTPRequestAttempt otp_request_attempt = 9;
    EnterpriseLoginProxyData enterprise_login_proxy_data = 10;
    EnterpriseLoginState enterprise_login_state = 11;
    RevertOTPAuthenticationCodeValue revert_otp_authentication_code_value = 12;
}
