syntax = "proto3";

package shortenurl;

import "scalapb/scalapb.proto";

import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.shortenurl"
  single_file: true
  import: "java.time.Instant"
  import: "anduin.id.offering.GlobalOfferingId"
  import: "anduin.id.environment.EnvironmentId"
};

message ShortenUrl {
    reserved 3;
    reserved "customDomainId";
    string path = 1;
    string offeringId = 2 [(scalapb.field).type = "Option[GlobalOfferingId]"];
    string environmentId = 5 [(scalapb.field).type = "Option[EnvironmentId]"];
    InstantMessage createdAt = 4 [(scalapb.field).type = "Instant"];
}
