syntax = "proto3";

package mfa;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.mfa"
  preserve_unknown_fields: false
  flat_package: true
  single_file: true
  lenses: false
  import: "anduin.model.common.user.UserId"
};

enum MFAMethod {
  PrimaryEmail = 0;
  SecondaryEmail = 1;
  AuthenticatorApp = 2;
  PhoneNumber = 4;
}

message PrimaryEmailMFAConfig {
  bool enabled = 1;
}

message SecondaryEmailMFAConfig {
  bool enabled = 1;
  string email = 2;
}

// We are using Keycloak to store the configuration, so it is empty here.
// In the future we may want to migrate away from keycloak, the new configuration
// should be put here.
message AuthenticatorAppMFAConfig {
  bool enabled = 1;
}

message PhoneNumberMFAConfig {
  bool enabled = 1;
  string countryCode = 2;
  string phoneNumber = 3;
}

message UserMFA {
  string userId = 1 [(scalapb.field).type = "UserId"];
  bool enabled = 2;
  MFAMethod defaultMethod = 3;
  PrimaryEmailMFAConfig primaryEmailMFAConfig = 4;
  SecondaryEmailMFAConfig secondaryEmailMFAConfig = 5;
  AuthenticatorAppMFAConfig authenticatorAppMFAConfig = 6;
  PhoneNumberMFAConfig phoneNumberMFAConfig = 7;
}

message RecordTypeUnion {
  UserMFA _UserMFA = 1;
}