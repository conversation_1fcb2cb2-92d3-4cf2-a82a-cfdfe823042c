syntax = "proto3";

package enterprise;

import "google/protobuf/wrappers.proto";
import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.account"
  single_file: true
  import: "anduin.id.account.EnterpriseLoginConfigId"
  import: "anduin.id.account.EnterpriseLoginLinkId"
  import: "anduin.id.account.EnterpriseLoginUserLinkId"
  import: "anduin.id.account.EnterpriseLoginEmailDomainBindingId"
  import: "anduin.id.entity.EntityId"
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.offering.GlobalOfferingId"
  import: "anduin.id.environment.EnvironmentId"
  import: "anduin.id.engagement.EngagementId"
  import: "java.time.Instant"
};

enum EnterpriseLoginType {
  Oauth2 = 0;
  Saml = 1;
}

enum EnterpriseLoginOauth2EndpointTemplate {
  Custom = 0;
  Google = 1;
  Github = 2;
}

enum EnterpriseLoginRequestMethod {
  GET = 0;
  POST = 1;
  PUT = 2;
}

message EnterpriseLoginLogoutConfig {
  google.protobuf.StringValue redirectUrl = 1;
}

message EnterpriseLoginOauth2Implementation {
  string clientId = 1;
  string clientSecretEncrypted = 2;
  EnterpriseLoginOauth2EndpointTemplate endpointTemplate = 3;
  string authorizationEndpoint = 4;
  map<string, string> authorizationEndpointParams = 5;
  string tokenEndpoint = 6;
  string userInfoEndpoint = 7;
  EnterpriseLoginRequestMethod userInfoEndpointMethod = 8;
  map<string, string> userInfoEndpointHeaders = 9;
  string userInfoEndpointBody = 10;
  string userInfoEmailQuery = 11;
  string userInfoNameQuery = 12;
}

message EnterpriseLoginSamlCertificate {
  string certificate = 1;
  InstantMessage createdAt = 2 [(scalapb.field).type = "Instant"];
}

message EnterpriseLoginSamlImplementation {
  string certificate = 1;
  string redirectLocation = 2;
  string nameIdFormat = 3;
  string emailAttribute = 4;
  string displayNameAttribute = 5;
  string displayFirstNameAttribute = 7;
  string displayLastNameAttribute = 8;
  string defaultTargetUrl = 6;
  bool useUniqueIdentifier = 9;
  repeated EnterpriseLoginSamlCertificate additionalCertificates = 10;
}

message EnterpriseLoginImplementationBox {
  oneof enterpriseLoginImplementations {
    EnterpriseLoginOauth2Implementation enterprise_login_oauth2_implementation = 1;
    EnterpriseLoginSamlImplementation enterprise_login_saml_implementation = 2;
  }
}

message EnterpriseLoginConfig {
  string id = 1 [(scalapb.field).type = "EnterpriseLoginConfigId"];
  string name = 2;
  EnterpriseLoginType loginType = 3;
  EnterpriseLoginImplementationBox implementation = 4;
  InstantMessage createdAt = 5 [(scalapb.field).type = "Instant"];

  // Display options
  string providerName = 6;
  google.protobuf.StringValue providerLogoUrl = 7;

  // Logout config
  EnterpriseLoginLogoutConfig logoutConfig = 8;
}

message EnterpriseLoginConfigEntityBinding {
  string entityId = 1 [(scalapb.field).type = "EntityId"];
  string configId = 2 [(scalapb.field).type = "EnterpriseLoginConfigId"];
  repeated string offeringIds = 3 [(scalapb.field).type = "GlobalOfferingId"];
}

message EnterpriseLoginLink {
  reserved 5;
  reserved "customDomainId";
  string id = 1 [(scalapb.field).type = "EnterpriseLoginLinkId"];
  string configId = 2 [(scalapb.field).type = "EnterpriseLoginConfigId"];
  string path = 3;
  string offeringId = 4 [(scalapb.field).type = "Option[GlobalOfferingId]"];
  string creatorId = 6 [(scalapb.field).type = "UserId"];
  string context = 7;
  InstantMessage createdAt = 8 [(scalapb.field).type = "Instant"];
  string environmentId = 9 [(scalapb.field).type = "Option[EnvironmentId]"];
  string engagementId = 10 [(scalapb.field).type = "Option[EngagementId]"];
}

message EnterpriseLoginUserLink {
  string linkId = 1 [(scalapb.field).type = "EnterpriseLoginUserLinkId"];
  string userId = 2 [(scalapb.field).type = "UserId"];
  string configId = 3 [(scalapb.field).type = "EnterpriseLoginConfigId"];
}

message EnterpriseLoginEmailDomainBinding {
  string domainId = 1 [(scalapb.field).type = "EnterpriseLoginEmailDomainBindingId"];
  string configId = 2 [(scalapb.field).type = "EnterpriseLoginConfigId"];
}

message RecordTypeUnion {
  EnterpriseLoginConfig _EnterpriseLoginConfig = 1;
  EnterpriseLoginConfigEntityBinding _EnterpriseLoginConfigEntityBinding = 2;
  EnterpriseLoginLink _EnterpriseLoginLink = 3;
  EnterpriseLoginUserLink _EnterpriseLoginUserLink = 4;
  EnterpriseLoginEmailDomainBinding _EnterpriseLoginEmailDomainBinding = 5;
}
