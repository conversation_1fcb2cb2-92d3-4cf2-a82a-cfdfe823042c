syntax = "proto3";

package authwhitelabel;

import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.authwhitelabel"
  single_file: true
  import: "anduin.id.authwhitelabel.AuthenticationWhitelabelId"
  import: "anduin.dms.DocumentStorageIdMapper.given"
  import: "anduin.model.document.DocumentStorageId"
};

message AuthenticationWhitelabelPolicy {
  bool disable_revert_otp = 1;
}

message ProfileSettingsWhitelabelModel {
  bool hide_password_tab = 1;
  bool hide_mfa_tab = 2;
}

message AuthenticationWhitelabel {
  string authentication_whitelabel_id = 1 [(scalapb.field).type = "AuthenticationWhitelabelId"];
  bool whitelabel_enabled = 2;
  google.protobuf.StringValue logo_storage_id = 3 [(scalapb.field).type = "DocumentStorageId"];
  google.protobuf.StringValue long_logo_storage_id = 4 [(scalapb.field).type = "DocumentStorageId"];
  google.protobuf.StringValue provider_logo_storage_id = 5 [(scalapb.field).type = "DocumentStorageId"];
  google.protobuf.StringValue provider_long_logo_storage_id = 6 [(scalapb.field).type = "DocumentStorageId"];
  map<string, string> custom_values = 7;
  bool custom_email_sender_enabled = 8;
  google.protobuf.StringValue sender_display_name = 9;
  google.protobuf.StringValue email_name_part = 10;
  AuthenticationWhitelabelPolicy authentication_whitelabel_policy = 11;
  google.protobuf.StringValue domain_part = 12;
  ProfileSettingsWhitelabelModel profile_settings_whitelabel = 13;
}

message RecordTypeUnion {
  AuthenticationWhitelabel _AuthenticationWhitelabel = 1;
}