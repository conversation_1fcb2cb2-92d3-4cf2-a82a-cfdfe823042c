package build.modules.heimdall

import build_.modules.heimdall.dependency_.*
import build_.build.util_.*
import build.modules.{graphql, bifrost}
import build.platform.stargazer

import mill.*
import mill.scalalib.*
import anduin.build.*
import anduin.mill.*
import anduin.mill.jsdeps.*

object `package` extends Module {

  object heimdallModel extends StargazerModelCrossPlatformModule {
    object jvm extends JvmModelModule
    object js extends JsModelModule
  }

  object heimdallCore extends AnduinCrossPlatformModule {

    object jvm extends JvmModule with SharedModule {

      override def moduleDeps =
        super.moduleDeps ++ Seq(heimdallModel.jvm, stargazer.jvm)

      override def mvnDeps = super.mvnDeps() ++ HeimdallCore.jvmDeps

    }

    object js extends JsModule with SharedModule {

      override def moduleDeps = super.moduleDeps ++ Seq(heimdallModel.js, stargazer.js)

      override def mvnDeps = super.mvnDeps() ++ HeimdallCore.jsDeps

    }

    trait SharedModule extends ScalaModule {
      override def scalacOptions = super.scalacOptions() ++ Seq("-Xmax-inlines:128")
    }

  }

  object heimdall extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {

      override def mvnDeps = super.mvnDeps() ++ Heimdall.jvmDeps

      override def moduleDeps = super.moduleDeps ++ Seq(
        heimdallCore.jvm,
        build.gondor.gondorCore.jvm
      )

    }

    object js extends JsModule {
      override def moduleDeps = super.moduleDeps ++ Seq(heimdallCore.js, graphql.js)
    }

  }

  object heimdallApp extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {
      override def moduleDeps = super.moduleDeps ++ Seq(bifrost.jvm)

      object it extends AnduinZioTests with AnduinIntegTests {
        override def moduleDeps = super.moduleDeps ++ Seq(build.gondor.gondorTest, jvm)
      }

    }

    object js extends JsModule with AnduinWebClientModule {
      override def webModule = AnduinWebModules.Heimdall
      override def moduleDeps = super.moduleDeps ++ Seq(build.gondor.gondorCore.js)

      override def jsDeps = super.jsDeps() ++ JsDeps(
        "universal-cookie" -> AnduinVersions.npm.universalCookie
      )

      override def mainClass = Some("anduin.account.AccountMainApp")
    }

  }

}
