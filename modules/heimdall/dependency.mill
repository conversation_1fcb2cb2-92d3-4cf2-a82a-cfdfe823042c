package build.modules.heimdall

import anduin.build.AnduinVersions
import mill.scalalib.*

object HeimdallCore {

  lazy val jvmDeps = Seq(
    mvn"io.fusionauth:fusionauth-jwt:${AnduinVersions.fusionAuthJwt}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind"),
    mvn"com.fasterxml.jackson.core:jackson-databind:${AnduinVersions.jacksonDatabind}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations"),
    mvn"com.fasterxml.jackson.core:jackson-annotations:${AnduinVersions.jacksonAnnotations}",
    mvn"com.jayway.jsonpath:json-path:${AnduinVersions.jsonpath}"
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("org.ow2.asm" -> "asm")
  )

  lazy val jsDeps = Seq(
    mvn"be.doeraene::url-dsl::${AnduinVersions.urlDsl}",
    mvn"com.raquo::waypoint::${AnduinVersions.waypoint}"
      .exclude("com.raquo" -> AnduinVersions.j2sjs("laminar"))
      .exclude("com.raquo" -> AnduinVersions.j2sjs("airstream"))
      .exclude("be.doeraene" -> AnduinVersions.j2sjs("url-dsl"))
      .exclude("org.scala-js" -> AnduinVersions.j2sjs("scalajs-dom"))
  )

}

object Heimdall {

  val acmeUtilsVersion = "2.16"

  lazy val jvmDeps = Seq(
    mvn"commons-codec:commons-codec:${AnduinVersions.commonsCodec}",
    mvn"org.bitbucket.b_c:jose4j:${AnduinVersions.jose4j}"
      .exclude("org.slf4j" -> "slf4j-api"),
    mvn"org.shredzone.acme4j:acme4j-client:${AnduinVersions.acme4j}"
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("org.bitbucket.b_c" -> "jose4j")
      .exclude("org.bouncycastle" -> "bcprov-jdk18on")
      .exclude("org.bouncycastle" -> "bcpkix-jdk18on"),
    mvn"org.shredzone.acme4j:acme4j-utils:$acmeUtilsVersion"
      .exclude("org.shredzone.acme4j" -> "acme4j-client")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("org.bitbucket.b_c" -> "jose4j")
      .exclude("org.bouncycastle" -> "bcprov-jdk18on")
      .exclude("org.bouncycastle" -> "bcpkix-jdk18on"),
    mvn"org.bouncycastle:bcprov-jdk18on:${AnduinVersions.bouncyCastle}",
    mvn"org.bouncycastle:bcpkix-jdk18on:${AnduinVersions.bouncyCastle}",
    mvn"com.github.scribejava:scribejava-apis:${AnduinVersions.scribeJava}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind"),
    mvn"com.onelogin:java-saml-core:${AnduinVersions.javasaml}"
      .exclude("commons-codec" -> "commons-codec")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("joda-time" -> "joda-time")
      .exclude("org.apache.commons" -> "commons-lang3")
      .exclude("org.apache.santuario" -> "xmlsec")
  )

}
