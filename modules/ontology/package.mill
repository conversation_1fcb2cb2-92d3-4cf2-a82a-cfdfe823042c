package build.modules.ontology

import build_.modules.ontology.dependency_.*
import build_.build.util_.*
import build.modules.heimdall
import build.platform.stargazer

import mill.*
import mill.scalalib.*
import anduin.build.*
import anduin.mill.*

object `package` extends Module {

  object ontologyModel extends StargazerModelCrossPlatformModule {

    object jvm extends JvmModelModule with SharedModule {
      override def moduleDeps = super.moduleDeps ++ Seq(build.gondor.gondorModel.jvm)
    }

    object js extends JsModelModule with SharedModule {
      override def moduleDeps = super.moduleDeps ++ Seq(build.gondor.gondorModel.js)
    }

    trait SharedModule extends ScalaModule {
      override def scalacOptions = super.scalacOptions() ++ Seq("-Xmax-inlines:128")
    }

  }

  object ontologyCore extends AnduinCrossPlatformModule {

    object jvm extends JvmModule with SharedModule {

      override def moduleDeps = super.moduleDeps ++ Seq(stargazer.jvm, ontologyModel.jvm)

    }

    object js extends JsModule with SharedModule {

      override def moduleDeps = super.moduleDeps ++ Seq(heimdall.heimdallCore.js, ontologyModel.js)

    }

    trait SharedModule extends ScalaModule {
      override def scalacOptions = super.scalacOptions() ++ Seq("-Xmax-inlines:128")
      override def mvnDeps = super.mvnDeps() ++ OntologyCore.sharedDeps
    }

  }

  object ontology extends Module {

    object jvm extends AnduinScalaModule {
      override def scalacOptions = super.scalacOptions() ++ Seq("-Xmax-inlines:128")

      override def mvnDeps = super.mvnDeps() ++ Ontology.jvmDeps

      override def moduleDeps =
        super.moduleDeps ++ Seq(
          heimdall.heimdallCore.jvm,
          build.gondor.gondorCore.jvm,
          ontologyCore.jvm
        )

    }

  }

}
