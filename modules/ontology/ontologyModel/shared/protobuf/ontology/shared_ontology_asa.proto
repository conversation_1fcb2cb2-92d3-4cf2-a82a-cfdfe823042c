syntax = "proto3";

package anduin.ontology.shared.asa;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.ontology.SharedOntologyAsaId"
  import: "anduin.model.common.user.UserId"
};

message SharedOntologyAsa {
  string id = 1 [(scalapb.field).type = "SharedOntologyAsaId"];
  string content = 2;
  string description = 3;
  string content_search_entry = 4;
  string description_search_entry = 5;
  string last_updated_by = 6 [(scalapb.field).type = "UserId"];
  InstantMessage last_updated_at = 7 [(scalapb.field).type = "java.time.Instant"];
}

message RecordTypeUnion {
  SharedOntologyAsa _SharedOntologyAsa = 1;
}
