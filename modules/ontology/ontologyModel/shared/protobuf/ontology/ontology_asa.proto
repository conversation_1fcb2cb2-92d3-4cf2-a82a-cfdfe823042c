syntax = "proto3";

package anduin.ontology.asa;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.ontology.OntologyAsaId"
  import: "anduin.id.ontology.SharedOntologyAsaId"
  import: "anduin.model.common.user.UserId"

  preamble: "private sealed trait OntologyAsaTrait extends scalapb.GeneratedMessage {"
  preamble: "  def edges: Seq[EdgeInfo]"
  preamble: "  def equal(other: OntologyAsaTrait): Boolean = edges == other.edges"
  preamble: "}"
};

message EdgeInfo {

  enum EdgeType {
    IN = 0;
    FIELD_BELOW = 1;
  }

  EdgeType edge_type = 1;
  string source_node = 2;
  string target_node = 3;
}

message SharedInfo {
  string shared_id = 1 [(scalapb.field).type = "SharedOntologyAsaId"];
  string last_updated_by = 2 [(scalapb.field).type = "UserId"];
  InstantMessage last_updated_at = 3 [(scalapb.field).type = "java.time.Instant"];
}

message OntologyAsa {
  option (scalapb.message).extends = "OntologyAsaTrait";
  string id = 1 [(scalapb.field).type = "OntologyAsaId"];
  repeated string nodes = 2;
  repeated EdgeInfo edges = 3;
  string unique_id = 4; // Workaround for now because Record Layer is not supporting concat index on nested key
  repeated string nodes_content = 5; // Cached raw content of each node in the ASA
  SharedInfo shared_info_opt = 6;
}

message RecordTypeUnion {
  OntologyAsa _OntologyAsa = 1;
}
