syntax = "proto3";

import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.ontology"
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.ontology.AsaId"
};

enum AsaSourceType {
  MANUAL = 0;
  RDF_INIT = 1;
}

enum AsaJsonType {
  STRING = 0;
  SEQ_STRING = 1;
  COMPOUND = 2;
}

enum AsaWidgetType {
  TEXT_BOX = 0;
  RADIO = 1;
  MULTIPLE_CHECKBOX = 2;
  COUNTRY = 3;
  STATE = 4;
  CUSTOM_FORMAT_SSN = 5;
  CUSTOM_FORMAT_EIN = 6;
  CUSTOM_FORMAT_ITIN = 7;
  NONE = 8;
}

message AsaSource {
  oneof sealed_value {
    AsaSourceManual asa_source_manual = 1;
    AsaSourceRdfInit asa_source_rdf_init = 2;
    AsaSourceBatchManual asa_source_batch_manual = 3;
  }
}

message AsaSourceManual {}
message AsaSourceRdfInit {}
message AsaSourceBatchManual {}

message AsaActivity {
  oneof sealed_value {
    CreateAsaActivity create_asa_activity = 1;
    RemoveAsaActivity remove_asa_activity = 2;
    UpdateAsaActivity update_asa_activity = 3;
  }
}

message CreateAsaActivity {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "java.time.Instant"];
  AsaSource source = 3;
}

message RemoveAsaActivity {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "java.time.Instant"];
}

message UpdateAsaActivity {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "java.time.Instant"];
  AsaSource source = 3;
  google.protobuf.StringValue prev_section_opt = 4;
  google.protobuf.StringValue prev_short_description_opt = 5;
  google.protobuf.StringValue prev_long_description_opt = 6;
  google.protobuf.StringValue prev_logic_guide_opt = 7;
  optional AsaJsonType prev_json_type_opt = 8;
  optional AsaWidgetType prev_widget_type_opt = 9;
  repeated AsaRelation added_relations = 10 [(scalapb.field).collection_type = "Set"];
  repeated AsaRelation removed_relations = 11 [(scalapb.field).collection_type = "Set"];
  repeated AsaFlag added_flags = 12 [(scalapb.field).collection_type = "Set"];
  repeated AsaFlag removed_flags = 13 [(scalapb.field).collection_type = "Set"];
}

message AsaFlag {
  oneof sealed_value {
    ForFsWorkflowSetupFlag for_fs_workflow_setup_flag = 1;
    ForFieldValueFlag for_field_value_flag = 2;
  }
}

message ForFsWorkflowSetupFlag {}
message ForFieldValueFlag {}

message AsaRelation {
  string related_asa_id = 1 [(scalapb.field).type = "AsaId"];
  AsaRelationType relation_type = 2;
}

enum AsaRelationType {
  PARENT = 0;
}
