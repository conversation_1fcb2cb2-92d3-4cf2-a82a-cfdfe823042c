syntax = "proto3";

import "scalapb/scalapb.proto";
import "ontology/common.proto";
import "google/protobuf/struct.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.ontology.anduinstandardalias"
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.ontology.AsaId"
  import: "anduin.cue.model.CommonCueTypeId"
  import: "io.circe.Json"
  import: "anduin.model.codec.ProtoCodecs.jsonStructMapper"
};

message AnduinStandardAlias {
  string id = 1 [(scalapb.field).type = "AsaId"];
  string section = 2;
  string short_description = 3;
  string long_description = 4;
  string logic_guide = 5;
  AsaJsonType json_type = 6;
  AsaWidgetType widget_type = 7;
  repeated AsaRelation relations = 8 [(scalapb.field).collection_type = "Set"];
  repeated AsaFlag flags = 9 [(scalapb.field).collection_type = "Set"];
  repeated AsaActivity activities = 10;
  string type_id = 11 [(scalapb.field).type = "CommonCueTypeId"];
  google.protobuf.Struct type_json_schema_opt = 12 [(scalapb.field).type = "Json"];
}

message RecordTypeUnion {
  AnduinStandardAlias _AnduinStandardAlias = 1;
}
