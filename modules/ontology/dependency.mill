package build.modules.ontology

import anduin.build.AnduinVersions
import mill.scalalib.*

object OntologyCore {

  lazy val sharedDeps = Seq(
    mvn"org.parboiled::parboiled::${AnduinVersions.parboiled}"
      .exclude("com.chuusai" -> AnduinVersions.j2s("shapeless"))
  )

}

object Ontology {

  lazy val jvmDeps = Seq(
    mvn"com.fasterxml.jackson.core:jackson-databind:${AnduinVersions.jacksonDatabind}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations"),
    mvn"com.fasterxml.jackson.core:jackson-annotations:${AnduinVersions.jacksonAnnotations}",
    mvn"com.fasterxml.jackson.core:jackson-core:${AnduinVersions.jackson}",
    mvn"commons-io:commons-io:${AnduinVersions.common_io}",
    mvn"commons-codec:commons-codec:${AnduinVersions.commonsCodec}",
    mvn"commons-logging:commons-logging:${AnduinVersions.commonsLogging}",
    mvn"org.apache.httpcomponents:httpcore:${AnduinVersions.httpCore}",
    mvn"org.apache.httpcomponents:httpclient-cache:${AnduinVersions.httpClient}"
      .exclude("commons-codec" -> "commons-codec")
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .exclude("commons-logging" -> "commons-logging")
      .exclude("commons-configuration" -> "commons-configuration"),
    mvn"org.apache.httpcomponents:httpclient-osgi:${AnduinVersions.httpClient}"
      .exclude("commons-codec" -> "commons-codec")
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .exclude("commons-logging" -> "commons-logging")
      .exclude("commons-configuration" -> "commons-configuration"),
    mvn"org.apache.httpcomponents:httpcore-osgi:${AnduinVersions.httpCore}"
      .exclude("commons-codec" -> "commons-codec")
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .exclude("commons-logging" -> "commons-logging")
      .exclude("commons-configuration" -> "commons-configuration"),
    mvn"com.github.jsonld-java:jsonld-java:${AnduinVersions.jsonldJava}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind")
      .exclude("commons-io" -> "commons-io")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("org.apache.httpcomponents" -> "httpclient-osgi")
      .exclude("org.apache.httpcomponents" -> "httpcore-osgi")
      .exclude("org.slf4j" -> "jcl-over-slf4j"),
    mvn"joda-time:joda-time:${AnduinVersions.jodaTime}"
      .exclude("joda-time" -> "joda-convert"),
    mvn"org.joda:joda-convert:${AnduinVersions.jodaConvert}",
    mvn"org.apache.commons:commons-lang3:${AnduinVersions.apacheCommonLang}",
    mvn"org.apache.httpcomponents:httpcore:${AnduinVersions.httpCore}",
    mvn"org.apache.httpcomponents:httpclient:${AnduinVersions.httpClient}"
      .exclude("commons-codec" -> "commons-codec")
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .exclude("commons-logging" -> "commons-logging")
      .exclude("commons-configuration" -> "commons-configuration"),
    mvn"org.slf4j:jcl-over-slf4j:${AnduinVersions.slf4j_api}"
      .exclude("org.slf4j" -> "slf4j-api"),
    mvn"com.edgedb:driver:${AnduinVersions.edgedb}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind")
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("io.netty" -> "netty-buffer")
      .exclude("io.netty" -> "netty-common")
      .exclude("io.netty" -> "netty-handler")
      .exclude("io.netty" -> "netty-transport")
      .exclude("io.netty" -> "netty-codec")
      .exclude("org.jetbrains" -> "annotations")
      .exclude("org.jetbrains.kotlin" -> "kotlin-stdlib")
      .exclude("org.jetbrains.kotlin" -> "kotlin-stdlib-jdk8")
      .exclude("org.jetbrains.kotlin" -> "kotlin-stdlib-jdk7"),
    mvn"com.github.tototoshi::scala-csv:${AnduinVersions.scalaCsv}"
  )

}
