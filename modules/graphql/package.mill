package build.modules.graphql

import build_.modules.graphql.dependency_.Graphql
import build_.build.util_.*
import build.modules.{rohan, signature, investorProfile, fundData, integplatform, ria}
import build.platform.{stargazer, stargazerConfig}

import mill.*
import anduin.build.*
import anduin.mill.*
import anduin.mill.jsdeps.*

object `package` extends AnduinCrossPlatformModule {

  object js extends JsModule with ESBuildScalaJSApp {
    override def jsDeps = super.jsDeps() ++ JsDeps(Graphql.npmDeps*)

    override def moduleDeps = super.moduleDeps ++ Seq(
      stargazer.js,
      rohan.rohanCommon.js,
      stargazerConfig.js
    )

  }

  object jvm extends JvmModule {
    override def mvnDeps = super.mvnDeps() ++ Graphql.jvmDeps

    override def moduleDeps = super.moduleDeps ++ Seq(
      signature.signature.jvm,
      investorProfile.investorProfile.jvm,
      fundData.fundData.jvm,
      integplatform.integplatform.jvm,
      ria.ria.jvm
    )

  }

}
