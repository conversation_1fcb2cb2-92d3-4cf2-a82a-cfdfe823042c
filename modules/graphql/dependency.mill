package build.modules.graphql

import anduin.build.AnduinVersions
import mill.scalalib.*

object Graphql {

  lazy val npmDeps = Seq(
    "react" -> AnduinVersions.npm.react,
    "react-dom" -> AnduinVersions.npm.reactDom
  )

  lazy val jvmDeps = Seq(
    mvn"org.parboiled::parboiled:${AnduinVersions.parboiled}"
      .exclude("com.chuusai" -> AnduinVersions.j2s("shapeless")),
    mvn"org.sangria-graphql::sangria:${AnduinVersions.sangria}"
      .exclude("org.sangria-graphql" -> AnduinVersions.j2s("sangria-marshalling-api"))
      .exclude("org.parboiled" -> AnduinVersions.j2s("parboiled")),
    mvn"org.sangria-graphql::sangria-marshalling-api:${AnduinVersions.sangriaMarshallingApi}",
    mvn"org.sangria-graphql::sangria-circe:${AnduinVersions.sangriaCirce}"
      .exclude("org.sangria-graphql" -> AnduinVersions.j2s("sangria-marshalling-api"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-core")),
    mvn"io.dropwizard.metrics:metrics-core:${AnduinVersions.dropWizardMetricCore}"
      .exclude("org.slf4j" -> "slf4j-api"),
    mvn"io.opentracing:opentracing-util:${AnduinVersions.opentracing}",
    mvn"org.sangria-graphql::sangria-slowlog:${AnduinVersions.sangriaSlowlog}"
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("org.sangria-graphql" -> AnduinVersions.j2s("sangria"))
      .exclude("io.opentracing" -> "opentracing-util")
      .exclude("io.dropwizard.metrics" -> "metrics-core")
  )

}
