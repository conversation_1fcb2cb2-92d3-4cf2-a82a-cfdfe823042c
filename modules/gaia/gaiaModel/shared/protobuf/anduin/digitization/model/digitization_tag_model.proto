syntax = "proto3";

package anduin.digitization.model.digitizationtag;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.digitization.DigitizationTagId"
  import: "anduin.model.common.user.UserId"
};

message DigitizationTagModel {
  string id = 1 [(scalapb.field).type = "DigitizationTagId"];
  string name = 2;
  string description = 3;
  string created_by = 4 [(scalapb.field).type = "UserId"];
  InstantMessage created_at = 5 [(scalapb.field).type = "java.time.Instant"];
  string last_updated_by = 6 [(scalapb.field).type = "UserId"];
  InstantMessage last_updated_at = 7 [(scalapb.field).type = "java.time.Instant"];
}

message RecordTypeUnion {
  DigitizationTagModel _DigitizationTagModel = 1;
}
