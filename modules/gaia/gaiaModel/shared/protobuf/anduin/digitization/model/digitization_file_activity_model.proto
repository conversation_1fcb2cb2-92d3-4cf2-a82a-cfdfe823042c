syntax = "proto3";

package anduin.digitization.model.activity;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.digitization.DigitizationFileActivityId"
  import: "anduin.id.digitization.DigitizationFileId"
  import: "anduin.model.common.user.UserId"
};

message DigitizationFileActivityModel {
  string digitization_file_activity_id = 1 [(scalapb.field).type = "DigitizationFileActivityId"];
  string digitization_file_id = 2 [(scalapb.field).type = "DigitizationFileId"];
  string actor = 3 [(scalapb.field).type = "UserId"];
  InstantMessage created_at = 4 [(scalapb.field).type = "java.time.Instant"];

  DigitizationFileActivityData data = 5;

}

message DigitizationFileActivityData {
  oneof sealed_value {
    DigitizationFileCreated digitization_file_created = 1;
    DigitizationFileArchived digitization_file_archived = 2;
    DigitizationFileEdited digitization_file_edited = 3;

  }
}

message DigitizationFileCreated {
}

message DigitizationFileArchived {
  bool is_archived = 1;
}

message DigitizationFileEdited {
}



message RecordTypeUnion {
  DigitizationFileActivityModel _DigitizationFileActivityModel = 1;
}
