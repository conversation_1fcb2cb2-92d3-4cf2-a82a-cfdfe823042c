syntax = "proto3";

package anduin.digitization.model.digitizationfolder;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.digitization.DigitizationFolderId"
  import: "anduin.model.common.user.UserId"
};

message DigitizationFolderModel {
  string folder_id = 1 [(scalapb.field).type = "DigitizationFolderId"];
  optional string parent_folder_id = 2 [(scalapb.field).type = "DigitizationFolderId"];
  string name = 3;

  string creator = 6 [(scalapb.field).type = "UserId"];
  InstantMessage created_at = 7 [(scalapb.field).type = "java.time.Instant"];
  repeated string tags = 8 [(scalapb.field).collection_type = "Set"];
  InstantMessage last_edited_at = 9 [(scalapb.field).type = "java.time.Instant"];
  string last_editor = 10 [(scalapb.field).type = "UserId"];
  repeated string owners = 11 [(scalapb.field).type = "UserId"];
}

message RecordTypeUnion {
  DigitizationFolderModel _DigitizationFolderModel = 1;
}
