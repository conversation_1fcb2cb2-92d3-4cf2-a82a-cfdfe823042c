syntax = "proto3";

package anduin.digitization.model.useractivity;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.model.common.user.UserId"
};

message DigitizationUserActivityModel {
  string user_id = 1 [(scalapb.field).type = "UserId"];
  string resource_id = 2;
  InstantMessage timestamp = 3 [(scalapb.field).type = "java.time.Instant"];
}

message RecordTypeUnion {
  DigitizationUserActivityModel _DigitizationUserActivityModel = 1;
}
