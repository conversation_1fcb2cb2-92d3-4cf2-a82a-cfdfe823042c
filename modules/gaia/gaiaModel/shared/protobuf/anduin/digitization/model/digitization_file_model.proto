syntax = "proto3";

package anduin.digitization.model.digitizationfile;

import "scalapb/scalapb.proto";
import "date_time.proto";
import "anduin/utils/system_tag.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.form.FormId"
  import: "anduin.id.form.DataTemplateId"
  import: "anduin.id.annotation.AnnotationDocumentId"
  import: "anduin.id.digitization.DigitizationFolderId"
  import: "anduin.id.digitization.DigitizationFileId"
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.blueprint.BlueprintId"
  import: "anduin.id.cue.CueModuleId"
};

message DigitizationFileModel {
  string file_id = 1 [(scalapb.field).type = "DigitizationFileId"];
  string name = 2;
  string parent_folder_id = 3 [(scalapb.field).type = "DigitizationFolderId"];
  repeated string tags = 4 [(scalapb.field).collection_type = "Set"];
  repeated string owners = 5 [(scalapb.field).type = "UserId"];
  string creator = 6 [(scalapb.field).type = "UserId"];
  InstantMessage created_at = 7 [(scalapb.field).type = "java.time.Instant"];

  InstantMessage last_edited_at = 8 [(scalapb.field).type = "java.time.Instant"];
  string last_editor = 9 [(scalapb.field).type = "UserId"];
  repeated anduin.utils.SystemTag system_tags = 10 [(scalapb.field).collection_type = "Set"];

  oneof digitization_resource {
    FormResource form_resource = 20 ;
    AnnotationDocumentResource annotation_document_resource = 21 ;
    DataTemplateResource data_template_resource = 22 ;
    BlueprintResource blueprint_resource = 23;
    CueModuleResource cue_module_resource = 24;
  }
}

message FormResource {
  string id = 1 [(scalapb.field).type = "FormId"];
}

message AnnotationDocumentResource {
  string id = 1 [(scalapb.field).type = "AnnotationDocumentId"];
}

message DataTemplateResource {
  string id = 1 [(scalapb.field).type = "DataTemplateId"];
}

message BlueprintResource {
  string id = 1 [(scalapb.field).type = "BlueprintId"];
}

message CueModuleResource {
  string id = 1 [(scalapb.field).type = "CueModuleId"];
}

message RecordTypeUnion {
  DigitizationFileModel _DigitizationFileModel = 1;
}
