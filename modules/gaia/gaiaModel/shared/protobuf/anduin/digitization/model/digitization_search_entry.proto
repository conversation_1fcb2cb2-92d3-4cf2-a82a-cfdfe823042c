syntax = "proto3";

package anduin.digitization.model.search;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
};

message DigitizationSearchEntry {
  string id = 1 ;
  string name = 2;
  repeated string tags = 3 [(scalapb.field).collection_type = "Set"];
  repeated string owners = 4 [(scalapb.field).collection_type = "Set"];
  string file_type = 5;
  InstantMessage last_edited_at = 6 [(scalapb.field).type = "java.time.Instant"];
  InstantMessage created_at = 7 [(scalapb.field).type = "java.time.Instant"];
}

message RecordTypeUnion {
  DigitizationSearchEntry _SearchEntry = 1;
}
