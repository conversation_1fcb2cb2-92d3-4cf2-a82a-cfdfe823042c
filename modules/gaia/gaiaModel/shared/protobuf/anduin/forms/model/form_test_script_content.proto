syntax = "proto3";

package anduin.forms.model.testscript.content;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.form.FormTestScriptId"
  import: "anduin.model.common.user.UserId"
};

message FormTestScriptContent {
  string id = 1 [(scalapb.field).type = "FormTestScriptId"];
  string content = 2;
  string created_by = 10 [(scalapb.field).type = "UserId"];
  InstantMessage created_at = 11 [(scalapb.field).type = "java.time.Instant"];
  InstantMessage last_updated_at = 12 [(scalapb.field).type = "java.time.Instant"];
}

message RecordTypeUnion {
  FormTestScriptContent _FormTestScriptContent = 1;
}
