syntax = "proto3";

package anduin.forms.model;

import "scalapb/scalapb.proto";
import "date_time.proto";
import "email_address.proto";
import "anduin/forms/model/form_version_models.proto";
import "anduin/utils/system_tag.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.form.FormId"
  import: "anduin.id.form.FormFolderId"
  import: "anduin.id.form.FormVersionId"
  import: "anduin.model.common.emailaddress.EmailAddress"
  import: "anduin.model.common.user.UserId"
};

message FormModel {
  reserved "workingVersion";

  string formId = 1 [(scalapb.field).type = "FormId"];
  string name = 2;
  InstantMessage createdAt = 3 [(scalapb.field).type = "java.time.Instant"];
  version.FormVersionModel draftVersion = 10;
  repeated EmailAddressMessage owners = 5 [(scalapb.field).type = "EmailAddress"];
  repeated string tags = 6 [(scalapb.field).collection_type = "Set"];
  InstantMessage lastEditedAt = 7 [(scalapb.field).type = "java.time.Instant"];

  enum FormTrackingStatus {
    Created = 0;
    Analyzing = 1;
    Building = 2;
    ReadyToTest = 3;
    Testing = 4;
    Completed = 5;
    Archived = 6;
  }
  FormTrackingStatus trackingStatus = 8;
  string latestVersionId = 9 [(scalapb.field).type = "FormVersionId"];
  int32 versionCount = 11;
  string storageUniqueKey = 12;
  // TODO: @hiepbui to move this metadata to a dedicated model for draft
  version.FormVersionMetadataModel draft_metadata = 13;
  string parent_folder_id = 14 [(scalapb.field).type = "FormFolderId"];
  map<string, AssociatedLink> associated_links = 15;

  enum FundEngagementType {
    Fundraise = 0;
    FundraiseSpvCoInvestment = 1; // TODO reserve after migration
    EmployeeInvestmentEmployeeScheme = 2;
    IndicationOfInterest = 3;
    BindingCommitmentForm = 4;
    MasterSideLetterMfn = 5;
    AmlKycCollection = 6;
    InvestorCommunications = 7;
    Unknown = 8;
    FriendAndFamily = 9;
    AdditionalForSpecificInvestorAfterSubscription = 10; // TODO reserve after migration
    WireInstruction = 11;
    CarriedInterest = 12;
    Spv = 13;
    CoInvestment = 14;
    AdditionalInvestment = 15;
    AdditionalForms = 16;
    LpTransfer = 17;
    Ddq = 18;
    TaxForm = 19;
    VotingConsent = 20;
    CapitalBidding = 21;
  }
  optional FundEngagementType fundEngagementType = 16;
  optional int32 numFundraise = 17;
  string last_editor = 18 [(scalapb.field).type = "Option[UserId]"];
  // TODO: @hiepbui to move this system metadata to a dedicated model for draft
  version.FormVersionSystemMetadataModel draft_system_metadata = 19;
  repeated anduin.utils.SystemTag system_tags = 20 [(scalapb.field).collection_type = "Set"];
}

message AssociatedLink {
  string ref = 1;
  string created_by = 2 [(scalapb.field).type = "Option[UserId]"];
  InstantMessage created_at = 3 [(scalapb.field).type = "java.time.Instant"];
}

message RecordTypeUnion {
  FormModel _FormModel = 1;
}
