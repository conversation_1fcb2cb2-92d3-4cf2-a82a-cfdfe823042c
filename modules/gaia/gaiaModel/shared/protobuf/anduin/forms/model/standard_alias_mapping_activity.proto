syntax = "proto3";

package anduin.forms.model.standardaliasmapping;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.model.common.user.UserId"
};

message StandardAliasMappingActivity {
  oneof sealed_value {
    AddStandardAliasMappingActivity add_standard_alias_mapping_activity = 1;
    RemoveStandardAliasMappingActivity remove_standard_alias_mapping_activity = 2;
  }
}

message AddStandardAliasMappingActivity {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "java.time.Instant"];
  map<string, string> added_items = 3;
}

message RemoveStandardAliasMappingActivity {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "java.time.Instant"];
  repeated string removed_items = 3;
}
