syntax = "proto3";

package anduin.forms.version;

import "scalapb/scalapb.proto";
import "date_time.proto";
import "email_address.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.form.FormId"
  import: "anduin.id.form.FormVersionId"
  import: "anduin.id.form.FormVersionMetadataId"
  import: "anduin.id.form.FormVersionSystemMetadataId"
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.id.funddata.FundDataFirmId"
  import: "anduin.model.common.emailaddress.EmailAddress"
  import: "anduin.model.id.FileId"
  import: "anduin.id.annotation.AnnotationDocumentVersionId"
  import: "anduin.id.blueprint.BlueprintId"
  import: "anduin.id.blueprint.BlueprintVersionId"
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.cue.CueModuleVersionId"
};

message FormVersionTagModel {
  string name = 1;
  bool editable = 2;
}

message FormVersionModel {
  enum FormType {
    Default = 0;
    Validation = 1;
  }

  reserved "lakeFSCommitId";
  reserved 3;
  string formVersionId = 1 [(scalapb.field).type = "FormVersionId"];
  string formId = 2 [(scalapb.field).type = "FormId"];
  string name = 4;
  InstantMessage createdAt = 5 [(scalapb.field).type = "java.time.Instant"];
  EmailAddressMessage author = 6 [(scalapb.field).type = "EmailAddress"];
  string parentVersionId = 7 [(scalapb.field).type = "Option[FormVersionId]"];
  string note = 8;
  int32 versionNumber = 9;
  string storageUniqueKey = 10;
  // The list of funds that use this as active form version
  repeated string fundSubIds = 11 [(scalapb.field) = {collection_type: "Set" type: "FundSubId"}];
  // The list of idm firms that use this as active form version
  repeated string firm_ids = 16 [(scalapb.field) = {collection_type: "Set" type: "FundDataFirmId"}];
  bool is_component_library = 12;
  repeated FormVersionTagModel tags = 13;
  int32 asa_count = 14;
  bool is_ia_form = 15;
  FormType form_type = 17;
}

message FormRuleWarning {
  string rule_name = 1;
  map<string, string> key_modifiers = 2;
  repeated string invalid_keys = 3 [(scalapb.field).collection_type = "Set"];
  repeated string invalid_libs = 4 [(scalapb.field).collection_type = "Set"];
  bool resolved = 5;
}

message AsaRejectedSuggestion {
  string item_id = 1;

  // remember rejected suggestion in case we want to learn more about
  // the reject pattern and improve it
  string suggestion = 2;
}

message GeneratedRule {
  string template_id = 1;
  repeated string inputs = 2;
  repeated string outputs = 3;
  bool duplicated = 4;
}

message BlueprintRef {
  string id = 1 [(scalapb.field).type = "BlueprintId"];
  string versionId = 2 [(scalapb.field).type = "BlueprintVersionId"];
  int32 versionIndex = 3;
  string name = 4;
  InstantMessage createdAt = 5 [(scalapb.field).type = "java.time.Instant"];
  InstantMessage lastUpdatedAt = 6 [(scalapb.field).type = "java.time.Instant"];
  string createdBy = 7 [(scalapb.field).type = "UserId"];
  string lastUpdatedBy = 8[(scalapb.field).type = "UserId"];
}

message FormVersionMetadataModel {

  enum WidgetSource {
    MANUAL = 0;
    COPY = 1;
    PDF_IMPORT = 2;
    COMPONENT_LIBRARY = 3;
    BLUEPRINT_IMPORT = 4;
  }

  message WidgetSourceSummary {
    // Map from WidgetSource index to the corresponding count
    map<int32, int32> source_count = 1;
    int32 total_count = 2;
  }

  message IgnoredMismatchAsa {
    string fileName = 1;
    string fieldName = 2;
  }

  message PdfCutInfo {
    string file_name = 1;
    int32 start = 2;
    int32 end = 3;
    bool need_review = 4;
  }

  message PdfFieldInfo {
    string fileName = 1;
    string fieldName = 2;
  }

  reserved 13;
  string id = 1 [(scalapb.field).type = "FormVersionMetadataId"];
  repeated FormRuleWarning rule_warnings = 2;
  repeated string ignored_invalid_require_setups = 3 [(scalapb.field).collection_type = "Set"];
  map<string, WidgetSource> widget_sources = 4;
  WidgetSourceSummary widget_source_summary = 5;
  repeated AsaRejectedSuggestion rejected_asa_suggestions = 6;
  repeated IgnoredMismatchAsa ignored_mismatch_asa = 7 [(scalapb.field).collection_type = "Set"];
  repeated GeneratedRule rejected_generated_rules = 8;
  map<string, PdfCutInfo> pdf_cuts = 9;
  bool is_pdf_cuts_migrated = 10;
  repeated string ignored_invalid_pdf_non_input_mapping_fields = 11 [(scalapb.field).collection_type = "Set"];
  repeated PdfFieldInfo ignored_unmapped_pdf_non_input_fields = 12 [(scalapb.field).collection_type = "Set"];
  BlueprintRef blueprint_ref = 14;
  repeated string ignored_mismatched_blueprint_metadata = 15 [(scalapb.field).collection_type = "Set"];
  repeated string ignored_non_imported_blueprint_metadata = 16 [(scalapb.field).collection_type = "Set"];
}

message FormVersionSystemMetadataModel {
  string id = 1 [(scalapb.field).type = "FormVersionSystemMetadataId"];
  map<string, string> annotation_mapping = 2 [(scalapb.field).key_type = "FileId", (scalapb.field).value_type = "AnnotationDocumentVersionId"];
  string cue_mapping_id = 3 [(scalapb.field).type = "Option[CueModuleVersionId]"];
}

message RecordTypeUnion {
  FormVersionModel _FormVersionModel = 1;
  FormVersionMetadataModel _FormVersionMetadataModel = 2;
  FormVersionSystemMetadataModel _FormVersionSystemMetadataModel = 3;
}
