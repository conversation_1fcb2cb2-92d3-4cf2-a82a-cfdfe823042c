syntax = "proto3";

package anduin.forms.model;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.forms.model"
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.dataextract.DataExtractProjectItemId"
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.id.fundsub.dataextract.FundSubDataExtractRequestId"
  import: "anduin.id.lpprofile.LpProfileId"
  import: "anduin.id.funddata.FundDataInvestmentEntityId"
  import: "anduin.model.common.user.UserId"
  import: "java.time.Instant"
};

message FormDataSource {
  oneof sealed_value {
    FromGpAutofill from_gp_autofill = 1;
    FromLpAutofill from_lp_autofill = 2;
    FromLpProfile from_lp_profile = 3;
    FromFundDataInvestmentEntity from_fund_data_investment_entity = 4;
    FromDataExtractionService from_data_extraction_service = 5;
  }
}

message FromGpAutofill {
  string gp_fund_id = 1 [(scalapb.field).type = "FundSubId"];
  string past_data_lp_id = 2 [(scalapb.field).type = "FundSubLpId"];
  string actor_id = 3 [(scalapb.field).type = "UserId"];
  InstantMessage occur_at = 4 [(scalapb.field).type = "Instant"];
}

message FromLpAutofill {
  string past_data_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string actor_id = 2 [(scalapb.field).type = "UserId"];
  InstantMessage occur_at = 3 [(scalapb.field).type = "Instant"];
}

message FromLpProfile {
  string profile_id = 1 [(scalapb.field).type = "LpProfileId"];
  string actor_id = 2 [(scalapb.field).type = "UserId"];
  InstantMessage occur_at = 3 [(scalapb.field).type = "Instant"];
  string investmentEntityName = 4;
}

message FromFundDataInvestmentEntity {
  string fund_data_investment_entity_id = 1 [(scalapb.field).type = "FundDataInvestmentEntityId"];
  string actor_id = 2 [(scalapb.field).type = "UserId"];
  InstantMessage occur_at = 3 [(scalapb.field).type = "Instant"];
  int32 prefilled_visible_fields = 4;
  int32 prefilled_hidden_fields = 5;
}

message FromDataExtractionService {
  reserved 1;
  string actor_id = 2 [(scalapb.field).type = "UserId"];
  InstantMessage occur_at = 3 [(scalapb.field).type = "Instant"];
  // FromDataExtractionService is used for data analysis, we should not reserved this field
  string request_id_deprecated = 4 [(scalapb.field).type = "DataExtractProjectItemId"];
  string request_id = 5 [(scalapb.field).type = "FundSubDataExtractRequestId"];
}
