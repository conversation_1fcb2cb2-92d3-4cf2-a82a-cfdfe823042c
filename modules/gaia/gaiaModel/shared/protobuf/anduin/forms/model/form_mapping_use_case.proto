syntax = "proto3";

package anduin.forms.model.formmapping;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
};

message FormMappingUseCase {
  oneof sealed_value {
    FormMappingMainAndTaxForms form_mapping_main_and_tax_forms = 1;
  }
}

message FormMappingMainAndTaxForms {
  int32 dummy = 999;
}

message SameAliasMappingNamespacePair {
  string src_namespace = 1;
  string dest_namespace = 2;
}
