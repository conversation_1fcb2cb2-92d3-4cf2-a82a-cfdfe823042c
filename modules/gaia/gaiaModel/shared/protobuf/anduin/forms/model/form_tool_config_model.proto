syntax = "proto3";

package anduin.forms.model.toolconfig;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.form.FormToolConfigId"
  import: "anduin.id.form.FormVersionId"
  import: "anduin.model.common.user.UserId"
  import: "anduin.forms.ui.WidgetType"
  import: "anduin.forms.model.FormToolConfig.LogicExtractorControlType"
  import: "anduin.forms.utils.WidgetTypeMapper.widgetTypeMapper"
};


message FormToolConfigProperty {
  message LogicSupportPage {
    string form_version_id = 1 [(scalapb.field).type = "FormVersionId"];
    string alias = 2;
  }
  message FormRuleLibrary {
    string key = 1;
  }
  message FormRuleLibraryVersion {
    string lib_id = 1 [(scalapb.field).type = "FormToolConfigId"];
    string version_key = 2;
    string content = 3;
  }
  message FormTermPattern {
    string search_text = 1;
    bool is_regex = 2;
    repeated string widgets = 3 [(scalapb.field) = {collection_type: "Set" type: "WidgetType"}];
  }
  message LogicExtractorControl {
    string search_library_name = 1;
    bool is_regex = 2;
    optional string control_type = 3 [(scalapb.field).type = "LogicExtractorControlType"];
  }

  oneof value {
    LogicSupportPage logic_support_page = 1;
    FormRuleLibrary form_rule_library = 2;
    FormRuleLibraryVersion form_rule_library_version = 3;
    FormTermPattern form_term_pattern = 4;
    LogicExtractorControl logic_extractor_control = 5;
  }
}

message FormToolConfigModel {
  string id = 1 [(scalapb.field).type = "FormToolConfigId"];

  string name = 2;
  string description = 3;
  bool active = 4;

  string createdBy = 5 [(scalapb.field).type = "UserId"];
  InstantMessage created_at = 6 [(scalapb.field).type = "java.time.Instant"];
  string lastEditedBy = 7 [(scalapb.field).type = "UserId"];
  InstantMessage last_edited_at = 8 [(scalapb.field).type = "java.time.Instant"];

  FormToolConfigProperty property = 9;
}

message RecordTypeUnion {
  FormToolConfigModel _FormToolConfigModel = 1;
}