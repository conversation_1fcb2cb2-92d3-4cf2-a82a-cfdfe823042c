syntax = "proto3";

package anduin.forms.model.activity;

import "scalapb/scalapb.proto";
import "date_time.proto";
import "anduin/forms/model/form_models.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.form.FormActivityId"
  import: "anduin.id.form.FormId"
  import: "anduin.model.common.user.UserId"
};

message FormActivityModel {
  string formActivityId = 1 [(scalapb.field).type = "FormActivityId"];
  string formId = 2 [(scalapb.field).type = "FormId"];
  string actor = 3 [(scalapb.field).type = "UserId"];
  InstantMessage at = 4 [(scalapb.field).type = "java.time.Instant"];
  FormActivityData data = 5;
}

message FormActivityData {
  oneof sealed_value {
    FormCreated formCreated = 1;
    FormStatusUpdated formStatusUpdated = 2;
  }
}

message FormCreated {
}

message FormStatusUpdated {
  FormModel.FormTrackingStatus status = 1;
}

message RecordTypeUnion {
  FormActivityModel _FormActivityModel = 1;
}
