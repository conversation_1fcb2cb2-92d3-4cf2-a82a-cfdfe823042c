syntax = "proto3";

package anduin.forms.model.formmapping;

import "scalapb/scalapb.proto";
import "anduin/forms/model/form_mapping_use_case.proto";
import "anduin/forms/model/schema_message.proto";
import "anduin/forms/model/rule_message.proto";
import "anduin/forms/model/form_mapping_activity.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.form.FormMappingId"
  import: "anduin.id.form.FormVersionId"
  import: "anduin.model.common.FieldAlias"
};

message FormMapping {
  option (scalapb.message).annotations = "private[forms]";
  option (scalapb.message).companion_annotations = "private[forms]";
  string id = 1 [(scalapb.field).type = "FormMappingId"];
  string name = 2;
  repeated FormInfo form_info_list = 3;
  // Extract the concatenated string of Form ID in formInfoList for searching purpose
  // - Use this mechanism since the FanType.Concatenate mechanism could not be nested -> doesn't work for form_info_list
  // - Need to properly sort form_info_list for any update anyway -> also update the concatenated ids string at that time
  string concatenated_form_ids_string = 4;
  FormMappingUseCase use_case_type = 5;
  repeated SameAliasMappingNamespacePair same_alias_mapping_namespace_pairs = 6;
  map<string, SchemaMessage> additional_mapping_aliases = 7 [(scalapb.field) = { key_type: "FieldAlias" }];
  repeated RuleMessage mapping_rules = 8;
  string defaultNamespace = 9;
  repeated FormMappingActivity activities = 10;
}

message FormInfo {
  string form_version_id = 1 [(scalapb.field).type = "FormVersionId"];
  map<string, string> namespace_map = 2;
}

message RecordTypeUnion {
  FormMapping _FormMapping = 1;
}
