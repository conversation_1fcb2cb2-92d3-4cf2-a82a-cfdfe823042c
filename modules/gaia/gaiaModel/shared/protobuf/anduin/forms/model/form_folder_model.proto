syntax = "proto3";

package anduin.forms.model.formfolder;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.form.FormFolderId"
  import: "anduin.model.common.user.UserId"
};

message FormFolderModel {
  string form_folder_id = 1 [(scalapb.field).type = "FormFolderId"];
  optional string parent_id = 2 [(scalapb.field).type = "FormFolderId"];
  string creator = 3 [(scalapb.field).type = "UserId"];
  string name = 4;
  InstantMessage created_at = 5 [(scalapb.field).type = "java.time.Instant"];
  InstantMessage last_edited_at = 6 [(scalapb.field).type = "java.time.Instant"];
  repeated string tags = 7 [(scalapb.field).collection_type = "Set"];
}

message RecordTypeUnion {
  FormFolderModel _FormFolderModel = 1;
}
