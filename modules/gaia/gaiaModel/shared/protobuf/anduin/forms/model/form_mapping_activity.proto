syntax = "proto3";

package anduin.forms.model.formmapping;

import "scalapb/scalapb.proto";
import "date_time.proto";
import "anduin/forms/model/form_mapping_use_case.proto";
import "anduin/forms/model/rule_message.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.form.FormVersionId"
  import: "anduin.model.common.FieldAlias"
};

message FormMappingActivity {
  option (scalapb.message).annotations = "private[forms]";
  option (scalapb.message).companion_annotations = "private[forms]";
  oneof sealed_value {
    FormMappingCreateActivity form_mapping_create_activity = 1;
    FormMappingRemoveActivity form_mapping_remove_activity = 2;
    FormMappingRenameActivity form_mapping_rename_activity = 3;
    FormMappingImportFormActivity form_mapping_import_form_activity = 4;
    FormMappingRemoveFormActivity form_mapping_remove_form_activity = 5;
    FormMappingUpdateAliasesActivity form_mapping_update_aliases_activity = 6;
    FormMappingUpdateRulesActivity form_mapping_update_rules_activity = 7;
    FormMappingUpdateUseCaseActivity form_mapping_update_use_case_activity = 8;
    FormMappingUpdateSameAliasMappingNamespacePairsActivity form_mapping_update_same_alias_mapping_namespace_pairs_activity = 9;
  }
}

message FormMappingCreateActivity {
  option (scalapb.message).annotations = "private[forms]";
  option (scalapb.message).companion_annotations = "private[forms]";
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "java.time.Instant"];
}

message FormMappingRemoveActivity {
  option (scalapb.message).annotations = "private[forms]";
  option (scalapb.message).companion_annotations = "private[forms]";
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "java.time.Instant"];
}

message FormMappingRenameActivity {
  option (scalapb.message).annotations = "private[forms]";
  option (scalapb.message).companion_annotations = "private[forms]";
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "java.time.Instant"];
  string new_name = 3;
}

message FormMappingImportFormActivity {
  option (scalapb.message).annotations = "private[forms]";
  option (scalapb.message).companion_annotations = "private[forms]";
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "java.time.Instant"];
  string form_version_id = 3 [(scalapb.field).type = "FormVersionId"];
  map<string, string> namespace_map = 4;
}

message FormMappingRemoveFormActivity {
  option (scalapb.message).annotations = "private[forms]";
  option (scalapb.message).companion_annotations = "private[forms]";
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "java.time.Instant"];
  repeated string removed_form_version_ids = 3 [(scalapb.field).type = "FormVersionId", (scalapb.field).collection_type = "Set"];
}

message FormMappingUpdateSameAliasMappingNamespacePairsActivity {
  option (scalapb.message).annotations = "private[forms]";
  option (scalapb.message).companion_annotations = "private[forms]";
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "java.time.Instant"];
  repeated SameAliasMappingNamespacePair added_pairs = 3;
  repeated SameAliasMappingNamespacePair removed_pairs = 4 [(scalapb.field).collection_type = "Set"];
}

message FormMappingUpdateAliasesActivity {
  option (scalapb.message).annotations = "private[forms]";
  option (scalapb.message).companion_annotations = "private[forms]";
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "java.time.Instant"];
  repeated string added_aliases = 3 [(scalapb.field).type = "FieldAlias"];
  repeated string removed_aliases = 4 [(scalapb.field).type = "FieldAlias", (scalapb.field).collection_type = "Set"];
}

message FormMappingUpdateRulesActivity {
  option (scalapb.message).annotations = "private[forms]";
  option (scalapb.message).companion_annotations = "private[forms]";
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "java.time.Instant"];
  repeated anduin.forms.model.RuleMessage added_rules = 3;
  repeated anduin.forms.model.RuleMessage removed_rules = 4 [(scalapb.field).collection_type = "Set"];
}

message FormMappingUpdateUseCaseActivity {
  option (scalapb.message).annotations = "private[forms]";
  option (scalapb.message).companion_annotations = "private[forms]";
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "java.time.Instant"];
  FormMappingUseCase new_use_case = 3;
}
