syntax = "proto3";

package anduin.forms.model.testscript.model;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.form.FormTestScriptId"
  import: "anduin.id.form.FormVersionId"
  import: "anduin.id.form.FormId"
  import: "anduin.model.common.user.UserId"
};

message FormTestScriptModel {
  string id = 1 [(scalapb.field).type = "FormTestScriptId"];
  string form_id = 2 [(scalapb.field).type = "FormId"];
  string name = 4;
  string description = 5;
  repeated string tags = 6;
  string compatible_form_version_id = 7 [(scalapb.field).type = "Option[FormVersionId]"];
  string created_by = 10 [(scalapb.field).type = "UserId"];
  InstantMessage created_at = 11 [(scalapb.field).type = "java.time.Instant"];
  InstantMessage last_updated_at = 12 [(scalapb.field).type = "java.time.Instant"];
}

message RecordTypeUnion {
  FormTestScriptModel _FormTestScriptModel = 1;
}
