syntax = "proto3";

package anduin.forms.model.formdiff;

import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.model.id.FormDiffJobId"
  import: "anduin.id.form.FormVersionId"
};

enum FormDiffStatus {
  PENDING = 0;
  SUCCESSFUL = 1;
  FAILED = 2;
}

enum FormDiffMode {
  InternalDiff = 0;
  FundSubDiff = 1;
  FormAliasDiff = 2;
  FormLabelDiff = 3;
}

message FormDiffResult {
  string id = 1 [(scalapb.field).type = "FormDiffJobId"];
  string old_form_version_id = 5 [(scalapb.field).type = "FormVersionId"];
  string new_form_version_id = 6 [(scalapb.field).type = "FormVersionId"];
  FormDiffMode mode = 7;
  FormDiffStatus status = 2;
  repeated Matching matchings = 3;
  repeated RuleMatching ruleMatchings = 4;
  InstantMessage job_completed_at = 8 [(scalapb.field).type = "java.time.Instant"];
  double matching_score = 9;
}

message Matching {
  google.protobuf.StringValue oldKey = 1;
  google.protobuf.StringValue newKey = 2;
  bool isSame = 3;
}

message RuleMatching {
  google.protobuf.StringValue oldRule = 1;
  google.protobuf.StringValue newRule = 2;
}

message RecordTypeUnion {
  FormDiffResult _FormDiffResult = 1;
}
