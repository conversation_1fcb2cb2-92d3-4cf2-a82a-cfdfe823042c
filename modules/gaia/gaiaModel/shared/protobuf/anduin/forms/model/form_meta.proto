syntax = "proto3";

package anduin.forms.model.formmeta;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.model.id.FileId"
  import: "anduin.id.lpprofile.LpProfileId"
  import: "anduin.id.form.FormVersionId"
};

message FormMeta {
  option (scalapb.message).annotations = "private[forms]";
  option (scalapb.message).companion_annotations = "private[forms]";
  string id = 1 [(scalapb.field).type = "FormVersionId"];
  FormMetaTrait form_meta = 2;
  string form_meta_search_id = 3;
}

message FormMetaTrait {
  option (scalapb.message).annotations = "private[forms]";
  option (scalapb.message).companion_annotations = "private[forms]";
  oneof sealed_value {
    FormForFile form_for_file = 1;
    FormForInvestorProfile form_for_investor_profile = 2;
  }
}

message FormForFile {
  option (scalapb.message).annotations = "private[forms]";
  option (scalapb.message).companion_annotations = "private[forms]";
  string file_id = 1 [(scalapb.field).type = "FileId"];
}

message FormForInvestorProfile {
  option (scalapb.message).annotations = "private[forms]";
  option (scalapb.message).companion_annotations = "private[forms]";
  string profile_id = 1 [(scalapb.field).type = "LpProfileId"];
}

message RecordTypeUnion {
  FormMeta _FormMeta = 1;
}
