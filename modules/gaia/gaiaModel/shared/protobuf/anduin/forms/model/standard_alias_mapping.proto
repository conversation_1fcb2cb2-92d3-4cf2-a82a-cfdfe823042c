syntax = "proto3";

package anduin.forms.model.standardaliasmapping;

import "scalapb/scalapb.proto";
import "anduin/forms/model/standard_alias_mapping_activity.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.fundsub.FundSubFormIdTrait"
};

message StandardAliasMapping {
  option (scalapb.message).annotations = "private[forms]";
  option (scalapb.message).companion_annotations = "private[forms]";
  string id = 1 [(scalapb.field).type = "FundSubFormIdTrait"];
  map<string, string> to_standard_alias_map = 2;
  repeated StandardAliasMappingActivity activities = 3;
}


message RecordTypeUnion {
  StandardAliasMapping _StandardAliasMapping = 1;
}
