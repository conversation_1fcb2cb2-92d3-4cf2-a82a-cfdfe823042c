syntax = "proto3";

package anduin.forms.integration;

import "scalapb/scalapb.proto";
import "date_time.proto";
import "google/protobuf/wrappers.proto";
import "external/squants.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.form.FormId"
  import: "anduin.id.form.FormVersionId"
  import: "anduin.model.common.user.UserId"
  import: "anduin.protobuf.SquantMappers.given"
  preamble: "sealed trait SignatureAmountFieldTrait {"
  preamble:  "  def id: String"
  preamble:  "  def prefix: String"
  preamble:  "  def uniqueId: String = if (prefix.nonEmpty) prefix + \":\" + id else id"
  preamble:  "}"
};

message SignatureAmountField {
  option (scalapb.message).extends = "SignatureAmountFieldTrait";

  string id = 1;
  string prefix = 2;
}

message SubFundConfig {
  reserved 3;
  string unique_id = 4;
  string display_name = 1; // Associated name of the sub-fund, used for display in the FS dashboard
  string amount_field = 2;
  repeated SignatureAmountField associated_signature_amount_fields = 6;
  external.squants.CurrencyMessage currency = 5 [(scalapb.field).type = "squants.market.Currency"];
}

message FormFieldOptionSelection {
  string field_alias = 1;
  string option_value = 2;
}

message SingleFundSelection {
  string display_name = 1; // Name of this fund selection (name of the radio/checkbox option in the form)
  FormFieldOptionSelection associated_field_opt = 2;
  repeated SubFundConfig sub_funds = 3;
}

message WithoutFundSelection {
  repeated SubFundConfig sub_funds = 1;
}

message WithFundSelection {
  repeated SingleFundSelection selections = 1;
}

message FundConfigData {
  oneof sealed_value {
    WithoutFundSelection without_fund_selection = 1;
    WithFundSelection with_fund_selection = 2;
  }
}

message FundEnvironmentConfigData {
  FundConfigData fund_config = 1;
  google.protobuf.StringValue investment_entity_field_opt = 2;
  string last_updated_by = 3 [(scalapb.field).type = "UserId"];
  InstantMessage last_updated_at = 4 [(scalapb.field).type = "java.time.Instant"];
}

message FormVersionIntegrationModel {
  string form_version_id = 1 [(scalapb.field).type = "FormVersionId"];
  string form_id = 2 [(scalapb.field).type = "FormId"]; // Must be parent of form_version_id

  FundEnvironmentConfigData fund_environment = 3;

  string created_by = 4 [(scalapb.field).type = "UserId"];
  InstantMessage created_at = 5 [(scalapb.field).type = "java.time.Instant"];
}

message RecordTypeUnion {
  FormVersionIntegrationModel _FormVersionIntegrationModel = 1;
}
