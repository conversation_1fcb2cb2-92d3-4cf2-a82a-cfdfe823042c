syntax = "proto3";

package anduin.forms.model;

import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";
import "google/protobuf/struct.proto";

option (scalapb.options) = {
  package_name: "anduin.forms.model"
  single_file: true
  preserve_unknown_fields: false
  import: "io.circe.Json"
  import: "anduin.model.codec.ProtoCodecs.given"
};

message SchemaMessage {
  oneof sealed_value {
    SchemaNull schema_null = 1;
    SchemaBoolean schema_boolean = 2;
    SchemaInteger schema_integer = 3;
    SchemaNumber schema_number = 4;
    SchemaString schema_string = 5;
    SchemaSet schema_set = 6;
    SchemaArray schema_array = 7;
    SchemaObj schema_obj = 8;
    SchemaEnum schema_enum = 9;
  }
}

message SchemaNull {
  google.protobuf.StringValue title = 1;
  google.protobuf.StringValue description = 2;
  google.protobuf.Value default = 3 [(scalapb.field).type = "Json"];
}

message SchemaBoolean {
  google.protobuf.StringValue title = 1;
  google.protobuf.StringValue description = 2;
  google.protobuf.Value default = 3 [(scalapb.field).type = "Json"];
}

message SchemaInteger {
  google.protobuf.StringValue title = 1;
  google.protobuf.StringValue description = 2;
  google.protobuf.Value default = 3 [(scalapb.field).type = "Json"];
}

message SchemaNumber {
  google.protobuf.StringValue title = 1;
  google.protobuf.StringValue description = 2;
  google.protobuf.Value default = 3 [(scalapb.field).type = "Json"];
}

message StringFormat {
  string value = 1;
}

message SchemaString {
  StringFormat format = 1;
  google.protobuf.StringValue title = 2;
  google.protobuf.StringValue description = 3;
  google.protobuf.Value default = 4 [(scalapb.field).type = "Json"];
}

message SchemaSet {
  SchemaMessage componentType = 1;
  google.protobuf.StringValue title = 2;
  google.protobuf.StringValue description = 3;
  google.protobuf.Value default = 4 [(scalapb.field).type = "Json"];
}

message SchemaArray {
  SchemaMessage componentType = 1;
  google.protobuf.StringValue title = 2;
  google.protobuf.StringValue description = 3;
  google.protobuf.Value default = 4 [(scalapb.field).type = "Json"];
}

message FieldMessage {
  string name = 1;
  SchemaMessage tpe = 2;
}

message SchemaObj {
  repeated FieldMessage fields = 1;
  google.protobuf.StringValue title = 2;
  google.protobuf.StringValue description = 3;
  google.protobuf.Value default = 4 [(scalapb.field).type = "Json"];
}

message SchemaEnum {
  SchemaMessage componentType = 1;
  repeated google.protobuf.Value values = 2 [
    (scalapb.field).type = "Json",
    (scalapb.field).collection_type = "Seq"
  ];
}
