syntax = "proto3";

package anduin.autofill;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.autofill.ComputeFormMatchingId"
};

message StringSet {
  repeated string items = 1 [(scalapb.field).collection_type = "Set"];
}

message MatchedAlias {
  string alias = 1;
  double similarity = 2;
}

message MatchedAliasSet {
  repeated MatchedAlias items = 1 [(scalapb.field).collection_type = "Set"];
}

message ComputeFormMatchingResult {
  enum ComputeFormMatchingStatus {
    NOT_STARTED = 0;
    IN_PROGRESS = 1;
    COMPLETED = 2;
    FAILED = 3;
  }

  string id = 1 [(scalapb.field).type = "ComputeFormMatchingId"];
  double reusability_score = 2;
  double matching_score = 3;
  map<string, MatchedAliasSet> matching_alias_map = 4;
  map<string, StringSet> matching_value_map = 5;
  ComputeFormMatchingStatus status = 6;
  InstantMessage last_updated_at = 7 [(scalapb.field).type = "java.time.Instant"];
}

message RecordTypeUnion {
  ComputeFormMatchingResult ComputeFormMatchingResult = 1;
}
