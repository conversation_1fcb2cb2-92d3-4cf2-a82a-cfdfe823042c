syntax = "proto3";

package anduin.ontology.model.asa.mapping;

import "scalapb/scalapb.proto";
import "date_time.proto";
import "google/protobuf/wrappers.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.ontology.OntologyAsaId"
};

message RepeatableInfo {
  google.protobuf.Int32Value max_length_opt = 1;
  google.protobuf.Int32Value repeated_index_opt = 2;
}

message DraftInfo {
  reserved 2;
  string content = 1; // Simply store JSON data, will be decoded in frontend
  RepeatableInfo repeatable_info = 5;
  string last_updated_by = 3 [(scalapb.field).type = "UserId"];
  InstantMessage last_updated_at = 4 [(scalapb.field).type = "java.time.Instant"];
}

message OntologyAsaMapping {
  reserved 5;
  string space_id = 1;
  string space_version_id = 2;
  string field_id = 3;
  string asa_id_opt = 4 [(scalapb.field).type = "Option[OntologyAsaId]"];
  RepeatableInfo repeatable_info = 11;
  string created_by = 6 [(scalapb.field).type = "UserId"];
  InstantMessage created_at = 7 [(scalapb.field).type = "java.time.Instant"];
  string last_updated_by = 8 [(scalapb.field).type = "UserId"];
  InstantMessage last_updated_at = 9 [(scalapb.field).type = "java.time.Instant"];
  DraftInfo draft = 10;
}

message RecordTypeUnion {
  OntologyAsaMapping _OntologyAsaMapping = 1;
}
