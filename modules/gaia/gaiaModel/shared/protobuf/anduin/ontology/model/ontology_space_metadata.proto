syntax = "proto3";

package anduin.ontology.model.space;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.model.common.user.UserId"
};

message OntologySpaceMetadata {

  enum InitializeStatus {
    NOT_STARTED = 0;
    IN_PROGRESS = 1;
    INITIALIZED = 2;
  }

  string space_id = 1;
  InitializeStatus status = 2;
  string last_updated_by = 3 [(scalapb.field).type = "UserId"];
  InstantMessage last_updated_at = 4 [(scalapb.field).type = "java.time.Instant"];
}

message RecordTypeUnion {
  OntologySpaceMetadata _OntologySpaceMetadata = 1;
}
