package build.modules.gaia

import build_.modules.gaia.dependency_.*
import build_.build.util_.*
import build.modules.{sa, signature, ontology, heimdall}
import build.platform.{stargazerCore, webModules}
import build.gondor.gondorModel

import mill.*
import mill.scalalib.*
import mill.scalajslib.api.*
import anduin.build.*
import anduin.mill.*

object `package` extends Module {

  object gaiaModel extends StargazerModelCrossPlatformModule {

    object jvm extends JvmModelModule with SharedModule {
      override def mvnDeps = super.mvnDeps() ++ GaiaModel.jvmDeps

      override def moduleDeps = super.moduleDeps ++ Seq(stargazerCore.jvm, signature.signatureModel.jvm, gondorModel.jvm)

    }

    object js extends JsModelModule with SharedModule {
      override def moduleDeps = super.moduleDeps ++ Seq(stargazerCore.js, signature.signatureModel.js, gondorModel.js)

    }

    trait SharedModule extends ScalaModule {
      override def mvnDeps = super.mvnDeps() ++ GaiaModel.sharedDeps
    }

  }

  object gaia extends AnduinCrossPlatformModule {

    object jvm extends JvmModule with SharedModule with AnduinPlatformScalaPBModule {
      override def mvnDeps = super.mvnDeps() ++ Gaia.jvmDeps

      override def moduleDeps = super.moduleDeps ++ Seq(
        signature.signatureIntegration,
        ontology.ontology.jvm,
        sa.sa.jvm
      )

      object it extends AnduinZioTests with AnduinIntegTests {
        override def moduleDeps = super.moduleDeps ++ Seq(build.gondor.gondorTest, heimdall.heimdall.jvm)
      }

    }

    object js extends JsModule with SharedModule {
      override def mvnDeps = super.mvnDeps() ++ Gaia.jsDeps

      override def moduleDeps = super.moduleDeps ++ Seq(
        webModules,
        signature.signature.js,
        gaiaModel.js,
        ontology.ontologyCore.js,
        sa.saCore.js
      )

    }

    trait SharedModule extends ScalaModule {
      override def scalacOptions = super.scalacOptions() ++ Seq("-Xmax-inlines:128")
    }

  }

  object gaiaBuilder extends AnduinScalaJSModule {

    override def esFeatures = super.esFeatures().withESVersion(ESVersion.ES2018)
    override def mvnDeps = super.mvnDeps() ++ GaiaBuilder.jsDeps
    override def moduleDeps = super.moduleDeps ++ Seq(gaia.js)

  }

}
