package build.modules.gaia

import build_.build.versions_.{AnduinGaia, AnduinDesign}
import anduin.build.AnduinVersions
import mill.scalalib.*

object GaiaModel {

  lazy val sharedDeps = Seq(
    mvn"com.anduin.stargazer::gaiaEngine::${AnduinGaia.version}"
      .exclude("org.yaml" -> "snakeyaml")
      .exclude("org.scala-js" -> AnduinVersions.j2sjs("scalajs-dom"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-core"))
      .exclude("io.circe" -> AnduinVersions.j2sjs("circe-core"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-parser"))
      .exclude("io.circe" -> AnduinVersions.j2sjs("circe-parser"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-generic"))
      .exclude("io.circe" -> AnduinVersions.j2sjs("circe-generic"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio-streams"))
      .exclude("io.github.cquiroz" -> AnduinVersions.j2sjs("scala-java-time"))
      .exclude("io.github.cquiroz" -> AnduinVersions.j2sjs("scala-java-time-tzdb")),
    mvn"com.github.eikek::yamusca-core::${AnduinVersions.yamusca}"
  )

  lazy val jvmDeps = Seq(
    mvn"com.github.scopt::scopt:${AnduinVersions.scopt}",
    mvn"org.tukaani:xz:${AnduinVersions.xz}"
  )

}

object Gaia {

  lazy val jsDeps = Seq(
    mvn"com.anduin.stargazer::loki::${AnduinGaia.version}"
      .exclude("org.scala-js" -> AnduinVersions.j2sjs("scalajs-dom"))
      .exclude("io.circe" -> AnduinVersions.j2sjs("circe-core"))
      .exclude("io.circe" -> AnduinVersions.j2sjs("circe-parser"))
      .exclude("io.circe" -> AnduinVersions.j2sjs("circe-generic"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio-streams"))
      .exclude("io.github.cquiroz" -> AnduinVersions.j2sjs("scala-java-time"))
      .exclude("io.github.cquiroz" -> AnduinVersions.j2sjs("scala-java-time-tzdb")),
    mvn"org.scala-js::scalajs-dom::${AnduinVersions.scalajsdom}",
    mvn"org.parboiled::parboiled::${AnduinVersions.parboiled}"
      .excludeOrg("com.chuusai"),
    mvn"org.scala-js::scalajs-dom::${AnduinVersions.scalajsdom}",
    mvn"com.olvind::scalablytyped-runtime::${AnduinVersions.scalablytypedRuntime}",
    mvn"design.anduin::web-smcat::${AnduinDesign.version}"
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio"))
      .exclude("com.github.japgolly.scalajs-react" -> AnduinVersions.j2sjs("core"))
      .exclude("org.portable-scala" -> AnduinVersions.j2sjs("portable-scala-reflect"))
      .exclude("com.olvind" -> AnduinVersions.j2sjs("scalablytyped-runtime"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("sourcecode")),
    mvn"com.raquo::domtypes::${AnduinVersions.domTypes}"
      .exclude("org.scala-js" -> AnduinVersions.j2sjs("scalajs-dom")),
    mvn"com.raquo::airstream::${AnduinVersions.airstream}"
      .exclude("org.scala-js" -> AnduinVersions.j2sjs("scalajs-dom")),
    mvn"com.raquo::laminar::${AnduinVersions.laminar}"
      .exclude("com.raquo" -> AnduinVersions.j2sjs("airstream"))
      .exclude("com.raquo" -> AnduinVersions.j2sjs("domtypes"))
  )

  lazy val jvmDeps = Seq(
    mvn"com.anduin.stargazer::loki:${AnduinGaia.version}"
      .exclude("org.yaml" -> "snakeyaml")
      .exclude("io.circe" -> AnduinVersions.j2s("circe-core"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-parser"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-generic"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams")),
    mvn"org.apache.commons:commons-lang3:${AnduinVersions.apacheCommonLang}",
    mvn"org.apache.commons:commons-text:${AnduinVersions.commonsText}"
      .exclude("org.apache.commons" -> "commons-lang3"),
    mvn"org.apache.commons:commons-lang3:${AnduinVersions.apacheCommonLang}",
    mvn"com.googlecode.owasp-java-html-sanitizer:owasp-java-html-sanitizer:${AnduinVersions.javaHtmlSanitizer}"
      .exclude("com.google.guava" -> "guava"),
    mvn"me.xdrop:fuzzywuzzy:${AnduinVersions.fuzzywuzzy}"
  )

}

object GaiaBuilder {

  lazy val jsDeps = Seq(
    mvn"com.olvind::scalablytyped-runtime::${AnduinVersions.scalablytypedRuntime}",
    mvn"design.anduin::web-monaco::${AnduinDesign.version}"
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio"))
      .exclude("com.github.japgolly.scalajs-react" -> AnduinVersions.j2sjs("core"))
      .exclude("org.portable-scala" -> AnduinVersions.j2sjs("portable-scala-reflect"))
      .exclude("com.olvind" -> AnduinVersions.j2sjs("scalablytyped-runtime"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("sourcecode"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("fastparse"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("pprint"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("ujson"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("scalatags"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("geny")),
    mvn"design.anduin::web-x6::${AnduinDesign.version}"
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio"))
      .exclude("com.github.japgolly.scalajs-react" -> AnduinVersions.j2sjs("core"))
      .exclude("org.portable-scala" -> AnduinVersions.j2sjs("portable-scala-reflect"))
      .exclude("com.olvind" -> AnduinVersions.j2sjs("scalablytyped-runtime"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("sourcecode"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("fastparse"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("pprint"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("ujson"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("scalatags"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("geny"))
  )

}
