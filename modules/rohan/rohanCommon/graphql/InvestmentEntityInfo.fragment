fragment InvestmentEntityInfoFields on InvestmentEntityInfoType {
  investmentEntityId
  entityName
  customId
  masterProfileId
  isMasterProfileEmpty
  profileDataProgress
  numSubscriptions
  numAssociatedSubscriptions
  permission
  userState
  entityState
  lastUpdated
  numUpdates
  lastDocumentAndProfileUpdated
  lastDocumentAndProfileUpdatedActorNameOpt
  isWireInstructionUpdated
  demoInfoOpt {
    demoFundSubId
  }
}