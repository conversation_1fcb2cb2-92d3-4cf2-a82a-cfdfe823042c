fragment FundSubLpRestrictedModelFields on FundSubLpRestrictedModelType {
    viewedSections
    lastChangeInfo {
      actor {
        ...ParticipantInfoData
      }
      changedAt
    }
    formFiles {
      key
      value
    }
    requiredDocGroupInfos {
      groupName
      groupInstruction
      docNames
    }
    subscriptionDocSignatureRequestIds
    counterSignatureRequestIds
    usersViewedSigningInstruction
    lpSignatureBlocks {
      groupName
      originalFileId
      prepField {
        id
        blockType
        location {
          ...SignatureLocation
        }
        data {
          ...SignatureData
        }
        radioOptions {
          id
          location {
            ...SignatureLocation
          }
          name
        }
        tooltip
        isOptional
        format
      }
    }
    uniqueEmailSigningRoles
    additionalSignatureRequestIds
    completedSignatureRequestIds
    usersViewedOnboardingInstruction
    formVersionIdOpt
    lpEditedForm
    isSkippedOnboardingFlow
    invitationTypeOpt
    filledProfile {
      fundDataProfile {
        filledProfileId
        filledAt
        filledBy {
          ...ParticipantInfoData
        },
        fillSource
      }
    },
    seenProfileModalUsers
}
