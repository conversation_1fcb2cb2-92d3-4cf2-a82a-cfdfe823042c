query FundSubAdminRestrictedModel($fundSubId: String!) {
  fundSubAdminRestrictedModel(id: $fundSubId) {
    activityLogId
    protectedLink {
      linkId
    }
    inactiveNotificationSetting {
      enabled
      days
    }
    lpIds
    reviewPackageData {
      isEnabled
      isEnabledUnsignedReviewSetting
      reviewers {
        userId
        firstName
        lastName
        email
      }
    },
    customFundIdSetting {
      isEnabled
      customFundId
    }
    investmentFunds {
      fundId
      currency
      name
    }
    metadata {
      key
      value
    }
  }
}
