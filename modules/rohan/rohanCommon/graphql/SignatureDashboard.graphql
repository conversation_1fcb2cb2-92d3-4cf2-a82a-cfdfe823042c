#import "DocumentSignature.fragment"
#import "SignatureTemplate.fragment"
#import "SignatureData.fragment"
#import "SignatureLocation.fragment"

query SignatureDashboard {
  signatureDashboard {
    userId
    personalRequests {
      ...SignatureRequest
    }
    entityDataSeq {
      entityId
      signatureModuleIdOpt
      requests {
        ...SignatureRequest
      }
    }
    baseUrl
  }
}

fragment SignatureRequest on SignatureRequest {
  requestId
  status
  subject
  name
  requester
  requesterName
  requesterEmail
  createdAt
  files {
    fileId
    name
    signedRecords {
      fileId
      name
      signers
      certificateFileIdOpt
      isUploaded
    }
  }
  recipients {
    userId
    name
    email
    status
    lastStatusUpdatedAt
    items {
      fileId
      itemId
      status
      signDataSeq {
        uploadedFileOpt {
          fileId
          name
        }
        documentSignatureOpt {
          ...DocumentSignature
        }
      }
      savedSignature {
        ...DocumentSignature
      }
    }
    preparedBlocks {
      fileId
      signatureTemplate {
        ...SignatureTemplate
      }
    }
  }
  collaborators {
    userId
    name
    email
    role
  }
  refDocs {
    fileId
    name
  }
  publicMessage
  actionLogs {
    actionType
    actor
    at
    clientIp
    targetUserIds
    fileIds
  }
  canReassign
}
