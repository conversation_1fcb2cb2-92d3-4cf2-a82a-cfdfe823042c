#import "InvestmentEntityFullSubscriptionInfo.fragment"
#import "InvestmentEntityShortSubscriptionInfo.fragment"

query InvestmentEntityLinkedSubscriptionQuery($investmentEntityId: String!) {
  linkedSubscriptions(investmentEntityId: $investmentEntityId) {
    shortInfos {
      ...InvestmentEntityShortSubscriptionInfoFields
    }

    fullInfos {
      ...InvestmentEntityFullSubscriptionInfoFields
    }
  }
}
