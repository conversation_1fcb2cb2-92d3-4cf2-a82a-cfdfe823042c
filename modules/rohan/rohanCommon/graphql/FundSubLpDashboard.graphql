#import "FundSubLpModel.fragment"
#import "FundSubPublicModel.fragment"
#import "FundSubFeatureSwitch.fragment"
#import "FundSubLpRestrictedModel.fragment"
#import "DisclaimerSettingModel.fragment"
#import "SignatureData.fragment"
#import "SignatureLocation.fragment"
#import "ParticipantInfo.fragment"
#import "FundSubSignatureConfig.fragment"

query FundSubLpDashboard($lpId: String!, $fundSubId: String!) {
  fundSubPublicModel(id: $fundSubId) {
    ...FundSubPublicModelFields
  }

  fundSubLpModel(lpId: $lpId) {
    ...FundSubLpModelFields
  }

  fundSubLpRestrictedModel(lpId: $lpId) {
    ...FundSubLpRestrictedModelFields
  }

  DisclaimerSettingClient(id: $fundSubId) {
    ... DisclaimerSettingClientFields
  }
}
