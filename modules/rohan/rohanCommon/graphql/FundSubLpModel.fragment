fragment FundSubLpModelFields on FundSubLpModelType {
  mainLp {
    ...ParticipantInfoData
  }
  collaborators {
    ...ParticipantInfoData
  }
  attachedDocs
  firmName
  formSetting {
    allowIncompleteForm
  }
  lastActivityAt
  orderType
  lpState {
    lp
    lpStatus
    submittedDocs {
      ...FundSubFileFields
    }
    counterSignedDocs {
      ...FundSubFileFields
    }
    lastRequestedChanges {
        requestTime
        requestBy
        requestChangeType
    }
    subscriptionMarkedAsCompleteBy {
      ...ParticipantInfoData
    }
  }
  isEnabledUnsignedReview
  addedAt
  duplicatedFromLpIdOpt
  creatorOpt
}

fragment FundSubFileFields on FundSubFile {
  fileId
  docType
}
