#import "DocumentSignature.fragment"
#import "SignatureTemplate.fragment"
#import "SignatureData.fragment"
#import "SignatureLocation.fragment"

query FundSubSupportingDocSignatureRequestData($fundSubLpId: String!) {
  fundSubSupportingDocSignatureRequestData(fundSubLpId: $fundSubLpId) {
    requestData {
      requestId
      files {
        fileId
        name
        signedRecords {
          fileId
          name
          signers
          certificateFileIdOpt
          isUploaded
        }
      }
      recipients {
        userId
        name
        email
        status
        lastStatusUpdatedAt
        items {
          fileId
          itemId
          status
          signDataSeq {
            uploadedFileOpt {
              fileId
              name
            }
            documentSignatureOpt {
              ...DocumentSignature
            }
          }
          savedSignature {
            ...DocumentSignature
          }
        }
        preparedBlocks {
          fileId
          signatureTemplate {
            ...SignatureTemplate
          }
        }
        hasFailedDocusignRecipientAuthenticationOpt
      }
      enabledQesOpt
      eSignatureProvider
    }
    eSignDisclaimerMessage
    applicableSignatureTypes
  }
}
