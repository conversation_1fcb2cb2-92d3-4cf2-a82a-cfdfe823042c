query AllPastSubscriptionInfoForLpAutoPrefill($lpId: String!) {
  allPastSubscriptionInfoForLpAutoPrefill(lpId: $lpId) {
    currentFundEntityId
    currentFundEntityName
    fundInfoList {
      fundSubId
      fundName
      numOfInvestors
      currencyOpt
      formSimilarity
      demoInfoOpt {
        demoLpId
      }
      computeSimilarityStatus
      similarityStatusUpdatedAt
    }
    investorInfoList {
      lpId
      investmentEntity
      mainLpContactInfo {
        userId
        email
        firstName
        lastName
      }
      collaboratorContactInfos {
        userId
        email
        firstName
        lastName
      }
      status
      commitmentAmount
      lastUpdated
      customId
      tagNames
      conflictLpOpt {
        lpId
        investmentEntity
        mainContactEmail
        similarityScore
      }
      formFillingProgress
    }
  }
}
