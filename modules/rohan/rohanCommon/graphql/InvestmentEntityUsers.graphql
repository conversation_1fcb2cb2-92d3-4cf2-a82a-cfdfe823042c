query InvestmentEntityUsersQuery($investmentEntityId: String!) {
  investmentEntityUsers(investmentEntityId: $investmentEntityId) {
    users {
      userIdInfo {
        userId
        userInfo {
          emailAddressStr
          firstName
          lastName
        }
      }
      userState
      permissionLevel
      inviterIdInfo {
        userId
        userInfo {
          emailAddressStr
          firstName
          lastName
        }
      }
      invitedAt
      lastRemindedAt
      joinedAt
    }
  }
}
