#import "DocumentSignature.fragment"
#import "SignatureTemplate.fragment"
#import "SignatureData.fragment"
#import "SignatureLocation.fragment"
#import "FundSubSignatureConfig.fragment"
#import "FundSubSharedEnvelopeType.fragment"

query FundSubLpNoLoginSignatureData($fundSubLpId: String!, $docTypeOpt: String, $signatureRequestIds: [String!]!) {
  fundSubLpNoLoginSignatureData(fundSubLpId: $fundSubLpId, docTypeOpt: $docTypeOpt, signatureRequestIds: $signatureRequestIds) {
    lpSignatureRequestDataOpt {
      userId
      userName
      entityId
      requester
      requesterName
      requesterEmail
      requestedAt
      requestEntries {
        requestId
        fileId
      }
      files {
        fileId
        name
        signedRecords {
          fileId
          name
          signers
          certificateFileIdOpt
          isUploaded
        }
      }
      currentRecipients {
        userId
        name
        email
        status
        lastStatusUpdatedAt
        items {
          fileId
          itemId
          status
          signDataSeq {
            uploadedFileOpt {
              fileId
              name
            }
          }
          savedSignature {
            ...DocumentSignature
          }
        }
        preparedBlocks {
          fileId
          signatureTemplate {
            ...SignatureTemplate
          }
        }
      }
      signers
      subject
      isComplete
      signerAuthenticationType
      docusignRecipientAuthMethodOpt
      obfuscatedPhoneNumberOpt
      lpFlowType
      finishSigningModalCopy
      eSignatureProvider
      actorHasFailedDocusignAuthentication
      refDocs
      publicMessage
      enabledQesOpt
      signatureConfigOpt {
        ...FundSubSignatureConfig
      }
      isSigningWithSchwabFlow,
      envelopeTypeOpt {
        ...FundSubSharedEnvelopeTypeFields
      }
    }
    isPastSigner
    fundName
  }
}
