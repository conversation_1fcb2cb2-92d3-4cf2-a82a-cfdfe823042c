#import "InvestmentEntityInfo.fragment"
#import "InvestmentEntityFullSubscriptionInfo.fragment"
query InvestmentEntityDashboardWithSubscriptionsQuery {
    investmentEntityDashboardWithSubscriptions {
        dashboardData {
            hasPermissionToCreateNewEntity
            hasDeletedInvestmentEntities
            entities {
                ...InvestmentEntityInfoFields
            }
        }

        notLinkedSubscriptions {
            ...InvestmentEntityFullSubscriptionInfoFields
        }
    }
}
