#import "InvestmentEntityShortAssociatedLinkInfo.fragment"
#import "InvestmentEntityInfo.fragment"
query InvestmentEntityAssociatedLinksQuery($fundSubLpId: String!) {
    associatedLinks(lpId: $fundSubLpId){
        investmentEntityShortInfo {
            ...InvestmentEntityShortInfoField
        }
        investmentEntityFullInfo {
            ...InvestmentEntityInfoFields
        }
        otherInvestedEntities {
            ...InvestmentEntityShortInfoField
        }
    }
}
