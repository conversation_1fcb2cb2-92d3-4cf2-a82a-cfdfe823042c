fragment FundSubPublicModelFields on FundSubPublicModelType {
  fundName
  investorEntity
  supportingContacts {
    orgName
    contacts {
      name
      email
      phone
    }
  }
  demoInfo {
    demoLpId
    demoFundDataFirmIdOpt
    mainDemoFundSubId
    additionalDemoFundSubIds
    additionalDemoLpIds
    demoMarketingDataRoomWorkflowIdOpt
    demoRiaFundGroupIdOpt
  }
  featureSwitch {
    ...FundSubFeatureSwitch
  }
  referenceFiles
  lpGeneralConfig {
    enableImportExportJsonValues
    enableLivePreviewMode
    enableLpSigningInstruction
    lpSigningInstructionContent
    embedInvestorDataOnSubDocConfig {
      placement
    }
  }
  sharedDataRoomLink
  sharedDataRoomLinkDisplayName
  disabledFormComment
  suppressSendingFormCommentDigestEmail
  formCommentDigestEmailExceptionLps
  signatureConfig {
    ...FundSubSignatureConfig
  }
  lpFlowType
  packageType
  dashboardConfig {
    enableAdvancedDashboard
    enableStandardDashboard
  }
  closingConfig {
    isClosed
    updatedBy
    updatedAt
  }
  subscriptionDocsOrder
  dataExtractionConfig {
    isEnabled
    isLiveExtractionEnabled
    isUsingDataExtractDeliveryTable
  }
  inactiveCommentSetting {
    enabled
    days
    useCustomValue
  }
}
