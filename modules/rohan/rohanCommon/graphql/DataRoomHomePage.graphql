query DataRoomHomePage($dataRoomWorkflowId: String!) {
  dataRoomHomePage(dataRoomWorkflowId: $dataRoomWorkflowId) {
    state {
      dataRoomWorkflowId
      published
      sections {
        __typename
        sectionId
        enabled
        ... on DataRoomHomeHeaderSection {
          title {
            ...TextFields
          }
          subtitle {
            ...TextFields
          }
          backgroundImage {
            ...FileIdWithUrlFields
          }
          backgroundColor
          cta {
            ...ButtonFields
          }
          titleBackgroundDisabled
        }
        ... on DataRoomHomeIntroSection {
          title {
            ...TextFields
          }
          subtitle {
            ...TextFields
          }
          mediaFileUrl {
            ...FileIdWithUrlFields
          }
          autoplayVideo
        }
        ... on DataRoomHomeDocumentSection {
          fileIds
          title {
            ...TextFields
          }
        }
        ... on DataRoomHomeQuestionSection {
          title {
            ...TextFields
          }
          questions {
            ...QuestionFields
          }
        }
        ... on DataRoomHomeFooterSection {
          title {
            ...TextFields
          }
          subtitle {
            ...TextFields
          }
          bgColor
          backgroundImage {
            ...FileIdWithUrlFields
          }
          cta {
            ...ButtonFields
          }
        }
      }
      modifiedAt
      modifiedBy
    }
    additional {
      fileMap {
        key
        value {
          name
          userPermission
        }
      }
    }
  }
}

fragment TextFields on TextFieldType {
  text
  color
}

fragment QuestionFields on QuestionFieldType {
  question
  answer
}

fragment FileIdWithUrlFields on FileIdWithUrlType {
  fileIdOpt
  url
}

fragment ButtonFields on ButtonFieldType {
  text
  color
  bgColor
  destination {
    __typename
    ...ButtonDestinationLinkFields
    ...ButtonDestinationEmailFields
  }
}

fragment ButtonDestinationLinkFields on ButtonDestinationLink {
  url
}

fragment ButtonDestinationEmailFields on ButtonDestinationEmail {
  address
}
