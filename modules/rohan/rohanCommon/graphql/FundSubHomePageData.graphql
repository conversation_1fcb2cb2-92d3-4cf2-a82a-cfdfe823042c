#import "InvestmentEntityInfo.fragment"

fragment fundSubEnvironmentNavigationData on FundSubEnvironmentNavigationData {
  environmentId
  environmentWhitelabelData {
    isEnabled
    customValues {
      key
      value
    }
    providerLongLogo
    hideLpProfile
  }
  baseUrlWithPrefixPath
  environmentName
}

query FundSubHomePageData {
  fundSubHomePageData {
    currentUserId
    fundSubsJoinedAsAdmin {
      fundSubId
      investorEntity
      fundName
      fundCreatedAt
      numberOfLp
      approvedAmount
      underReviewAmount
      logoUrl
      logoBgColor
      currency
      customFundId
      isFundClosed
      targetAmount {
        unit
        value
      }
      targetClosingDate
      lastActivityAt
      needCurrencySetup
    }
    fundSubsJoinedAsLp {
      fundSubLpId
      lpFirmName
      lpUser {
        userId
        firstName
        lastName
        email
      }
      collaborators {
        userId
        firstName
        lastName
        email
      }
      investorEntity
      fundName
      fundCreatedAt
      lpStatus
      logoUrl
      logoBgColor
      firmLabel
      orderType
      flowType
      isFundClosed
      commitmentAmount {
        unit
        value
      }
      lastActivityAt
    }
    investmentEntityList {
      ...InvestmentEntityInfoFields
    }
    currentEnvironmentOpt {
      ...fundSubEnvironmentNavigationData
    }
  }
}