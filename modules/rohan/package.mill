package build.modules.rohan

import build_.build.util_.*
import build.modules.{graphql, investorProfile, ontology, dataroom, dataextract}
import build.platform.{stargazerBuildInfo, stargazerCore}

import mill.*
import mill.scalalib.*
import anduin.build.*
import anduin.mill.*

object `package` extends Module {

  object rohan<PERSON><PERSON><PERSON> extends AnduinCrossPlatformModule {

    object jvm extends JvmModule with SharedModule {

      override def moduleDeps = Seq(
        stargazerBuildInfo.jvm,
        stargazerCore.jvm
      )

    }

    object js extends JsModule with SharedModule {

      override def moduleDeps = Seq(stargazerCore.js)

    }

    trait SharedModule extends AnduinGraphQLGenModule {
      override def graphqlSources = Task.Sources(moduleDir / os.up / "graphql")
      override def graphqlOutputPackage = "anduin.rohan.operation"

      override def graphqlBaseTrait = "AnduinQuery"
    }

  }

  object rohan extends AnduinScalaModule {

    override def moduleDeps = super.moduleDeps ++ Seq(
      graphql.jvm,
      investorProfile.investorProfileSubscription,
      ontology.ontologyCore.jvm,
      dataroom.dataroom.jvm,
      dataextract.dataextract.jvm
    )

  }

}
