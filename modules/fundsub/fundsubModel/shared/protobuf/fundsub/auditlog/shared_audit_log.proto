syntax = "proto3";

package anduin.fundsub.auditlog;

import "scalapb/scalapb.proto";

option (scalapb.options) = {single_file: true};

enum AuditLogEventType {
  reserved 42, 110, 10;
  FUND_MEMBER_INVITED = 0;
  FUND_MEMBER_INVITATION_WITHDRAW = 1;
  FUND_MEMBER_REMOVED = 2;

  INVESTOR_INVITED = 3;
  INVESTOR_INVITATION_WITHDRAW = 4;
  OFFLINE_INVESTOR_INVITED = 5;
  RESEND_INVESTOR_INVITATION = 6;
  INVESTOR_REMOVED = 7;

  COLLABORATOR_INVITED = 8;
  COLLABORATOR_REMOVED = 9;
  COLLABORATOR_PROMOTED = 173;

  INVESTOR_JOINED = 11;
  COLLABORATOR_JOINED = 12;
  SUBSCRIPTION_DOCUMENT_EDITED = 13;
  UNSIGNED_SUBSCRIPTION_DOCUMENT_MARKED_AS_APPROVED = 14;
  SUBSCRIPTION_DUPLICATED = 15;
  SUBSCRIPTION_DOCUMENT_FILLED = 16;
  INITIAL_SUBSCRIPTION_DOCUMENT_REVIEW_CANCELED = 17;
  CHANGE_REQUEST_SENT = 18;
  REMINDER_TO_SIGN_EDITED_SUBSCRIPTION_DOCUMENT_SENT = 19;
  SUBSCRIPTION_DOCUMENT_SUBMITTED_FOR_INITIAL_REVIEW = 20;
  SUBSCRIPTION_DOCUMENT_SUBMITTED_FOR_APPROVAL = 21;
  SIGNED_SUBSCRIPTION_DOCUMENT_MARKED_AS_APPROVED = 22;
  SUBSCRIPTION_DOCUMENT_SUBMITTED = 23;
  COUNTERSIGNATURE_REQUEST_SENT = 24;
  COUNTERSIGNATURE_REQUEST_CANCELED = 25;
  REMINDER_TO_COUNTERSIGN_SUBSCRIPTION_DOCUMENT_SENT = 26;
  SIGNATURE_REQUEST_ON_ADDITIONAL_DOCUMENT_SENT = 27;
  SIGNATURE_REQUEST_ON_ADDITIONAL_DOCUMENT_CANCELED = 28;
  REMINDER_TO_COMPLETE_SIGNATURE_REQUEST_ON_ADDITIONAL_DOCUMENT_SENT = 29;
  SIGNATURE_REQUEST_ON_ADDITIONAL_DOCUMENT_COMPLETED = 30;
  SIGNATURE_REQUEST_ON_SUBSCRIPTION_DOCUMENT_MARKED_AS_COMPLETE = 31;
  SUBSCRIPTION_DOCUMENT_COUNTERSIGNED = 32;
  COUNTERSIGNED_SUBSCRIPTION_DOCUMENT_UPLOADED = 33;
  COUNTERSIGNED_SUBSCRIPTION_DOCUMENT_DELETED = 34;
  COUNTERSIGNED_SUBSCRIPTION_DOCUMENT_DISTRIBUTED = 35;
  COUNTERSIGNED_SUBSCRIPTION_DOCUMENT_UPDATED = 36;
  EMAIL_SENT = 37;
  REMINDERS_TO_SUBMIT_SUBSCRIPTION_DOCUMENT_SENT = 38;
  REMINDERS_TO_SUBMIT_ALL_REQUESTED_DOCUMENTS_SENT = 39;
  SUBSCRIPTION_DOCUMENTS_COUNTERSIGNED = 40;
  DOCUMENTS_ADDED_TO_EXECUTED_PACKAGES = 41;
  INVESTOR_DASHBOARD_DATA_EXPORTED = 43;
  INVESTOR_DATA_EXPORTED = 44;
  INVESTOR_DOCUMENTS_DOWNLOADED = 45;
  ADDITIONAL_DOCUMENT_REQUEST_SENT = 46;
  ADDITIONAL_DOCUMENT_REQUEST_DELETED = 47;
  REVIEWER_ASSIGNED = 48;
  REVIEWER_UNASSIGNED = 49;
  REMINDER_TO_SUBMIT_SUBSCRIPTION_DOCUMENT_SENT = 50;
  REFERENCE_DOCUMENT_UPLOADED = 51;
  SIGNED_COPIES_SENT = 52;
  NEW_COMMENT_NOTIFICATION_SENT = 53;
  NEW_INVESTOR_REPORT_SENT = 54;
  NEW_ADDITIONAL_DOCUMENT_UPLOAD_REPORT_SENT = 55;
  SIGNATURE_REQUEST_OF_SUBSCRIPTION_DOCUMENT_SENT = 56;
  REMINDER_TO_COMPLETE_SIGNATURE_REQUEST_ON_SUBSCRIPTION_DOCUMENT_SENT = 57;
  SIGNATURE_REQUEST_REASSIGNED = 58;
  OFFLINE_SUBSCRIPTION_ADDED = 59;
  NULL = 60;
  SIGNED_EXECUTED_DOCUMENT = 61;
  INVITATION_EMAIL_BOUNCED = 62;
  SUBSCRIPTION_DOCUMENT_SIGNED = 63;
  SIGNATURE_REQUEST_ON_SUBSCRIPTION_DOCUMENT_CANCELED = 64;
  FUND_MEMBER_JOINED = 65;
  SIGNATURE_REQUEST_ON_TAX_FORM_CANCELED = 66;
  REMINDER_TO_COMPLETE_SIGNATURE_REQUEST_ON_TAX_FORM_SENT = 67;
  COUNTERSIGNATURE_REQUEST_COMPLETED = 68;
  SIGNATURE_REQUEST_ON_TAX_FORM_COMPLETED = 69;
  SIGNATURE_REQUEST_ON_TAX_FORM_SENT = 70;
  REMINDER_TO_COMPLETE_SIGNATURE_REQUEST_ON_SIDE_LETTER_SENT = 174;

  // Admin Group
  FUND_MEMBER_MOVED_TO_ANOTHER_GROUP = 71;
  FUND_MEMBERS_MOVED_TO_ANOTHER_GROUP = 72;
  FUND_MEMBER_GROUP_CREATED = 73;
  FUND_MEMBER_GROUP_RENAMED = 74;
  GROUP_PERMISSION_UPDATED = 75;
  GROUP_VISIBILITY_UPDATED = 76;
  GROUP_DELETED = 77;

  // Dashboard
  GROUP_ACCESS_TO_DASHBOARD_UPDATED = 78;
  DASHBOARD_RENAMED = 79;
  NEW_DASHBOARD_CREATED = 80;
  DASHBOARD_LAYOUT_UPDATED = 81;
  DASHBOARD_DELETED = 82;

  // Investor Group
  INVESTOR_GROUP_CREATED = 83;
  INVESTOR_GROUP_RENAMED = 84;
  INVESTOR_GROUP_DELETED = 85;
  INVESTOR_ASSIGNED_TO_AN_INVESTOR_GROUP = 86;
  INVESTOR_MOVED_TO_ANOTHER_INVESTOR_GROUP = 87;
  INVESTOR_UNASSIGNED_FROM_AN_INVESTOR_GROUP = 88;
  INVESTOR_GROUP_ACCESSIBILITY_GRANTED = 89;
  INVESTOR_GROUP_ACCESSIBILITY_WITHDRAWN = 90;

  // Supporting doc review
  SUPPORTING_DOC_APPROVED = 91;
  SUPPORTING_DOC_SUBMITTED = 92;
  SUPPORTING_DOC_CHANGE_REQUEST_SENT = 93;
  CANCEL_CHANGE_REQUEST_ON_SUPPORTING_DOC = 94;
  SUPPORTING_DOC_REVIEW_WORKFLOW_UPDATED = 95;
  SUPPORTING_DOC_REVIEW_WORKFLOW_DISABLED = 96;
  SUPPORTING_DOC_REVIEW_WORKFLOW_ENABLED = 97;

  // Subscription doc review
  SIGNED_SUBSCRIPTION_DOCUMENT_REVIEW_WORKFLOW_UPDATED = 98;
  SIGNED_SUBSCRIPTION_DOCUMENT_REVIEW_WORKFLOW_DISABLED = 99;
  SIGNED_SUBSCRIPTION_DOCUMENT_REVIEW_WORKFLOW_ENABLED = 100;

  UNSIGNED_SUBSCRIPTION_DOCUMENT_REVIEW_WORKFLOW_UPDATED = 101;
  UNSIGNED_SUBSCRIPTION_DOCUMENT_REVIEW_WORKFLOW_DISABLED = 102;
  UNSIGNED_SUBSCRIPTION_DOCUMENT_REVIEW_WORKFLOW_ENABLED = 103;
  SUBSCRIPTION_DOCUMENT_REVIEW_SETTINGS_UPDATED = 189;

  // Investment Entity
  INVESTMENT_ENTITY_LINKED = 104;
  INVESTMENT_ENTITY_REMOVED = 105;
  INVESTMENT_ENTITY_REPLACED = 106;

  // Amendment
  AMENDMENT_ADDED = 107;
  AMENDMENT_EDITED = 108;
  AMENDMENT_REMOVED = 109;

  CUSTOM_COLUMN_VALUE_UPDATED = 111;

  // Custom IDs
  CUSTOM_LP_ID_UPDATED = 112;
  CUSTOM_FUND_ID_UPDATED = 113;

  // comment
  NEW_INTERNAL_COMMENT = 114;
  NEW_SHARED_COMMENT = 115;
  INTERNAL_COMMENT_EDITED = 116;
  SHARED_COMMENT_EDITED = 117;
  INTERNAL_COMMENT_DELETED = 118;
  SHARED_COMMENT_DELETED = 119;
  INTERNAL_COMMENT_THREAD_DELETED = 120;
  SHARED_COMMENT_THREAD_DELETED = 121;
  INTERNAL_COMMENT_THREAD_RESOLVED = 122;
  SHARED_COMMENT_THREAD_RESOLVED = 123;
  INTERNAL_COMMENT_THREAD_REOPENED = 124;
  SHARED_COMMENT_THREAD_REOPENED = 125;

  UNSIGNED_SUBSCRIPTION_DOCUMENT_REVIEWED = 126;
  SIGNED_SUBSCRIPTION_DOCUMENT_REVIEWED = 127;

  COMMENT_THREAD_ASSIGNED = 128;
  COMMENT_THREAD_REASSIGNED = 129;
  COMMENT_THREAD_UNASSIGNED = 130;
  NEW_COMMENT_ASSIGNED_NOTIFICATION_SENT = 131;

  COMMENT_EXPORT = 132;

  ADDITIONAL_DOCUMENT_MARKED_AS_PROVIDED = 133;
  ADDITIONAL_DOCUMENT_UNMARKED_AS_PROVIDED = 134;
  ADDITIONAL_DOCUMENT_MARK_AS_NOT_APPLICABLE = 135;
  ADDITIONAL_DOCUMENT_MARK_AS_APPLICABLE = 136;

  // Data extract
  DATA_EXTRACTION_STARTED = 140;
  DATA_EXTRACTION_READY_FOR_REVIEW = 141;
  DATA_EXTRACTION_MARKED_AS_COMPLETE = 142;
  DATA_EXTRACTION_EXTRACTED_DATA_EDITED = 145;

  LP_RESOLVING_COMMENT_ENABLED = 143;
  LP_RESOLVING_COMMENT_DISABLED = 144;

  // Close
  CLOSE_CREATED = 146;
  CLOSE_UPDATED = 147;
  CLOSE_DELETED = 148;
  INVESTOR_ASSIGNED_TO_NEW_CLOSE = 149;

  // Self-service export
  EXPORT_TEMPLATE_CREATED = 150;
  EXPORT_TEMPLATE_RENAMED = 151;
  EXPORT_TEMPLATE_UPDATED = 152;
  EXPORT_TEMPLATE_DELETED = 153;

  // Email template
  EMAIL_TEMPLATE_CREATED = 154;
  EMAIL_TEMPLATE_UPDATED = 155;
  EMAIL_TEMPLATE_DELETED = 156;
  EMAIL_TEMPLATE_RENAMED = 157;
  EMAIL_TEMPLATE_SET_AS_DEFAULT = 158;

  SIGNATURE_DATE_FORMAT_UPDATED = 159;
  OFFLINE_INVESTOR_JOINED = 160;
  SUBSCRIPTION_ACCESSED = 161;
  SUBSCRIPTION_WITHDRAWN = 162;
  SUBSCRIPTION_RESTORED = 163;
  SUBSCRIPTION_MARKED_AS_COMPLETE = 164;

  // Ria
  ADVISOR_ENTITY_JOINED = 165;
  ADVISOR_ENTITY_NAME_UPDATED = 166;
  ADVISOR_INVITED = 167;
  ADVISOR_JOINED = 168;
  RESEND_ADVISOR_INVITATION = 169;
  REVOKE_ADVISOR_INVITATION = 170;
  ADVISOR_ENTITY_DISABLED = 171;
  ADVISOR_ENTITY_ENABLED = 172;
  SUBSCRIPTION_SYNCED = 175;
  SUBSCRIPTION_UNLINKED = 176;
  ADVISOR_ENTITY_LEFT = 187;

  // Side letter
  SIDE_LETTER_VERSION_CREATED = 177;
  SIDE_LETTER_FILES_UPLOADED = 178;
  SIDE_LETTER_FILES_REMOVED = 179;
  SIDE_LETTER_MARKED_AS_AGREED = 180;
  SIDE_LETTER_MARKED_AS_COMPLETED = 181;

  // Custom data column
  CUSTOM_COLUMN_CREATED = 182;
  CUSTOM_COLUMN_DELETED = 183;
  CUSTOM_COLUMN_RENAMED = 184;

  TAG_LIST_UPDATED = 185;
  ALLOW_POST_SIGNING_UPDATES_WITHOUT_RE_SIGNATURE_UPDATED = 186;
  PROFILE_FILLED = 188;
}

enum AuditLogActorType {
  FundSide = 0;
  InvestorSide = 1;
  System = 2;
  Ria = 3;
}
