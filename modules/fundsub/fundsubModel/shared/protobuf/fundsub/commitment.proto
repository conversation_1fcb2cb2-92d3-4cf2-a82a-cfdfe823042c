syntax = "proto3";

package anduin.protobuf.fundsub.commitment;

import "scalapb/scalapb.proto";
import "external/squants.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.commitment"
  single_file: true
};

message LpCommitment {
  // Initial expected commitment from LP
  external.squants.MoneyMessage expected_commitment = 1;
  // Actual submitted commitment from LP
  external.squants.MoneyMessage submitted_commitment = 2;
  // Final accepted commitment by FM
  external.squants.MoneyMessage accepted_commitment = 3;
}