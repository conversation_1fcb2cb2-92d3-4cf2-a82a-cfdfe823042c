syntax = "proto3";

package anduin.protobuf.fundsub.whitelabel;

import "scalapb/scalapb.proto";
import "document.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.whitelabel"
  single_file: true
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.id.entity.EntityId"
  import: "anduin.id.environment.EnvironmentWhitelabelId"
};

message FundSubWhiteLabelModel {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
  bool is_enabled = 2;
  DocumentStorageIdMessage logo_storage_id = 3;
  DocumentStorageIdMessage long_logo_storage_id = 4;
  map<string, string> custom_values = 5;

  bool enable_provider_branding = 6;
  string provider_entity = 7 [(scalapb.field).type = "Option[EntityId]"];
  DocumentStorageIdMessage provider_logo = 8;
  DocumentStorageIdMessage provider_long_logo = 9;
}

message FundSubEnvironmentWhitelabel {
  string id = 1 [(scalapb.field).type = "EnvironmentWhitelabelId"];
  map<string, string> custom_values = 2;
}

message RecordTypeUnion {
  FundSubWhiteLabelModel _FundSubWhiteLabelModel = 1;
  FundSubEnvironmentWhitelabel _FundSubEnvironmentWhitelabel = 2;
}
