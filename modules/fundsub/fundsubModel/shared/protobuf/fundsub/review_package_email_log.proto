syntax = "proto3";

package anduin.protobuf.fundsub.reviewpackage.emaillog;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.reviewpackage.emaillog"
  single_file: true
  import: "anduin.id.fundsub.ReviewPackageEmailLogId"
  import: "anduin.model.common.user.UserId"
};

message ReviewPackageEmailLog {
  string id = 1 [(scalapb.field).type = "ReviewPackageEmailLogId"];
  map<string, InstantMessage> lastDocReadyEmailSentTimeLog = 2 [(scalapb.field).key_type = "UserId", (scalapb.field).value_type = "java.time.Instant"];
}

message RecordTypeUnion {
    ReviewPackageEmailLog _ReviewPackageEmailLog = 1;
}