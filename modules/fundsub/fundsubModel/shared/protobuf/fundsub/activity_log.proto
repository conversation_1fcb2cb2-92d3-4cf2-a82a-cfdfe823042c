syntax = "proto3";

package anduin.protobuf.activitylog;

import "date_time.proto";
import "fundsub/admin_activity_log.proto";
import "fundsub/lp_activity_log_models.proto";
import "fundsub/status/lp_status_change.proto";
import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.activitylog"
  single_file: true
  import: "anduin.model.common.user.UserId"
  import: "anduin.model.id.ActivityLogId"
  import: "java.time.Instant"
};

message ActivityDetail {
  oneof activity {
    anduin.protobuf.activitylog.fundsub.admin.FundAdminActivity fund_admin_activity = 1;
    anduin.protobuf.activitylog.fundsub.status.LpStatusChangeActivity lp_status_change_activity = 2;
  }
}

message GeneralActivity {
  oneof value {
    anduin.protobuf.fundsub.activitylog.lp.LpActivityInfo lp_activity = 1;
    anduin.protobuf.activitylog.fundsub.admin.FundAdminActivity fa_activity = 2;
  }
}

message ActivityModel {
  string activityLogId = 1 [(scalapb.field).type = "ActivityLogId"];
  int32 itemOrder = 2;
  string actorOpt = 3 [(scalapb.field).type = "Option[UserId]"];
  repeated string seenBy = 4 [(scalapb.field).type = "UserId"];
  InstantMessage at = 5 [(scalapb.field).type = "Instant"];
  ActivityDetail detail = 6;
}

message RecordTypeUnion {
  ActivityModel _ActivityModel = 1;
}
