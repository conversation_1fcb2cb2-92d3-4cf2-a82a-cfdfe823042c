syntax = "proto3";

package anduin.protobuf.fundsub.models.user;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.models.user"
  single_file: true
  import: "anduin.id.fundsub.*"
};

message UserFundSubPublicModel {
  string user_id = 1 [(scalapb.field).type = "UserFundSubPublicId"];
  repeated string fund_sub_ids = 2 [(scalapb.field).type = "FundSubId", (scalapb.field).collection_type = "Set"];
}

message UserFundSubAdminModel {
  string user_id = 1 [(scalapb.field).type = "UserFundSubAdminId"];
  repeated string fund_sub_ids = 2 [(scalapb.field).type = "FundSubId", (scalapb.field).collection_type = "Set"];
}

message UserFundSubLpModel {
  string user_id = 1 [(scalapb.field).type = "UserFundSubLpId"];
  repeated string fund_sub_lp_ids = 2 [(scalapb.field).type = "FundSubLpId", (scalapb.field).collection_type = "Set"];
}

message RecordTypeUnion {
  UserFundSubPublicModel _UserFundSubPublicModel = 1;
  UserFundSubAdminModel _UserFundSubAdminModel = 2;
  UserFundSubLpModel _UserFundSubLpModel = 3;
}