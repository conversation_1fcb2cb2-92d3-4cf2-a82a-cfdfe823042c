syntax = "proto3";

package anduin.protobuf.fundsub.user.usertracking;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.usertracking"
  single_file: true

  import: "anduin.id.user.UserTrackingId"
  import: "anduin.id.fundsub.FundSubDashboardId"
  import: "anduin.id.fundsub.FundSubId"
};

message FundSubUserTrackingModel {
  reserved 2, 8;
  string user_tracking_id = 1 [(scalapb.field).type = "UserTrackingId"];
  bool seen_lp_onboard_guide_tour = 3;
  bool seen_attach_comment_guide = 4;
  bool seen_auto_selected_comments_banner = 5;
  map<string, string> fund_sub_recent_dashboard_id = 6 [(scalapb.field).key_type = "FundSubId", (scalapb.field).value_type = "FundSubDashboardId"];
  bool seen_batch_action_guide = 7;
  bool seen_filter_onboard_guide_tour = 9;
  bool seen_batch_approve_per_lp_guide_tour = 10;
  bool seen_batch_approve_cross_lp_guide_tour = 11;
  bool seen_self_service_export_guide_tour = 12;
  bool seen_manage_self_service_export_guide_tour = 13;
  bool clicked_on_manage_self_service_export_button = 14;
  bool seen_multiple_email_template_guide_tour = 15;
  bool closed_complete_data_extraction_guideline_callout = 16;
  bool seen_api_export_template_guide_tour = 17;
}

message RecordTypeUnion {
  FundSubUserTrackingModel _FundSubUserTrackingModel = 1;
}