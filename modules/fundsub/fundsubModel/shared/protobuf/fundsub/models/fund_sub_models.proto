syntax = "proto3";

package anduin.protobuf.fundsub.models;

import "anduin/form/form_version_id.proto";
import "date_time.proto";
import "external/squants.proto";
import "fundsub/common_models.proto";
import "fundsub/email/fund_sub_email.proto";
import "fundsub/form_data.proto";
import "fundsub/lp_flow_type.proto";
import "fundsub/models.proto";
import "google/protobuf/wrappers.proto";
import "scalapb/scalapb.proto";
import "signature/e_signature.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.models"
  single_file: true
  import: "anduin.form.id.FormVersionIdTypeMapper.given"
  import: "anduin.id.entity.EntityId"
  import: "anduin.id.form.FormVersionId"
  import: "anduin.id.form.FormTemplateMappingVersionId"
  import: "anduin.id.fundsub.*"
  import: "anduin.model.common.user.UserId"
  import: "anduin.model.id.*"
  import: "anduin.id.linkrestriction.LinkRestrictionListId"
  import: "anduin.id.authwhitelabel.AuthenticationWhitelabelId"
  import: "java.time.ZonedDateTime"
  import: "anduin.id.fundsub.auditlog.FundSubAuditLogId"
  import: "anduin.id.environment.EnvironmentId"
  import: "anduin.id.dataextract.DataExtractProjectId"
  import: "anduin.id.signature.SignatureModuleId"
};

message InactiveNotificationSetting {
  bool enabled = 1;
  int32 days = 2;
}

message TaxFormGroup {
  reserved 2;

  // Old form
  repeated string form_ids = 1 [(scalapb.field).type = "DynamicFormId"];
  // New form
  repeated anduin.form.FormVersionIdMessage form_version_ids = 3 [(scalapb.field).type = "FormVersionId"];
}

message SupportingDocInfo {
  string name = 1;
  string description = 2;
}

message FundSubFormVersion {
  string id = 1 [(scalapb.field).type = "FormVersionId"];
  InstantMessage created_at = 2 [(scalapb.field).type = "java.time.Instant"];
  InstantMessage updated_at = 5 [(scalapb.field).type = "java.time.Instant"];
  string name = 3;
  string description = 4;
  string editor = 6 [(scalapb.field).type = "Option[UserId]"];
  repeated string import_mapping_ids = 7 [(scalapb.field).type = "FormTemplateMappingVersionId"];
  repeated string export_mapping_ids = 8 [(scalapb.field).type = "FormTemplateMappingVersionId"];
}

// FIXME: Most of these settings should stay in JVM
message FundSubAdminRestrictedModel {
  reserved 2, 4, 6, 11, 13 to 17, 26, 28, 29, 31, 38, 44, 46;

  string fund_sub_admin_restricted_id = 1 [(scalapb.field).type = "FundSubAdminRestrictedId"];
  repeated string removed_lps = 3 [(scalapb.field).type = "FundSubLpId"];

  // Old form
  repeated string form_ids = 5 [(scalapb.field).type = "DynamicFormId"];
  map<string, FormExportSettings> export_settings = 7 [(scalapb.field).key_type = "DynamicFormId"];
  repeated string additional_tax_forms = 24 [(scalapb.field).type = "DynamicFormId"];

  // New form
  repeated FundSubFormVersion form_versions = 34;
  map<string, FormExportSettings> form_version_export_settings = 27 [(scalapb.field).key_type = "FormVersionId"];
  repeated InvestmentFundModel investment_funds = 45;
  repeated anduin.form.FormVersionIdMessage additional_tax_form_versions = 30 [(scalapb.field).type = "FormVersionId"];
  map<string, TaxFormGroup> tax_form_groups = 23;

  // The map of original form files and form embedded files to the their shared clone in fundsub common folder
  map<string, string> shared_form_files = 8 [(scalapb.field) = {
    key_type: "FileId"
    value_type: "FileId"
  }];
  repeated EmailTemplateMessage email_templates = 9;
  repeated string lp_ids = 10 [(scalapb.field).type = "FundSubLpId"];
  repeated FundSubEvent disabled_emails = 12 [(scalapb.field).collection_type = "Set"];

  anduin.fundsub.email.FundSubCustomSenderEmailAddress customSenderEmailAddress = 25;
  anduin.fundsub.email.FundSubCustomEmailCcConfig custom_email_cc_config = 35;
  anduin.fundsub.email.FundSubSenderCcConfig sender_cc_config = 40;

  anduin.fundsub.email.FundSubCustomEmailBccConfig custom_email_bcc_config = 51;
  anduin.fundsub.email.FundSubSenderBccConfig sender_bcc_config = 52;

  FundSubProtectedLinkModel protect_link = 18;
  string activity_log_id = 19 [(scalapb.field).type = "Option[ActivityLogId]"];
  FundSubExportTemplateInfo export_template_info = 20;
  anduin.fundsub.email.FundSubCustomEmailReply custom_email_reply = 21;
  anduin.fundsub.email.FundSubEmailReplyToSenderConfig email_reply_to_sender_config = 36;
  InactiveNotificationSetting inactive_notification_setting = 22;
  CustomFundIdSetting custom_fund_id_setting = 41;
  repeated SupportingDocInfo supporting_docs = 32;

  string dashboard_id = 33 [(scalapb.field).type = "Option[FundSubDashboardId]"];
  string default_close_id = 47 [(scalapb.field).type = "FundSubCloseId"];
  string authentication_whitelabel_id = 37 [(scalapb.field).type = "Option[AuthenticationWhitelabelId]"];
  string audit_log_id = 39 [(scalapb.field).type = "FundSubAuditLogId"];

  // This value is only computed once when the fund is being archived to help us ingest into our data analytics pipeline
  double total_commitment_amount = 42;
  FlowType flow_type = 43;

  map<string, string> metadata = 48;

  anduin.fundsub.email.FundSubCustomSmtpServerConfig custom_smtp_server_config = 49;

  // This module is used to store batch countersign request via Docusign
  string batch_countersign_module_id = 50 [(scalapb.field).type = "Option[SignatureModuleId]"];
}

message InvestmentFundModel {
  string fund_id = 1 [(scalapb.field).type = "InvestmentFundId"];
  // Commitment amount field in form, this needs to be unique for all sub-funds in one fundsub
  string amount_field_id = 2;
  external.squants.CurrencyMessage currency = 3;
  // sub-fund name, to be displayed in Fundsub Reporting
  string name = 4;
  // unique ID synced from the form to support update some sub-fund's data
  string form_config_unique_id = 5;
  string custom_id = 11;
  // Commitment amount fields for GP countersign to pre-fill
  message InvestmentFundSignatureField {
    string id = 1;
    string prefix = 2;
  }
  repeated InvestmentFundSignatureField signature_fields = 6;

  string amount_column_name = 7;
  string estimated_amount_column_name = 8;
  string submitted_amount_column_name = 9;
  string accepted_amount_column_name = 10;
}

message InactiveCommentSetting {
  bool enabled = 1;
  int32 days = 2;
  bool use_custom_value = 3;
}

// Data accessible to all participants of the fund sub, including both Admins and LPs.
// E.g., Fund name, expected closing date...
message FundSubPublicModel {
  option (scalapb.message).extends = "anduin.fundsub.model.FundSubPublicModelTrait";

  reserved 9, 11, 14, 18 to 20, 37, 38, 40;
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];

  string fund_name = 2;
  FundSubStatus status = 3;
  string creator = 30 [(scalapb.field).type = "Option[UserId]"];
  string investor_entity = 4 [(scalapb.field).type = "EntityId"];
  InstantMessage start_date = 16 [(scalapb.field).type = "java.time.Instant"];
  LocalDateMessage closing_date = 17 [(scalapb.field).type = "java.time.LocalDate"];

  enum FundType {
    Internal = 0; // for our internal testing, demo, debug, etc
    Production = 1; // actual customer funds that will be included in billings and analytics
    External = 2; // for customer's testing and validation
  }
  FundType fund_type = 31;

  enum PackageType {
    Enterprise = 0;
    Standard = 1;
  }
  PackageType package_type = 35;

  // Customized data
  repeated FundSubSupportingContacts supporting_contacts = 5;
  repeated string reference_files = 6 [(scalapb.field).type = "FileId"];
  external.squants.MoneyMessage target_capital = 7;
  FeatureSwitch feature_switch = 8;
  DemoInfo demo_info = 10;
  FundSubLpGeneralConfig lp_general_config = 12;
  string shared_data_room_link = 13;
  string shared_data_room_link_display_name = 43;
  bool disabled_form_comment = 15; // default = false, meaning the form comment is turned on by default.
  FundSubSignatureConfig signature_config = 21;
  LpFlowType lp_flow_type = 22;
  // Form commenting config
  bool suppress_sending_form_comment_digest_email = 23; // default = false, meaning sending form comment digest email
  repeated string form_comment_digest_email_exception_lps = 29 [(scalapb.field).type = "FundSubLpId"];

  DashboardConfig dashboard_config = 24;
  RestrictionConfig restriction_config = 25;
  google.protobuf.StringValue pdf_text_color_opt = 26;
  FundSubClosingConfig closing_config = 27;
  AuthenticationConfig authentication_config = 28;
  string environment_id_opt = 32 [(scalapb.field).type = "Option[EnvironmentId]"];
  repeated FundSubAdminFundContact fund_admin_contacts = 33;
  repeated FundSubExternalFundContact fund_external_contacts = 34;
  FlowType flow_type = 36;
  repeated string subscription_docs_order = 39;
  FundSubDataExtractionConfig data_extraction_config = 41;
  InstantMessage last_activity_at = 42 [(scalapb.field).type = "java.time.Instant"];
  InactiveCommentSetting inactive_comment_setting = 44;
  InstantMessage last_dashboard_mutation_at = 45 [(scalapb.field).type = "java.time.Instant"];
}

message AuthenticationConfig {
  bool enforce_password_lp = 1;
  bool enforce_mfa_gp = 2;
  bool enforce_mfa_lp = 3;
  bool enforce_short_session_gp = 4;
  bool enforce_short_session_lp = 5;
}

message FundSubClosingConfig {
  bool is_closed = 1;
  string updated_by = 2 [(scalapb.field).type = "UserId"];
  InstantMessage updated_at = 3 [(scalapb.field).type = "java.time.Instant"];
}

message DashboardConfig {
  bool enableAdvancedDashboard = 1;
  repeated anduin.fundsub.model.FormFieldConfig form_fields_configs = 2;
  bool sync_data_enabled = 3;
  bool enableStandardDashboard = 4;
}

message RestrictionConfig {
  string link_restriction_list_id = 1 [(scalapb.field).type = "Option[LinkRestrictionListId]"];
}

message FundSubAdminInfo {
  reserved 3;
  string fund_sub_admin_info_id = 1 [(scalapb.field).type = "FundSubAdminInfoId"];
  FormFillData testFormData = 2;
}

message FundSubCloseModel {
  reserved 3, 4;
  string fund_sub_close_id = 1 [(scalapb.field).type = "FundSubCloseId"];
  string name = 2;
  ZonedDateTimeMessage closing_date_zoned = 5 [(scalapb.field).type = "ZonedDateTime"];
  string custom_close_id = 6;
  InstantMessage created_at = 7 [(scalapb.field).type = "java.time.Instant"];
}

message FundSubSignatureConfig {
  reserved 1, 3, 4, 5;
  enum SignerAuthenticationMethod {
    reserved 2;
    None = 0;
    EmailOtp = 1;
  }
  SignerAuthenticationMethod signer_authentication_method = 2;
  AnduinSignSignatureConfig anduin_sign_signature_config = 6 [(scalapb.field).no_box = true];
  DocusignSignatureConfig docusign_signature_config = 7 [(scalapb.field).no_box = true];
  // If this is None, use the format configured in form or the default one: MMM d, yyyy
  optional string date_format = 8;
  bool enable_one_envelope_experience = 9;
}

message AnduinSignSignatureConfig {
  google.protobuf.StringValue e_sign_disclaimer_message = 1;
  repeated anduin.protobuf.signature.SignatureTypeMessage applicable_signature_types = 2 [(scalapb.field).collection_type = "Set"];
  bool enabled_finish_signing_2fa = 3;
  bool enabled_electronic_seal = 4;
  bool is_enabled = 5;
}

message DocusignSignatureConfig {
  bool is_enabled = 1;
  bool enabled_qes = 2;
  bool enabled_recipient_authentication = 3;
  optional string brand_id_opt = 4;
  bool enforce_id_verification = 5;
}

message FundSubDataExtractionConfig {
  bool is_enabled = 1;
  optional string data_extract_project_id = 2 [(scalapb.field).type = "DataExtractProjectId"];
  bool is_live_extraction_enabled = 3;
  bool is_test_profile_enabled = 4;

  enum DataExtractDeliveryType {
    Form = 0;
    Table = 1;
  }
  DataExtractDeliveryType delivery_type = 5;
}

message FundSubReportingModel {
  string fund_sub_reporting_id = 1 [(scalapb.field).type = "FundSubReportingId"];
  external.squants.CurrencyMessage main_currency = 2;

  message ExchangeRateEntry {
    external.squants.CurrencyMessage currency = 1;
    double rate = 2;
  }
  repeated ExchangeRateEntry exchange_rates = 3;

  repeated string excluded_investment_funds = 4 [
    (scalapb.field).collection_type = "Set",
    (scalapb.field).type = "InvestmentFundId"
  ];
}

message RecordTypeUnion {
  FundSubAdminRestrictedModel _FundSubAdminRestrictedModel = 1;
  FundSubPublicModel _FundSubPublicModel = 2;
  FundSubAdminInfo _FundSubAdminInfo = 3;
  FundSubCloseModel _FundSubCloseModel = 4;
  FundSubReportingModel _FundSubReportingModel = 5;
}
