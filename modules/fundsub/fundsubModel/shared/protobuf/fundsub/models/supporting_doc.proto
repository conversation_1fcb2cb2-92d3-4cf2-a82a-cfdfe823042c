syntax = "proto3";

package anduin.protobuf.fundsub.models.supdoc;

import "scalapb/scalapb.proto";

import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.models.supdoc"
  single_file: true
  import: "anduin.id.fundsub.FundSubSupportingDocId"
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.model.id.FileId"
  import: "anduin.id.fundsub.FundSubLpFormIdTrait"
  import: "anduin.id.signature.SignatureRequestId"
};

enum SupportingDocType {
  AdditionalTaxForm = 0;
  FormRequiredDoc = 1;
  LpAdditionalDoc = 2;
  AdminRequestedDoc = 3;
}

message FundSubSupportingDocInfo {
  reserved 4, 16;

  string id = 1 [(scalapb.field).type = "FundSubSupportingDocId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"];
  SupportingDocType docType = 11;
  // either old form or new form, for indexing only
  string formId = 9;
  string name = 8;
  string instruction = 14;
  bool isRemoved = 3;
  bool isHidden = 13;
  bool markedAsNotApplicable = 12;
  // either old or new form
  string lp_form_id_opt = 10 [(scalapb.field).type = "Option[FundSubLpFormIdTrait]"];
  repeated string files = 5 [(scalapb.field).type = "FileId"];

  // Store certificateFiles fot the latest signature requests
  // also use this to indicate that this doc was e-signed.
  // Should migrate to store this as FundSubFile together with the signed file
  // For now we'll have to remember to manage this field in pair with files filed.
  repeated string certificateFiles = 19 [(scalapb.field).type = "FileId"];
  bool submitted = 6;
  InstantMessage createdAt = 7 [(scalapb.field).type = "java.time.Instant"];
  InstantMessage lastUpdatedAt = 15 [(scalapb.field).type = "java.time.Instant"];

  string pending_signature_request_id_opt = 17 [(scalapb.field).type = "Option[SignatureRequestId]"];

  // keep past request ids for logging purpose
  repeated string  past_signature_request_ids = 18 [(scalapb.field).type = "SignatureRequestId"];
}

message RecordTypeUnion {
  FundSubSupportingDocInfo _FundSubSupportingDocInfo = 1;
}