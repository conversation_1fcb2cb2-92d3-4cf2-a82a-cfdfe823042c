syntax = "proto3";

package anduin.protobuf.dashboard;

import "fundsub/dashboard/filter_cell.proto";
import "scalapb/scalapb.proto";

option (scalapb.options) = {
    package_name: "anduin.protobuf.dashboard"
    import: "anduin.model.common.user.UserId"
    import: "anduin.id.fundsub.FundSubDashboardId"
    import: "anduin.id.fundsub.dashboard.FilterPresetId"
    single_file: true
};

message FilterPresetInfoModel {
    FilterPresetModel current_preset_model = 1;
    repeated DefinedFilterPresetModel filter_presets = 2;
    string user_id = 3 [(scalapb.field).type = "UserId"];
    string view_id = 4 [(scalapb.field).type = "FundSubDashboardId"];
}

message FilterPresetModel {
    oneof sealed_value {
        UnsavedFilterPresetModel unsaved_filter_preset = 1;
        DefinedFilterPresetModel defined_filter_preset = 2;
    }
}

message UnsavedFilterPresetModel {
    repeated CellFilterModel filters = 1;
}

message DefinedFilterPresetModel {
    string id = 1 [(scalapb.field).type = "FilterPresetId"];
    string name = 2;
    repeated CellFilterModel filters = 3;
}

message RecordTypeUnion {
    FilterPresetInfoModel _FilterPresetInfoModel = 1;
}