syntax = "proto3";

package anduin.protobuf.dashboard;

import "fundsub/custom_data_type_filter.proto";
import "fundsub/form_data_filter.proto";
import "fundsub/lp_info.proto";
import "fundsub/models.proto";
import "scalapb/scalapb.proto";
import "fundsub/fund_sub_data_extract.proto";
import "anduin/amlcheck/shared_aml_check.proto";

option (scalapb.options) = {
    package_name: "anduin.protobuf.dashboard"
    single_file: true
    import: "anduin.id.fundsub.FundSubCloseId"
    import: "anduin.id.fundsub.FundSubLpTagId"
    import: "anduin.id.fundsub.FundSubAdvisorTagId"
    import: "anduin.id.fundsub.group.FundSubInvestorGroupId"
    import: "anduin.id.fundsub.ria.FundSubRiaGroupId"
};

message CellFilterModel {
    string column_id = 1;
    oneof cell_type {
        ContactFilterModel contact_filter = 2;
        DocumentRequestFilterModel document_request_filter = 3;
        CloseFilterModel close_filter = 4;
        SubscriptionDocStatusFilterModel subscription_doc_status_filter = 5;
        TagFilterModel tag_filter = 6;
        CustomDataFilterModel custom_data_filter = 7;
        FormFieldFilterModel form_field_filter = 8;
        DataExtractRequestStatusFilterModel data_extract_request_status_filter = 9;
        AmlCheckFilterModel aml_check_filter = 10;
        AdvisorGroupFilterModel advisor_group_filter = 11;
        InvestorGroupFilterModel investor_group_filter = 12;
        AdvisorTagFilterModel advisor_tag_filter = 13;
    }
}

message ContactFilterModel {
    repeated LpOrderType selected_order_types = 1;
    bool filter_inactive_orders = 2;
}

message DocumentRequestFilterModel {
    repeated flow.fundsub.admin.lpdashboard.LpDocRequestStatus selected_doc_request_status = 1;
}

message CloseFilterModel {
    repeated string selected_close_ids = 1 [(scalapb.field).type = "FundSubCloseId"];
}

message SubscriptionDocStatusFilterModel {
    repeated flow.fundsub.admin.lpdashboard.LpStatus selected_statuses = 1;
    int32 form_progress_min = 2;
    int32 form_progress_max = 3;
}

message TagFilterModel {
    repeated string selected_tags = 1 [(scalapb.field).type = "FundSubLpTagId"];
}

message CustomDataFilterModel {
    string column_id = 1;
    CustomDataFilterValue custom_data_filter_value = 2;
}

message FormFieldFilterModel {
    string column_id = 1;
    FormFieldFilterValue form_field_filter_value = 2;
}

message DataExtractRequestStatusFilterModel {
    string column_id = 1;
    repeated anduin.protobuf.fundsub.LpDataExtractRequestStatusModel data_extract_request_status = 2;
}

message AmlCheckFilterModel {
    string column_id = 1;
    repeated AmlCheckStatus aml_check_statuses = 2;
}

message AdvisorGroupFilterModel {
    repeated string selected_advisor_group_options = 1 [(scalapb.field).type = "Option[FundSubRiaGroupId]"];
}

message InvestorGroupFilterModel {
    repeated string selected_investor_group_options = 1 [(scalapb.field).type = "Option[FundSubInvestorGroupId]"];
}

message AdvisorTagFilterModel {
    repeated string selected_tags = 1 [(scalapb.field).type = "FundSubAdvisorTagId"];
}
