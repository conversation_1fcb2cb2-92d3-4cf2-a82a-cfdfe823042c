syntax = "proto3";

package anduin.fundsub.migration;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  import: "java.time.Instant"
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.id.fundsub.FundSubLpId"
};

message FundSubMigrationDataModel {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
  string migration_name = 2;
  repeated string migrated_lp_ids = 3 [(scalapb.field).type = "FundSubLpId", (scalapb.field).collection_type = "Set"];
  bool is_done = 4;
  InstantMessage last_updated_at = 5 [(scalapb.field).type = "Instant"];
}

message RecordTypeUnion {
  FundSubMigrationDataModel _FundSubMigrationDataModel = 1;
}
