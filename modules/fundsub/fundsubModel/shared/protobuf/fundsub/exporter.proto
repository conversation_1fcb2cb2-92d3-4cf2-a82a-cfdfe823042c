syntax = "proto3";

package anduin.protobuf.fundsub.exporter;

import "date_time.proto";
import "fundsub/lp_info.proto";
import "fundsub/models.proto";
import "fundsub/storage_integration.proto";
import "google/protobuf/wrappers.proto";
import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.exporter"
  single_file: true
  import: "anduin.model.id.FileId"
  import: "anduin.model.id.FolderId"
  import: "anduin.model.id.TemporalWorkflowId"
  import: "anduin.id.fundsub.FundSubExportTemplateId"
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.model.common.user.UserId"
  import: "java.time.Instant"
};

message FundSubExportTemplate {
  repeated FundSubExportFieldConfig field_configs = 1;
  int32 start_row = 2;
  int32 start_col = 3;
  string template_file_opt = 4 [(scalapb.field).type = "Option[FileId]"];
  string template_id = 5 [(scalapb.field).type = "FundSubExportTemplateId"];
  string template_name = 6;
  bool in_used = 7;
  int32 sheet = 8;
  FormReference tax_form_reference_opt = 9;
  google.protobuf.BoolValue should_export_option_label = 10;
  google.protobuf.BoolValue should_export_csv_opt = 11;
  FundSubExportTemplateDataRoomSyncConfig data_room_sync_config = 12;
  string output_file_name_prefix = 13;
  FundSubExportTemplateMetaData meta_data_opt = 14;
  optional int32 max_option_label_length = 15;
}

message FundSubExportFieldConfig {
  string alias = 2;
  int32 from_col = 4;
}

message FundSubExportTemplateDataRoomSyncConfig {
  reserved 1;
  string subfolder_id_opt = 4 [(scalapb.field).type = "Option[FolderId]"];
  bool sync_on_user_export = 2;
  FundSubExportTemplateDataRoomSyncSchedule schedule = 3;
}

message FundSubExportTemplateDataRoomSyncSchedule {
  bool enabled = 1;
  string sync_schedule = 2;
  InstantMessage end_date = 3 [(scalapb.field).type = "java.time.Instant"];
  repeated flow.fundsub.admin.lpdashboard.LpStatus status_filter = 5;
  string temporal_workflow_id = 6 [(scalapb.field).type = "Option[TemporalWorkflowId]"];
}

// Workflow protocols
message SyncExportedFileWorkflowParams {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
  string exported_file_id = 2 [(scalapb.field).type = "FileId"];
  FundSubStorageWhenToSend when_to_send = 3;
  FundSubExportTemplateDataRoomSyncConfig data_room_sync_config = 4;
}

message SyncExportedFileWorkflowResponse {
  repeated string synced_file_ids = 1 [(scalapb.field).type = "FileId"];
}

message StartDataExportWorkflowParams {
  string template_id = 1 [(scalapb.field).type = "FundSubExportTemplateId"];
  string fund_sub_id = 2 [(scalapb.field).type = "FundSubId"];
}

message StartDataExportWorkflowResponse {
  string temporal_workflow_id = 1 [(scalapb.field).type = "Option[TemporalWorkflowId]"];
}

message FundSubExportTemplateMetaData {
  InstantMessage last_updated = 1 [(scalapb.field).type = "Instant"];
  optional string last_editing_user = 2 [(scalapb.field).type = "UserId"];
  string note_content = 3;
}

message RecordTypeUnion {
  FundSubExportTemplate _FundSubExportTemplate = 1;
}
