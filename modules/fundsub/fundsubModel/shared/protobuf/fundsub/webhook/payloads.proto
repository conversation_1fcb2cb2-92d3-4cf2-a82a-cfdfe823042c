syntax = "proto3";

package fundsub.webhook;

import "date_time.proto";
import "fundsub/lp_info.proto";
import "google/protobuf/descriptor.proto";
import "scalapb/scalapb.proto";
import "external/squants.proto";

option (scalapb.options) = {
  scope: FILE
  package_name: "fundsub.webhook"
  preserve_unknown_fields: false
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.fundsub.FundSubCloseId"
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.id.fundsub.FundSubLpTagId"
  import: "anduin.id.fundsub.InvestmentFundId"
  import: "anduin.id.fundsub.CustomDataColumnId"
  import: "anduin.model.id.FileId"
  import: "java.time.Instant"
  preamble: [
    "sealed trait WebhookPayloadTrait {"
        "  def lpIdOpt: Option[FundSubLpId] = None"
        "}"
  ]
  single_file: true
};

message WebhookPayload {
  option (scalapb.message).sealed_oneof_extends = "WebhookPayloadTrait";

  oneof sealed_value {
    NewInvestorAddedPayload new_investor_added_payload = 1;
    SubscriptionStatusChangedPayload subscription_status_changed_payload = 2;
    SubscriptionCloseChangedPayload subscription_close_changed_payload = 3;
    SubscriptionTagsChangedPayload subscription_tags_changed_payload = 4;
    FilesUploadedPayload files_uploaded_payload = 5;
    DataExtractionStatusChangedPayload data_extraction_status_changed_payload = 6;
    FundApiSetupPayload fund_api_setup_payload = 7;
    FundOrderContactsAddedPayload fund_order_contacts_added_payload = 8;
    FundOrderSignersAddedPayload fund_order_signers_added_payload = 9;
    FundOrderCustomColumnValueUpdatedPayload fund_order_custom_column_value_updated_payload = 10;
    FundOrderFormDataColumnValueUpdatedPayload fund_order_form_data_column_value_updated_payload = 11;
  }
}

message UserInfo {
  string email = 1;
  string first_name = 2;
  string last_name = 3;
}

message NewInvestorAddedPayload {
  string fund_id = 1 [(scalapb.field).type = "FundSubId"];
  string lp_id_opt = 2 [
    (scalapb.field).type = "Option[FundSubLpId]",
    (scalapb.field).annotations = "override val"
  ];
  InstantMessage created_at = 3 [(scalapb.field).type = "Instant"];
}

message SubscriptionStatusChangedPayload {
  string lp_id_opt = 1 [
    (scalapb.field).type = "Option[FundSubLpId]",
    (scalapb.field).annotations = "override val"
  ];
  anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus previous_status = 2;
  anduin.protobuf.flow.fundsub.admin.lpdashboard.LpStatus new_status = 3;
  InstantMessage created_at = 4 [(scalapb.field).type = "Instant"];
}

message SubscriptionCloseChangedPayload {
  string lp_id_opt = 1 [
    (scalapb.field).type = "Option[FundSubLpId]",
    (scalapb.field).annotations = "override val"
  ];
  string previous_close_id = 2 [(scalapb.field).type = "FundSubCloseId"];
  string next_close_id = 3 [(scalapb.field).type = "FundSubCloseId"];
  InstantMessage created_at = 4 [(scalapb.field).type = "Instant"];
}

message SubscriptionTagsChangedPayload {
  string lp_id_opt = 1 [
    (scalapb.field).type = "Option[FundSubLpId]",
    (scalapb.field).annotations = "override val"
  ];
  repeated TagInfo previous_tags = 2;
  repeated TagInfo next_tags = 3;
  InstantMessage created_at = 4 [(scalapb.field).type = "Instant"];
}

message DataExtractionStatusChangedPayload {
  string lp_id_opt = 1 [
    (scalapb.field).type = "Option[FundSubLpId]",
    (scalapb.field).annotations = "override val"
  ];
  DataExtractionStatus data_extraction_status = 2;
  InstantMessage created_at = 3 [(scalapb.field).type = "Instant"];
}

message TagInfo {
  string id = 1 [(scalapb.field).type = "FundSubLpTagId"];
  string name = 2;
}

enum DocumentType {
  SUBSCRIPTION_DOC = 0;
  UNSIGNED_SUBSCRIPTION_DOC = 1;
  COUNTERSIGNED_DOC = 2;
  DISTRIBUTED_DOC = 3;
  SUPPORTING_DOC = 4;
}

enum DataExtractionStatus {
  STARTED = 0;
  READY_FOR_REVIEW = 1;
  COMPLETED = 2;
}

message FileInfo {
  string file_id = 1 [(scalapb.field).type = "FileId"];
  string file_name = 2;
}

message SupportingDocInfo {
  string doc_group = 1;
  string doc_name = 2;
}

message FilesUploadedPayload {
  string lp_id_opt = 1 [
    (scalapb.field).type = "Option[FundSubLpId]",
    (scalapb.field).annotations = "override val"
  ];
  DocumentType document_type = 2;
  repeated FileInfo files = 3;
  int32 subscription_doc_version = 5;
  SupportingDocInfo supporting_doc_info = 6;
  InstantMessage created_at = 4 [(scalapb.field).type = "Instant"];
}

message SubFundInfo {
  string sub_fund_id = 1 [(scalapb.field).type = "InvestmentFundId"];
  string sub_fund_custom_id = 2;
}

message FundApiSetupPayload {
  string service_account = 1 [(scalapb.field).type = "UserId"];
  string fund_id = 2 [(scalapb.field).type = "FundSubId"];
  string fund_custom_id = 3;
  repeated SubFundInfo sub_funds = 4;
}

enum ContactType {
  MainContact = 0;
  Collaborator = 1;
}

message FundOrderContactsAddedPayload {
  string lp_id_opt = 1 [
    (scalapb.field).type = "Option[FundSubLpId]",
    (scalapb.field).annotations = "override val"
  ];
  repeated UserInfo user_info = 2;
  ContactType contact_type = 3;
}

message FundOrderSignersAddedPayload {
  string lp_id_opt = 1 [
    (scalapb.field).type = "Option[FundSubLpId]",
    (scalapb.field).annotations = "override val"
  ];
  repeated UserInfo user_info = 2;
}

message FundOrderCustomColumnValueUpdatedPayload {
  string lp_id_opt = 1 [
    (scalapb.field).type = "Option[FundSubLpId]",
    (scalapb.field).annotations = "override val"
  ];
  string column_name = 2;
  string column_id = 3 [(scalapb.field).type = "CustomDataColumnId"];
  OrderCustomDataType old_value = 4;
  OrderCustomDataType new_value = 5;
}

message FundOrderFormDataColumnValueUpdatedPayload {
  string lp_id_opt = 1 [
    (scalapb.field).type = "Option[FundSubLpId]",
    (scalapb.field).annotations = "override val"
  ];
  string column_name = 2;
  string namespace = 3;
  string alias = 4;
  OrderCustomDataType old_value = 5;
  OrderCustomDataType new_value = 6;
}

message OrderCustomDataType {
  oneof sealed_value {
    StringType string_type = 1;
    StringArrayType string_array_type = 2;
    IntegerType integer_type = 3;
    FloatType float_type = 4;
    BooleanType boolean_type = 5;
    DateTimeType date_time_type = 6;
    CurrencyType currency_type = 7;
  }
}

message StringType {
  string value = 1;
}

message StringArrayType {
  repeated string values = 1;
}

message IntegerType {
  int32 value = 1;
}

message FloatType {
  float value = 1;
}

message BooleanType {
  bool value = 1;
}

message DateTimeType {
  InstantMessage value = 1 [(scalapb.field).type = "Instant"];
}

message CurrencyType {
  external.squants.CurrencyMessage currency = 1;
  double amount = 2;
}