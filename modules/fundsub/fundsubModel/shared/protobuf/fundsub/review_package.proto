syntax = "proto3";

package anduin.protobuf.fundsub.reviewpackage;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.reviewpackage"
  single_file: true
  import: "anduin.id.fundsub.ReviewPackageSettingId"
  import: "anduin.model.common.user.UserId"
};

message ReviewPackageSetting {
  string review_package_setting_id = 1 [(scalapb.field).type = "ReviewPackageSettingId"];
  bool is_enabled = 2;
  repeated string reviewers = 3 [(scalapb.field).type = "UserId"];
  bool is_enabled_unsigned_review_setting = 4;
}

message RecordTypeUnion {
  ReviewPackageSetting _ReviewPackageSetting = 1;
}