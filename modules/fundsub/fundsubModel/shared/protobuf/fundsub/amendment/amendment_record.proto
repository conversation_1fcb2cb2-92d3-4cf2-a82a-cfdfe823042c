syntax = "proto3";

package anduin.protobuf.fundsub.amendment;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.amendment";
  single_file: true
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.model.common.user.UserId"
  import: "anduin.model.id.FileId"
  import: "java.time.Instant"
};

message AmendmentRecord {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  int32 submission_version_index = 2;
  string original_file_id_opt = 3 [(scalapb.field).type = "Option[FileId]"];
  string amendment_file_id_opt = 4 [(scalapb.field).type = "Option[FileId]"];
  int32 amendment_page_count = 5;
  int32 insert_before_page_index = 6;
  bool is_insert_before_signature_page = 7;
  string amended_file_id_opt = 8 [(scalapb.field).type = "Option[FileId]"];
  InstantMessage last_updated_at = 9 [(scalapb.field).type = "Instant"];
  string last_updated_by = 10 [(scalapb.field).type = "Option[UserId]"];
}


message RecordTypeUnion {
  AmendmentRecord _AmendmentRecord = 1;
}
