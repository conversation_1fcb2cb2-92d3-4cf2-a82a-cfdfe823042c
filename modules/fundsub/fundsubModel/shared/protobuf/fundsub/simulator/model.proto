syntax = "proto3";

package anduin.fundsub.simulator;

import "google/protobuf/wrappers.proto";
import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.fundsub.simulator.model"
  single_file: true
  import: "anduin.id.form.*"
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.id.link.ProtectedLinkId"
};

message DemoFormData {
  option (scalapb.message).companion_extends = "DemoFormDataCompanion";

  repeated string form_version_ids = 1 [(scalapb.field).type = "FormVersionId"];
  string form_name = 2;
}

message SampleFormFiles {
  map<string, string> event_file_map = 1 [(scalapb.field).key_type = "FormVersionId"];
  google.protobuf.StringValue data_file = 2;
  google.protobuf.StringValue page_section_file = 3;
}

message OrderProgress {
  google.protobuf.StringValue joined = 1; // None if not joined, else Some(expectedCommitment)
  repeated int32 filled = 2; // filled percentages of different versions
  google.protobuf.Int32Value submit_sup_docs = 3; // None if not start submitting, else Some(submitPercentage)
  bool signed = 4; // Is signed
  bool approved = 5; // Is approved
  google.protobuf.StringValue distributed = 6; // None if not countersigned & distributed, else Some(acceptedCommitment)
  bool submitted_for_review = 7; // Is submitted for soft review
}

message DemoOrderCollaborator {
  bool is_demo_user = 1;
  string email = 2;
  string first_name = 3;
  string last_name = 4;
}

message AmlKycComment {
  string comment = 1;
  string commentorEmail = 2;
}

message AmlKycCommentThread {
  string doctype = 1;
  repeated AmlKycComment comments = 2;
  bool is_resolved = 3;
  bool is_public = 4;
}

message OcrOrderData {
  string uploaded_pdf = 1;
  string form_data_file = 2;
  bool should_mark_request_complete = 3;
}

message SideLetterFile {
  string filename = 1;
  bool is_main_file = 2;
  bool is_signed_offline = 3;
  bool is_executed = 4;
}

message SideLetterVersion {
  bool gp_uploaded = 1;
  string folder_name = 2;
  repeated SideLetterFile files = 3;
  bool is_agreed = 4;
  bool is_completed = 5;
}

message SideLetterPackage {
  string folder_name = 1;
  repeated SideLetterVersion versions = 2;
}

message DemoOrderRiaAdvisor {
  string email = 1;
  string first_name = 2;
  string last_name = 3;
}

message DemoOrderRiaData {
  repeated DemoOrderRiaAdvisor advisors = 1;
}

message DemoOrder {
  string name = 1;
  string email = 2;
  string firm_name = 3;
  SampleFormFiles sample_files = 4;
  int32 close_index = 5;
  OrderProgress progress = 6;
  repeated string tags = 7;
  repeated DemoOrderCollaborator collaborators = 8;
  bool is_main_lp_demo = 9;
  bool is_commenting_supported = 10;
  bool auto_approved_aml_kyc_docs = 11;
  repeated AmlKycCommentThread aml_kyc_comment_threads = 12;
  bool is_additional_lp_demo = 13;
  bool is_gp_autofilled = 14;
  OcrOrderData orc_data = 15;
  SideLetterPackage side_letter_package = 16;
  DemoOrderRiaData ria_data = 17;
}

message FormCommentData {
  string field_alias = 1;
  string field_description = 2;
  string toc_section = 3;
  string comment = 4;
  bool is_public = 5;
}

message RequestChangeCommentData {
  string lp_email_address = 1;
  repeated FormCommentData comments = 2;
}

message DemoTemplateVersionData {
  string template_id = 1 [(scalapb.field).type = "DataTemplateId"];
  string template_version_id = 2 [(scalapb.field).type = "DataTemplateVersionId"];
  string template_name = 3;
  string template_spreadsheet_file_name = 4;
  int32 template_header_start_col = 5;
  int32 template_header_start_row = 6;
}

message FormTemplateMappingData {
  DemoTemplateVersionData template_data = 1;
  string form_template_mapping_json_file_name = 2;
  string mapping_id = 3 [(scalapb.field).type = "FormTemplateMappingId"];
  string mapping_version_id = 4 [(scalapb.field).type = "FormTemplateMappingVersionId"];
  string mapping_name = 5;
}

message DemoFundData {
  reserved 7;
  string fund_name = 1;
  DemoFormData form_data = 2;
  string capital_id = 3;
  repeated DemoOrder demo_orders = 4;
  repeated FormCommentData form_comment_data = 5;
  string data_room_name_for_integration = 6;
  repeated RequestChangeCommentData request_change_comment_data = 8;
  FormTemplateMappingData form_template_mapping_config_opt = 9;
}

message AdditionalFundInfo {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
  string link_id = 2 [(scalapb.field).type = "ProtectedLinkId"];
  DemoFundData fund_data = 3;
}
