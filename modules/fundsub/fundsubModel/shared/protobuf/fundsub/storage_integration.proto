syntax = "proto3";

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub"
  import: "anduin.model.id.FolderId"
  single_file: true
};

message FundSubStorageIntegration {
  DataRoomIntegrationConfig data_room_integration_config = 5;
}

message DataRoomIntegrationConfig {
  bool isEnabled = 1;
  string rootFolderId = 2 [(scalapb.field).type = "Option[FolderId]"];
  string lp_folder_name_format = 3;
  repeated FundSubStorageWhenToSend when_to_send = 4;
  string lp_file_name_format = 5;
}

enum FundSubStorageWhenToSend {
  LP_UPLOAD_SUBDOC = 0;
  LP_UPLOAD_SUPPORTING = 1;
  FA_COUNTER_SIGNED = 2;
  FA_EXPORT_DATA = 3;
  AUTO_SYNC_EXPORT_DATA = 4;
  FA_DISTRIBUTE_COUNTERSIGNED_DOCS = 5;
  LP_SUBMIT_FOR_SOFT_REVIEW = 6;
}

