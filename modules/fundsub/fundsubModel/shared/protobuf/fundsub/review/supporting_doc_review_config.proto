syntax = "proto3";

import "scalapb/scalapb.proto";

package fundsub.review.supportingdoc;

import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  import: "anduin.id.fundsub.group.FundSubInvestorGroupId"
  import: "anduin.id.review.ReviewConfigId"
  import: "anduin.id.review.SupportingDocReviewConfigId"
  import: "anduin.model.common.user.UserId"
  import: "java.time.Instant"
};

enum SupportingDocReviewConfigMode {
  SUPPORTING_DOC_REVIEW_CONFIG_MODE_DISABLED = 0;
  SUPPORTING_DOC_REVIEW_CONFIG_MODE_ENABLED_WITH_SINGLE_CONFIG = 1;
  SUPPORTING_DOC_REVIEW_CONFIG_MODE_ENABLED_WITH_MULTI_GROUP_CONFIG = 2;
}

message SupportingDocReviewConfig {
  string id = 1 [(scalapb.field).type = "SupportingDocReviewConfigId"];
  SupportingDocReviewConfigMode mode = 2;
  string all_investor_group_config_id_opt = 3 [(scalapb.field).type = "Option[ReviewConfigId]"];
  string unassigned_investor_group_config_id_opt = 4 [(scalapb.field).type = "Option[ReviewConfigId]"];
  map<string, string> investor_group_config_mapping = 5 [(scalapb.field).key_type = "FundSubInvestorGroupId", (scalapb.field).value_type = "ReviewConfigId"];

  string created_by = 6 [(scalapb.field).type = "Option[UserId]"];
  InstantMessage created_at = 7 [(scalapb.field).type = "Instant"];
  string last_updated_by = 8 [(scalapb.field).type = "Option[UserId]"];
  InstantMessage last_updated_at = 9 [(scalapb.field).type = "Instant"];

  repeated SupportingDocGroup supporting_doc_groups = 10;
  MultiDocGroupReviewConfigModel all_investor_group_config_opt = 11;
  MultiDocGroupReviewConfigModel unassigned_investor_group_config_opt = 12;
  map<string, MultiDocGroupReviewConfigModel> investor_group_config_map = 13 [(scalapb.field).key_type = "FundSubInvestorGroupId"];
}

message SupportingDocGroupReviewConfig {
  string group_name = 1;
  string review_note = 2;
  repeated string reviewers = 3 [(scalapb.field).type = "UserId"];
}

message MultiDocGroupReviewConfigModel {
  SupportingDocGroupReviewConfig unassigned_doc_group_config_opt = 1;
  repeated SupportingDocGroupReviewConfig doc_group_configs = 2;
}

message SupportingDocGroup {
  string group_name = 1;
  repeated string doc_types = 2;
}

message RecordTypeUnion {
  SupportingDocReviewConfig _SupportingDocReviewConfig = 1;
}
