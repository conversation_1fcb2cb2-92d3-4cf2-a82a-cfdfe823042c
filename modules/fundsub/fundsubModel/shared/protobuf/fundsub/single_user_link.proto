syntax = "proto3";

package anduin.protobuf.fundsub.invitation;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.invitation"
  single_file: true
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.id.fundsub.FundSubSingleUserInvitationLinkId"
  import: "anduin.model.common.user.UserId"
};

message FundSubSingleUserInvitationLink {
  reserved 3;
  string single_user_link_id = 1 [(scalapb.field).type = "FundSubSingleUserInvitationLinkId"];
  string user_id = 2 [(scalapb.field).type = "UserId"];
  repeated string lp_ids = 4 [(scalapb.field).type = "FundSubLpId"];
}

message RecordTypeUnion {
  FundSubSingleUserInvitationLink _FundSubSingleUserInvitationLink = 1;
}