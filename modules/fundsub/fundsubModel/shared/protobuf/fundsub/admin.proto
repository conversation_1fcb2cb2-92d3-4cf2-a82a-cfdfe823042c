syntax = "proto3";

package anduin.protobuf.flow.fundsub.admin;

import "scalapb/scalapb.proto";
import "fundsub/models.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.admin"
  single_file: true
  import: "anduin.model.common.user.UserId"
  import: "anduin.model.id.*"
  import: "anduin.id.fundsub.*"
};

// This model is queried frequently to verify fund admin permission. Keep it lightweight.
message FundSubAdminGeneralModel {
  string fund_sub_admin_general_id = 1 [(scalapb.field).type = "FundSubAdminGeneralId"];
  map<string, FundSubAdminRole> admins = 2 [(scalapb.field).key_type = "UserId"];
  string admin_team_id = 3 [(scalapb.field).type = "Option[TeamId]"];
  map<string, string> admin_info = 4 [(scalapb.field) = {key_type: "UserId" value_type: "FundSubAdminInfoId"}];
  repeated string contact_admins = 5 [(scalapb.field).type = "UserId"];
}
