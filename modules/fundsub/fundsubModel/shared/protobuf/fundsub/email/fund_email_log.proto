syntax = "proto3";

package anduin.fundsub.email;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.model.common.user.UserId"

  import: "java.time.Instant"
};

message FundEmailLog {
  string receiver_id = 1 [(scalapb.field).type = "UserId"];
  string fund_id = 2 [(scalapb.field).type = "FundSubId"];
  InstantMessage last_daily_fund_activities_sent_at = 3 [(scalapb.field).type = "Instant"];
  InstantMessage last_weekly_fund_activities_sent_at = 4 [(scalapb.field).type = "Instant"];
  InstantMessage last_daily_new_investor_report_sent_at = 5 [(scalapb.field).type = "Instant"];
}

message RecordTypeUnion {
  FundEmailLog _FundEmailLog = 1;
}
