syntax = "proto3";

package anduin.fundsub.email;

import "fundsub/models.proto";
import "scalapb/scalapb.proto";
import "email/smtp.proto";
import "google/protobuf/wrappers.proto";

option (scalapb.options) = {
  single_file: true
  import: "anduin.model.id.email.provider.EmailProviderId"
};

// FIXME: Move all of these settings to JVM

message FundSubCustomSenderEmailAddress {
  bool enabled = 1;
  string emailNamePart = 2;
  string senderName = 3;
  google.protobuf.StringValue domainPart = 4;
}

message FundSubCustomEmailReply {
  bool is_enabled = 1;
  repeated string email_addresses = 2;
  repeated FundSubEvent enabled_email_events = 3;
}

message FundSubEmailReplyToSenderConfig {
  bool enabled = 1;
  repeated FundSubEvent enabled_email_events = 2;
}

message FundSubCustomEmailCcConfig {
  bool enabled = 1;
  repeated string email_addresses = 2;
  repeated FundSubEvent enabled_email_events = 3;
}

message FundSubSenderCcConfig {
  bool enabled = 1;
  repeated FundSubEvent enabled_email_events = 2;
}

message FundSubCustomEmailBccConfig {
  bool enabled = 1;
  repeated string email_addresses = 2;
  repeated FundSubEvent enabled_email_events = 3;
}

message FundSubSenderBccConfig {
  bool enabled = 1;
  repeated FundSubEvent enabled_email_events = 2;
}

message FundSubCustomSmtpServerConfig {
  reserved 2;
  bool enabled = 1;
  string provider_id = 3 [(scalapb.field).type = "Option[EmailProviderId]"];
}
