syntax = "proto3";

package anduin.protobuf.fundsub.environment.policy;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.environment.policy"
  single_file: true
  import: "anduin.id.environment.EnvironmentReauthenticationPolicyId"
  import: "anduin.id.environment.EnvironmentSSOBindingId"
  import: "anduin.id.environment.UserEnvironmentSSOBindingId"
  import: "anduin.id.fundsub.FundSubGpCompositeId"
  import: "anduin.id.fundsub.FundSubLpCompositeId"
  import: "anduin.id.fundsub.FundSubCollaboratorSSOTombstoneId"
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.model.common.user.UserId"
};

enum FundSubAppReauthenticationPolicyForMultipleBindings {
  UseOrdering = 0;
  ListAll = 1;
}

enum FundSubAppReauthenticationPolicyBindingType {
  Gp = 0;
  Lp = 1;
  GpCollaborator = 2;
  LpCollaborator = 3;
}

message FundSubAppReauthenticationPolicyConfig {
  string id = 1 [(scalapb.field).type = "EnvironmentReauthenticationPolicyId"];
  FundSubAppReauthenticationPolicyForMultipleBindings multipleBindingPolicy = 2;
  string gpBinding = 3 [(scalapb.field).type = "EnvironmentSSOBindingId"];
  int32 gpBindingOrder = 4;
  string lpBinding = 5 [(scalapb.field).type = "EnvironmentSSOBindingId"];
  int32 lpBindingOrder = 6;
  string gpCollaboratorBinding = 7 [(scalapb.field).type = "EnvironmentSSOBindingId"];
  int32 gpCollaboratorBindingOrder = 8;
  string lpCollaboratorBinding = 9 [(scalapb.field).type = "EnvironmentSSOBindingId"];
  int32 lpCollaboratorBindingOrder = 10;
  bool enableProtectedLink = 11;
}

message FundSubGpEnvironmentSSOBinding {
  string id = 1 [(scalapb.field).type = "FundSubGpCompositeId"];
  string bindindId = 2 [(scalapb.field).type = "UserEnvironmentSSOBindingId"];
  string fundSubId = 3 [(scalapb.field).type = "FundSubId"];
  string userId = 4 [(scalapb.field).type = "UserId"];
}

enum FundSubLpEnvironmentSSOType {
  MainLp = 0;
  CollaboratorInvitedByGp = 1;
  CollaboratorInvitedByLp = 2;
}

message FundSubLpEnvironmentSSOBinding {
  string id = 1 [(scalapb.field).type = "FundSubLpCompositeId"];
  string bindindId = 2 [(scalapb.field).type = "UserEnvironmentSSOBindingId"];
  string fundSubId = 3 [(scalapb.field).type = "FundSubId"];
  string fundSubLpId = 4 [(scalapb.field).type = "FundSubLpId"];
  string userId = 5 [(scalapb.field).type = "UserId"];
  FundSubLpEnvironmentSSOType lpType = 6;
}

message FundSubCollaboratorSSOTombstone {
  string id = 1 [(scalapb.field).type = "FundSubCollaboratorSSOTombstoneId"];
  FundSubLpEnvironmentSSOType lpType = 2;
}

message RecordTypeUnion {
  FundSubAppReauthenticationPolicyConfig _FundSubAppReauthenticationPolicyConfig = 1;
  FundSubGpEnvironmentSSOBinding _FundSubGpEnvironmentSSOBinding = 2;
  FundSubLpEnvironmentSSOBinding _FundSubLpEnvironmentSSOBinding = 3;
  FundSubCollaboratorSSOTombstone _FundSubCollaboratorSSOTombstone = 4;
}