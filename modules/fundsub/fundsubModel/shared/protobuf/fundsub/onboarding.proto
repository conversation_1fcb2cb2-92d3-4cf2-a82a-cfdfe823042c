syntax = "proto3";

package anduin.protobuf.fundsub.onboarding;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.onboarding"
  single_file: true
  import: "anduin.id.user.UserRestrictedId"
};

enum ModuleOnboardingMessage {
  FundSubContact = 0;
  FundSubNPSSurvey = 1;
}

message FundSubUserModuleOnboarding {
  string user_restricted_id = 1 [(scalapb.field).type = "UserRestrictedId"];
  repeated ModuleOnboardingMessage module_onboarding = 2 [(scalapb.field) = { collection_type: "Set" }];
}

message RecordTypeUnion {
  FundSubUserModuleOnboarding _FundSubUserModuleOnboarding = 1;
}