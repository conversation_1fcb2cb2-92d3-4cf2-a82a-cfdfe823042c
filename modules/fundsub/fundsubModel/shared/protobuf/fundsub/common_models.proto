syntax = "proto3";

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub"
  single_file: true
};

message FundSubLpGeneralConfig {
  reserved 1 to 4, 9;
  bool enable_import_export_json_values = 5;
  bool enable_live_preview_mode = 8;
  bool enable_lp_signing_instruction = 6;
  string lp_signing_instruction_content = 7;
  EmbedInvestorDataOnSubDocConfig embed_investor_data_on_sub_doc_config = 10;
}

message EmbedInvestorDataOnSubDocConfig {
  enum Placement {
    TopLeft = 0;
    BottomLeft = 1;
    TopRight = 2;
    BottomRight = 3;
  }

  Placement placement = 1;
}
