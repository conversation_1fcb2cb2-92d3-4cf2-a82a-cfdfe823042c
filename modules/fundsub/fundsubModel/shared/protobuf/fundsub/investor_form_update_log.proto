syntax = "proto3";

package anduin.protobuf.fundsub.updatelog;

import "scalapb/scalapb.proto";
import "date_time.proto";
import "dynamicform/form_data.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.form.FormVersionId"
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.id.fundsub.InvestorFormUpdateLogId"
  import: "anduin.model.common.user.UserId"
};

message InvestorFormUpdateLogModel {
  string investorFormUpdateLogId = 1 [(scalapb.field).type = "InvestorFormUpdateLogId"];
  string fundSubId = 2 [(scalapb.field).type = "FundSubId"];
  string actor = 3 [(scalapb.field).type = "UserId"];
  InstantMessage at = 4 [(scalapb.field).type = "java.time.Instant"];
  repeated string lpIds = 5 [(scalapb.field).type = "FundSubLpId"];
  S3FormChangeInfo s3FormChangeInfo = 6;
  string formVersionId = 7 [(scalapb.field).type = "Option[FormVersionId]"];
}

message RecordTypeUnion {
  InvestorFormUpdateLogModel _InvestorFormUpdateLogModel = 1;
}
