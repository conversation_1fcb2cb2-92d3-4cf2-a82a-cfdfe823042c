syntax = "proto3";

package anduin.protobuf.fundsub.docusignaccount;

import "scalapb/scalapb.proto";
import "fundsub/models.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.docusignaccount"
  single_file: true
  import: "anduin.id.fundsub.*"
  import: "anduin.id.signature.CustomDocusignAccountId"
};

message FundSubCustomDocusignAccountMessage {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
  string custom_docusign_account_id = 2 [(scalapb.field).type = "CustomDocusignAccountId"];
}


message RecordTypeUnion {
  FundSubCustomDocusignAccountMessage _FundSubCustomDocusignAccountMessage = 1;
}