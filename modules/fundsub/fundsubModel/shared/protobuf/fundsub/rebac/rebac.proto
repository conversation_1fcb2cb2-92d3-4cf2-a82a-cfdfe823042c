syntax = "proto3";

package anduin.fundsub.rebac;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  single_file: true
  import: "anduin.id.fundsub.rebac.PortalFundSubTeamId"
  import: "anduin.model.common.user.UserId"
};

message PortalFundSubTeam {
  string teamId = 1 [(scalapb.field).type = "PortalFundSubTeamId"];
  string name = 2;
  string createdBy = 3 [(scalapb.field).type = "UserId"];
  int64 createdAt = 4;
}

message RecordTypeUnion {
  PortalFundSubTeam _PortalFundSubTeam = 1;
}
