syntax = "proto3";

package anduin.fundsub.rebac.store;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  import: "java.time.Instant"
  import: "anduin.id.fundsub.FundSubId"
};

message FundSubOpenFgaStoreMappingModel {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
  string store_id = 2;
  InstantMessage created_at = 3 [(scalapb.field).type = "Instant"];
  InstantMessage last_updated_at = 4 [(scalapb.field).type = "Instant"];
}

message RecordTypeUnion {
  FundSubOpenFgaStoreMappingModel _FundSubOpenFgaStoreMappingModel = 1;
}
