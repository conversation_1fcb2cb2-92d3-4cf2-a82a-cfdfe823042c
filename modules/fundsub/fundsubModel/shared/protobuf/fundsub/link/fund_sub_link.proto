syntax = "proto3";

package anduin.fundsub.link;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  single_file: true
  import: "anduin.id.fundsub.FundSubId"
};

// TODO: re-organize packages so that we can make these model private to fundsub scope.
message FundsubProtectedLinkTypes {
    oneof sealed_value {
        FundSubCreateFormLink create_form_link = 1;
    }
}

message FundSubCreateFormLink {
    string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}
