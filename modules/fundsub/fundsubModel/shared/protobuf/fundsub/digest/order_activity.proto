syntax = "proto3";

package anduin.fundsub.digest;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.id.fundsub.FundSubLpId"
  import: "java.time.Instant"
};

enum OrderActivityType {
  ORDER_ACTIVITY_TYPE_SUBMITTED = 0;
  ORDER_ACTIVITY_TYPE_SUPPORTING_DOCS_SUBMITTED = 1;
  ORDER_ACTIVITY_TYPE_COUNTERSIGNED = 2;
  ORDER_ACTIVITY_TYPE_DISTRIBUTED = 3;
}

message OrderActivity {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string fund_id = 4 [(scalapb.field).type = "FundSubId"];
  OrderActivityType activity_type = 2;
  InstantMessage created_at = 3 [(scalapb.field).type = "Instant"];
}

message RecordTypeUnion {
  OrderActivity _OrderActivity = 1;
}
