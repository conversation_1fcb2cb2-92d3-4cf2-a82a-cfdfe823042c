syntax = "proto3";

import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";

import "date_time.proto";
import "external/squants.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.models.customdata.item"
  single_file: true
  import: "java.time.Instant"
  import: "anduin.fundsub.model.FundSubEnumTypeMappers.given"
  import: "anduin.protobuf.fundsub.LpTagColor"
};

message DateTimeValue {
  InstantMessage value = 1 [(scalapb.field).type = "Instant"];
}

message CurrencyValue {
   double amount = 1;
   external.squants.CurrencyMessage currency = 2;
}

message StringWithColor {
  string content = 1;
  google.protobuf.Int32Value color = 2 [(scalapb.field).type = "LpTagColor"];
}

message SingleStringValue {
  reserved 1;
  repeated StringWithColor value_with_color = 2;
}

message MultipleStringValue {
  reserved 1;
  repeated StringWithColor value_with_color = 2;
}

message StringValue {
  string value = 1;
}

message ChecklistValue {
  repeated string value = 1;
}

message MetadataValue {
  string value = 1;
}

message CustomData {
  oneof sealed_value {
    DateTimeValue date_time_value = 1;
    CurrencyValue currency_value = 2;
    SingleStringValue single_string_value = 3;
    MultipleStringValue multiple_string_value = 4;
    StringValue string_value = 5;
    ChecklistValue checklist_value = 6;
    MetadataValue metadata_value = 7;
  }
}
