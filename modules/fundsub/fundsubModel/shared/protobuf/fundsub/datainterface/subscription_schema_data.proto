syntax = "proto3";

package anduin.protobuf.fundsub.datainterface.subscription;

import "scalapb/scalapb.proto";
import "google/protobuf/struct.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.datainterface.subscription"
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.id.fundsub.SubscriptionSchemaDataId"
  import: "anduin.model.codec.ProtoCodecs.given"
  import: "io.circe.Json"
};

message SubscriptionSchemaData {
  option (scalapb.message).extends = "anduin.fundsub.model.SubscriptionSchemaDataTrait";

  string id = 1 [(scalapb.field).type = "SubscriptionSchemaDataId"];
  string fund_sub_id = 2 [(scalapb.field).type = "FundSubId"];
  google.protobuf.Value data = 3 [(scalapb.field).type = "Json"];
}

message RecordTypeUnion {
  SubscriptionSchemaData _SubscriptionSchemaData = 1;
}