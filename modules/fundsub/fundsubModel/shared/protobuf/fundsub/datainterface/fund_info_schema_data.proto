syntax = "proto3";

package anduin.protobuf.fundsub.datainterface.fund;

import "scalapb/scalapb.proto";
import "google/protobuf/struct.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.datainterface.fund"
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.fundsub.FundInfoSchemaDataId"
  import: "anduin.model.codec.ProtoCodecs.given"
  import: "io.circe.Json"
};

message FundInfoSchemaData {
  option (scalapb.message).extends = "anduin.fundsub.model.FundInfoSchemaDataTrait";

  string id = 1 [(scalapb.field).type = "FundInfoSchemaDataId"];
  google.protobuf.Value data = 2 [(scalapb.field).type = "Json"];
}

message RecordTypeUnion {
  FundInfoSchemaData _FundInfoSchemaData = 1;
}
