syntax = "proto3";

package anduin.protobuf.fundsub.simulator;

import "date_time.proto";
import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.simulator"
  single_file: true
  import: "java.time.Instant"
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.id.fundsub.FundSubSimulatorProgressId"
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.funddata.FundDataFirmId"
};

enum FundSubSimulatorProgressStatus {
  NotStarted = 0;
  Processing = 1;
  Done = 2;
  Error = 3;
}

message FundSubSimulatorProgressModel {
  reserved 11;
  string progress_id = 1 [(scalapb.field).type = "FundSubSimulatorProgressId"];
  int32 progress = 2;
  string fundSubLpIdOpt = 3 [(scalapb.field).type = "Option[FundSubLpId]"];
  FundSubSimulatorProgressStatus status = 4;
  InstantMessage last_updated_at = 5 [(scalapb.field).type = "Instant"];
  string creator = 6 [(scalapb.field).type = "UserId"];
  string creatorEmail = 7;
  InstantMessage started_at = 8 [(scalapb.field).type = "Instant"];
  string demo_form_type = 9;
  bool created_via_sandbox_dashboard = 10;
  string fundDataFirmIdOpt = 12 [(scalapb.field).type = "Option[FundDataFirmId]"];
}

message RecordTypeUnion {
  FundSubSimulatorProgressModel _FundSubSimulatorProgressModel = 1;
}