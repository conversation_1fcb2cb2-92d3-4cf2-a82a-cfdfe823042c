syntax = "proto3";

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub"
  single_file: true
  import: "anduin.model.id.FileId"
};

message FundSubDocument {
  oneof sealed_value {
    FilledDocument filled_document = 1;
    LpSignedDocument lp_signed_document = 2;
  }
}

message FilledDocument {
  string file_id = 1 [(scalapb.field).type = "FileId"];
  string original_form_file_id = 2 [(scalapb.field).type = "FileId"];
}

message LpSignedDocument {
  string file_id = 1 [(scalapb.field).type = "FileId"];
  string certificate_file_id_opt = 2 [(scalapb.field).type = "Option[FileId]"];
}
