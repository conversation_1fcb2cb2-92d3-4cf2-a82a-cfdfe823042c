syntax = "proto3";

package anduin.protobuf.fundsub.comment.assignment;

import "scalapb/scalapb.proto";
import "date_time.proto";
import "anduin/fundsub/comment/topic_data.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.comment.assignment"
  single_file: true
  import: "anduin.id.issuetracker.CommentAssignmentId"
  import: "anduin.model.common.user.UserId"
  import: "anduin.model.id.TeamId"
  import: "java.time.Instant"
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.id.fundsub.FundSubLpId"
};
message AssignActivity {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occur_at = 2 [(scalapb.field).type = "Instant"];
  string assigned_gp_user_id_opt = 3 [(scalapb.field).type = "Option[UserId]"];
  string assigned_gp_team_id_opt = 4 [(scalapb.field).type = "Option[TeamId]"];
}

message UnassignActivity {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occur_at = 2 [(scalapb.field).type = "Instant"];
}

message CommentAssignmentActivity {
  oneof sealed_value {
    AssignActivity assign_activity = 1;
    UnassignActivity unassign_activity = 2;
  }
}

message CommentAssignmentIndexRecordModel {
  string id = 1 [(scalapb.field).type = "CommentAssignmentId"];
  string fund_sub_id = 2 [(scalapb.field).type = "FundSubId"];
  string last_assigned_gp_user_id_opt = 3 [(scalapb.field).type = "Option[UserId]"]; // used for indexing
  string last_assigned_gp_team_id_opt = 4 [(scalapb.field).type = "Option[TeamId]"];
  repeated CommentAssignmentActivity assignment_activities = 5;
  InstantMessage created_at = 6 [(scalapb.field).type = "Instant"];
  InstantMessage last_updated_at = 7 [(scalapb.field).type = "Instant"];
  string fund_sub_lp_id = 8 [(scalapb.field).type = "FundSubLpId"];
  TopicData topic_data = 9;
}

message RecordTypeUnion {
  CommentAssignmentIndexRecordModel _CommentAssignmentIndexRecordModel = 1;
}
