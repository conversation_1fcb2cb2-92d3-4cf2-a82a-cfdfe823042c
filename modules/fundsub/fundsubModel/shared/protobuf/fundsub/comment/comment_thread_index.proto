syntax = "proto3";

package anduin.protobuf.fundsub.comment;

import "scalapb/scalapb.proto";
import "date_time.proto";
import "anduin/fundsub/comment/topic_data.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.comment"
  single_file: true
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.id.issuetracker.IssueId"
  import: "java.time.Instant"
};

enum CommentThreadStatus {
  COMMENT_THREAD_STATUS_OPEN = 0;
  COMMENT_THREAD_STATUS_RESOLVED = 1;
}

enum CommentThreadTopic {
  COMMENT_THREAD_TOPIC_FORM_QUESTION = 0;
  COMMENT_THREAD_TOPIC_AML_KYC_QUESTION = 1;
}

message CommentThreadIndexRecord {
  string id = 1 [(scalapb.field).type = "IssueId"];
  bool is_removed = 9;
  bool is_internal = 10;
  string fund_id = 2 [(scalapb.field).type = "FundSubId"];
  string lp_id = 3 [(scalapb.field).type = "FundSubLpId"];
  CommentThreadStatus status = 4;
  CommentThreadTopic topic = 5;
  TopicData topic_data = 6;
  InstantMessage created_at = 7 [(scalapb.field).type = "Instant"];
  InstantMessage last_reply_at = 8 [(scalapb.field).type = "Instant"];
  bool created_by_investor = 11;
  bool last_comment_made_by_investor = 12;
}

message RecordTypeUnion {
  CommentThreadIndexRecord _CommentThreadIndexRecord = 1;
}
