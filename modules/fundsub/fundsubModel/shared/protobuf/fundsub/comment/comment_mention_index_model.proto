syntax = "proto3";

package anduin.protobuf.fundsub.comment.mention;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.comment.mention"
  single_file: true
  import: "anduin.id.issuetracker.IssueId"
  import: "anduin.id.issuetracker.MentionId"
  import: "anduin.model.common.user.UserId"
  import: "anduin.model.id.TeamId"
  import: "java.time.Instant"
};

message CommentMentionIndexRecordModel {
  reserved 1, 4;
  string issue_id = 2 [(scalapb.field).type = "IssueId"];
  string comment_id = 3;
  string mentioned_user_id = 8 [(scalapb.field).type = "UserId"];
  string mentioned_gp_team_id_opt = 5 [(scalapb.field).type = "Option[TeamId]"];
  InstantMessage created_at = 6 [(scalapb.field).type = "Instant"];
  string id = 7 [(scalapb.field).type = "MentionId"];
}

message RecordTypeUnion {
  CommentMentionIndexRecordModel _CommentMentionIndexRecordModel = 1;
}
