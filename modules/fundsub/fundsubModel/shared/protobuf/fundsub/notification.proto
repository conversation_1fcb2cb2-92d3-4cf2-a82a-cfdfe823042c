syntax = "proto3";

package anduin.protobuf.fundsub.notification;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.notification"
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.fundsub.FundSubId"
  single_file: true
};

enum FundAdminNewCommentEmailNotificationSetting {
  FUND_ADMIN_NEW_COMMENT_EMAIL_NOTIFICATION_SETTING_ALL_COMMENTS = 0;
  FUND_ADMIN_NEW_COMMENT_EMAIL_NOTIFICATION_SETTING_MENTIONS_AND_REPLIES_ONLY = 1;
}

enum FundActivitiesEmailFrequency {
  FUND_ACTIVITIES_EMAIL_FREQUENCY_DAILY = 0;
  FUND_ACTIVITIES_EMAIL_FREQUENCY_WEEKLY = 1;
}

message FundAdminNotificationPreference {
  reserved 6;

  bool notify_on_investor_filled_form_event = 1;
  bool notify_on_investor_sign_event = 2;
  bool notify_on_fund_countersign_event = 3;
  bool notify_on_investor_submit_supporting_doc_event = 4;
  bool subscribe_to_daily_digest_email = 5;
  bool subscribe_to_new_form_comments_digest_email = 7;
  bool wait_for_required_docs_before_sending_doc_review_email = 8;
  bool notify_on_countersigned_doc_distributed_event = 9;
  bool subscribe_to_data_extract_ready_for_review_email = 13;

  // new emails
  FundActivitiesEmailFrequency fund_activities_email_frequency = 10;
  optional FundAdminNewCommentEmailNotificationSetting fund_admin_new_comment_email_notification_setting = 11;
  bool is_notify_on_comment_assigned_disabled = 12;
}

message FundAdminNotificationSetting {
  string userId = 1 [(scalapb.field).type = "UserId"];
  FundAdminNotificationPreference defaultPreference = 2;
  map<string, FundAdminNotificationPreference> fundPreferences = 3 [(scalapb.field).key_type = "FundSubId"];
}

message RecordTypeUnion {
  FundAdminNotificationSetting _FundAdminNotificationSetting = 1;
}
