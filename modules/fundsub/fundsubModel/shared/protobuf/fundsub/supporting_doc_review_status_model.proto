syntax = "proto3";

package anduin.protobuf.fundsub.supportingdoc.review;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.supportingdoc.review"
  import: "java.time.Instant"
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.fundsub.FundSubLpId"
  single_file: true
};

message FundSubSupportingDocReviewStatusModel {
  string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string doc_type = 2;
  SupportingDocReviewStatus review_status = 3;
  string updated_by = 4 [(scalapb.field).type = "Option[UserId]"];
  InstantMessage updated_at = 5 [(scalapb.field).type = "Instant"];
  string comment = 6;
}

enum SupportingDocReviewStatus {
  PendingReview = 0;
  ChangesRequested = 1;
  Approved = 2;
}

message RecordTypeUnion {
  FundSubSupportingDocReviewStatusModel _FundSubSupportingDocReviewStatusModel = 1;
}
