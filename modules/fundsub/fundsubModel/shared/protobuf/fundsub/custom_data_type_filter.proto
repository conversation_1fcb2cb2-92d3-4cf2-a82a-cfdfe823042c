syntax = "proto3";

import "scalapb/scalapb.proto";

import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.models.customdata.item"
  single_file: true
  import: "java.time.Instant"
};

message CustomDataFilterValue {
  oneof filter {
    DateTimeValueFilter date_time_value_filter = 1;
    CurrencyValueFilter currency_value_filter = 2;
    SingleStringValueFilter single_string_value_filter = 3;
    MultipleStringValueFilter multiple_string_value_filter = 4;
    StringValueFilter string_value_filter = 5;
    MetadataValueFilter metadata_value_filter = 6;
  }
}

message StringValueFilter {
  oneof sealed_value {
   StringValueFilterContains string_value_filter_contains = 1;
   StringValueFilterNotContains string_value_filter_not_contains = 2;
   StringValueFilterEmpty string_value_filter_empty = 3;
  }
}

message StringValueFilterContains {
  string value = 1;
}

message StringValueFilterNotContains {
  string value = 1;
}

message StringValueFilterEmpty {
}


message DateTimeValueFilter {
  oneof sealed_value {
    DateTimeValueFilterBetween date_time_value_filter_between = 1;
    DateTimeValueFilterNotBetween date_time_value_filter_not_between = 2;
    DateTimeValueFilterEmpty date_time_value_filter_empty = 3;
  }
}

message DateTimeValueFilterBetween {
  InstantMessage from = 1 [(scalapb.field).type = "Instant"];
  InstantMessage to = 2 [(scalapb.field).type = "Instant"];
}

message DateTimeValueFilterNotBetween {
  InstantMessage from = 1 [(scalapb.field).type = "Instant"];
  InstantMessage to = 2 [(scalapb.field).type = "Instant"];
}

message DateTimeValueFilterEmpty {
}

message CurrencyValueFilter {
  oneof sealed_value {
    CurrencyValueFilterBetween currency_value_filter_between = 1;
    CurrencyValueFilterEmpty currency_value_filter_empty = 2;
  }
}

message CurrencyValueFilterBetween {
  optional double from = 1;
  optional double to = 2;
}

message CurrencyValueFilterEmpty {
}


message SingleStringValueFilter {
  oneof sealed_value {
    SingleStringValueFilterContains single_string_value_filter_contains = 1;
    SingleStringValueFilterNotContains single_string_value_filter_not_contains = 2;
    SingleStringValueFilterEmpty single_string_value_filter_empty = 3;
  }
}

message SingleStringValueFilterContains {
  repeated string values = 1;
}

message SingleStringValueFilterNotContains {
  repeated string values = 1;
}

message SingleStringValueFilterEmpty {
}

message MetadataValueFilter {
  oneof sealed_value {
    MetadataValueFilterContains metadata_value_filter_contains = 1;
    MetadataValueFilterNotContains metadata_value_filter_not_contains = 2;
    MetadataValueFilterEmpty metadata_value_filter_empty = 3;
  }
}

message MetadataValueFilterContains {
  string value = 1;
}

message MetadataValueFilterNotContains {
  string value = 1;
}

message MetadataValueFilterEmpty {
}

message MultipleStringValueFilter {
  oneof sealed_value {
    MultipleStringValueFilterContains multiple_string_value_filter_contains = 1;
    MultipleStringValueFilterNotContains multiple_string_value_filter_not_contains = 2;
    MultipleStringValueFilterEmpty multiple_string_value_filter_empty = 3;
  }
}

message MultipleStringValueFilterContains {
  repeated string values = 1;
}

message MultipleStringValueFilterNotContains {
  repeated string values = 1;
}

message MultipleStringValueFilterEmpty {
}
