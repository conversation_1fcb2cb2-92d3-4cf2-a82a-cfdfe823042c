syntax = "proto3";

package anduin.protobuf.fundsub.activitylog.lp;

import "date_time.proto";
import "fundsub/models.proto";
import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.activitylog.lp"
  import: "anduin.id.fundsub.FundSubLpActivityId"
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.id.fundsub.FundSubSupportingDocId"
  import: "anduin.id.fundsub.dataextract.FundSubDataExtractRequestId"
  import: "anduin.id.signature.SignatureRequestId"
  import: "anduin.model.common.user.UserId"
  import: "anduin.model.id.FileId"
  import: "anduin.id.investmententity.InvestmentEntityId"
  import: "anduin.id.funddata.FundDataInvestmentEntityId"
  import: "anduin.id.offering.GlobalOfferingId"
  single_file: true
};

message LpActivityInfo {
  oneof sealed_value {
    LpInvited lp_invited = 1;
    LpReminded lp_reminded = 2;
    LpJoined lp_joined = 3;
    CollaboratorInvited collaborator_invited = 4;
    CollaboratorJoined collaborator_joined = 5;
    CollaboratorRemoved collaborator_removed = 6;
    FormEdited form_edited = 7;
    SignedSubscriptionDocument signed_subscription_document = 8;
    LpRequestedSignature lp_requested_signature = 9;
    LpCancelledSigning lp_cancelled_signing = 10;
    LpUploadedSupportingDocs lp_uploaded_supporting_docs = 11;
    LpRemovedSupportingDocs lp_removed_supporting_docs = 12;
    SubmittedSubscriptionPackage submit_subscription_package = 13;
    UndoneSubmission undone_submission = 14;
    UploadedCounterSignDoc uploaded_counter_sign_doc = 15;
    RemovedCounterSignDoc removed_counter_sign_doc = 16;
    CounterEsigned counter_esigned = 17;
    SentCounterSignRequests sent_counter_sign_requests = 18;
    CancelledCounterSignRequests cancelled_counter_sign_requests = 19;
    SignedCounterSignRequest signed_counter_sign_request = 20;
    DistributedCounterSignedDoc distributed_counter_signed_doc = 21;
    LpInvitationEmailBounced lp_email_bounced = 22;
    LpInvitationEmailClicked lp_email_clicked = 23;
    BouncedInvitationEmailResent bounced_invitation_email_resent = 24;
    CustomRecipientsReInvitationSent custom_recipients_re_invitation_sent = 25;
    CustomRecipientsReminderSent custom_recipients_reminder_sent = 26;
    SubscriptionMarkedAsReviewed subscription_marked_as_reviewed = 27;
    SkipSubscriptionReview skip_subscription_review = 28;
    UpdatedCounterSignedDoc updated_countersigned_doc = 29;
    LpRequestedToChange lp_requested_to_change = 30;
    LpRequestedToUploadSupportingDoc lp_requested_to_upload_supporting_doc = 31;
    LpRequestedToSignOnSupportingDoc lp_requested_to_sign_on_supporting_doc = 32;
    RemovedSupportingDocRequest removed_supporting_doc_request = 33;
    RemovedSignatureRequestOnSupportingDoc removed_signature_request_on_supporting_doc = 34;
    RemovedSupportingDoc removed_supporting_doc = 35;
    RemovedAdditionalSupportingDocFile remove_additional_supporting_doc_file = 36;
    RemovedReferenceDocFile removed_reference_doc_file = 37;
    UploadReferenceDocs upload_reference_docs = 38;
    RequestedSignatureGeneral requested_signature_general = 39;
    CancelledSignatureRequestGeneral cancel_signature_request_general = 40;
    SignedSignatureRequestGeneral signed_signature_request_general = 41;
    MarkSubDocRequestComplete mark_sub_doc_request_complete = 42;
    LpCancelledSubmission lp_cancelled_submission = 43;
    LpSubmitForSoftReview lp_submit_soft_review = 44;
    LpCancelSoftReview lp_cancel_soft_review = 45;
    CancelledSubscriptionSignatureRequest cancel_subscription_signature_request = 46;
    InvestmentEntityNameUpdated investment_entity_name_updated = 47;
    RemovedAdditionalForm removed_additional_form = 48;
    FundAdminReviewedDoc fund_admin_reviewed_doc = 49;
    InvestmentEntityLinked investment_entity_linked = 50;
    InvestmentEntityRemoved investment_entity_removed = 51;
    InvestmentEntityReplaced investment_entity_replaced = 52;
    SignedSubscriptionMarkedAsApproved signed_subscription_marked_as_approved = 53;
    UnsignedSubscriptionMarkedAsApproved unsigned_subscription_marked_as_approved = 54;
    LpRemindedToUploadSupportingDoc lp_reminded_to_upload_supporting_doc = 55;
    SignedSubscriptionDocReviewed signed_subscription_doc_reviewed = 56;
    UnsignedSubscriptionDocReviewed unsigned_subscription_doc_reviewed = 57;
    AutoSaveEnabled auto_save_enabled = 58;
    AutoSaveDisabled auto_save_disabled = 59;
    DocumentAutoSaved document_auto_saved = 60;
    SubscriptionDataAutoSaved subscription_data_auto_saved = 61;
    LpCommentActivity comment_activity = 62;
    LpCommentNotificationSent comment_notification_sent = 63;
    ActionOnSupportingDoc action_on_supporting_doc = 64;
    DataExtractResultImportedToForm data_extract_result_imported_to_form = 65;
    SubscriptionMarkedAsComplete subscription_marked_as_complete = 66;
    CollaboratorPromoted collaborator_promoted = 67;
    CreateOneEnvelopeRequest create_one_envelope_request = 68;
    SignedOneEnvelopeRequest signed_one_envelope_request = 69;
    ProfileFilled profile_filled = 70;
  }
}

message FundSubLpActivityModel {
  string id = 1 [(scalapb.field).type = "FundSubLpActivityId"];
  string lpId = 2 [(scalapb.field).type = "FundSubLpId"]; // for indexing
  string actorOpt = 3 [(scalapb.field).type = "Option[UserId]"];
  InstantMessage at = 4 [(scalapb.field).type = "java.time.Instant"];
  LpActivityInfo detail = 5;
  repeated string seenBy = 6 [(scalapb.field).type = "UserId"];
}

message LpInvited {
  bool via_protected_link = 1;
  bool self_duplicated = 2;
  bool is_offline_order = 3;
  string investment_entity = 4;
}

message CustomRecipientsReInvitationSent {
  repeated string recipients = 1 [(scalapb.field).type = "UserId"];
}

message CustomRecipientsReminderSent {
  repeated string recipients = 1 [(scalapb.field).type = "UserId"];
}

message LpReminded {
}

message LpRemindedToUploadSupportingDoc {
}

message LpJoined {
}

message CollaboratorInvited {
  string collaborator = 1 [(scalapb.field).type = "UserId"];
  bool is_offline_order = 2;
}

message CollaboratorJoined {
  string lp = 1 [(scalapb.field).type = "UserId"];
}

message CollaboratorRemoved {
  string collaborator = 1 [(scalapb.field).type = "UserId"];
}

message CollaboratorPromoted {
  string collaborator = 1 [(scalapb.field).type = "UserId"];
}

message FormEdited {
}

message SubscriptionMarkedAsReviewed {
}

message SignedSubscriptionMarkedAsApproved{
  string firm_name = 1;
}

message UnsignedSubscriptionMarkedAsApproved{
  string firm_name = 1;
}

message SkipSubscriptionReview {
}

message UnsignedSubscriptionDocReviewed {
  string firm_name = 1;
}

message SignedSubscriptionDocReviewed {
  string firm_name = 1;
}

// for LP side
message SignedSubscriptionDocument {
  repeated string requestIds = 1 [(scalapb.field).type = "SignatureRequestId"];
  bool signedByUploadingSignedDoc = 2;
}

message LpCancelledSubmission {
}

message LpSubmitForSoftReview {
  int32 versionIndex = 1;
}

message LpCancelSoftReview {
  int32 versionIndex = 1;
}

message LpRequestedSignature {
  repeated string signers = 1 [(scalapb.field).type = "UserId"];
}

message CancelledSubscriptionSignatureRequest {
  repeated string requestIds = 1 [(scalapb.field).type = "SignatureRequestId"];
}

message LpCancelledSigning {
  repeated string requestIds = 1 [(scalapb.field).type = "SignatureRequestId"];
}

message MarkSubDocRequestComplete {
}

message SupportingDocsGroup {
  string name = 1;
  repeated string fileIds = 2 [(scalapb.field).type = "FileId"];
}

message LpUploadedSupportingDocs {
  reserved 1;
  repeated SupportingDocsGroup filesGroup = 2;
}

message LpRemovedSupportingDocs {
  repeated string fileIds = 1 [(scalapb.field).type = "FileId"];
}

message SubmittedSubscriptionPackage {
  repeated FundSubFile files = 1;
}

message UndoneSubmission {
  bool refill_form_required = 1;
}

message LpRequestedToChange {
  bool refill_form_required = 1;
}
message SupportingDoc{
  string supportingDocName = 1;
  string supportingDocId = 2 [(scalapb.field).type = "Option[FundSubSupportingDocId]"];
}

message LpRequestedToUploadSupportingDoc {
  reserved 1, 2;
  repeated SupportingDoc supporting_docs = 3;
}

message LpRequestedToSignOnSupportingDoc {
  repeated string fileIds = 1 [(scalapb.field).type = "FileId"];
  repeated string signers = 2 [(scalapb.field).type = "UserId"];
  repeated string requestIds = 3 [(scalapb.field).type = "SignatureRequestId"];
}

message RemovedSupportingDocRequest {
  string supportingDocName = 1;
  string supportingDocId = 2 [(scalapb.field).type = "FundSubSupportingDocId"];
}

message RemovedSignatureRequestOnSupportingDoc {
  repeated string fileIds = 1 [(scalapb.field).type = "FileId"];
  repeated string signers = 2 [(scalapb.field).type = "UserId"];
  repeated string requestIds = 3 [(scalapb.field).type = "SignatureRequestId"];
}

message RemovedSupportingDoc {
  repeated string fileIds = 1 [(scalapb.field).type = "FileId"];
  string name = 2;
  string supportingDocId = 3 [(scalapb.field).type = "FundSubSupportingDocId"];
}

message RemovedAdditionalSupportingDocFile {
  string fileId = 1 [(scalapb.field).type = "FileId"];
}

message RemovedReferenceDocFile {
  string fileId = 1 [(scalapb.field).type = "FileId"];
}

message UploadReferenceDocs {
  repeated string fileIds = 1 [(scalapb.field).type = "FileId"];
}

message UploadedCounterSignDoc {
  repeated string fileIds = 1 [(scalapb.field).type = "FileId"];
}

message RemovedCounterSignDoc {
  repeated string fileIds = 1 [(scalapb.field).type = "FileId"];
}

message CounterEsigned {
}

message SentCounterSignRequests {
  repeated string signers = 1 [(scalapb.field).type = "UserId"];
  repeated string requestIds = 2 [(scalapb.field).type = "SignatureRequestId"];
}

message CancelledCounterSignRequests {
  repeated string requestIds = 1 [(scalapb.field).type = "SignatureRequestId"];
}

message SignedCounterSignRequest {
  bool signedByUploadingSignedDoc = 1;
  repeated string requestIds = 2 [(scalapb.field).type = "SignatureRequestId"];
}

message DistributedCounterSignedDoc {
  repeated string fileIds = 1 [(scalapb.field).type = "FileId"];
}

message UpdatedCounterSignedDoc {
  repeated string fileIds = 1 [(scalapb.field).type = "FileId"];
}

message LpInvitationEmailBounced {
  reserved 2;

  string userId = 1 [(scalapb.field).type = "UserId"];
}

message BouncedInvitationEmailResent {
  repeated string user_ids = 1 [(scalapb.field).type = "UserId"];
}

message LpInvitationEmailClicked {
  reserved 2;

  string userId = 1 [(scalapb.field).type = "UserId"];
}

// Note that we don't keep file ids here since it might not available for both LP and FA
// since those files might still be in signature request of LP team / FA team

message RequestedSignatureGeneral {
  string userId = 1 [(scalapb.field).type = "UserId"];
  string documentName = 2;
  repeated string signers = 3 [(scalapb.field).type = "UserId"];
  repeated string requestIds = 4 [(scalapb.field).type = "SignatureRequestId"];
}

message CancelledSignatureRequestGeneral {
  string userId = 1 [(scalapb.field).type = "UserId"];
  string documentName = 2;
  repeated string signers = 3 [(scalapb.field).type = "UserId"];
  repeated string requestIds = 4 [(scalapb.field).type = "SignatureRequestId"];
}

message SignedSignatureRequestGeneral {
  string userId = 1 [(scalapb.field).type = "UserId"];
  string documentName = 2;
  repeated string requestIds = 3 [(scalapb.field).type = "SignatureRequestId"];
}

message InvestmentEntityNameUpdated {
  string new_value = 1;
  bool synced_from_form = 2;
}

message RemovedAdditionalForm {
  string name = 1;
}

message TriggeringEventInfo {
  string triggering_app = 1 [(scalapb.field).type = "GlobalOfferingId"];
  TriggeringEventType triggering_event_type = 2;
  optional string failed_reason = 4;
}

enum TriggeringEventType {
  INVESTMENT_ENTITY_LINKED = 0;
  INVESTMENT_ENTITY_REMOVED = 1;
}

message InvestmentEntityLinked {
  string investment_entity_id = 1 [(scalapb.field).type = "InvestmentEntityId"];
  string investment_entity_name = 2;
}

message InvestmentEntityReplaced{
  string from_investment_entity_id = 1 [(scalapb.field).type = "InvestmentEntityId"];
  string from_investment_entity_name = 2;
  string to_investment_entity_id = 3 [(scalapb.field).type = "InvestmentEntityId"];
  string to_investment_entity_name = 4;
}

message InvestmentEntityRemoved{
  string investment_entity_id = 1 [(scalapb.field).type = "InvestmentEntityId"];
  string investment_entity_name = 2;
  optional TriggeringEventInfo triggering_event_info_opt = 3;
}

enum ReviewActionLog {
  REVIEW_ACTION_LOG_APPROVED = 0;
  REVIEW_ACTION_LOG_CHANGES_REQUESTED = 1;
  REVIEW_ACTION_LOG_CANCELED_CHANGES_REQUEST = 2;
}

message FundAdminReviewedDoc {
  reserved 1;
  ReviewActionLog action = 2;
  repeated string doc_types = 3;
}

enum SupportingDocAction {
  MarkAsNotApplicable = 0;
  UnMarkAsNotApplicable = 1;
  MarkAsProvided = 2;
  UnMarkAsProvided = 3;
}

message ActionOnSupportingDoc {
  SupportingDocAction action = 1;
  repeated string doc_types = 2;
}

message RecordTypeUnion {
  FundSubLpActivityModel _FundSubLpActivityModel = 1;
}

message AutoSaveEnabled {
  string investment_entity_id = 1 [(scalapb.field).type = "InvestmentEntityId"];
  string investment_entity_name = 2;
}

message AutoSaveDisabled {
  string investment_entity_id = 1 [(scalapb.field).type = "InvestmentEntityId"];
  string investment_entity_name = 2;
}

message DocumentAutoSaved {
  string investment_entity_id = 1 [(scalapb.field).type = "InvestmentEntityId"];
  string investment_entity_name = 2;
  repeated string fileIds = 3 [(scalapb.field).type = "FileId"];
}

message  SubscriptionDataAutoSaved {
  string investment_entity_id = 1 [(scalapb.field).type = "InvestmentEntityId"];
  string investment_entity_name = 2;
}

enum LpCommentActivityType {
  LEFT_COMMENT = 0;
  RESOLVED_COMMENT = 1;
  REOPENED_COMMENT = 2;
  DELETE_COMMENT = 3;
  DELETE_COMMENT_THREAD = 4;
}

enum LpCommentDocType {
  SUBSCRIPTION_DOC = 0;
  SUPPORTING_DOC = 1;
}

message LpCommentActivity {
  string actor = 1 [(scalapb.field).type = "UserId"];
  LpCommentActivityType comment_activity_type = 2;
  LpCommentDocType comment_doc_type = 3;
}

message LpCommentNotificationSent {
  string actor = 1 [(scalapb.field).type = "UserId"];
  repeated string recipients = 2 [(scalapb.field).type = "UserId"];
}

message DataExtractResultImportedToForm {
  reserved 1;
  string data_extract_request_id = 2 [(scalapb.field).type = "FundSubDataExtractRequestId"];
}

message SubscriptionMarkedAsComplete {
  string firm_name = 1;
}

message SignedOneEnvelopeRequest {
  string request_id = 1 [(scalapb.field).type = "SignatureRequestId"];
  repeated string file_ids = 3 [(scalapb.field).type = "FileId"];
}

message OneEnvelopeSupportingFormFiles {
  string doc_type = 1;
  repeated string file_ids = 2 [(scalapb.field).type = "FileId"];
}

message CreateOneEnvelopeRequest {
  string request_id = 1 [(scalapb.field).type = "SignatureRequestId"];
  repeated string subscription_file_ids = 2 [(scalapb.field).type = "FileId"];
  repeated OneEnvelopeSupportingFormFiles supporting_form_files = 3;
  repeated string uploaded_file_ids = 4 [(scalapb.field).type = "FileId"];
  repeated string signer_user_ids = 5 [(scalapb.field).type = "UserId"];
}

message DocumentTypeFillInfo {
  string document_type = 1;
  string file_id = 2 [(scalapb.field).type = "FileId"];
  string file_name = 3;
}

message ProfileFilled {
  string profile_id = 1 [(scalapb.field).type = "FundDataInvestmentEntityId"];
  string profile_name = 2;
  repeated DocumentTypeFillInfo documents = 3;
}