syntax = "proto3";

package fundsub;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.admin.report"
  single_file: true
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.model.common.user.UserId"
};

message NewLpReportEmailLog {
  string fundSubId = 1 [(scalapb.field).type = "FundSubId"];
  map<string, InstantMessage> lastEmailSentTimeLog = 2 [(scalapb.field).key_type = "UserId", (scalapb.field).value_type = "java.time.Instant"];
}

message RecordTypeUnion {
  NewLpReportEmailLog _NewLpReportEmailLog = 1;
}
