syntax = "proto3";

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
    package_name: "anduin.protobuf.fundsub.models.formfield"
    single_file: true
    import: "java.time.Instant"
};

message FormFieldFilterValue {
    oneof filter {
        NumberFormValueFilter number_form_value_filter = 1;
        StringFormValueFilter string_form_value_filter = 2;
        BooleanFormValueFilter boolean_form_value_filter = 3;
        DateTimeFormValueFilter date_time_form_value_filter = 4;
    }
}

message NumberFormValueFilter {
    oneof sealed_value {
        NumberFormValueFilterBetween number_form_value_filter_between = 1;
        NumberFormValueFilterNotBetween number_form_value_filter_not_between = 2;
        NumberFormValueFilterEmpty number_form_value_filter_empty = 3;
    }
}

message NumberFormValueFilterBetween {
    optional double from = 1;
    optional double to = 2;
}

message NumberFormValueFilterNotBetween {
    optional double from = 1;
    optional double to = 2;
}

message NumberFormValueFilterEmpty {
}

message StringFormValueFilter {
    oneof sealed_value {
        StringFormValueFilterContains string_form_value_filter_contains = 1;
        StringFormValueFilterNotContains string_form_value_filter_not_contains = 2;
        StringFormValueFilterEmpty string_form_value_filter_empty = 3;
    }
}

message StringFormValueFilterContains {
    repeated string values = 1;
}

message StringFormValueFilterNotContains {
    repeated string values = 1;
}

message StringFormValueFilterEmpty {
}

message BooleanFormValueFilter {
    oneof sealed_value {
        BooleanFormValueFilterValue boolean_form_value_filter_value = 1;
        BooleanFormValueFilterEmpty boolean_form_value_filter_empty = 2;
    }
}

message BooleanFormValueFilterValue {
    bool value = 1;
}

message BooleanFormValueFilterEmpty {
}

message DateTimeFormValueFilter {
    oneof sealed_value {
        DateTimeFormValueFilterBetween date_time_form_value_filter_between = 1;
        DateTimeFormValueFilterNotBetween date_time_form_value_filter_not_between = 2;
        DateTimeFormValueFilterEmpty date_time_form_value_filter_empty = 3;
    }
}

message DateTimeFormValueFilterBetween {
    InstantMessage from = 1 [(scalapb.field).type = "Instant"];
    InstantMessage to = 2 [(scalapb.field).type = "Instant"];
}

message DateTimeFormValueFilterNotBetween {
    InstantMessage from = 1 [(scalapb.field).type = "Instant"];
    InstantMessage to = 2 [(scalapb.field).type = "Instant"];
}

message DateTimeFormValueFilterEmpty {
}
