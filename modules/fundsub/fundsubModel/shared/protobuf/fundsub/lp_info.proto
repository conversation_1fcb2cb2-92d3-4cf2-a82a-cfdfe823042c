syntax = "proto3";

package anduin.protobuf.flow.fundsub.admin.lpdashboard;

import "date_time.proto";
import "fundsub/commitment.proto";
import "google/protobuf/wrappers.proto";
import "scalapb/scalapb.proto";
import "fundsub/models.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.flow.fundsub.admin.lpdashboard"
  import: "anduin.id.fundsub.FundSubCloseId"
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.id.fundsub.FundSubLpTagId"
  import: "anduin.id.fundsub.InvestmentFundId"
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.review.ReviewStepConfigId"
  import: "java.time.Instant"
  single_file: true
};

// It's important to keep lp status in correct flow order
enum LpStatus {
  reserved 5;
  LPNotStarted = 0;
  LPInProgress = 1;
  LPChangeInProgress = 11;
  LPPendingUnsignedReview = 10;
  LPFilledForm = 2;
  LPRequestedSignature = 8;
  LPSignedForm = 3;
  LPPendingSubmission = 13;
  LPPendingReview = 9;
  LPFormReviewed = 14;
  LPSubmitted = 4;
  LPCountersigned = 12;
  LPCompleted = 6;
  LPRemoved = 7;
}

enum LpActivityType {
  Invited = 0;
  Reminded = 1;
  ViewForm = 2;
  FillForm = 3;
  RequestSignature = 7;
  SignForm = 4;
  SubmitDocument = 5;
  DocumentApproved = 9;
  DocumentAccepted = 6;
}

enum LpInvitationType {
  Normal = 0;
  OfflineOrder = 1;
  InvestedInAdditionalFund = 2;
  ViaInvitationLink = 3;
}

enum LpDocRequestStatus {
  PendingSubmission = 0;
  ChangesInProgress = 1;
  PendingReview = 2;
  Complete = 3;
}

message LpActivity {
  LpActivityType activityType = 1;
  InstantMessage at = 2 [(scalapb.field).type = "java.time.Instant"];
}

message UserBasicInfo {
  option (scalapb.message).extends = "anduin.fundsub.model.UserBasicInfoTrait";

  string userId = 1 [(scalapb.field).type = "UserId"];
  string firstName = 2;
  string lastName = 3;
  string email = 4;
}

message LpInfoRecord {
  reserved 11, 12, 14, 31, 32;

  option (scalapb.message).extends = "anduin.fundsub.model.LpInfoRecordTrait";
  string lpId = 1 [(scalapb.field).type = "FundSubLpId"];
  string fundSubId = 2 [(scalapb.field).type = "FundSubId"];
  string userId = 3 [(scalapb.field).type = "UserId"];
  google.protobuf.StringValue fundSubCloseIdOpt = 4 [(scalapb.field).type = "FundSubCloseId"];
  bool isRemoved = 5;
  string firmName = 6;
  string firstName = 7;
  string lastName = 8;
  string email = 9;
  LpStatus status = 10;
  InstantMessage lastUpdatedAt = 13 [(scalapb.field).type = "Instant"];
  LpNotes lp_notes = 15;
  map<string, InstantMessage> statusStartedAt = 16 [(scalapb.field).value_type = "Instant"];
  repeated string tags = 17 [(scalapb.field).type = "FundSubLpTagId"];
  repeated string pendingCollaborators = 18 [(scalapb.field).type = "UserId"];
  bool lp_not_joined = 19;
  LpInvitationType invitationType = 20;
  InstantMessage createdAt = 21 [(scalapb.field).type = "Instant"];
  repeated string seenByAdmins = 22 [(scalapb.field).type = "UserId"];
  string customId = 23;
  repeated UserBasicInfo collaborators = 24;
  InstantMessage lastActiveAt = 25 [(scalapb.field).type = "Instant"];
  InstantMessage lastReminderSentAt = 26 [(scalapb.field).type = "Instant"];
  InstantMessage lastReinvitationSentAt = 27 [(scalapb.field).type = "Instant"];
  InstantMessage lastRemindSupportingDocAt = 35 [(scalapb.field).type = "Instant"];
  float form_filling_progress = 28;
  string documents_mark_as_reviewed_by = 29 [(scalapb.field).type = "Option[UserId]"];
  string documents_mark_review_skipped_by = 30 [(scalapb.field).type = "Option[UserId]"];
  string subscription_marked_as_complete_by = 39 [(scalapb.field).type = "Option[UserId]"];
  map<string, anduin.protobuf.fundsub.commitment.LpCommitment> commitments = 38 [(scalapb.field).key_type = "InvestmentFundId"];
  LpOrderType orderType = 33;
  bool hasPendingRequiredSupportingDoc = 34;
  string current_unsigned_subscription_pending_review_step_id = 36 [(scalapb.field).type = "Option[ReviewStepConfigId]"];
  string current_signed_subscription_pending_review_step_id = 37 [(scalapb.field).type = "Option[ReviewStepConfigId]"];
}

message LpNotes {
  string notes = 1;
  InstantMessage updated_at = 2 [(scalapb.field).type = "java.time.Instant"];
}

message RecordTypeUnion {
  LpInfoRecord _LpInfoRecord = 1;
}