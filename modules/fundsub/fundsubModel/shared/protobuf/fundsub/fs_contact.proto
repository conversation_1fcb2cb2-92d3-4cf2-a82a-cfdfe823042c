syntax = "proto3";

package anduin.protobuf.fundsub.contact;

import "date_time.proto";
import "google/protobuf/wrappers.proto";
import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub.contact"
  single_file: true
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.id.fundsub.FundSubContactGroupId"
  import: "anduin.model.common.user.UserId"
};

message FundSubContactGroup {
  string id = 1 [(scalapb.field).type = "FundSubContactGroupId"];
  string fundSubId = 3 [(scalapb.field).type = "FundSubId"];
  string name = 4;
  string custom_id = 7;

  string creator = 5 [(scalapb.field).type = "Option[UserId]"];
  InstantMessage createdAt = 6 [(scalapb.field).type = "java.time.Instant"];
  google.protobuf.StringValue mainLpEmailOpt = 8;
}

message RecordTypeUnion {
  FundSubContactGroup _FundSubContactGroup = 1;
}
