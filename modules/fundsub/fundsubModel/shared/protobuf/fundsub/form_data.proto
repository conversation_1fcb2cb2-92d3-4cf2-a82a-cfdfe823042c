syntax = "proto3";

package anduin.fundsub.model;

import "google/protobuf/wrappers.proto";
import "scalapb/scalapb.proto";
import "external/squants.proto";

option (scalapb.options) = {
  package_name: "anduin.dashboard.model"
  single_file: true
  lenses: false
  getters: false
  retain_source_code_info: false
  no_default_values_in_constructor: true
};

enum FormFieldValueType {
  StringType = 0;
  IntType = 1;
  FloatType = 2;
  BooleanType = 3;
  ArrayStringType = 4;
}

message FormFieldValueFormat {
  oneof sealed_value {
    TextFormat text_format = 1;
    MultipleLineTextFormat multiple_line_text_format = 2;
    IntegerFormat integer_format = 3;
    FloatFormat float_format = 4;
    DateFormat date_format = 5;
    MoneyFormat money_format = 6;
    PercentageFormat percentage_format = 7;
    BooleanFormat boolean_format = 8;
  }
}

message TextFormat {
}

message MultipleLineTextFormat {
}

message IntegerFormat {}

message FloatFormat {
  int32 decimal = 1;
}

message DateFormat {
  string format = 1;
}

message MoneyFormat {
  external.squants.CurrencyMessage unit = 1;
  int32 decimal = 2;
}

message PercentageFormat {
  int32 decimal = 1;
}

message BooleanFormat {
  string label_true = 1;
  string label_false = 2;
}

message FormFieldConfig {
  google.protobuf.StringValue namespace = 1;
  string alias = 2;
  FormFieldValueType value_type = 3;
  FormFieldValueFormat value_format = 4;
  string title = 5;
  string description = 6;
  bool use_label = 7;
}

