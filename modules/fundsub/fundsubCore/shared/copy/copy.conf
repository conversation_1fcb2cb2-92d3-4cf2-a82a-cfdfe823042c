{
  "default": {
    "both_side": {
      "_label": "Both side",
      "privacy_policy_link": {
        "_description": "",
        "_description_image": "",
        "_flow": "all",
        "_is_rich_text": false,
        "_label": "Privacy policy link in the footer",
        "_value": "https://www.anduintransact.com/privacy-policy"
      }
    },
    "continue_to_signature_btn_label": {
      "_description": "",
      "_description_image": "continue_to_signature_btn_label.jpg",
      "_flow": "restricted",
      "_is_rich_text": false,
      "_label": "Continue to signature",
      "_value": "Continue to signature page"
    },
    "continue_to_signature_confirm_modal_content": {
      "_description": "",
      "_description_image": "continue_to_signature_confirm_modal_content.jpg",
      "_flow": "restricted",
      "_is_rich_text": false,
      "_label": "Continue to signature confirm modal content",
      "_value": "You're about to proceed to the sign and submit step. You can return to the form at any time."
    },
    "continue_to_signature_confirm_modal_title": {
      "_description": "",
      "_description_image": "continue_to_signature_confirm_modal_title.jpg",
      "_flow": "restricted",
      "_is_rich_text": false,
      "_label": "Continue to signature confirm modal title",
      "_value": "Continue to signature step?"
    },
    "document_submitted_description": {
      "_description": "{0} is Entity Name",
      "_description_image": "document_submitted_description.jpg",
      "_flow": "restricted",
      "_is_rich_text": false,
      "_label": "Document submitted description",
      "_value": "{0} has received your signed document(s). We'll let you know when your submission has been accepted."
    },
    "document_submitted_header": {
      "_description": "",
      "_description_image": "document_submitted_header.jpg",
      "_flow": "restricted",
      "_is_rich_text": false,
      "_label": "Document submitted header",
      "_value": "All documents have been submitted"
    },
    "download_countersigned_doc_btn_label": {
      "_description": "",
      "_description_image": "download_countersigned_doc_btn_label.png",
      "_flow": "restricted",
      "_is_rich_text": false,
      "_label": "Download countersigned documents button label",
      "_value": "Download countersigned document(s)"
    },
    "download_countersigned_doc_description": {
      "_description": "",
      "_description_image": "",
      "_flow": "restricted",
      "_is_rich_text": false,
      "_label": "Download countersigned documents description",
      "_value": "Please find the countersigned subscription documents available to download below."
    },
    "download_subscription_doc_btn_label": {
      "_description": "",
      "_description_image": "download_subscription_doc_btn_label.jpg",
      "_flow": "restricted",
      "_is_rich_text": false,
      "_label": "Download subscription document button label",
      "_value": "Download subscription document(s)"
    },
    "fund_management_side": {
      "_label": "Fund management side",
      "dashboard": {
        "_label": "Dashboard",
        "dashboard_commitment_amount_column": {
          "_description": "",
          "_description_image": "dashboard_commitment_amount_column.jpg",
          "_flow": "all",
          "_is_rich_text": false,
          "_label": "Amount column wording",
          "_value": "Amount"
        },
        "fund_admin_dashboard_commitment_wording": {
          "_description": "",
          "_description_image": "commitment_word/invite_single_lp_modal.png,commitment_word/invite_multiple_lps_modal.png,commitment_word/lp_detail_amount_cell.png,commitment_word/reporting_tab.png",
          "_flow": "all",
          "_is_rich_text": false,
          "_label": "Commitment wording",
          "_value": "commitment"
        }
      },
      "modal": {
        "_label": "Modal",
        "add_investor_button": {
          "_label": "Add investor button",
          "investment_entity_field": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Investment entity field (x3 modals)",
            "_value": "Investment entity"
          }
        },
        "review_document_modal": {
          "_label": "Review document modal",
          "review_document_modal_warning": {
            "_description": "{0} is lp name or firm name with apostrophe",
            "_flow": "flexible",
            "_is_rich_text": true,
            "_label": "Soft review document modal warning description",
            "_value": "<p>You're about to approve <strong>{0}</strong> unsigned subscription documents. Reviewers in the next step will be notified by email to review and approve.</p>"
          },
          "review_document_modal_last_step_warning": {
            "_description": "{0} is lp name or firm name with apostrophe. {1} is status to be changed to",
            "_flow": "flexible",
            "_is_rich_text": true,
            "_label": "Soft review document modal last step warning",
            "_value": "<div class=\"space-y-4\"><div>You're about to approve {0} unsigned subscription documents.</div><div class=\"font-semi-bold space-y-4\"><div>Their status will be changed to \"{1}\"</div><div>They'll be notified by email to sign and submit their subscription</div></div></div>"
          },
          "approve_document_modal_warning": {
            "_description": "{0} is lp name or firm name with apostrophe",
            "_flow": "flexible",
            "_is_rich_text": true,
            "_label": "Hard review document modal warning description",
            "_value": "<p>You're about to approve <strong>{0}</strong> signed subscription documents. Reviewers in the next step will be notified by email to review and approve.</p>"
          },
          "approve_document_modal_last_step_warning": {
            "_description": "{0} is lp name or firm name with apostrophe. {1} is status to be changed to",
            "_flow": "flexible",
            "_is_rich_text": true,
            "_label": "Hard review document modal last step warning",
            "_value": "<p>You're about to approve <strong>{0}</strong> signed subscription documents. Their status will be changed to \"{1}\"</p>"
          }
        }
      },
      "reporting_tab": {
        "_label": "Reporting tab",
        "report_tab_capital_commitment_label": {
          "_description": "",
          "_description_image": "report_tab_capital_commitment_label.jpg",
          "_flow": "all",
          "_is_rich_text": false,
          "_label": "Capital commitment label",
          "_value": "Capital commitment"
        }
      },
      "settings_tab": {
        "_label": "Settings tab",
        "custom_fund_id_wording": {
          "_description": "",
          "_description_image": "",
          "_flow": "all",
          "_is_rich_text": false,
          "_label": "Custom Fund ID Wording",
          "_value": "Custom Fund ID"
        },
        "countersignature_email_wording": {
          "_description": "",
          "_flow": "all",
          "_is_rich_text": false,
          "_label": "Countersignature email wording",
          "_value": "Countersignature email"
        }
      }
    },
    "invest_from_addition_entity_modal_firm_name_warning": {
      "_description": "",
      "_description_image": "invest_from_addition_entity_modal_firm_name_warning.jpg",
      "_flow": "restricted",
      "_is_rich_text": false,
      "_label": "Firm name warning",
      "_value": "Please edit the name above to differentiate your investments from other entities"
    },
    "investor_access": {
      "_label": "Investor access",
      "investor_access_welcome_screen": {
        "_label": "Investor Access welcome screen",
        "fund_disclaimer": {
          "_description": "",
          "_description_image": "",
          "_flow": "flexible",
          "_is_rich_text": true,
          "_label": "Fund disclaimer",
          "_value": ""
        }
      }
    },
    "investor_side": {
      "_label": "Investor side",
      "dashboard_card": {
        "_label": "Dashboard card",
        "banner_well": {
          "_label": "Banner/Well",
          "approved_unsigned_sub_doc_content": {
            "_description": "Only visible when signing section is hide",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Reviewed (when hide signing section) - Description",
            "_value": "The fund reviewed the progress on your subscription to date and there are no changes requested."
          },
          "document_submitted_status_banner_description": {
            "_description": "",
            "_description_image": "document_submitted_status_banner_description.jpg",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Subscription document submitted with AML/KYC done - Description",
            "_value": "The fund will review your submission. You'll be notified by email once the subscription is accepted or if changes are required"
          },
          "document_submitted_status_banner_description_title": {
            "_description": "",
            "_description_image": "document_submitted_status_banner_description.jpg",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Subscription document submitted with AML/KYC done - Title",
            "_value": "Subscription form submitted"
          },
          "document_submitted_status_banner_description_button": {
            "_description": "",
            "_description_image": "document_submitted_status_banner_description.jpg",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Subscription document submitted with AML/KYC done - Button",
            "_value": "Download signed documents"
          },
          "document_submitted_without_aml_kyc_banner": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Subscription document submitted without AML/KYC done - Description",
            "_value": "The fund will review your signed documents. Please upload all required AML/KYC and other documents to complete your submission"
          },
          "document_submitted_without_aml_kyc_banner_title": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Subscription document submitted without AML/KYC done - Title",
            "_value": "You're almost done!"
          },
          "document_submitted_without_aml_kyc_banner_button": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Subscription document submitted without AML/KYC done - Button",
            "_value": "View required documents"
          },
          "pending_signature_banner": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Signature needed as last step - Description",
            "_value": "Please sign the subscription document(s) to complete your submission"
          },
          "pending_signature_banner_title": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Signature needed as last step - Title",
            "_value": "You're almost done!"
          },
          "pending_signature_banner_button": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Signature needed as last step - Button",
            "_value": "Add e-signature"
          },
          "pending_signature_request_banner_button": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Signature request resolve needed as last step - Button",
            "_value": "Sign or review pending requests"
          },
          "pending_submission_banner": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Manual submission needed as last step - Description",
            "_value": "Please submit your signed subscription document(s) to complete your subscription package"
          },
          "pending_submission_banner_title": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Manual submission needed as last step - Title",
            "_value": "You're almost done!"
          },
          "pending_submission_banner_button": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Manual submission needed as last step - Button",
            "_value": "Submit documents"
          },
          "subscription_package_accepted_banner_title": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Subscription package accepted by the fund - Title",
            "_value": "Subscription completed"
          },
          "subscription_package_accepted_banner": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Subscription package accepted by the fund - Description",
            "_value": "Your executed documents are ready to download"
          },
          "subscription_package_accepted_banner_button": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Subscription package accepted by the fund - Download Button",
            "_value": "Download subscription form"
          },
        },
        "fill_out_the_subscription_document": {
          "_label": "Fill out the subscription document(s)",
          "edit_subscription_button_title": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Edit form button",
            "_value": "Edit form"
          },
          "fill_sub_doc_description": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": true,
            "_label": "Initial - Description",
            "_value": "<p>Click <strong>Start filling</strong> below to begin your investor subscription.</p>"
          },
          "fill_sub_doc_change_requested_description": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Change requested - Description",
            "_value": "The fund requested an update to your form"
          },
          "fill_sub_doc_reviewed_description": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Reviewed - Description",
            "_value": "Your form has been reviewed"
          },
          "fill_sub_doc_form_prepared_description": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": true,
            "_label": "Form prepared - Description",
            "_value": "<p>The subscription form has been prepared for you by the fund managers. Click <strong>Start filling</strong> below to finish your form.</p>"
          },
          "fill_sub_doc_title": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Title",
            "_value": "Fill out the subscription document(s)"
          },
          "fill_sub_doc_submitted_for_review_email_instruction": {
            "_description": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Submitted for review email instruction",
            "_value": "We will notify you by email once your submitted form is reviewed."
          },
        },
        "investment_entity": {
          "_label": "Investment entity",
          "lp_firm_label": {
            "_description": "",
            "_description_image": "lp_firm_label.jpg",
            "_flow": "all",
            "_is_rich_text": false,
            "_label": "LP firm label",
            "_value": "Investment entity"
          }
        },
        "sign_the_subscriptions_document": {
          "_label": "Sign the subscriptions document(s)",
          "sign_sub_doc_title": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Title",
            "_value": "Sign the subscription document(s)"
          },
          "sign_subscription_documents_description": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": true,
            "_label": "In progress - Description",
            "_value": "<p>Once ready, you can e-sign your subscription by clicking <strong>Add e-signature</strong>. Alternatively, you can upload individual signature pages or the complete signed document below.</p>"
          },
          "signed_subscription_description": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Completed - Description",
            "_value": "Your signed documents have been submitted to the fund"
          },
          "sign_subscription_documents_description_submit_for_review": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Submitted for review - Description",
            "_value": "You'll be able to sign once the fund reviewed your submission"
          },
          "signing_description_before_lp_fill": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": true,
            "_label": "Initial - Description",
            "_value": "<p>You can start signing once all the required fields in your subscription document are filled.</p>"
          }
        },
        "submit_the_subscription_document": {
          "_label": "Submit the subscription document(s)",
          "submit_doc_description_switch_enabled": {
            "_description": "Available when the feature \"Only allow LP submission after both sub docs & AML/KYC docs were completed\" is enabled",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": true,
            "_label": "Initial - Description",
            "_value": "<p>Your signed subscription documents will appear here when available.</p>"
          },
          "submit_subscription_doc_title": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Title",
            "_value": "Submit the subscription document(s)"
          },
          "submit_subscription_docs_including_supporting_doc": {
            "_description": "Available when the feature \"Only allow LP submission after both sub docs & AML/KYC docs were completed\" is enabled",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": true,
            "_label": "Reminder of completing both sub docs & AML/KYC docs",
            "_value": "<p>Complete the <strong>subscription</strong> and <strong>AML/KYC sections</strong> above to submit your signed documents</p>"
          },
          "submit_subscription_documents_description": {
            "_description": "Available when the feature \"Only allow LP submission after both sub docs & AML/KYC docs were completed\" is disabled",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": true,
            "_label": "Initial - Description",
            "_value": "<p>Your signed subscription documents will appear here when ready to be submitted.</p>"
          }
        },
        "upload_aml_kyc_and_other_documents": {
          "_label": "Upload AML/KYC & other documents",
          "add_additional_tax_form_button_label": {
            "_description": "",
            "_description_image": "add_additional_tax_form_button_label.jpg,add_additional_tax_form_button_label_restricted_flow.jpg",
            "_flow": "all",
            "_is_rich_text": false,
            "_label": "Add additional form button",
            "_value": "Add form"
          },
          "upload_supporting_doc_section_description": {
            "_description": "",
            "_description_image": "upload_supporting_doc_section_description.png",
            "_flow": "flexible",
            "_is_rich_text": true,
            "_label": "Description",
            "_value": "Upload the required documents listed below, they'll be made available to your fund manager immediately."
          },
          "upload_supporting_doc_section_external_description": {
            "_description": "This copy config is set to {0} by default, which is order Id, when it is set to other value or empty the upload supporting doc section will only display title and this description and hide the rest of the UI",
            "_description_image": "upload_supporting_doc_section_description.png",
            "_flow": "flexible",
            "_is_rich_text": true,
            "_label": "Description for external integration",
            "_value": "{0}"
          },
          "upload_supporting_docs_title": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Title",
            "_value": "Upload AML/KYC & other documents"
          },
          "wet_signature_instruction_for_supporting_docs": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": true,
            "_label": "Wet signature instruction for supporting docs",
            "_value": "<div>The fund requires wet signatures on tax forms. Once you've filled the digital tax form, click <strong>Add signature</strong> under the form and follow the instruction to upload a signed version of the form.</div>"
          },
          "provided_supporting_doc_description": {
            "_description": "",
            "_description_image": "",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Description - Provided documents",
            "_value": "This document was added from the selected fund-shared profile"
          }
        }
      },
      "side_bar": {
        "_label": "Side bar",
        "comment_form_button": {
          "_description": "",
          "_flow": "flexible",
          "_is_rich_text": false,
          "_label": "Comment form button",
          "_value": "Subscription form"
        }
      },
      "general": {
        "_label": "General",
        "reminder_to_sign_after_subdoc_changes": {
          "_description": "This copy appears as a reminder in the Sign the subscription document(s) card (as a banner) and in the Form filling screen (in a modal) when data changes",
          "_description_image": "",
          "_flow": "flexible",
          "_is_rich_text": false,
          "_label": "Reminder to sign after making changes to subdoc",
          "_value": "Make sure to sign your subscription documents after making any changes."
        }
      },
      "homepage": {
        "_label": "Homepage",
        "homepage_lp_card_firm_label": {
          "_description": "",
          "_description_image": "",
          "_flow": "all",
          "_is_rich_text": false,
          "_label": "LP card firm label",
          "_value": "Investment entity"
        }
      },
      "modal": {
        "_label": "Modal",
        "finish_signing_subscription_document": {
          "_label": "Finish signing subscription documents",
          "finish_signing_subdoc_requester_modal_content": {
            "_description": "",
            "_description_image": "finish_signing_sub_doc_requester_modal_content.jpg",
            "_flow": "all",
            "_is_rich_text": false,
            "_label": "Content - Requester",
            "_value": "You won't be able to edit your signature on the documents. Other signers will be able to see your signature after you send the request on the next step.\n\nThe signed documents will automatically be shared with the fund managers once all signers have signed."
          },
          "finish_signing_subdoc_signer_modal_content": {
            "_description": "",
            "_description_image": "finish_signing_sub_doc_requester_modal_content.jpg",
            "_flow": "all",
            "_is_rich_text": false,
            "_label": "Content - Signer",
            "_value": "You won't be able to edit your signature on the documents. Other signers will be able to see your signature after you click Finish signing.\n\nThe signed documents will automatically be shared with the fund managers once all signers have signed."
          },
          "missing_required_fields_warning_modal_content": {
            "_description": "",
            "_description_image": "missing_required_fields_warning_modal_content.png",
            "_flow": "flexible",
            "_is_rich_text": false,
            "_label": "Missing required field(s) content",
            "_value": "We've noticed missing required fields or invalid inputs.\nThese fields are necessary to e-sign your subscription and generate the required AML/KYC documents.\n"
          },
          "before_upload_signed_documents_modal_content": {
            "_description": "",
            "_description_image": "",
            "_flow": "all",
            "_is_rich_text": true,
            "_label": "Before upload signed documents modal content",
            "_value": "Make sure you've reviewed, and agree with, the contents and terms within the subscription document before you submit it to the fund."
          }
        },
        "submit_for_review": {
          "_label": "Submit for review",
          "submit_for_review_modal_content_when_signature_block": {
            "_flow": "all",
            "_is_rich_text": true,
            "_label": "Submit for review modal content",
            "_value": "The fund will be notified that your form is ready for review."
          },
          "submit_for_review_modal_content": {
            "_flow": "all",
            "_is_rich_text": true,
            "_label": "Submit for review modal content",
            "_value": "The fund will be notified that your form is ready for review."
          }
        }
        "invest_from_additional_entity": {
          "_label": "Invest from additional entity",
          "invest_from_addition_entity_button_label": {
            "_description": "",
            "_description_image": "invest_from_addition_entity_button_label.jpg",
            "_flow": "all",
            "_is_rich_text": false,
            "_label": "Button label",
            "_value": "Invest from additional entity"
          },
          "invest_from_addition_entity_modal_title": {
            "_description": "",
            "_description_image": "invest_from_addition_entity_modal_title.jpg",
            "_flow": "all",
            "_is_rich_text": false,
            "_label": "Modal title",
            "_value": "Invest from additional entity"
          },
          "investment_entity_field_instruction": {
            "_description": "",
            "_flow": "all",
            "_is_rich_text": false,
            "_label": "Investment entity field instruction",
            "_value": "Please edit the name above to differentiate your investments from other entities"
          },
          "invest_from_addition_entity_modal_cta": {
            "_description": "",
            "_flow": "all",
            "_is_rich_text": false,
            "_label": "Modal title CTA",
            "_value": "Create"
          }
        },
        "manage_collaborator": {
          "_label": "Manage collaborator",
          "manage_collaborator_modal_desc": {
            "_description": "",
            "_description_image": "manage_collaborator_modal_desc_1.jpg,manage_collaborator_modal_desc_2.jpg",
            "_flow": "all",
            "_is_rich_text": false,
            "_label": "Manage collaborator modal description",
            "_value": "Collaborators can edit the form's contents, sign, and submit subscription documents."
          },
          "manage_collaborator_modal_title": {
            "_description": "{0} is represent Investor Name",
            "_description_image": "manage_collaborator_modal_title_1.jpg,manage_collaborator_modal_title_2.jpg",
            "_flow": "all",
            "_is_rich_text": false,
            "_label": "Manage collaborator modal title",
            "_value": " {0}'s Collaborators"
          },
          "disabled_invite_collaborator_button_tooltip": {
            "_description": "Tooltip for invite collaborator button when it is disabled (by uncheck the switch \"Enable ability of investor to invite collaborator\")",
            "_flow": "all",
            "_is_rich_text": false,
            "_label": "Disabled invite collaborator button tooltip",
            "_value": "You can't invite collaborators at the moment. Please contact your fund manager for more information"
          }
        }
      }
    },
    "submission_accepted_header": {
      "_description": "{0} is represent Entity",
      "_description_image": "submission_accepted_header.png",
      "_flow": "restricted",
      "_is_rich_text": false,
      "_label": "Submission accepted header",
      "_value": "{0} accepted your submission."
    },
    "submission_additional_document_instruction": {
      "_description": "",
      "_description_image": "submission_additional_document_instruction.jpg",
      "_flow": "restricted",
      "_is_rich_text": false,
      "_label": "Submission additional document instruction",
      "_value": "Sign and/or upload the additional documents below. Once submitted, they'll be made available to the fund manager."
    },
    "submission_subscription_document_header": {
      "_description": "",
      "_description_image": "submission_subscription_document_header.jpg",
      "_flow": "restricted",
      "_is_rich_text": false,
      "_label": "Submit subscription document header",
      "_value": "Subscription document(s)"
    },
    "submission_subscription_document_instruction": {
      "_description": "",
      "_description_image": "submission_subscription_document_instruction.jpg",
      "_flow": "restricted",
      "_is_rich_text": false,
      "_label": "Submission subscription document instruction",
      "_value": "Please sign the subscription document(s) below. Once signed, it'll be made available to the fund manager."
    }
  }
}
