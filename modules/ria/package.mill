package build.modules.ria

import build_.modules.ria.dependency_.Ria
import build_.build.util_.*
import build.modules.{fundsub, heimdall}

import mill.*
import mill.scalalib.*
import anduin.build.*
import anduin.mill.*

object `package` extends Module {

  object riaModel extends StargazerModelCrossPlatformModule {

    object jvm extends JvmModelModule with ScalaModule {
      override def moduleDeps = super.moduleDeps ++ Seq(build.gondor.gondorModel.jvm)
    }

    object js extends JsModelModule with ScalaModule {
      override def moduleDeps = super.moduleDeps ++ Seq(build.gondor.gondorModel.js)
    }

  }

  object riaCore extends AnduinCrossPlatformModule {

    object jvm extends JvmModule with ScalaModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        build.gondor.gondorCore.jvm,
        riaModel.jvm
      )

    }

    object js extends JsModule with ScalaModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        build.gondor.gondorCore.js,
        riaModel.js
      )

    }

  }

  object ria extends AnduinCrossPlatformModule {

    object jvm extends JvmModule with ScalaModule {
      override def moduleDeps = super.moduleDeps ++ Seq(riaCore.jvm, fundsub.fundsubCore.jvm)

      object it extends AnduinZioTests with AnduinIntegTests {
        override def moduleDeps = super.moduleDeps ++ Seq(fundsub.fundsubTest, heimdall.heimdall.jvm)
      }

    }

    object js extends JsModule with ScalaModule {
      override def moduleDeps = super.moduleDeps ++ Seq(riaCore.js, fundsub.fundsubCore.js)
    }

  }

  object riaApp extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {
      override def moduleDeps = super.moduleDeps ++ Seq(ria.jvm)
    }

    object js extends JsModule with AnduinWebClientModule {
      override def webModule = AnduinWebModules.Ria

      override def mvnDeps = super.mvnDeps() ++ Ria.jsDeps
      override def moduleDeps = super.moduleDeps ++ Seq(ria.js)

      override def mainClass = Some("anduin.ria.client.RiaMainApp")
    }

  }

}