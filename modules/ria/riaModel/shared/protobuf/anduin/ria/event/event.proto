syntax = "proto3";

package anduin.protobuf.ria.event;

import "scalapb/scalapb.proto";

import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.ria.event"
  single_file: true
  import: "anduin.id.ria.RiaEntityId"
  import: "anduin.model.common.user.UserId"
  import: "java.time.Instant"
  preamble: [
    "sealed trait RiaEntityEventBaseTrait {",
    "  def riaEntityId: RiaEntityId",
    "  def actor: UserId",
    "  def createdAt: Option[Instant]",
    "}",
    "sealed trait RiaEntityEventEmptyTrait {",
    "  def riaEntityId: RiaEntityId = RiaEntityId.defaultValue.get",
    "  def actor: UserId = UserId.defaultValue.get",
    "  def createdAt: Option[Instant] = None",
    "}"
  ]
};


message RiaEntityEvent {
  option (scalapb.message).sealed_oneof_extends = "RiaEntityEventBaseTrait";
  option (scalapb.message).sealed_oneof_empty_extends = "RiaEntityEventEmptyTrait";
  
  oneof sealed_value {
    RiaEntityCreatedEvent ria_entity_created_event = 1;
    RiaEntityUserInvitedEvent ria_entity_user_invited_event = 2;
    RiaEntityUserInvitationRevokedEvent ria_entity_user_invitation_revoked_event = 3;
    RiaEntityUserRoleUpdatedEvent ria_entity_user_role_updated_event = 4;

    RiaFundGroupCreatedWithoutAdminEvent ria_fund_group_created_without_admin_event = 100;
    RiaFundGroupUserInvitedEvent ria_fund_group_user_invited_event = 101;
    RiaFundGroupUserInvitationRevokedEvent ria_fund_group_user_invitation_revoked_event = 102;
    RiaFundGroupUserRoleUpdatedEvent ria_fund_group_user_role_updated_event = 103;
  }
}

message RiaEntityCreatedEvent {
  string ria_entity_id = 1 [(scalapb.field).type = "RiaEntityId"];
  string actor = 2 [(scalapb.field).type = "UserId"];
  InstantMessage created_at = 3 [(scalapb.field).type = "Instant"];
}

message RiaEntityUserInvitedEvent {
  string ria_entity_id = 1 [(scalapb.field).type = "RiaEntityId"];
  string actor = 2 [(scalapb.field).type = "UserId"];
  InstantMessage created_at = 3 [(scalapb.field).type = "Instant"];
}

message RiaEntityUserInvitationRevokedEvent {
  string ria_entity_id = 1 [(scalapb.field).type = "RiaEntityId"];
  string actor = 2 [(scalapb.field).type = "UserId"];
  InstantMessage created_at = 3 [(scalapb.field).type = "Instant"];
}

message RiaEntityUserRoleUpdatedEvent {
  string ria_entity_id = 1 [(scalapb.field).type = "RiaEntityId"];
  string actor = 2 [(scalapb.field).type = "UserId"];
  InstantMessage created_at = 3 [(scalapb.field).type = "Instant"];
}

message RiaFundGroupCreatedWithoutAdminEvent {
  string ria_entity_id = 1 [(scalapb.field).type = "RiaEntityId"];
  string actor = 2 [(scalapb.field).type = "UserId"];
  InstantMessage created_at = 3 [(scalapb.field).type = "Instant"];
}

message RiaFundGroupUserInvitedEvent {
  string ria_entity_id = 1 [(scalapb.field).type = "RiaEntityId"];
  string actor = 2 [(scalapb.field).type = "UserId"];
  InstantMessage created_at = 3 [(scalapb.field).type = "Instant"];
}

message RiaFundGroupUserInvitationRevokedEvent {
  string ria_entity_id = 1 [(scalapb.field).type = "RiaEntityId"];
  string actor = 2 [(scalapb.field).type = "UserId"];
  InstantMessage created_at = 3 [(scalapb.field).type = "Instant"];
}

message RiaFundGroupUserRoleUpdatedEvent {
  string ria_entity_id = 1 [(scalapb.field).type = "RiaEntityId"];
  string actor = 2 [(scalapb.field).type = "UserId"];
  InstantMessage created_at = 3 [(scalapb.field).type = "Instant"];
}
