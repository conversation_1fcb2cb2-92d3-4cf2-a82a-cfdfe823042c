package build.modules.ria

import anduin.build.AnduinVersions
import mill.scalalib.*

object Ria {

  lazy val jsDeps = Seq(
    mvn"be.doeraene::url-dsl::${AnduinVersions.urlDsl}",
    mvn"com.raquo::waypoint::${AnduinVersions.waypoint}"
      .exclude("com.raquo" -> AnduinVersions.j2sjs("laminar"))
      .exclude("com.raquo" -> AnduinVersions.j2sjs("airstream"))
      .exclude("be.doeraene" -> AnduinVersions.j2sjs("url-dsl"))
      .exclude("org.scala-js" -> AnduinVersions.j2sjs("scalajs-dom"))
  )

}
