package build.modules.investorProfile

import build_.build.util_.*
import build.modules.{fundsub, gaia, bifrost, fundsubFormData, heimdall}
import build.platform.stargazerCore

import mill.*
import anduin.build.*
import anduin.mill.*

object `package` extends Module {

  object investorProfileModel extends StargazerModelCrossPlatformModule {

    object jvm extends JvmModelModule {
      override def moduleDeps = super.moduleDeps ++ Seq(fundsub.fundsubModel.jvm)

    }

    object js extends JsModelModule {

      override def moduleDeps = super.moduleDeps ++ Seq(fundsub.fundsubModel.js)

    }

  }

  object investorProfileCore extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {
      override def moduleDeps = super.moduleDeps ++ Seq(investorProfileModel.jvm, gaia.gaia.jvm)
    }

    object js extends JsModule {

      override def moduleDeps =
        super.moduleDeps ++ Seq(stargazerCore.js, gaia.gaia.js, investorProfileModel.js)

    }

  }

  object investorProfile extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        investorProfileCore.jvm,
        fundsubFormData.fundsubFormData,
        build.gondor.gondorCore.jvm,
        fundsub.fundsubCore.jvm
      )

    }

    object js extends JsModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        bifrost.js,
        investorProfileCore.js,
        gaia.gaiaModel.js,
        build.gondor.gondorCore.js
      )

    }

  }

  object investorProfileSubscription extends AnduinScalaModule {
    override def moduleDeps = super.moduleDeps ++ Seq(fundsub.fundsub.jvm)

    object it extends AnduinZioTests with AnduinIntegTests {
      override def moduleDeps = super.moduleDeps ++ Seq(fundsub.fundsubTest, heimdall.heimdall.jvm)
    }

  }

  object investorProfileApp extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {
      override def moduleDeps = super.moduleDeps ++ Seq(investorProfile.jvm)
    }

    object js extends JsModule with AnduinWebClientModule {
      override def webModule = AnduinWebModules.LpProfile

      override def moduleDeps = super.moduleDeps ++ Seq(investorProfile.js)

      override def mainClass = Some("anduin.investorprofile.client.LpProfileMainApp")
    }

  }

}
