syntax = "proto3";

package anduin.protobuf.investorprofile.investmententity.associatedsubscription;

import "scalapb/scalapb.proto";
import "date_time.proto";
import "google/protobuf/wrappers.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.investorprofile.investmententity.associatedsubscription"
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.investmententity.InvestmentEntityId"
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.model.common.user.UserId"
  import: "java.time.Instant"
};

message SubscriptionInvestmentEntityLinkingModel {
  string subscription_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string entity_id = 2 [(scalapb.field).type = "InvestmentEntityId"];
  string entity_name = 3;
  google.protobuf.StringValue entity_custom_id = 4;
  string linked_by = 5 [(scalapb.field).type = "UserId"];
  InstantMessage linked_at = 6 [(scalapb.field).type = "Instant"];
  bool enable_auto_save_data = 7;
}

message RecordTypeUnion {
  SubscriptionInvestmentEntityLinkingModel _SubscriptionInvestmentEntityLinkingModel = 1;
}
