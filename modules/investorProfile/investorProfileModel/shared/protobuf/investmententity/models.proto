syntax = "proto3";

package anduin.protobuf.investorprofile.investmententity;

import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.investorprofile.investmententity"
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.id.investmententity.InvestmentEntityId"
  import: "java.time.Instant"
  import: "anduin.model.id.TeamId"
  import: "anduin.id.funddata.FundDataFirmId"
};

message InvestmentEntityModel {
  string id = 1 [(scalapb.field).type = "InvestmentEntityId"];
  string name = 2;
  google.protobuf.StringValue custom_id = 3;
  InvestmentEntityState state = 4;
  string created_by = 7 [(scalapb.field).type = "Option[UserId]"];
  InstantMessage created_at = 5 [(scalapb.field).type = "Instant"];
  InstantMessage deleted_at = 6 [(scalapb.field).type = "Instant"];
  DemoInfo demo_info = 8;
  string team_id = 9 [(scalapb.field).type = "TeamId"];
}

message DemoInfo {
  string demo_fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
  optional string demo_firm_id_opt = 2 [(scalapb.field).type = "FundDataFirmId"];
}

enum InvestmentEntityState {
  Active = 0;
  Deleted = 1;
}

message RecordTypeUnion {
  InvestmentEntityModel _InvestmentEntityModel = 1;
}
