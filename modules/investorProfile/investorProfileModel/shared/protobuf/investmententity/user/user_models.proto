syntax = "proto3";

package anduin.protobuf.investorprofile.investmententity.user;

import "investmententity/permissions.proto";
import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.investorprofile.investmententity.user"
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.investmententity.InvestmentEntityId"
  import: "anduin.model.common.user.UserId"
  import: "java.time.Instant"
};

message InvestmentEntityUserModel {
  string entity_id = 1 [(scalapb.field).type = "InvestmentEntityId"];
  string user_id = 2 [(scalapb.field).type = "UserId"];
  InvestmentEntityUserState user_state = 3;
  InvestmentEntityPermissionLevel permission_level = 4;
  string inviter_user_id = 5 [(scalapb.field).type = "UserId"];
  InstantMessage invited_at = 6 [(scalapb.field).type = "Instant"];
  InstantMessage last_reminded_at = 7 [(scalapb.field).type = "Instant"];
  InstantMessage joined_at = 8 [(scalapb.field).type = "Instant"];
  InstantMessage removed_at = 9 [(scalapb.field).type = "Instant"];
}

enum InvestmentEntityUserState {
  Invited = 0;
  Joined = 1;
  Removed = 2;
  EmailBounced = 4;
  InvitationCancelled = 5;
  InvitationDeclined = 6;
}

message RecordTypeUnion {
  InvestmentEntityUserModel _InvestmentEntityUserModel = 1;
}
