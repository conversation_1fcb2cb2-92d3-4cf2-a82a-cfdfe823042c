syntax = "proto3";

package anduin.protobuf.investorprofile.investmententity.subscription;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.investorprofile.investmententity.subscription"
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.investmententity.InvestmentEntityId"
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.model.common.user.UserId"
  import: "java.time.Instant"
};

message InvestmentEntitySubscriptionLinkingModel {
  string entity_id = 1 [(scalapb.field).type = "InvestmentEntityId"];
  string subscription_id = 2 [(scalapb.field).type = "FundSubLpId"];
  string fund_name = 3;
  string subscription_name = 4;
  string linked_by = 5 [(scalapb.field).type = "UserId"];
  InstantMessage linked_at = 6 [(scalapb.field).type = "Instant"];
  InvestmentEntitySubscriptionLinkingState linking_state = 7;
}

message InvestmentEntitySubscriptionLinkingState {
  oneof sealed_value {
    InvestmentEntitySubscriptionLinkingActiveState active_state = 1;
    InvestmentEntitySubscriptionLinkingDeletedState deleted_state = 2;
  }
}

message InvestmentEntitySubscriptionLinkingActiveState {}

message InvestmentEntitySubscriptionLinkingDeletedState {
  InstantMessage deleted_at = 1 [(scalapb.field).type = "Instant"];
}

message RecordTypeUnion {
  InvestmentEntitySubscriptionLinkingModel _InvestmentEntitySubscriptionLinkingModel = 1;
}
