syntax = "proto3";

package anduin.protobuf.auditlog.usertracking;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.investorprofile.auditlog.usertracking"
  single_file: true
  import: "anduin.id.investmententity.InvestorProfileAuditLogId"
  import: "anduin.model.common.user.UserId"
};


message UserAuditLogTrackingModel {
  string log_id = 1 [(scalapb.field).type = "InvestorProfileAuditLogId"];
  string user_id = 2 [(scalapb.field).type = "UserId"];
  int32 tracking_number = 3;
}

message RecordTypeUnion {
  UserAuditLogTrackingModel _UserAuditLogTrackingModel = 1;
}
