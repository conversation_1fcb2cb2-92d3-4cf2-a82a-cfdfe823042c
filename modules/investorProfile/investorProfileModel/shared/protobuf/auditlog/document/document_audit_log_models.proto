syntax = "proto3";

package anduin.protobuf.auditlog.document;

import "scalapb/scalapb.proto";
import "date_time.proto";
import "auditlog/audit_log_utils.proto";
import "auditlog/document/document_log_detail.proto";

option (scalapb.options) = {
    package_name: "anduin.protobuf.investorprofile.auditlog.document"
    single_file: true
    import: "anduin.id.investmententity.InvestorProfileAuditLogId"
    import: "anduin.model.common.user.UserId"
    import: "anduin.model.id.FileId"
    import: "java.time.Instant"
};


message DocumentAuditLogModel {
    string log_id = 1 [(scalapb.field).type = "InvestorProfileAuditLogId"];
    int32 event_order = 2;
    InstantMessage timestamp = 3 [(scalapb.field).type = "Instant"];
    string ip_address = 4;
    string actor = 5 [(scalapb.field).type = "Option[UserId]"];
    DocumentAuditLogEventType event_type = 6;
    DocumentLogDetail log_detail = 7;
    string document_file_id = 8 [(scalapb.field).type = "FileId"];
}

message RecordTypeUnion {
    DocumentAuditLogModel _DocumentAuditLogModel = 1;
}
