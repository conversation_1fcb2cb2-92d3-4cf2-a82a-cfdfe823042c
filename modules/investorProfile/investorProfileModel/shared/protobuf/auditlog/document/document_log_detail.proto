syntax = "proto3";

package anduin.protobuf.auditlog.document;

import "date_time.proto";
import "scalapb/scalapb.proto";

option (scalapb.options) = {
    package_name: "anduin.protobuf.investorprofile.auditlog.document"
    single_file: true
    preserve_unknown_fields: false
};


message DocumentLogDetail {
    oneof sealed_value {
        UploadDocument upload_document = 1;
        AddDocumentNote add_document_note = 2;
        SetDocumentExpirationDate set_document_expiration_date = 3;
        DeleteDocument delete_document = 4;
        RenameDocument rename_document = 5;
        EditDocumentNote edit_document_note = 6;
        EditDocumentExpiration edit_document_expiration = 7;
        DownloadDocument download_document = 8;
        SaveFromFund save_from_fund = 9;
    }
}

message UploadDocument {
    string document_name = 1;
}

message AddDocumentNote {
    string document_name = 1;
    string document_note = 2;
}

message SetDocumentExpirationDate {
    string document_name = 1;
    LocalDateMessage document_expiration_date = 2;
}

message DeleteDocument {
    string document_name = 1;
}

message RenameDocument {
    string document_new_name = 1;
    string document_old_name = 2;
}

message EditDocumentNote {
    string document_name = 1;
    string document_new_note = 2;
}

message EditDocumentExpiration {
    string document_name = 1;
    LocalDateMessage document_new_expiration = 2;
}

message DownloadDocument {
    string document_name = 1;
}

message SaveFromFund {
    string document_name = 1;
    string fund_name = 2;
    bool is_auto_saved = 3;
}
