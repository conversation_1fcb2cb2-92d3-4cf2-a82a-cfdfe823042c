syntax = "proto3";

package anduin.protobuf.auditlog.investmententity;

import "scalapb/scalapb.proto";
import "date_time.proto";
import "auditlog/audit_log_utils.proto";
import "auditlog/investmententity/investment_entity_log_detail.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.investorprofile.auditlog.investmententity"
  single_file: true
  import: "anduin.id.investmententity.InvestorProfileAuditLogId"
  import: "anduin.model.common.user.UserId"
  import: "java.time.Instant"
};


message InvestmentEntityAuditLogModel {
  string log_id = 1 [(scalapb.field).type = "InvestorProfileAuditLogId"];
  int32 event_order = 2;
  InstantMessage timestamp = 3 [(scalapb.field).type = "Instant"];
  string ip_address = 4;
  string actor = 5 [(scalapb.field).type = "Option[UserId]"];
  InvestmentEntityAuditLogEventType event_type = 6;
  InvestmentEntityLogDetail log_detail = 7;
}

message RecordTypeUnion {
  InvestmentEntityAuditLogModel _InvestmentEntityAuditLogModel = 1;
}
