syntax = "proto3";

package anduin.protobuf.auditlog.investmententity;

import "scalapb/scalapb.proto";
import "investmententity/permissions.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.investorprofile.auditlog.investmententity"
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.id.offering.GlobalOfferingId"
};


message InvestmentEntityLogDetail {
  reserved 9, 10;
  oneof sealed_value {
    InvestmentEntityCreate investment_entity_create = 1;
    InvestmentEntityRename investment_entity_rename = 2;
    InvestmentEntityChangeCustomId investment_entity_change_custom_id = 3;
    RoleGrant permission_grant = 4;
    RoleEdit permission_revoke = 5;
    RoleRevoke permission_edit = 6;
    SubscriptionLinked subscription_linked = 7;
    SubscriptionRemoved subscription_removed = 8;
    AutoSaveEnabled auto_save_enabled = 11;
    AutoSaveDisabled auto_save_disabled = 12;
  }
}

message InvestmentEntityCreate{
  string investment_entity_name = 1;
}

message InvestmentEntityRename{
  string investment_entity_from_name = 1;
  string investment_entity_to_name = 2;
}

message InvestmentEntityChangeCustomId{
  string investment_entity_from_custom_id = 1;
  string investment_entity_to_custom_id = 2;
}

message RoleGrant{
  string user = 1 [(scalapb.field).type = "UserId"];
  protobuf.investorprofile.investmententity.InvestmentEntityPermissionLevel granted_permission_level = 2;
}

message RoleEdit{
  string user = 1 [(scalapb.field).type = "UserId"];
  protobuf.investorprofile.investmententity.InvestmentEntityPermissionLevel from_permission_level = 2;
  protobuf.investorprofile.investmententity.InvestmentEntityPermissionLevel to_permission_level = 3;
}

message RoleRevoke{
  string user = 1 [(scalapb.field).type = "UserId"];
}

// 3 cases:
// Link in IE app
// Link by syncing from FS app
// Link by syncing from FS app but fail due to already existed
message SubscriptionLinked{
  reserved 5;
  string user = 1 [(scalapb.field).type = "UserId"];
  string subscription_id = 2 [(scalapb.field).type = "FundSubLpId"];
  string fund_name = 3;
  string subscription_name = 4;
  optional TriggeringEventInfo triggering_event_info_opt = 6;
}

// Remove in IE app
// Remove by syncing from FS
// Remove by syncing from FS but fail due to permission
message SubscriptionRemoved{
  reserved 5;
  string user = 1 [(scalapb.field).type = "UserId"];
  string subscription_id = 2 [(scalapb.field).type = "FundSubLpId"];
  string fund_name = 3;
  string subscription_name = 4;
  optional TriggeringEventInfo triggering_event_info_opt = 6;
}

message TriggeringEventInfo {
  string triggering_app = 1 [(scalapb.field).type = "GlobalOfferingId"];
  TriggeringEventType triggering_event_type = 2;
  optional string failed_reason = 4;
}

enum TriggeringEventType {
  SUBSCRIPTION_LINKED = 0;
  SUBSCRIPTION_REMOVED = 1;
}

message AutoSaveEnabled {
  string subscription_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string fund_name = 2;
  string firm_name = 3;
}

message AutoSaveDisabled {
  string subscription_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string fund_name = 2;
  string firm_name = 3;
}
