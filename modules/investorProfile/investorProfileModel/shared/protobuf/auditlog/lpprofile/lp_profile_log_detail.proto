syntax = "proto3";

package anduin.protobuf.auditlog.lpprofile;

import "scalapb/scalapb.proto";
import "google/protobuf/struct.proto";


option (scalapb.options) = {
  package_name: "anduin.protobuf.investorprofile.auditlog.lpprofile"
  single_file: true
  preserve_unknown_fields: false
  import: "io.circe.Json"
  import: "anduin.model.id.FileId"
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.model.codec.ProtoCodecs.given"
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.permission.AccessTypeId"
  import: "anduin.id.form.FormVersionId"
  import: "anduin.id.docrequest.FormSubmissionId"
};

message LpProfileLogDetail {
  oneof sealed_value {
    FieldUpdate field_update = 1;
    FieldCleared field_cleared = 2;
    PermissionGrant permission_grant = 3;
    PermissionEdit permission_revoke = 4;
    PermissionRevoke permission_edit = 5;
    ProfileClear profile_clear = 6;
  }
}

message ProfileClear {}

message FieldUpdate{
  string field_alias = 1;
  repeated string field_path = 2;
  string profile_template_id = 3 [(scalapb.field).type = "FormVersionId"];
  google.protobuf.Value from_value = 4 [(scalapb.field).type = "Json"];
  google.protobuf.Value to_value = 5 [(scalapb.field).type = "Json"];
  google.protobuf.Value from_value_formatted_text = 6 [(scalapb.field).type = "Json"];
  google.protobuf.Value to_value_formatted_text = 7 [(scalapb.field).type = "Json"];
}

message FieldCleared{
  string field_alias = 1;
  repeated string field_path = 2;
  string profile_template_id = 3 [(scalapb.field).type = "FormVersionId"];
  google.protobuf.Value last_value = 4 [(scalapb.field).type = "Json"];
  google.protobuf.Value last_value_formatted_text = 5 [(scalapb.field).type = "Json"];
}

message PermissionGrant{
  string user = 1 [(scalapb.field).type = "UserId"];
  string granted_permission_level = 2 [(scalapb.field).type = "AccessTypeId"];
}

message PermissionEdit{
  string user = 1 [(scalapb.field).type = "UserId"];
  string from_permission_level = 2 [(scalapb.field).type = "AccessTypeId"];
  string to_permission_level = 3 [(scalapb.field).type = "AccessTypeId"];
}

message PermissionRevoke{
  string user = 1 [(scalapb.field).type = "UserId"];
}


message UpdateSource {
  oneof sealed_value {
    ManualEdit manual_edit = 1;
    ImportFromFundSub import_from_fund_sub = 2;
    ImportFromFile import_from_file = 3;
    Api api = 4;
    AutoSaveFromFundSub auto_save_from_fund_sub = 5;
  }
}

message ManualEdit {}
message ImportFromFundSub {
  string fund_name = 1;
  string fund_sub_lp_id = 2 [(scalapb.field).type = "FundSubLpId"];
  bool is_saving_main_form = 3;
  repeated string supporting_forms = 4 [(scalapb.field).type = "FormSubmissionId"];
}
message ImportFromFile {
  string file_name = 1;
  string file_id = 2 [(scalapb.field).type = "FileId"];
}
message AutoSaveFromFundSub{
  string fund_name = 1;
  string fund_sub_lp_id = 2 [(scalapb.field).type = "FundSubLpId"];
  bool is_saving_main_form = 3;
  repeated string supporting_forms = 4 [(scalapb.field).type = "FormSubmissionId"];
}
message Api {}
