syntax = "proto3";

package anduin.protobuf.auditlog.lpprofile;

import "scalapb/scalapb.proto";
import "date_time.proto";
import "auditlog/audit_log_utils.proto";
import "auditlog/lpprofile/lp_profile_log_detail.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.investorprofile.auditlog.lpprofile"
  single_file: true
  import: "anduin.id.investmententity.InvestorProfileAuditLogId"
  import: "anduin.model.common.user.UserId"
  import: "java.time.Instant"
};


message LpProfileAuditLogModel {
  string log_id = 1 [(scalapb.field).type = "InvestorProfileAuditLogId"];
  int32 event_order = 2;
  InstantMessage timestamp = 3 [(scalapb.field).type = "Instant"];
  string ip_address = 4;
  string actor = 5 [(scalapb.field).type = "Option[UserId]"];
  UpdateSource source = 6;
  LpProfileAuditLogEventType event_type = 7;
  LpProfileLogDetail log_detail = 8;
  bool is_important_event = 9;
}

message RecordTypeUnion {
  LpProfileAuditLogModel _LpProfileAuditLogModel = 1;
}
