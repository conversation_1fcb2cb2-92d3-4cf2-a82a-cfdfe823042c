syntax = "proto3";

package anduin.protobuf.auditlog.lpprofilelogtracking;

import "scalapb/scalapb.proto";
import "lpprofile/common.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.investorprofile.auditlog.lpprofilelogtracking"
  single_file: true
  import: "anduin.id.investmententity.InvestorProfileAuditLogId"
  import: "anduin.id.lpprofile.*"
};


message LpProfileAuditLogTrackingModel {
  string log_id = 1 [(scalapb.field).type = "InvestorProfileAuditLogId"];
  string lp_profile_id = 2 [(scalapb.field).type = "LpProfileId"];
  lpprofile.LpProfileType lp_profile_type = 3;
}

message RecordTypeUnion {
  LpProfileAuditLogTrackingModel _LpProfileAuditLogTrackingModel = 1;
}
