syntax = "proto3";

package anduin.protobuf.auditlog.documenttrackingv2;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.investorprofile.auditlog.documenttrackingv2"
  single_file: true
  import: "anduin.id.investmententity.InvestorProfileAuditLogId"
  import: "anduin.id.lpprofile.LpProfileId"
};
message DocumentAuditLogTrackingModelV2 {
  string log_id = 1 [(scalapb.field).type = "InvestorProfileAuditLogId"];
  string lp_profile_id = 2 [(scalapb.field).type = "LpProfileId"];
}

message RecordTypeUnion {
  DocumentAuditLogTrackingModelV2 _DocumentAuditLogTrackingModelV2 = 1;
}
