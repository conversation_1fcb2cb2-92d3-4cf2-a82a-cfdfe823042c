syntax = "proto3";

package anduin.protobuf.auditlog;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.investorprofile.auditlog"
  single_file: true
  preserve_unknown_fields: false
};


enum AuditLogType {
  INVESTMENT_ENTITY_LOG = 0;
  LP_PROFILE_LOG = 1;
}

enum InvestmentEntityAuditLogEventType {
  reserved 1001;
  INVESTMENT_ENTITY_CREATE = 0;
  INVESTMENT_ENTITY_RENAME = 1;
  INVESTMENT_ENTITY_CHANGE_CUSTOM_ID = 2;
  ROLE_GRANT = 3;
  ROLE_EDIT = 4;
  ROLE_REVOKE = 5;
  SUBSCRIPTION_LINKED = 6;
  SUBSCRIPTION_REMOVED = 7;
  AUTO_SAVE_ENABLED = 8;
  AUTO_SAVE_DISABLE = 9;
}

enum LpProfileAuditLogEventType {
  FIELD_UPDATE = 0;
  FIELD_CLEAR = 1;
  PERMISSION_GRANT = 2;
  PERMISSION_EDIT = 3;
  PERMISSION_REVOKE = 4;
  PROFILE_CLEAR = 5;
}

enum DocumentAuditLogEventType {
  UPLOAD_DOCUMENT = 0;
  ADD_NOTE = 1;
  SET_EXPIRATION_DATE = 2;
  DELETE_DOCUMENT = 3;
  EDIT_DOCUMENT_NAME = 4;
  EDIT_DOCUMENT_NOTE = 5;
  EDIT_DOCUMENT_EXPIRATION = 6;
  DOWNLOAD_DOCUMENT = 7;
  SAVE_FROM_FUND = 8;
}
