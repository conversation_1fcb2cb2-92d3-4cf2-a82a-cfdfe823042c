syntax = "proto3";

package anduin.protobuf.auditlog.documenttracking;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
    package_name: "anduin.protobuf.investorprofile.auditlog.documenttracking"
    single_file: true
    import: "anduin.id.investmententity.InvestorProfileAuditLogId"
    import: "anduin.id.investmententity.InvestmentEntityId"
};
message DocumentAuditLogTrackingModel {
    string log_id = 1 [(scalapb.field).type = "InvestorProfileAuditLogId"];
    string investment_entity_id = 2 [(scalapb.field).type = "InvestmentEntityId"];
}

message RecordTypeUnion {
    DocumentAuditLogTrackingModel _DocumentAuditLogTrackingModel = 1;
}
