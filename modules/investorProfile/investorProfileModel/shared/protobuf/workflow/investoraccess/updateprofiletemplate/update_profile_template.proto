syntax = "proto3";

package anduin.protobuf.workflow.investoraccess.updateprofiletemplate;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.workflow.investoraccess.updateprofiletemplate"
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.form.FormVersionId"
  import: "anduin.id.investmententity.InvestmentEntityId"
  single_file: true
};

message EmptyResp {}

message UpdateProfileTemplateParams {
  string actor = 1 [(scalapb.field).type = "UserId"];
  string form_version_id = 2 [(scalapb.field).type = "FormVersionId"];
  UpdateFormType update_form_type = 3;
}

message BatchUpdateProfileTemplateParams {
  string actor = 1 [(scalapb.field).type = "UserId"];
  string form_version_id = 2 [(scalapb.field).type = "FormVersionId"];
  UpdateFormType update_form_type = 3;
  repeated string investment_entity_ids = 4 [(scalapb.field).type = "InvestmentEntityId"];
}

message BatchUpdateProfileTemplateResp {
  int32 num_processed = 1;
  int32 num_failed = 2;
}

message GetOutdatedInvestmentEntityIdsParams {
  string actor = 1 [(scalapb.field).type = "UserId"];
  string form_version_id = 2 [(scalapb.field).type = "FormVersionId"];
}

message GetOutdatedInvestmentEntityIdsResp {
  repeated string investment_entity_ids = 1 [(scalapb.field).type = "InvestmentEntityId"];
}

message StartUpdateProfileTemplateSetupParams {
  string actor = 1 [(scalapb.field).type = "UserId"];
  int32 num_need_updates = 2;
}

message SetUpdateProfileTemplateProgressParams {
  string actor = 1 [(scalapb.field).type = "UserId"];
  int32 num_processed = 2;
  int32 num_failed = 3;
}

message FailUpdateProfileTemplateParams {
  string actor = 1 [(scalapb.field).type = "UserId"];
}

message FinishUpdateProfileTemplateParams {
  string actor = 1 [(scalapb.field).type = "UserId"];
}


enum UpdateFormType {
  ReplayEvents = 0;
  ImportValues = 1;
}
