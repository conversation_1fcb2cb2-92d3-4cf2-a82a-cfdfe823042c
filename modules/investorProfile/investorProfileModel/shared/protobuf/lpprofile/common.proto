syntax = "proto3";

package anduin.protobuf.lpprofile;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.lpprofile"
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.form.FormVersionId"
  import: "anduin.id.fundsub.FundSubLpId"
  import: "anduin.model.id.FileId"
};

message LpProfileType {
  oneof sealed_value {
    LpProfileMasterType lp_profile_master_type = 1;
    LpProfilePerFundType lp_profile_per_fund_type = 2;
  }
}

message LpProfileMasterType {
  int32 dummy = 9999;
}

message LpProfilePerFundType {
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
}

// EntityLpProfileTemplate activities
message EntityLpProfileTemplateActivity {
  oneof sealed_value {
    SetLpProfileTemplateActivity set_lp_profile_template_activity = 1;
    DeleteLpProfileTemplateActivity delete_lp_profile_template_activity = 2;
  }
}

message SetLpProfileTemplateActivity {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "java.time.Instant"];
  string set_profile_template_id = 3 [(scalapb.field).type = "FormVersionId"];
}

message DeleteLpProfileTemplateActivity {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "java.time.Instant"];
}

message Contact {
  string userId = 1 [(scalapb.field).type = "Option[UserId]"];
  string firstName = 2;
  string lastName = 3;
  string phone = 4;
  string notes = 5;
}

message ProfileDocument {
  string file_id = 1 [(scalapb.field).type = "FileId"];
  string file_name = 2;
  string note = 3;
  LocalDateMessage expiration_date_opt = 4 [(scalapb.field).type = "java.time.LocalDate"];
  DocumentType document_type = 5;
  DocumentSource document_source = 6;
  string creator = 7 [(scalapb.field).type = "UserId"];
  InstantMessage created_at = 8 [(scalapb.field).type = "java.time.Instant"];
  bool is_deleted = 9;
}

// AML/KYC document type (TBD)
message DocumentType {
  oneof sealed_value {
    UnspecifiedDocumentType unspecified_document_type = 1;
  }
}

message UnspecifiedDocumentType {}

message DocumentSource {
  oneof sealed_value {
    DocumentFromFund document_from_fund = 1;
    DocumentFromManualUpload document_from_manual_upload = 2;
  }
}

message DocumentFromFund {
  string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
  string fund_name = 2;
  bool is_auto_saved = 3;
}

message DocumentFromManualUpload {}
