package build.modules.dynamicFormTest

import build_.build.util_.*
import build.modules.dynamicForm
import build.platform.{stargazerConfig, stargazerModel, stargazer}

import mill.*
import anduin.build.*
import anduin.mill.*

object `package` extends Module {

  object dynamicFormTestModel extends StargazerModelCrossPlatformModule {

    object jvm extends JvmModelModule

    object js extends JsModule {
      override def moduleDeps = Seq(stargazerModel.js)
    }

  }

  object dynamicFormTest extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        dynamicFormTestModel.jvm,
        build.gondor.gondorCore.jvm,
        stargazerConfig.jvm,
        stargazer.jvm,
        dynamicForm.dynamicForm.jvm
      )

    }

    object js extends JsModule {

      override def moduleDeps =
        super.moduleDeps ++ Seq(
          dynamicFormTestModel.js,
          stargazerConfig.js,
          dynamicForm.dynamicForm.js
        )

    }

  }

}
