// Copyright (C) 2014-2025 Anduin Transactions Inc.

package build.modules.greylin

import build_.modules.greylin.dependency_.GreylinCore
import build_.build.util_.*
import build.modules.{fundData, fundsub, dataroom, heimdall}
import build.platform.{stargazer, stargazerModel}

import mill.*
import anduin.build.*
import anduin.mill.*

object `package` extends Module {

  object greylinCore extends AnduinScalaModule with AnduinScalaPBModule {

    override def mvnDeps = super.mvnDeps() ++ GreylinCore.jvmDeps

    override def moduleDeps = super.moduleDeps ++ Seq(
      stargazer.jvm,
      stargazerModel.jvm,
      build.gondor.gondorCore.jvm
    )

    object it extends AnduinZioTests with AnduinIntegTests {
      override def moduleDeps = super.moduleDeps ++ Seq(build.gondor.gondorTest, heimdall.heimdall.jvm)
    }

  }

  object greylin extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        fundData.fundData.jvm,
        fundsub.fundsub.jvm,
        dataroom.dataroom.jvm,
        greylinCore,
        heimdall.heimdallCore.jvm
      )

      object it extends AnduinZioTests with AnduinIntegTests {
        override def moduleDeps = super.moduleDeps ++ Seq(fundsub.fundsubTest, heimdall.heimdall.jvm)
      }

    }

    object js extends JsModule {
      override def moduleDeps = super.moduleDeps ++ Seq(heimdall.heimdall.js)
    }

  }

}