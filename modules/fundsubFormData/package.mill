package build.modules.fundsubFormData

import build_.build.util_.*
import build.modules.{heimdall, gaia}
import build.platform.stargazerModel

import mill.*
import anduin.build.*
import anduin.mill.*

object `package` extends Module {

  object fundsubFormDataModel extends AnduinScalaModule with AnduinScalaPBModule {

    override def moduleDeps = super.moduleDeps ++ Seq(stargazerModel.jvm, gaia.gaiaModel.jvm)

  }

  object fundsubFormData extends AnduinScalaModule {

    override def moduleDeps = super.moduleDeps ++ Seq(fundsubFormDataModel, heimdall.heimdallCore.jvm)

  }

}
