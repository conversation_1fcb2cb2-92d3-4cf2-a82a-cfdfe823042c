package build.modules.amlKyc

import mill.*
import anduin.mill.*
import build_.build.util_.StargazerModelCrossPlatformModule

object `package` extends Module {

  object amlKycModel extends StargazerModelCrossPlatformModule {
    object jvm extends JvmModelModule

    object js extends JsModelModule
  }

  object amlKyc<PERSON><PERSON> extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        amlKycModel.jvm,
        build.gondor.gondorCore.jvm
      )

    }

    object js extends JsModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        amlKycModel.js,
        build.gondor.gondorCore.js
      )

    }

  }

}
