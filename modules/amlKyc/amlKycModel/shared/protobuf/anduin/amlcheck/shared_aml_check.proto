syntax = "proto3";

import "scalapb/scalapb.proto";
import "date_time.proto";


option (scalapb.options) = {
    single_file: true
    package_name: "anduin.protobuf.amlcheck"
};

enum AmlCheckStatus {
    NOT_STARTED = 0;
    RESULT_AVAILABLE = 1;
}

enum AmlCheckEntityRole {
    INVESTOR = 0;
    BENEFICIAL_OWNER = 1;
    AUTHORIZED_SIGNER = 2;
    ASSOCIATE = 3;
}

enum AmlCheckInvestorType {
    INDIVIDUAL = 0;
    ENTITY = 1;
}

enum AmlCheckProvider {
    WORLDCHECK = 0;
    MESH = 1;
}

