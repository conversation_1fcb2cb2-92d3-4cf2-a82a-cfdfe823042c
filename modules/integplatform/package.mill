package build.modules.integplatform

import build_.build.dependency_.CommonDependencies
import build_.build.util_.*
import build.modules.{bifrost, fundsub, heimdall, fundData, dataroom}

import mill.*
import mill.scalalib.*
import anduin.build.*
import anduin.mill.*
import io.github.hoangmaihuy.mill.caliban.*

object `package` extends Module {

  object integplatformModel extends StargazerModelCrossPlatformModule {

    object jvm extends JvmModelModule with SharedModule {

      override def moduleDeps = super.moduleDeps ++ Seq(build.gondor.gondorModel.jvm)

    }

    object js extends JsModule with SharedModule {

      override def moduleDeps = super.moduleDeps ++ Seq(build.gondor.gondorModel.js)

    }

    trait SharedModule extends ScalaModule {
      override def scalacOptions = super.scalacOptions() ++ Seq("-Xmax-inlines:128")
    }

  }

  object integplatform<PERSON>ore extends AnduinCrossPlatformModule {

    object jvm extends JvmModule with SharedModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        build.gondor.gondorCore.jvm,
        integplatformModel.jvm
      )

      override def mvnDeps = super.mvnDeps() ++ CommonDependencies.calibanDeps
    }

    object js extends JsModule with SharedModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        build.gondor.gondorCore.js,
        integplatformModel.js
      )

      override def mvnDeps = super.mvnDeps() ++ CommonDependencies.calibanLaminextDeps
    }

    trait SharedModule extends ScalaModule with AnduinCalibanSourceGenModule {

      override def scalacOptions = {
        super
          .scalacOptions()
          .filterNot(Set("-source:future", "-language:strictEquality")) ++
          Seq(
            "-Wconf:msg=wildcard.import:s",
            "-Wconf:msg=wildcard.arguments:s"
          )
      }

      override def calibanSourcePath = Task.Source(moduleDir / os.up / "shared" / "prismatic-graphql")

      override def calibanFileSettings =
        Task {
          Lib
            .findSourceFiles(Seq(calibanSourcePath()), extensions = Seq("graphql"))
            .map(filePath => filePath.relativeTo(calibanSourcePath().path))
            .map { fileRelPath =>
              CalibanFileSettings.forFile(fileRelPath)(
                _.withClientName(s"${fileRelPath.baseName}")
                  .withPackageName("anduin.integplatform.prismatic.graphql")
                  .withGenView(false)
              )
            }
        }

    }

  }

  object integplatform extends AnduinCrossPlatformModule {

    object jvm extends JvmModule with SharedModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        integplatformCore.jvm,
        fundsub.fundsubCore.jvm,
        fundData.fundDataCore.jvm,
        dataroom.dataroomIntegration.jvm
      )

      object it extends AnduinZioTests with AnduinIntegTests {

        override def moduleDeps =
          super.moduleDeps ++ Seq(fundsub.fundsubTest, fundData.fundData.jvm, heimdall.heimdall.jvm)

      }

    }

    object js extends JsModule with SharedModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        integplatformCore.js,
        bifrost.js
      )

    }

    trait SharedModule extends ScalaModule {
      override def scalacOptions = super.scalacOptions() ++ Seq("-Xmax-inlines:128")

    }

  }

  object integplatformApp extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {
      override def moduleDeps = super.moduleDeps ++ Seq(integplatform.jvm)
    }

    object js extends JsModule with AnduinWebClientModule {
      override def webModule = AnduinWebModules.IntegPlatform

      override def moduleDeps = super.moduleDeps ++ Seq(integplatform.js)

      override def mainClass = Some("anduin.integplatform.client.IntegPlatformMainApp")
    }

  }

}
