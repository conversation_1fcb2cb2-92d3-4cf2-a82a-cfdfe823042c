############
## Object ##
############

type Customer {
  id: ID!
  name: String!
  labels: [String!]
  externalId: String
}

type Integration {
  id: ID!
  name: String!
  overview: String
  avatarUrl: String
  description: String
  documentation: String
  category: String
  labels: [String!]
  instances: InstanceConnections!
  versionSequenceId: String!
}

type Instance {
  id: ID!
  name: String!
  enabled: Boolean!
  integration: Integration!
  configState: InstanceConfigState
  customer: Customer!
  configVariables: InstanceConfigVariableConnection!
}

type ErrorType {
  field: String!
  messages: [String!]!
}

type InstanceConfigVariable {
  value: String
}

type InstanceConfigVariableConnection {
  nodes: [InstanceConfigVariable]!
}


##################################

input CreateCustomerInput {
  name:	String!
  description: String
  labels: [String]
  externalId: String
  allowEmbeddedDesigner: Boolean
}

type CreateCustomerPayload {
  clientMutationId: String
  customer: Customer,
  errors: [ErrorType!]!
}

input UpdateCustomerInput {
  id: ID!
  name: String
  description: String
  labels: [String]
  externalId: String
  allowEmbeddedDesigner: Boolean
}

type UpdateCustomerPayload {
  clientMutationId: String
  customer: Customer,
  errors: [ErrorType!]!
}

input DeleteCustomerInput {
  id: ID!
}

type DeleteCustomerPayload {
  clientMutationId: String
  customer: Customer,
  errors: [ErrorType!]!
}

type IntegrationConnection {
  nodes: [Integration]!
}

type InstanceConnections {
  nodes: [Instance]!
  totalCount: Int!
}

enum OrderDirection {
  ASC
  DESC
}

enum IntegrationOrderField {
  CATEGORY
  CREATED_AT
  CUSTOMER
  DESCRIPTION
  NAME
  PUBLISHED_AT
  UPDATED_AT
  VERSION_NUMBER
}

enum InstanceConfigState {
  FULLY_CONFIGURED
  NEEDS_INSTANCE_CONFIGURATION
  NEEDS_USER_LEVEL_CONFIGURATION
}

input IntegrationOrder {
  direction: OrderDirection!
  field: IntegrationOrderField!
}

input CreateInstanceInput {
  integration: ID!
  customer: ID!
  name: String!
  labels: [String]
}

type CreateInstancePayload {
  clientMutationId: String
  instance: Instance,
  errors: [ErrorType!]!
}

input InputInstanceConfigVariable {
  key: String!
  value: String
  values: String # JSON format
}

input UpdateInstanceConfigVariablesInput {
  id: ID
  configVariables: [InputInstanceConfigVariable]
}

type UpdateInstanceConfigVariablesPayload {
  clientMutationId: String
  instance: Instance,
  errors: [ErrorType!]!
}

input UpdateInstanceInput {
  id: ID!
  enabled: Boolean
}

type UpdateInstancePayload {
  errors: [ErrorType!]!
}

input DeleteInstanceInput {
  id: ID
}

type DeleteInstancePayload {
  errors: [ErrorType!]!
}

##################################

type Query {
  customer(id: ID!): Customer
  instance(id: ID!): Instance
  instances(customer: ID, integration: ID, labels_Contains: String, name: String): InstanceConnections!
  marketplaceIntegration(id: ID!): Integration
  marketplaceIntegrations(sortBy: [IntegrationOrder], labels_Contains: String): IntegrationConnection!
}

type Mutation {
  createCustomer(input: CreateCustomerInput!): CreateCustomerPayload
  updateCustomer(input: UpdateCustomerInput!): UpdateCustomerPayload
  deleteCustomer(input: DeleteCustomerInput!): DeleteCustomerPayload

  createInstance(input: CreateInstanceInput!): CreateInstancePayload
  updateInstance(input: UpdateInstanceInput!): UpdateInstancePayload
  updateInstanceConfigVariables(input: UpdateInstanceConfigVariablesInput!): UpdateInstanceConfigVariablesPayload
  deleteInstance(input: DeleteInstanceInput!): DeleteInstancePayload
}
