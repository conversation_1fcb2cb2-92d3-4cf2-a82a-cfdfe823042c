package build.modules.brienne

import build_.build.util_.*
import build.modules.{fundsub, heimdall, fundData, dataroom}
import build.platform.stargazer

import mill.*
import anduin.mill.*

object `package` extends Module {

  object brienneModel extends AnduinScalaModule with AnduinScalaPBModule {
    override def moduleDeps = super.moduleDeps ++ Seq(fundsub.fundsubModel.jvm, dataroom.dataroomModel.jvm)
    override def scalacOptions: T[Seq[String]] = super.scalacOptions() ++ Seq("-Xmax-inlines:128")
  }

  object brienneCore extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {

      override def moduleDeps =
        super.moduleDeps ++ Seq(
          fundsub.fundsubCore.jvm,
          brienneModel,
          build.gondor.gondorCore.jvm,
          dataroom.dataroomCore.jvm
        )

    }

    object js extends JsModule {
      override def moduleDeps = super.moduleDeps ++ Seq(fundsub.fundsubCore.js)
    }

  }

  object brienne extends AnduinScalaModule {
    override def moduleDeps = super.moduleDeps ++ Seq(fundsub.fundsub.jvm, fundData.fundData.jvm, dataroom.dataroom.jvm)

    object it extends AnduinZioTests with AnduinIntegTests {

      override def moduleDeps =
        super.moduleDeps ++ Seq(fundsub.fundsubTest, dataroom.dataroom.jvm, heimdall.heimdall.jvm)

    }

  }

}
