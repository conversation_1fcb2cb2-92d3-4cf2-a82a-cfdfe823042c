syntax = "proto3";

package anduin.sa.model.sadatatemplate.sadatatemplatemessage;

import "scalapb/scalapb.proto";
import "sa_common.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.sa.SaDataTemplateId"
};

message SaDataTemplateMessage {
  string id = 1 [(scalapb.field).type = "SaDataTemplateId"];
  string name = 2;
  string description = 3;
  // NOTE @trancuong81
  // - Would change to use CUE lang for field_list
  // - Might still use FDB for fields to be indexed (for efficient Template management, e.g., to search template by name)
  repeated SaTemplateField field_list = 4;
  bool is_deleted = 5;
}

message RecordTypeUnion {
  SaDataTemplateMessage _SaDataTemplateMessage = 1;
}
