syntax = "proto3";

package anduin.sa.model.sadatatemplate;

import "scalapb/scalapb.proto";
import "date_time.proto";
import "sa_common.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "java.time.Instant"
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.sa.SaDataTemplateId"
};

message SaDataTemplateActivity {
  oneof sealed_value {
    CreateSaDataTemplateActivity create_sa_data_template_activity = 1;
    RemoveSaDataTemplateActivity remove_sa_data_template_activity = 2;
    UpdateSaDataTemplateActivity update_sa_data_template_activity = 3;
    UpdateSingleTemplateFieldActivity update_single_template_field_activity = 4;
    DeleteSaDataTemplatePermanentlyActivity delete_sa_data_template_permanently_activity = 5;
  }
}

message CreateSaDataTemplateActivity {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "Instant"];
  optional string transferred_from_template_id_opt = 3 [(scalapb.field).type = "SaDataTemplateId"];
}

message RemoveSaDataTemplateActivity {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "Instant"];
}

message DeleteSaDataTemplatePermanentlyActivity {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "Instant"];
}

// NOTE @trancuong81 When using CUE lang, the schema updates (e.g., field_list) could already be tracked by CUE versions
message UpdateSaDataTemplateActivity {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "Instant"];
  optional string new_name_opt = 3;
  optional string new_description_opt = 4;
  optional int32 added_field_at_index_opt = 5;
  repeated SaTemplateField added_field_list = 6;
  repeated SaTemplateField removed_fields = 7 [(scalapb.field) = { collection_type: "Set" }];
  repeated ReorderAction reorder_actions = 8;
}

message UpdateSingleTemplateFieldActivity {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "Instant"];
  string field_resource_id = 3;
  string field_alias = 4;
  optional string new_field_label_opt = 5;
}
