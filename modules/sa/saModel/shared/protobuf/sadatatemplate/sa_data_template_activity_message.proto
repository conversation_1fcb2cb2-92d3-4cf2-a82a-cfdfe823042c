syntax = "proto3";

package anduin.sa.model.sadatatemplate.sadatatemplateactivitymessage;

import "scalapb/scalapb.proto";
import "sadatatemplate/sa_data_template_common.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.sa.SaDataTemplateId"
};

message SaDataTemplateActivityMessage {
  string id = 1 [(scalapb.field).type = "SaDataTemplateId"];
  repeated SaDataTemplateActivity activities = 2;
}

message RecordTypeUnion {
  SaDataTemplateActivityMessage _SaDataTemplateActivityMessage = 1;
}
