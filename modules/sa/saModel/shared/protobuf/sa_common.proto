syntax = "proto3";

package anduin.sa.model;

import "scalapb/scalapb.proto";
import "google/protobuf/struct.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.ontology.AsaId"
  import: "anduin.id.form.FormVersionId"
  import: "anduin.id.annotation.AnnotationDocumentVersionId"
  import: "anduin.id.sa.MappingDestinationId"
  import: "anduin.id.sa.SaProfileId"
  import: "anduin.cue.model.CommonCueTypeId"
  import: "io.circe.Json"
  import: "anduin.model.codec.ProtoCodecs.jsonStructMapper"
};

message SaMessage {
  reserved 6;
  string alias = 1;
  string description = 2;
  string ontology_annotation = 3;
  string asa_id = 4 [(scalapb.field).type = "AsaId"];
  SaSourceInfo source_info_opt = 7;
  repeated string option_parent_list = 8;
  string sa_type_id = 9 [(scalapb.field).type = "CommonCueTypeId"];
  // For types with options (e.g., MultipleCheckbox, RadioGroup, or structure types containing those),
  // more details about option values are needed beside saTypeId
  google.protobuf.Struct sa_type_json_schema_opt = 10 [(scalapb.field).type = "Json"];
  bool is_repeatable = 11;
  optional string local_option_value_opt = 12;
}

message SaSourceInfo {
  string source_id = 1 [(scalapb.field).type = "MappingDestinationId"];
  string field_name = 2;
}

message PageRange {
  int32 start_index = 1;
  int32 end_index = 2;
}

message ResourceDetails {
  oneof sealed_value {
    FormResourceDetails form_resource_details = 1;
    DocumentResourceDetails document_resource_details = 2;
    ExcelResourceDetails excel_resource_details = 3;
    SaProfileResourceDetails sa_profile_resource_details = 4;
    AsaResourceDetails asa_resource_details = 5;
    CsvResourceDetails csv_resource_details = 6;
    ManualResourceDetails manual_resource_details = 7;
  }
}

message ManualResourceDetails {}

message FormResourceDetails {
  string form_version_id = 1 [(scalapb.field).type = "FormVersionId"];
  string form_name = 2;
  string form_version_name = 3;
}

message DocumentResourceDetails {
  string document_version_id = 1 [(scalapb.field).type = "AnnotationDocumentVersionId"];
  string document_name = 2;
  string document_version_name = 3;
  repeated PageRange page_ranges = 4; // Empty -> use all pages
}

message ExcelResourceDetails {
  string excel_name = 1;
  string sheet_name = 2;
  int32 sheet_index = 3;
}

message CsvResourceDetails {
  string csv_file_name = 1;
}

message SaProfileResourceDetails {
  string sa_profile_id = 1 [(scalapb.field).type = "SaProfileId"];
  string profile_name = 2;
  string profile_description = 3;
}

message AsaResourceDetails {}

message SaTemplateField {
  string resource_id = 1;
  string alias = 2;
  string label = 3;
}

message ReorderAction {
  oneof sealed_value {
    SwapItem swap_item = 1;
    MoveItem move_item = 2;
  }
}

message SwapItem {
  int32 first_index = 1;
  int32 second_index = 2;
}

message MoveItem {
  int32 old_index = 1;
  int32 new_index = 2;
}
