syntax = "proto3";

package anduin.sa.model.saprofile.saprofilemessage;

import "scalapb/scalapb.proto";
import "sa_common.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.sa.SaProfileId"
};

message SaProfileMessage {
  string id = 1 [(scalapb.field).type = "SaProfileId"];
  string name = 2;
  string description = 3;
  repeated SaMessage sa_list = 4;
  bool is_deleted = 5;
}

message RecordTypeUnion {
  SaProfileMessage _SaProfileMessage = 1;
}
