syntax = "proto3";

package anduin.sa.model.saprofile;

import "scalapb/scalapb.proto";
import "date_time.proto";
import "sa_common.proto";
import "google/protobuf/struct.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "java.time.Instant"
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.ontology.AsaId"
  import: "anduin.id.sa.SaProfileId"
  import: "anduin.cue.model.CommonCueTypeId"
  import: "io.circe.Json"
  import: "anduin.model.codec.ProtoCodecs.jsonStructMapper"
};

message SaProfileActivity {
  oneof sealed_value {
    CreateSaProfileActivity create_sa_profile_activity = 1;
    RemoveSaProfileActivity remove_sa_profile_activity = 2;
    UpdateSaProfileActivity update_sa_profile_activity = 3;
    UpdateSingleSaActivity update_single_sa_activity = 4;
    DeleteSaProfilePermanentlyActivity delete_sa_profile_permanently_activity = 5;
  }
}

message CreateSaProfileActivity {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "Instant"];
  optional string transferred_from_profile_id_opt = 3 [(scalapb.field).type = "SaProfileId"];
}

message RemoveSaProfileActivity {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "Instant"];
}

message DeleteSaProfilePermanentlyActivity {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "Instant"];
}

message UpdateSaProfileActivity {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "Instant"];
  optional string new_name_opt = 3;
  optional string new_description_opt = 4;
  repeated SaMessage added_sa_list = 5;
  repeated string removed_sa_aliases = 6 [(scalapb.field) = { collection_type: "Set" }];
}

message UpdateSingleSaActivity {
  reserved 7;
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "Instant"];
  string alias = 3;
  optional string new_description_opt = 4;
  optional string new_ontology_annotation_opt = 5;
  optional string asa_id_opt = 6 [(scalapb.field).type = "AsaId"];
  optional SaSourceInfo new_sa_source_info_opt = 8;
  optional string new_sa_type_opt = 9 [(scalapb.field).type = "CommonCueTypeId"];
  optional google.protobuf.Struct new_sa_type_json_schema_opt = 10 [(scalapb.field).type = "Json"];
}
