syntax = "proto3";

package anduin.sa.model.saprofile.saprofileactivitymessage;

import "scalapb/scalapb.proto";
import "saprofile/sa_profile_common.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.sa.SaProfileId"
};

message SaProfileActivityMessage {
  string id = 1 [(scalapb.field).type = "SaProfileId"];
  repeated SaProfileActivity activities = 2;
}

message RecordTypeUnion {
  SaProfileActivityMessage _SaProfileActivityMessage = 1;
}
