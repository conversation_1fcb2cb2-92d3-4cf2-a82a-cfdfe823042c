syntax = "proto3";

package anduin.sa.model.saprofilemapping.mappingdestinationmessage;

import "scalapb/scalapb.proto";
import "date_time.proto";
import "sa_common.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.sa.MappingDestinationId"
  import: "anduin.model.common.user.UserId"
  import: "java.time.Instant"
};

message MappingDestinationMessage {
  string id = 1 [(scalapb.field).type = "MappingDestinationId"];
  string creator = 2 [(scalapb.field).type = "UserId"];
  InstantMessage last_updated_at = 3 [(scalapb.field).type = "Instant"];
  // Expose ID, description for indexing & querying
  string resource_id = 4;
  string resource_description = 5;
  ResourceDetails resource_details = 6;
}

message RecordTypeUnion {
  MappingDestinationMessage _MappingDestinationMessage = 1;
}
