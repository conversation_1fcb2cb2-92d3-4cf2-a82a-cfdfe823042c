syntax = "proto3";

package anduin.sa.model.saprofilemapping;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.sa.MappingDestinationId"
  import: "java.time.Instant"
};

message SaProfileMappingActivity {
  oneof sealed_value {
    AddSaProfileMappingActivity add_sa_profile_mapping_activity = 1;
    RemoveSaProfileMappingActivity remove_sa_profile_mapping_activity = 2;
    TransferSaProfileMappingActivity transfer_sa_profile_mapping_activity = 3;
    DeleteSaProfileMappingPermanentlyActivity delete_sa_profile_mapping_permanently_activity = 4;
    ImportSaProfileMappingActivity import_sa_profile_mapping_activity = 5;
  }
}

message AddSaProfileMappingActivity {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "Instant"];
  map<string, string> added_mapping = 3;
}

message ImportSaProfileMappingActivity {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "Instant"];
  map<string, string> imported_mapping = 3;
  bool clear_previous_mapping = 4;
}

message RemoveSaProfileMappingActivity {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "Instant"];
  repeated string removed_field_names = 3 [(scalapb.field).collection_type = "Set"];
}

// Transfer mapping of the same SaProfile from a previous mapping destination to a new one
message TransferSaProfileMappingActivity {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "Instant"];
  string src_destination_id = 3 [(scalapb.field).type = "MappingDestinationId"];
  map<string, string> transferred_mapping = 4;
}

message DeleteSaProfileMappingPermanentlyActivity {
  string actor = 1 [(scalapb.field).type = "UserId"];
  InstantMessage occurred_at = 2 [(scalapb.field).type = "Instant"];
}
