syntax = "proto3";

package anduin.sa.model.saprofilemapping.saprofilemappingactivitymessage;

import "scalapb/scalapb.proto";
import "saprofilemapping/sa_profile_mapping_common.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.sa.SaProfileId"
  import: "anduin.id.sa.MappingDestinationId"
};

message SaProfileMappingActivityMessage {
  string sa_profile_id = 1 [(scalapb.field).type = "SaProfileId"];
  string destination_id = 2 [(scalapb.field).type = "MappingDestinationId"];
  repeated SaProfileMappingActivity activities = 3;
}

message RecordTypeUnion {
  SaProfileMappingActivityMessage _SaProfileMappingActivityMessage = 1;
}
