syntax = "proto3";

package anduin.sa.model.saprofilemapping.saprofilemappingmessage;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.id.sa.SaProfileId"
  import: "anduin.id.sa.MappingDestinationId"
  import: "anduin.model.common.user.UserId"
  import: "java.time.Instant"
};

message SaProfileMappingMessage {
  string sa_profile_id = 1 [(scalapb.field).type = "SaProfileId"];
  string destination_id = 2 [(scalapb.field).type = "MappingDestinationId"];
  map<string, string> field_to_sa_map = 3;
  optional string last_updated_by_opt = 4 [(scalapb.field).type = "UserId"];
  InstantMessage last_updated_at_opt = 5 [(scalapb.field).type = "Instant"];
}

message RecordTypeUnion {
  SaProfileMappingMessage _SaProfileMappingMessage = 1;
}
