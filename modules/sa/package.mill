package build.modules.sa

import build_.build.util_.*
import build.modules.{heimdall, ontology}
import build.platform.stargazer
import mill.*
import mill.scalalib.*

import anduin.build.*
import anduin.mill.*

object `package` extends Module {

  object saModel extends StargazerModelCrossPlatformModule {

    object jvm extends JvmModelModule with SharedModule {
      override def moduleDeps = super.moduleDeps ++ Seq(build.gondor.gondorModel.jvm)
    }

    object js extends JsModelModule with SharedModule {
      override def moduleDeps = super.moduleDeps ++ Seq(build.gondor.gondorModel.js)
    }

    trait SharedModule extends ScalaModule {
      override def scalacOptions = super.scalacOptions() ++ Seq("-Xmax-inlines:128")
    }

  }

  object saCore extends AnduinCrossPlatformModule {

    object jvm extends JvmModule with SharedModule {

      override def moduleDeps = super.moduleDeps ++ Seq(stargazer.jvm, saModel.jvm, ontology.ontologyCore.jvm)

    }

    object js extends JsModule with SharedModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        stargazer.js,
        heimdall.heimdallCore.js,
        saModel.js,
        ontology.ontologyCore.js
      )

    }

    trait SharedModule extends ScalaModule {
      override def scalacOptions = super.scalacOptions() ++ Seq("-Xmax-inlines:128")
    }

  }

  object sa extends Module {

    object jvm extends AnduinScalaModule {
      override def scalacOptions = super.scalacOptions() ++ Seq("-Xmax-inlines:128")

      override def moduleDeps =
        super.moduleDeps ++ Seq(
          heimdall.heimdallCore.jvm,
          build.gondor.gondorCore.jvm,
          saCore.jvm
        )

    }

  }

}
