syntax = "proto3";

import "date_time.proto";
import "email_address.proto";
import "google/protobuf/wrappers.proto";
import "scalapb/scalapb.proto";
import "dynamicform/form_rule.proto";
import "signature/e_signature.proto";
import "external/squants.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.dynamicform"
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.model.common.emailaddress.EmailAddress"
  import: "anduin.id.fundsub.AliasId"
  import: "anduin.model.id.*"
  preamble: "trait DynamicFormSectionTrait {"
  preamble: "  def id: String"
  preamble: "  def alias: String"
  preamble: "  def aliasIdOpt: Option[AliasId]"
  preamble: "  def aliasId: AliasId = {"
  preamble: "    aliasIdOpt.getOrElse {"
  preamble: "      val aliasStr = if (alias.nonEmpty) alias else id"
  preamble: "      AliasId.generate(aliasStr)"
  preamble: "    }"
  preamble: "  }"
  preamble: "}"
};

// The underlying info of the given form
message DynamicFormModel {
  reserved 1, 3, 5, 7;
  string form_name = 2;
  repeated string form_files = 4 [(scalapb.field).type = "FileId"];
  DynamicFormSection form = 6;
  map<string, NewFormRule> new_files_event = 9;
  S3FormChangeInfo s3_form_info_opt = 8;
  DynamicFormSetting form_setting = 10;
  repeated string embeded_files = 11 [(scalapb.field).type = "FileId"];
  bool is_removed = 12;
}

message FileInfoMessage {
  repeated string field_ids = 1;
  repeated string duplicated_field_ids = 2;
  map<string, NewFormDescription> field_infos = 3;
}

message DynamicFormSetting {
  RenderLabelType render_label_type = 1;
}

enum RenderLabelType {
  OldLabel = 0; // Label and Helper text
  NewLabel = 1; // General Label and Form Label
}

message InputWithMapping {
  string value = 1;
  string pdf_mapping = 2;
  google.protobuf.StringValue html = 3;
}

// If you introduce new type, remember to:
// - If it has user data to export, add it to defaultInputTypeToExport in FundSubWorkbookService
enum InputType {
  String = 0;
  Integer = 1;
  Float = 2;
  Percentage = 3;
  Email = 4;
  Checkbox = 5;
  MultipleCheckbox = 6;
  Radio = 7;
  RadioWithInput = 8; // Deprecated
  Money = 9;
  Dropdown = 10;
  DropdownWithInput = 11; // Deprecated
  Date = 12;
  DateTime = 13;
  TextArea = 14;
  File = 15; // For required supporting docs or tax docs
  Signature = 16;
  None = 17;
  CustomFormat = 18;
  RequestedSignatureInput = 19; // Deprecated
  ESignatureInput = 20; // Deprecated
  UploadedSignatureInput = 21; // Deprecated
  CurrentDate = 22;
  PhoneNumber = 23;
  GpSignature = 24;
  EmbededPdf = 25;
  USTIN = 26;
  FileGroup = 27; // For grouping supporting docs
  PhoneNumberWithCountryCode = 28;
}

message SignatureInfo {
  reserved 1 to 2;
  repeated anduin.protobuf.signature.PrepFieldModel prep_fields = 3;
}

message NewFormDescription {
  string label = 1;
  InputType input_type = 2;
  repeated InputWithMapping input_options = 3;
  string description = 4;
  string html_description = 10;
  string place_holder = 5;
  repeated string pdf_mapping = 6;
  repeated string validations = 7;
  string input_format = 8;
  map<string, SignatureInfo> signature_mappings = 9;
  string default_value = 11;
  DynamicFormSectionPosition position = 12;
  string tooltip = 13; // tooltip for disabled page in TOC
  repeated DynamicFormSignatureMapping signature_field_mappings = 14;
  google.protobuf.StringValue initial_signature_alias = 15; // indicate this field checkbox/radio will be replace with referenced initial field value in pdf
  string fill_pdf_format = 16;
  google.protobuf.BoolValue disabled_comment_opt = 17;
  external.squants.CurrencyMessage currency = 18; // Currency for money input type
}

message DynamicFormSectionPosition {
  oneof sealed_value {
    DynamicFormSectionNormalPosition normal = 1;
    DynamicFormSectionBelowOptionPosition below_option = 2;
  }
}

message DynamicFormSectionNormalPosition {
}

message DynamicFormSectionBelowOptionPosition {
  string section_id = 1;
  string option_value = 2;
}

message DynamicFormSignatureMapping {
  string pdf_field_id = 1;
  anduin.protobuf.signature.SignatureEnum.SignatureBlockType blkType = 2;
  bool is_customized = 3;
  google.protobuf.StringValue description = 4;
  // To support prefill commitment amounts for investors during batch countersign
  bool is_commitment_amount = 5;
}

message DynamicFormSection {
  reserved 2, 6;
  option (scalapb.message).extends = "DynamicFormSectionTrait";
  string id = 1;
  string alias = 8;
  string alias_id_opt = 10 [(scalapb.field).type = "Option[AliasId]"];
  repeated string properties = 3 [(scalapb.field).collection_type = "Set"];
  NewFormDescription form_description = 4;
  repeated DynamicFormSection children = 5;
  repeated NewFormRule new_rules = 9;
  int32 max_duplicates = 7;
}

enum ServerType {
  Canary = 0;
  Internal = 1;
  Demo = 2;
  Deals = 3;
  TestServer = 4;
  Unknown = 5;
  E2E = 6;
}

message S3FormChangeInfo {
  reserved 3;

  EmailAddressMessage authorEmail = 1 [(scalapb.field).type = "EmailAddress", (scalapb.field).no_box = true];
  string description = 2;
  InstantMessage savedAt = 5 [(scalapb.field).type = "java.time.Instant", (scalapb.field).no_box = true];
  ServerType fromServer = 4;
  int32 versionNumber = 6;
}
