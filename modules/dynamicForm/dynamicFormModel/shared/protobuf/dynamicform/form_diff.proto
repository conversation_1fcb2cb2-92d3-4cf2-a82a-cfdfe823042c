syntax = "proto3";

import "scalapb/scalapb.proto";
import "dynamicform/form_data.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.dynamicform"
  single_file: true
  preserve_unknown_fields: false
  import: "anduin.model.id.*"
};

message FormDiffRequest {
  string id = 1 [(scalapb.field).type = "FormDiffJobId"];
  DynamicFormSection oldSection = 2;
  DynamicFormSection newSection = 3;
}

enum FormDiffStatus {
  PENDING = 0;
  SUCCESS = 1;
  FAILED = 2;
}

message FormDiffResult {
  string id = 1 [(scalapb.field).type = "FormDiffJobId"];
  FormDiffStatus status = 2;
  repeated MatchingResult matchings = 3;
}

message MatchingResult {
  DynamicFormSection oldSection = 1;
  DynamicFormSection newSection = 2;
  bool ruleChanged = 3;
  bool mappingChanged = 4;
  bool textChanged = 5;
  bool styleChanged = 6;
}

message RecordTypeUnion {
  FormDiffResult _FormDiffResult = 1;
}
