syntax = "proto3";

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.dynamicform"
  single_file: true
  preserve_unknown_fields: false
};

message FormFillingProgress {
  repeated string completed_pages = 1 [(scalapb.field).collection_type = "Set"];
  repeated string uncompleted_pages = 2 [(scalapb.field).collection_type = "Set"];
  repeated string hidden_pages = 3 [(scalapb.field).collection_type = "Set"];
}