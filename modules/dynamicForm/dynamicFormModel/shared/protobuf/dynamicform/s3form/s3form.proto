syntax = "proto3";

import "scalapb/scalapb.proto";
import "date_time.proto";
import "email_address.proto";
import "dynamicform/form_data.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.dynamicform.s3form"
  single_file: true
  import: "anduin.id.dynamicform.s3form.*"
  import: "anduin.model.common.emailaddress.EmailAddress"
  import: "anduin.model.id.*"
};

message S3FormInfo {
  reserved 4;

  string name = 1;
  string description = 2;
  InstantMessage createdAt = 3 [(scalapb.field).type = "java.time.Instant", (scalapb.field).no_box = true];
  repeated string tags = 5 [(scalapb.field).collection_type = "Set"];
  bool isArchived = 6;
  repeated EmailAddressMessage owners = 7 [(scalapb.field).type = "EmailAddress"];
}

message S3FormFileInfo {
  reserved 6;
  string fileName = 1;
  EmailAddressMessage uploadedBy = 2 [(scalapb.field).type = "EmailAddress", (scalapb.field).no_box = true];
  InstantMessage uploadedAt = 3 [(scalapb.field).type = "java.time.Instant", (scalapb.field).no_box = true];
  ServerType fromServer = 4;
  string generatedPdfFile = 5 [(scalapb.field).type = "Option[FileId]"];
  // Deprecated. @poorguy to remove this field soon
  FileInfoMessage fileInfo = 7;
}

message S3FormTemporaryChangeInfo {
  string changeId = 1 [(scalapb.field).type = "S3FormChangeId"];
  string description = 2;
  EmailAddressMessage actor = 3 [(scalapb.field).type = "EmailAddress"];
  InstantMessage savedAt = 4 [(scalapb.field).type = "java.time.Instant"];
}

message S3FormDirectoryModel {
  map<string, S3FormInfo> forms = 1 [(scalapb.field).key_type = "DynamicFormId"];
}

message S3FormChangeDirectoryModel {
  map<string, S3FormChangeInfo> changes = 1 [(scalapb.field).key_type = "S3FormChangeId"];
  S3FormTemporaryChangeInfo temporaryChange = 2;
}

message S3FormFileDirectoryModel {
  map<string, S3FormFileInfo> files = 1 [(scalapb.field).key_type = "FileId"];
}
