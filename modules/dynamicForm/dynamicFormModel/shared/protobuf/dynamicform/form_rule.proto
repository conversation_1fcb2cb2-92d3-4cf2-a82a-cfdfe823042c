syntax = "proto3";

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.dynamicform"
  single_file: true
  preserve_unknown_fields: false
};

message Variable {
  string value = 1;
}

message Value {
  string value = 1;
}

message UnaryExpr {
  string op = 1;
  Expr expr = 2;
}

message BinaryExpr {
  Expr lhs = 1;
  string op = 2;
  Expr rhs = 3;
}

message InvalidExpr {
  string expr_str = 1;
}

message Expr {
  oneof sealed_value {
    Variable variable = 1;
    Value value = 2;
    UnaryExpr unaryExpr = 3;
    BinaryExpr binaryExpr = 4;
    InvalidExpr invalidExpr = 5;
  }
}

enum NewEventType {
  EventHide = 0;
  EventDisable = 1;
  EventSetValue = 2;
}

message NewFormEvent {
  NewEventType event_type = 1;
  Expr value = 2;
}

message NewFormRule {
  Expr condition = 1;
  NewFormEvent event = 2;
}
