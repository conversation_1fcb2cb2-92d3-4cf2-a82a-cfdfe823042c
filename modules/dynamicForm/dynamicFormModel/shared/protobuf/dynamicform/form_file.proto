syntax = "proto3";

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.dynamicform"
  single_file: true
  preserve_unknown_fields: false
};

message DynamicFormFileInfo {
  repeated DynamicFormFileFieldInfo field_infos = 1;
}

message DynamicFormFileFieldInfo {
  string name = 1;
  DynamicFormFileFieldType field_type = 2;
  DynamicFormFileFieldPosition position = 3;
  repeated string radio_values = 4;

  enum DynamicFormFileFieldType {
    CheckBox = 0;
    Text = 1;
    Radio = 2;
    Signature = 3;
    Undefined = 4;
  }
}



// The position and size of a field in a file.
message DynamicFormFileFieldPosition {
  // Page number in the document, start from 0.
  int32 pageIndex = 1;
  // x coordinate, in range [0, 1], 0 is left edge of the page.
  float xPos = 2;
  // y coordinate, in range [0, 1], 0 is bottom edge of the page.
  float yPos = 3;
  // ratio of location width to page width, in range [0, 1].
  float width = 4;
  // ratio of location height to page height, in range [0, 1].
  float height = 5;
}



