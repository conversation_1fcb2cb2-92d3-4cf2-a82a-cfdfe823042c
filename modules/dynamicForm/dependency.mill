package build.modules.dynamicForm

import anduin.build.AnduinVersions
import mill.scalalib.*

object DynamicForm {

  lazy val jvmDeps = Seq(
    mvn"org.apache.commons:commons-text:${AnduinVersions.commonsText}"
      .exclude("org.apache.commons" -> "commons-lang3"),
    mvn"org.apache.commons:commons-lang3:${AnduinVersions.apacheCommonLang}"
  )

  lazy val jsDeps = Seq(
    mvn"org.parboiled::parboiled::${AnduinVersions.parboiled}"
      .excludeOrg("com.chuusai")
  )

}
