package build.modules.dynamicForm

import build_.modules.dynamicForm.dependency_.DynamicForm
import build_.build.util_.*
import build.modules.signature
import build.platform.webModules

import mill.*
import mill.scalalib.*
import anduin.build.*
import anduin.mill.*

object `package` extends Module {

  object dynamicFormModel extends StargazerModelCrossPlatformModule {

    object jvm extends JvmModelModule {
      override def moduleDeps = super.moduleDeps ++ Seq(signature.signatureModel.jvm)
    }

    object js extends JsModelModule {
      override def moduleDeps = super.moduleDeps ++ Seq(signature.signatureModel.js)
    }

  }

  object dynamicForm extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {

      override def moduleDeps =
        super.moduleDeps ++ Seq(build.gondor.gondorCore.jvm, dynamicFormModel.jvm)

      override def mvnDeps = super.mvnDeps() ++ DynamicForm.jvmDeps

    }

    object js extends JsModule {

      override def moduleDeps =
        super.moduleDeps ++ Seq(webModules, signature.signature.js, dynamicFormModel.js)

      override def mvnDeps = super.mvnDeps() ++ DynamicForm.jsDeps

    }

  }

}
