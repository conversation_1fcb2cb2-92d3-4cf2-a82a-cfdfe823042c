syntax = "proto3";

package anduin.protobuf.signature;

import "scalapb/scalapb.proto";

import "google/protobuf/wrappers.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.signature"
  single_file: true
  import: "anduin.model.id.*"
};

// Types of user signatures. For other fields (name, title, ...), only Typing is used.
enum SignatureTypeMessage {
  Handwriting = 0;
  Typing = 1;
  Image = 2;
}

message SignatureEnum {
  enum SignatureBlockType {
    Signature = 0;
    Name = 1;
    Title = 2;
    Date = 3;
    Company = 4;
    Address = 5;
    Phone = 6;
    Custom = 7;
    Checkbox = 8;
    Radio = 9;
  }
}

// The position and size of a signature/field in a document.
message SignatureLocationMessage {
  reserved 6;
  // Page number in the document, start from 0 when signing, start from 1 when preparing .
  int32 pageIndex = 1;
  // x coordinate, in range [0, 1], 0 is left edge of the page.
  float xPos = 2;
  // y coordinate, in range [0, 1], 0 is bottom edge of the page.
  float yPos = 3;
  // ratio of location width to page width, in range [0, 1].
  float width = 4;
  // ratio of location height to page height, in range [0, 1].
  float height = 5;
}

// User's last used signature size.
message SignatureSizeMessage {
  // ratio of location width to page width, in range [0, 1].
  float width = 1;
  // ratio of location height to page height, in range [0, 1].
  float height = 2;
}

// The content of an e-signature or a common field (name, title, company, ...).
message SignatureDataBox {
  message HandwritingDataMessage {
    message Point {
      double x = 1;
      double y = 2;
      double time = 3;
    }

    message PathsList {
      repeated Point points = 1;
    }

    reserved 30;

    double xPos = 1;
    double yPos = 2;
    double width = 3;
    double height = 4;
    string stroke = 10;
    string strokeWidth = 11;
    repeated string paths = 20;
    repeated PathsList pointsPaths = 5;
  }

  message TypingDataMessage {
    reserved 1, 2, 12, 31;

    double width = 3;
    double height = 4;
    string fontName = 10;
    double fontSize = 11;
    string text = 30;
  }

  message ImageDataMessage {
    reserved 1, 2;
    double width = 3;
    double height = 4;
    string fileId = 5 [(scalapb.field).type = "FileId"];
  }

  oneof data {
    HandwritingDataMessage handwriting = 1;
    TypingDataMessage typing = 2;
    ImageDataMessage image = 3;
    bool boolean = 4;
    string radio = 5;
  }
}

message SignFieldModel {
  string id = 1;
  SignatureEnum.SignatureBlockType blockType = 2;
  SignatureLocationMessage location = 3;
  SignatureDataBox data = 4;
  google.protobuf.StringValue prepFileId = 5;
  repeated RadioOptionModel radioOptions = 6;
}

message RadioOptionModel {
  string id = 1;
  SignatureLocationMessage location = 2;
  string name = 3;
}

message PrepFieldModel {
  string id = 1;
  SignatureEnum.SignatureBlockType blockType = 2;
  SignatureLocationMessage location = 3;
  // only applicable for Radio block type
  repeated RadioOptionModel radioOptions = 7;
  SignatureDataBox data = 4;
  // ID to support classifying field type for prefilling data or batch signing
  string typeId = 5;
  // description to be displayed to signer, applicable to only batch countersign for now
  google.protobuf.StringValue description = 6;
  // tooltip to be displayed to signer in e-sign view
  google.protobuf.StringValue tooltip = 8;
  // is this field optional or required to finish signing
  bool isOptional = 9;
  // only use to customize date format at the moment
  google.protobuf.StringValue format = 10;
}

// All sign fields of one user in one document
message DocumentSignatureMessage {
  reserved 1 to 5;
  repeated SignFieldModel fields = 6;
}

// All prep fields for one user in one document
message DocumentSignatureTemplateMessage {
  reserved 1 to 5;
  repeated PrepFieldModel fields = 6;
}

// Represent e-signature data and preference of a user
message OldSignatureMessage {
  reserved 2 to 4, 7, 8, 10;

  message SignatureMessageEntry {
    SignatureTypeMessage key = 1;
    SignatureDataBox value = 2;
  }
  // all signatures created by user, can contain at most one instant for each type (typing/handwriting/image)
  repeated SignatureMessageEntry signature = 1;

  message SignatureBlockEntry {
    SignatureEnum.SignatureBlockType blockType = 1;
    SignatureDataBox data = 2;
  }
  // all fields that is not related to UserInfo created by user (company)
  repeated SignatureBlockEntry signatureBlocks = 9;

  // user's preferred signature type, to be used when user e-signs
  SignatureTypeMessage preferredSignatureType = 5;
}

// Represent e-signature data and preference of a user
message SignatureMessage {

  string signature_id = 1 [(scalapb.field).type = "SignatureId"];

  message SignatureMessageEntry {
    SignatureTypeMessage key = 1;
    SignatureDataBox value = 2;
  }
  // all signatures created by user, can contain at most one instant for each type (typing/handwriting/image)
  repeated SignatureMessageEntry signature = 2;

  message SignatureBlockEntry {
    SignatureEnum.SignatureBlockType blockType = 1;
    SignatureDataBox data = 2;
  }
  // all fields that is not related to UserInfo created by user (company)
  repeated SignatureBlockEntry signatureBlocks = 3;

  // user's preferred signature type, to be used when user e-signs
  SignatureTypeMessage preferredSignatureType = 4;
}

message RecordTypeUnion {
    SignatureMessage _SignatureMessage = 1;
}
