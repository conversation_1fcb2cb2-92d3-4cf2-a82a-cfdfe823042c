package build.modules.signature

import build_.build.util_.*
import build.modules.bifrost

import mill.*
import mill.scalalib.*
import anduin.build.*
import anduin.mill.*

object `package` extends Module {

  object signatureModel extends StargazerModelCrossPlatformModule {
    object jvm extends JvmModelModule

    object js extends JsModelModule
  }

  object signatureIntegration extends AnduinScalaModule {
    override def moduleDeps = Seq(build.gondor.gondorCore.jvm)
  }

  object signature extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {

      override def moduleDeps = super.moduleDeps ++ Seq(bifrost.jvm, build.gondor.gondorCore.jvm, signatureIntegration)

      object it extends AnduinZioTests with AnduinIntegTests {
        override def moduleDeps = super.moduleDeps ++ Seq(build.gondor.gondorTest)
      }

    }

    object js extends JsModule {

      override def moduleDeps = super.moduleDeps ++ Seq(build.gondor.gondorCore.js)

    }

  }

  object signatureApp extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {
      override def moduleDeps = super.moduleDeps ++ Seq(signature.jvm)
    }

    object js extends JsModule with AnduinWebClientModule {
      override def webModule = AnduinWebModules.Signature

      override def moduleDeps = super.moduleDeps ++ Seq(signature.js, bifrost.js)

      override def mainClass = Some("anduin.signature.app.SignatureMainApp")
    }

  }

}