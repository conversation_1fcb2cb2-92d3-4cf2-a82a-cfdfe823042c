module Overcommit::Hook::PreCommit
    # Runs `scalafix` against any modified Scala files.
    #
    class Scalafix < Base
  
      def run
  
        applicable_files.each_slice(200) do |files_group|
          result = execute(command, args: files_group)
  
          output = result.stderr.chomp
  
          return [:fail, output] unless result.success?
        end
  
        :pass
  
      end
    end
  end
  