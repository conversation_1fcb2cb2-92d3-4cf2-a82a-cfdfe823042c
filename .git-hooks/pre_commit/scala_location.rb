require 'pathname'

module Overcommit::Hook::PreCommit
  class ScalaLocation < Base
    WHITE_LIST = [
      'build/', 'project/',
      'platform/', 'gondor/', 'apps/', 'modules/',
      'mima/', 'office-add-in/', 'refactoring/'
    ]

    def run
      errors = []
      root = Pathname.new(File.expand_path('.'))

      applicable_files.each do |file|
        path = Pathname.new(file).relative_path_from(root).to_s
        approved = WHITE_LIST.any? { |prefix| path.start_with? prefix }
        unless approved
          errors << path
        end
      end

      return :fail, %{
Found Scala code in wrong location:
#{errors.join("\n")}

Approved directories:
#{WHITE_LIST.join("\n")}} if errors.any?

      :pass
    end
  end
end
