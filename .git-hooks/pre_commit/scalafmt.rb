module Overcommit::Hook::PreCommit
  # Runs `scalafmt` against any modified Scala files.
  #
  class Scalafmt < Base

    def run

      applicable_files.each_slice(200) do |files_group|
        result = execute(command, args: files_group)

        messages = result.stdout.chomp
        # TODO: return all of them?
        return [:fail, messages] unless result.success? || messages.empty?
      end

      :pass

    end
  end
end
