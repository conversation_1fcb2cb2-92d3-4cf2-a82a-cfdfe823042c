syntax = "proto3";

package anduin.analytics.workflow;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
    package_name: "anduin.analytics.workflow"
    single_file: true
};

enum WorkflowNameProto {
    ActionEventExportWorkflow = 0;
    FundSubBillingExportWorkflow = 1;
    AuditLogsExportWorkflow = 2;
    DataRoomInsightsExportWorkflow = 3;
    SandboxSetupTimeExportWorkflow = 4;
    EmailsExportWorkflow = 5;
    FormAsaExportWorkflow = 6;
    UserExportWorkflow = 7;
    FundSubLpSubmissionExportWorkflow = 8;
    InvestorAccessAnalyticExportWorkflow = 9;
    FundSubIAConfigExportWorkflow = 10;
    FundSubCommentExportWorkflow = 11;
    FundSubLpFormFieldsSourceExportWorkflow = 12;
    FundSubPermissionAnalyticsExportWorkflow = 13;
    FundDataLandingPageAnalyticsExportWorkflow = 14;
}
