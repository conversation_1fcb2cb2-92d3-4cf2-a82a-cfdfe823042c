syntax = "proto3";

package anduin.protobuf.dataprotection;

import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";

import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.dataprotection"
  single_file: true
  import: "anduin.id.job.DataRequestJobId"
  import: "anduin.id.entity.EntityId"
  import: "anduin.id.TransactionId"
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.model.common.user.UserId"
  import: "java.time.Instant"
};

message ExportEntity {
  string entity_id = 1 [(scalapb.field).type = "EntityId"];

  bool should_export_db = 2;
  bool should_export_files = 3;
  bool should_export_trxns = 4;
  bool should_export_users = 5;

  bool should_delete_db = 6;
  bool should_delete_files = 7;
  bool should_delete_trxns = 8;
  bool should_delete_users = 9;
}

message ExportTransaction {
  string trxn_id = 1 [(scalapb.field).type = "TransactionId"];

  bool should_export_db = 2;
  bool should_export_files = 3;
  bool should_export_users = 4;

  bool should_delete_db = 5;
  bool should_delete_files = 6;
  bool should_delete_users = 7;
}

message ExportUser {
  string user_id = 1 [(scalapb.field).type = "UserId"];

  bool should_export_files = 2;

  bool should_delete_user = 3;
  bool should_delete_files = 4;
}

message ExportFundSub {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];

  bool should_export_db = 2;
  bool should_export_files = 3;
  bool should_export_trxns = 4;
  bool should_export_users = 5;

  bool should_delete_db = 6;
  bool should_delete_files = 7;
  bool should_delete_trxns = 8;
  bool should_delete_users = 9;
}

message DataExportRequest {
  oneof sealed_value {
    ExportEntity entity = 1;
    ExportTransaction transaction = 2;
    ExportUser user = 3;
    ExportFundSub fund_sub = 4;
  }
}

message RequestResult {
  InstantMessage completed_at = 1 [(scalapb.field).type = "Instant"];
  google.protobuf.StringValue request_download_url = 2;
}

message DataRequestJobModel {
  string request_id = 1 [(scalapb.field).type = "DataRequestJobId"];
  DataExportRequest request = 2;
  InstantMessage requested_at = 3 [(scalapb.field).type = "Instant"];
  string requested_by = 4 [(scalapb.field).type = "UserId"];
  RequestResult result = 5;
  InstantMessage cancelled_at = 6 [(scalapb.field).type = "Instant"];
}

message RecordTypeUnion {
  DataRequestJobModel _DataRequestJobModel = 1;
}
