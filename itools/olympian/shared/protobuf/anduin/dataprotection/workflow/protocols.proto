syntax = "proto3";

package anduin.dataprotection.workflow;

import "scalapb/scalapb.proto";
option (scalapb.options) = {
    package_name: "anduin.dataprotection.workflow"
    single_file: true
    import: "anduin.id.job.DataRequestJobId"
    import: "anduin.id.job.DataRequestJobId.given"
};

message DataRequestWorkflowParams {
    string job_id = 1  [(scalapb.field).type = "DataRequestJobId"];
}

message DataRequestWorkflowResponse {
    string job_id = 1  [(scalapb.field).type = "DataRequestJobId"];
}