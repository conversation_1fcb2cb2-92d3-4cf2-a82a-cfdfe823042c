syntax = "proto3";

package anduin.dataprotection.workflow;

import "scalapb/scalapb.proto";
option (scalapb.options) = {
    package_name: "anduin.dataprotection.workflow"
    single_file: true
    import: "anduin.id.job.DataRequestJobId"
    import: "anduin.id.job.DataRequestJobId.given"
    import: "anduin.id.fundsub.FundSubId"
    import: "anduin.id.fundsub.FundSubId.given"
    import: "anduin.id.fundsub.FundSubLpId"
    import: "anduin.id.fundsub.FundSubLpId.given"
    import: "anduin.model.common.user.UserId"
    import: "anduin.model.common.user.UserId.given"
    import: "anduin.model.id.FolderId"
    import: "anduin.model.id.FolderId.given"
    import: "anduin.id.TransactionId"
    import: "anduin.id.TransactionId.given"
};

// ===== JOB MANAGEMENT =====
message GetJobDetailsParams {
    string job_id = 1 [(scalapb.field).type = "DataRequestJobId"];
}

message GetJobDetailsResponse {
    string job_id = 1 [(scalapb.field).type = "DataRequestJobId"];
    string request_type = 2;
    string fund_sub_id = 3 [(scalapb.field).type = "FundSubId"];
    string user_id = 4 [(scalapb.field).type = "UserId"];
    string transaction_id = 5 [(scalapb.field).type = "TransactionId"];
    bool should_delete_db = 6;
    bool should_delete_files = 7;
    bool should_delete_user = 8;
}

// ===== EXPORT OPERATIONS - SCAN PHASE =====
message GetAdminFolderIdsParams {
    string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message GetAdminFolderIdsResponse {
    repeated string folder_ids = 1 [(scalapb.field).type = "FolderId"];
}

message GetLpFolderIdsParams {
    string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message GetLpFolderIdsResponse {
    repeated string folder_ids = 1 [(scalapb.field).type = "FolderId"];
}

message GetAllFundSubFolderIdsParams {
    string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message GetAllFundSubFolderIdsResponse {
    repeated string folder_ids = 1 [(scalapb.field).type = "FolderId"];
}

// ===== EXPORT OPERATIONS - EXECUTION PHASE =====
message CreateExportZipParams {
    string job_id = 1 [(scalapb.field).type = "DataRequestJobId"];
    repeated string folder_ids = 2 [(scalapb.field).type = "FolderId"];
    string export_type = 3;
}

message CreateExportZipResponse {
    string download_url = 1;
}

// ===== USER REMOVAL OPERATIONS =====
message DeactivateUserParams {
    string user_id = 1 [(scalapb.field).type = "UserId"];
    string job_id = 2 [(scalapb.field).type = "DataRequestJobId"];
}

message DeactivateUserResponse {
    bool deactivated = 1;
}

message UpdateUserProfileParams {
    string user_id = 1 [(scalapb.field).type = "UserId"];
    string dummy_first_name = 2;
    string dummy_last_name = 3;
    string dummy_email = 4;
}

message UpdateUserProfileResponse {
    bool updated = 1;
}

message DeleteUserSessionsParams {
    string user_id = 1 [(scalapb.field).type = "UserId"];
}

message DeleteUserSessionsResponse {
    int32 sessions_deleted = 1;
}

// ===== FUND VERIFICATION =====
message VerifyFundArchivedParams {
    string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message VerifyFundArchivedResponse {
    bool is_archived = 1;
}

// ===== DATABASE REMOVAL - SCAN PHASE =====
message GetFormIdsToRemoveParams {
    string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message GetFormIdsToRemoveResponse {
    repeated string form_ids = 1;
}

message GetLpIdsToRemoveParams {
    string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message GetLpIdsToRemoveResponse {
    repeated string lp_ids = 1 [(scalapb.field).type = "FundSubLpId"];
}

// ===== DATABASE REMOVAL - INDIVIDUAL STORE OPERATIONS =====
// Each of the 17 stores gets its own activity for fine-grained control
message RemoveFundSubModelStoreDataParams {
    string transaction_id = 1 [(scalapb.field).type = "TransactionId"];
}

message RemoveFundSubModelStoreDataResponse {
    int32 records_removed = 1;
}

message RemoveFundSubLpModelStoreDataParams {
    string transaction_id = 1 [(scalapb.field).type = "TransactionId"];
}

message RemoveFundSubLpModelStoreDataResponse {
    int32 records_removed = 1;
}

message RemoveLpInfoStoreDataParams {
    string transaction_id = 1 [(scalapb.field).type = "TransactionId"];
}

message RemoveLpInfoStoreDataResponse {
    int32 records_removed = 1;
}

message RemoveLpActivityStoreDataParams {
    string transaction_id = 1 [(scalapb.field).type = "TransactionId"];
}

message RemoveLpActivityStoreDataResponse {
    int32 records_removed = 1;
}

message RemoveLpTagStoreDataParams {
    string transaction_id = 1 [(scalapb.field).type = "TransactionId"];
}

message RemoveLpTagStoreDataResponse {
    int32 records_removed = 1;
}

message RemoveNewLpReportEmailLogDataParams {
    string transaction_id = 1 [(scalapb.field).type = "TransactionId"];
}

message RemoveNewLpReportEmailLogDataResponse {
    int32 records_removed = 1;
}

message RemoveFundSubContactGroupStoreDataParams {
    string transaction_id = 1 [(scalapb.field).type = "TransactionId"];
}

message RemoveFundSubContactGroupStoreDataResponse {
    int32 records_removed = 1;
}

message RemoveFundSubWhiteLabelStoreDataParams {
    string transaction_id = 1 [(scalapb.field).type = "TransactionId"];
}

message RemoveFundSubWhiteLabelStoreDataResponse {
    int32 records_removed = 1;
}

message RemoveFundSubExportTemplateStoreDataParams {
    string transaction_id = 1 [(scalapb.field).type = "TransactionId"];
}

message RemoveFundSubExportTemplateStoreDataResponse {
    int32 records_removed = 1;
}

// ===== FORM DATA REMOVAL =====
message RemoveFormDataParams {
    string form_id = 1;
}

message RemoveFormDataResponse {
    int32 records_removed = 1;
}

// ===== GRAPHQL DATA REMOVAL =====
message GetSubscriptionDataSchemasToRemoveParams {
    string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message GetSubscriptionDataSchemasToRemoveResponse {
    repeated string schema_ids = 1;
}

message RemoveSubscriptionDataSchemaParams {
    string schema_id = 1;
}

message RemoveSubscriptionDataSchemaResponse {
    bool removed = 1;
}

message RemoveFundDataSchemaParams {
    string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message RemoveFundDataSchemaResponse {
    bool removed = 1;
}

message RemoveFundDataTemplatesParams {
    string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message RemoveFundDataTemplatesResponse {
    bool removed = 1;
}

message GetFundDataSubscriptionsToRemoveParams {
    string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message GetFundDataSubscriptionsToRemoveResponse {
    repeated string subscription_ids = 1;
}

message RemoveFundDataSubscriptionsParams {
    repeated string subscription_ids = 1;
}

message RemoveFundDataSubscriptionsResponse {
    int32 subscriptions_removed = 1;
}

// ===== FILE REMOVAL OPERATIONS =====
message RemoveAdminChannelFilesParams {
    string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message RemoveAdminChannelFilesResponse {
    int32 files_removed = 1;
    int32 folders_removed = 2;
}

message RemoveLpChannelFilesParams {
    string lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
    string channel_type = 2; // SubscriptionDoc, SupportingDoc, ReferenceDoc, SideLetter
}

message RemoveLpChannelFilesResponse {
    int32 files_removed = 1;
    int32 folders_removed = 2;
}

// ===== RESULT OPERATIONS =====
message UpdateJobResultParams {
    string job_id = 1 [(scalapb.field).type = "DataRequestJobId"];
    string download_url = 2;
    int64 completed_at_millis = 3;
}

message UpdateJobResultResponse {
    bool updated = 1;
}