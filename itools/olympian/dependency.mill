package build.itools.olympian

import build_.build.versions_.AnduinDesign

import anduin.build.AnduinVersions
import mill.scalalib.*

import anduin.build.AnduinVersions

object Olympian {

  lazy val jsDeps = Seq(
    mvn"design.anduin::web-monaco::${AnduinDesign.version}"
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio"))
      .exclude("com.github.japgolly.scalajs-react" -> AnduinVersions.j2sjs("core"))
      .exclude("com.olvind" -> AnduinVersions.j2sjs("scalablytyped-runtime"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("sourcecode"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("fastparse"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("pprint"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("ujson"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("scalatags"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("geny")),
    mvn"design.anduin::web-x6::${AnduinDesign.version}"
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio"))
      .exclude("com.github.japgolly.scalajs-react" -> AnduinVersions.j2sjs("core"))
      .exclude("org.portable-scala" -> AnduinVersions.j2sjs("portable-scala-reflect"))
      .exclude("com.olvind" -> AnduinVersions.j2sjs("scalablytyped-runtime"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("sourcecode"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("fastparse"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("pprint"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("ujson"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("scalatags"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("geny"))
  )

}
