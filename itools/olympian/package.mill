package build.itools.olympian

import build_.build.util_.*
import build_.itools.olympian.dependency_.Olympian

import build.modules.{
  bifrost,
  dataroom,
  fundsub,
  gaia,
  heimdall,
  dynamicFormTest,
  brienne,
  investorProfile,
  ontology,
  sa,
  fundData,
  greylin,
  dataextract,
  integplatform,
  ria,
  graphql
}

import anduin.build.*
import anduin.mill.*

object `package` extends AnduinCrossPlatformModule {

  object jvm extends JvmModule with AnduinPlatformScalaPBModule {

    override def moduleDeps = super.moduleDeps ++ Seq(
      fundsub.fundsub.jvm,
      dataroom.dataroom.jvm,
      graphql.jvm,
      brienne.brienne,
      investorProfile.investorProfileSubscription
    )

    object it extends AnduinZioTests with AnduinIntegTests {
      override def moduleDeps = super.moduleDeps ++ Seq(fundsub.fundsubTest)
    }

  }

  object js extends JsModule with AnduinWebClientModule with AnduinPlatformScalaPBModule {
    override def webModule = AnduinWebModules.Olympian
    override def mvnDeps = super.mvnDeps() ++ Olympian.jsDeps
    override def mainClass = Some("anduin.olympian.client.OlympianMainApp")

    override def moduleDeps = super.moduleDeps ++ Seq(
      bifrost.js,
      dataroom.dataroomIntegration.js,
      fundsub.fundsubCore.js,
      gaia.gaia.js,
      heimdall.heimdall.js,
      dynamicFormTest.dynamicFormTest.js,
      brienne.brienneCore.js,
      investorProfile.investorProfileCore.js,
      ontology.ontologyCore.js,
      sa.saCore.js,
      fundData.fundDataCore.js,
      greylin.greylin.js,
      dataextract.dataextractCore.js,
      integplatform.integplatformCore.js,
      ria.riaCore.js
    )

    // TODO: @hoangmai to investigate linking error
    //        object test extends JsTests {
    //          override def moduleDeps = super.moduleDeps ++ Seq(stargazerTest.js)
    //        }

  }

}
