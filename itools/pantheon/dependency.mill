// Copyright (C) 2014-2025 Anduin Transactions Inc.
package build.itools.pantheon

import anduin.build.AnduinVersions
import mill.scalalib.*

object PantheonWebWorker {

  lazy val jsDeps = Seq(
    mvn"org.scala-js::scalajs-dom::${AnduinVersions.scalajsdom}",
    mvn"com.lihaoyi::sourcecode::${AnduinVersions.sourcecode}",
    mvn"com.outr::scribe::${AnduinVersions.scribe}"
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("sourcecode"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("sourcecode"))
      .exclude("com.github.jnr" -> "jnr-posix")
      .exclude("org.ow2.asm" -> "asm")
      .exclude("org.ow2.asm" -> "asm-commons")
      .exclude("org.ow2.asm" -> "asm-analysis")
      .exclude("org.ow2.asm" -> "asm-tree")
      .exclude("org.ow2.asm" -> "asm-util")
  )

}

object Pantheon {

  lazy val jsDeps = Seq(
    mvn"com.raquo::domtypes::${AnduinVersions.domTypes}"
      .exclude("org.scala-js" -> AnduinVersions.j2sjs("scalajs-dom")),
    mvn"be.doeraene::url-dsl::${AnduinVersions.urlDsl}",
    mvn"com.raquo::waypoint::${AnduinVersions.waypoint}"
      .exclude("com.raquo" -> AnduinVersions.j2sjs("laminar"))
      .exclude("com.raquo" -> AnduinVersions.j2sjs("airstream"))
      .exclude("be.doeraene" -> AnduinVersions.j2sjs("url-dsl"))
      .exclude("org.scala-js" -> AnduinVersions.j2sjs("scalajs-dom")),
    mvn"com.raquo::airstream::${AnduinVersions.airstream}"
      .exclude("org.scala-js" -> AnduinVersions.j2sjs("scalajs-dom")),
    mvn"com.raquo::laminar::${AnduinVersions.laminar}"
      .exclude("com.raquo" -> AnduinVersions.j2sjs("airstream"))
      .exclude("com.raquo" -> AnduinVersions.j2sjs("domtypes"))
  )

}
