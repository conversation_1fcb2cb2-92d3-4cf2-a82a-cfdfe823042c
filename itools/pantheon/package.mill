// Copyright (C) 2014-2025 Anduin Transactions Inc.

package build.itools.pantheon

import mill.Module
import build_.build.util_.*
import build_.itools.pantheon.dependency_.*
import build.modules.{fundsub, fundData, graphql, dataextract, gaia}
import build.gondor.gondorCore
import build.itools.olympian
import mill.scalajslib.api.*
import anduin.build.*
import anduin.mill.*
import anduin.mill.jsdeps.*

object `package` extends Module {

  object pantheonWebWorker extends AnduinScalaJSModule with AnduinWebClientModule {
    override def webModule = AnduinWebModules.PantheonWorker
    override def mvnDeps = super.mvnDeps() ++ PantheonWebWorker.jsDeps
    override def mainClass = Some("anduin.pantheon.client.PantheonWebWorker")
  }

  object pantheon extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {

      override def moduleDeps = super.moduleDeps ++ Seq(
        fundsub.fundsub.jvm,
        fundData.fundDataCore.jvm,
        graphql.jvm
      )

    }

    object js extends JsModule with AnduinWebClientModule {

      override def esFeatures = ESFeatures.Defaults.withESVersion(ESVersion.ES2018)

      override def webModule = AnduinWebModules.Pantheon

      override def mvnDeps = super.mvnDeps() ++ Pantheon.jsDeps

      override def moduleDeps = super.moduleDeps ++ Seq(
        gondorCore.js,
        gaia.gaia.js,
        gaia.gaiaBuilder,
        olympian.js,
        fundData.fundDataCore.js
      )

      override def mainClass = Some("anduin.pantheon.client.PantheonMainApp")

    }

  }

}
