networkDnsSuffix = ".gondor.svc.kube"

stargazer {
  timeout  = 1 minute
  # enableMetric = false # Uncomment this to disable metric tracking

  commonConfig {
    captchaCommonConfig {
      isEnabled = false
    }

    multiRegionConfig {
      isSecondaryRegion = false
      mainRegionEndpoint = "http://localhost:8080"
    }

    cueModuleConfig {
      root = "dev.anduintransact.com/acs_root.cue:v0.0.0"
      table = "dev.anduintransact.com/acs_table.cue:v0.0.0"
      fundsub = "dev.anduintransact.com/acs_fundsub.cue:v0.0.0"
      dataextract = "dev.anduintransact.com/acs_dataextract.cue:v0.0.0"
      datalayer = "dev.anduintransact.com/acs_datalayer.cue:v0.0.0"
    }
  }

  # All the services
  backendConfig {
    server {
      baseUrl = "http://gondor-local.io:8080"
      portalUrl = "http://gondor-local.io:8080"
    }

    # account service
    account {
      token {
        duration = 24 hours
      }
    }

    couchbase {
      ioThreadPoolSize = 2
      computeThreadPoolSize = 2
    }

    cronCouchbaseConfig {
      couchbaseServiceName = "gondor"
    }

    ses {
      configurationSet = "ses-dev-v2-01" # You can change the suffix from 01 -> 05
      sqsReceiveQueue = "ses-receive-dev-v2-01"
      sqsEventQueue = "ses-event-dev-v2-01"
    }

    email {
      sending {
        disableSendingEmail = false
        retry {
          maxCount = 2 # Make this short in local dev
        }
      }
    }

    aws {
      S3 {
        cloudFrontConfig = {
          cloudFrontEnabled = true
        }
      }
    }

    customDomainConfig {
      isEnabled = false
    }

    analyticsConfig {
      s3Prefix = "my-prefix" # Change this to your taste
    }

    oauth2Config {
      clients = [
        {
          name = "google"
          clientId = "************-477b38och9aaaodrvls12uma2ucq2403.apps.googleusercontent.com"
          secret = "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"
        }
      ]
    }

    deploymentTesterConfig {
      oauth2BaseUrl = "http://gondor-local.io:8080"
    }

    systemAuditLogConfig {
      # enable = true
    }

    dataPipelineConfig {
      # enableDataIngest = true
      # enableDataTransform = true
      # enableDataCorrectionCron = true
    }

    actionLoggerConfig {
      # isEnabled = true
    }

    getFeedbackBackendConfig {
      # cspHash = "sha256-PxHicg3EHzcB5/UTT635V9kUvtAVs9dpjYs7U7BbUVo=" # Local
    }

    serverlessConfig {
      namespace = "dev"
      serverlessRegionConfig {
        region = "ap-southeast-1"
      }
    }
    prismaticConfig {
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    }

    turnstileBackendConfig {
      # invisibleSecret = "1x0000000000000000000000000000000AA" # Always pass
      # invisibleSecret = "2x0000000000000000000000000000000AA" # Always block
      # visibleSecret = "1x0000000000000000000000000000000AA" # Always pass
      # visibleSecret = "2x0000000000000000000000000000000AA" # Always pass
    }

    tidbConfig {
      # host = tidb-fake-mysql.gondor.svc.kube
      # port = 3306
    }

    textract {
      useApi = true
    }
  }

  # frontend config
  frontendConfig {

    oauth2FrontendConfig {
      clients = [
        {
          name = "google"
          isEnabled = true
        }
      ]
    }

    getFeedbackConfig {
      # fundSubButtonId = "df0a1e165e53" # Local
    }

    turnstileFrontendConfig {
      # invisibleSiteKey = "1x00000000000000000000BB" # Always pass
      # invisibleSiteKey = "2x00000000000000000000BB" # Always block
      # visibleSiteKey = "1x00000000000000000000AA" # Always pass
      # visibleSiteKey = "2x00000000000000000000AB" # Always pass
      # visibleSiteKey = "3x00000000000000000000FF" # Always challenge
    }

  }
}

# vi: ft=hocon
