#!/usr/bin/env bash

here=$(cd $(dirname $BASH_SOURCE); pwd)
mailgunApi="https://api.mailgun.net/v3"
webhooks=(bounce deliver drop spam unsubscribe click open)

function getConfigFile {
    localConfFile="$here/../../local/local.conf"
    filterNotCommentsRegex="^\s*//\s*"
    configFile=$(cat "$localConfFile" | grep -vE "$filterNotCommentsRegex")
}

function getKey {
    keyRegex="mailgun.*apiKey = \"([^\"]*)\""
    if [[ "$configFile" =~ $keyRegex ]]; then
        key="${BASH_REMATCH[1]}"
    else
        echo "Mailgun apiKey not found"
        exit 1
    fi
}

function getDomain {
    domainRegex="mailgun.*domain = \"([^\"]*)\""
    if [[ "$configFile" =~ $domainRegex ]]; then
        domain="${BASH_REMATCH[1]}"
    else
        echo "Mailgun domain not found"
        exit 1
    fi
}

function checkDomain {
    if [[ "$domain" =~ "anduin" ]]; then
        echo "Domain contains anduin. This doesn't look like a test account"
        exit 1
    fi
}

function getNgrokServer {
    ngrokServer=$(curl -s http://localhost:4040/api/tunnels | jq -r ".tunnels | map(select(.proto == \"http\"))[0].public_url")
    if [[ -n "$ngrokServer" ]]; then
        echo "Mapping $domain => $ngrokServer"
    else
        echo "ngrok is not started yet"
        exit 1
    fi
}

function setupMailgun {
    API_KEY="${key}" ${here}/mailgun-routes.sh "${domain} ${ngrokServer}"
}

getConfigFile &&
    getKey &&
    getDomain &&
    checkDomain &&
    getNgrokServer &&
    setupMailgun
