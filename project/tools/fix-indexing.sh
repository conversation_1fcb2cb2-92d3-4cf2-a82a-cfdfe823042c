#!/usr/bin/env bash

sedCmd=''

case `uname` in
  Darwin)
    sedCmd=gsed
  ;;
  Linux)
    sedCmd=sed
  ;;
esac

deleteContent() {
  $sedCmd -i "\<sourceFolder.*src_managed.*/d" $1
}

insertContent() {
  $sedCmd -i "/$1/i $2" $3
}

deleteContent '.idea/modules/gondorModelJS.iml' &> /dev/null
deleteContent '.idea/modules/gondorModelJVM.iml' &> /dev/null
deleteContent '.idea/modules/gondorModel-sources.iml' &> /dev/null

insertContent '\/content' '\
      \<sourceFolder url\="file\://\$MODULE_DIR\$/\.\./\.\./gondor/gondorModel/\.js/target/scala\-2\.13/src_managed/main" isTestSource\="false" /\> \
      \<sourceFolder url\="file\://\$MODULE_DIR\$/\.\./\.\./gondor/gondorModel/\.jvm/target/scala\-2\.13/src_managed/main" isTestSource\="false" /\>' '.idea/modules/gondorModel-sources.iml'

deleteContent '.idea/modules/stargazerModelJS.iml' &> /dev/null
deleteContent '.idea/modules/stargazerModelJVM.iml' &> /dev/null
deleteContent '.idea/modules/stargazerModel-sources.iml' &> /dev/null

insertContent '\/content' '\
      \<sourceFolder url\="file\://\$MODULE_DIR\$/\.\./\.\./platform/stargazerModel/\.js/target/scala\-2\.13/src_managed/main" isTestSource\="false" /\> \
      \<sourceFolder url\="file\://\$MODULE_DIR\$/\.\./\.\./platform/stargazerModel/\.jvm/target/scala\-2\.13/src_managed/main" isTestSource\="false" /\>' '.idea/modules/stargazerModel-sources.iml'

deleteContent '.idea/modules/inboxModelJVM.iml' &> /dev/null
deleteContent '.idea/modules/inboxModelJS.iml' &> /dev/null
deleteContent '.idea/modules/inboxModel-sources.iml' &> /dev/null

insertContent '\/content' '\
      \<sourceFolder url\="file\://\$MODULE_DIR\$/\.\./\.\./modules/inbox/inboxModel/.js/target/scala\-2\.13/src_managed/main" isTestSource\="false" /\> \
      \<sourceFolder url\="file\://\$MODULE_DIR\$/\.\./\.\./modules/inbox/inboxModel/.jvm/target/scala\-2\.13/src_managed/main" isTestSource\="false" /\>' '.idea/modules/inboxModel-sources.iml'
