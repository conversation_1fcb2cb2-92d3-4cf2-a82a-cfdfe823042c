#!/usr/bin/env bash

export ARTIFACTORY_URL=http://artifactory.anduin.co
if [ -z "$ARTIFACTORY_PACKAGE" ]; then
    export ARTIFACTORY_PACKAGE=gondorAppServer
fi
export ARTIFACTORY_NAMESPACE=com/anduin/stargazer/$ARTIFACTORY_PACKAGE
export ARTIFACTORY_USER=readonly
export ARTIFACTORY_PASSWORD=gBz363GIM7oNJdxm6e

function showHelp {
    echo "USAGE:"
    echo "    - $0 run [artifact-type] [tag]"
    echo "    - $0 list [artifact-type]"
}

function installDeps {
    which jq > /dev/null 2>&1
    if [ $? -ne 0 ]; then
        brew install jq
    fi
    which gsed > /dev/null 2>&1
    if [ $? -ne 0 ]; then
        brew install gnu-sed
    fi
    which wget > /dev/null 2>&1
    if [ $? -ne 0 ]; then
        brew install wget
    fi
}

function listArtifact {
    artifactType=$1
    query='items.find({"repo":{"$eq":"'$artifactType'"}}).include("repo","path","name","created")'
    url=$ARTIFACTORY_URL/artifactory/api/search/aql
    results=`curl -sS -u "$ARTIFACTORY_USER:$ARTIFACTORY_PASSWORD" $url -d "$query"`
    echo "$results" | jq '.results[] | .created + " " + .name' | sed 's/"//g' | sort | cut -d ' ' -f 2 | grep '.zip' | sed 's/.zip//' | grep $ARTIFACTORY_PACKAGE | sed 's/'$ARTIFACTORY_PACKAGE'-//'
}

function runArtifact {
    artifactType=$1
    if [ $# -lt 2 ]; then
        echo "Using latest tag"
        TAG=`listArtifact $1 | tail -1`
        if [ -z "$TAG" ]; then
            echo "Cannot find latest tag"
            exit 1
        fi
    else
        TAG=$2
    fi
    echo "Running artifact $TAG"

    here=`cd $(dirname $BASH_SOURCE); pwd`
    artifactFolder=$here/../../local/artifacts/$artifactType/$TAG
    zipFile=$artifactFolder/$TAG.zip
    rm -rf $artifactFolder/stargazer
    mkdir -p $artifactFolder
    if [ ! -f $zipFile ]; then
        wget --user $ARTIFACTORY_USER --password $ARTIFACTORY_PASSWORD -O $zipFile $ARTIFACTORY_URL/artifactory/$artifactType/$ARTIFACTORY_NAMESPACE/$TAG/$ARTIFACTORY_PACKAGE-$TAG.zip
        if [ $? -ne 0 ]; then
            rm -f $zipFile
            exit 1
        fi
    fi
    cd $artifactFolder
    if [ -z "$API_PORT" ]; then
        export API_PORT=8080
    fi
    if [ $1 == "stargazer-pr" ]; then
        PRODUCTION=false
    else
        PRODUCTION=true
    fi
    unzip $zipFile && \
        cp $here/../../local/local.conf $artifactFolder/stargazer/conf/application.conf && \
        gsed -i 's/-Dcom.anduin.stargazer.production=true/-Dcom.anduin.stargazer.production='$PRODUCTION'/' $artifactFolder/stargazer/conf/application.ini && \
        gsed -i 's/ sed / gsed /' $artifactFolder/stargazer/bin/gondorappserver && \
        cd $artifactFolder/stargazer/bin
    exec ./gondorappserver
}

if [ $# -lt 1 ]; then
    showHelp
    exit 1
fi

installDeps

command=$1
shift
case $command in
    list)
        listArtifact $@
        ;;
    run)
        runArtifact $@
        ;;
    *)
        showHelp
        exit 1
        ;;
esac
