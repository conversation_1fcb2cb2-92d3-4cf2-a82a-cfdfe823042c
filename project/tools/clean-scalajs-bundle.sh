#!/usr/bin/env bash
set -e

function deleteScalajsBundler() {
    rm -rf $1/target/
    rm -f $1/yarn.lock
}

yarn config delete registry

deleteScalajsBundler gondor/js

deleteScalajsBundler apps/gondor/gondorAppClient
deleteScalajsBundler apps/maya/mayaApp/js
deleteScalajsBundler apps/boardconsent/boardconsentApp/js

deleteScalajsBundler itools/olympian/olympian/js
deleteScalajsBundler itools/pantheon/pantheon/js

deleteScalajsBundler modules/standaloneapp/standaloneapp/js
deleteScalajsBundler modules/narya/narya/js
deleteScalajsBundler modules/fundsub/fundsubApp/js
deleteScalajsBundler modules/signature/signatureApp/js
deleteScalajsBundler modules/dataroom/dataroomApp/js
deleteScalajsBundler modules/heimdall/heimdallApp/js
deleteScalajsBundler modules/investorprofile/lpprofileApp/js