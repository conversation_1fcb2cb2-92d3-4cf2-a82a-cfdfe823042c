#!/usr/bin/env bash

TINYPROXY=$(which tinyproxy)
KEYCLOAK_SERVER=keycloak.gondor.svc.kube-local.io
KEYCLOAK_REALM=anduin
KEYCLOAK_CLIENT=gondor
IP=""

function ensureTinyproxy() {
    if [ ! -f "$TINYPROXY" ]; then
        echo "Installing tinyproxy"
        brew install tinyproxy
        TINYPROXY=$(which tinyproxy)
    fi
}

function findIpAddresses() {
    possibleIps=`ifconfig | grep inet | grep -v inet6 | awk '{print $2}' | grep -v '127.0' | grep -v '*************'`
    if [ $? -ne 0 ]; then
        return 1
    fi
    numberOfIps=`echo "$possibleIps" | wc -l`
    case $numberOfIps in
        "0")
            echo "Cannot find any private ip address"
            return 1
            ;;
        *)
            IP=`echo "$possibleIps" | head -1`
            ;;
    esac
}

function runProxy {
    echo "Running proxy. Please use '$IP:8888' as your device HTTP Proxy"
    $TINYPROXY -d -c $here/tinyproxy.conf
}

here=`cd $(dirname $BASH_SOURCE); pwd`
ensureTinyproxy &&
    findIpAddresses &&
    runProxy
