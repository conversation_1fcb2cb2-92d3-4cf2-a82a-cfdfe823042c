#!/usr/bin/env bash

here=$(cd $(dirname $BASH_SOURCE); pwd)
mailgunApi="https://api.mailgun.net/v3"
webhooks=(bounce deliver drop spam unsubscribe click open)

function getArgs {
    if [ $# -lt 2 ] || [ -z "${API_KEY}" ]; then
        showHelp
        exit 1
    else
        key=${API_KEY}
        emailDomain=$1
        backendUrl=$2
        mailgunApiWithDomain="$mailgunApi/domains/$emailDomain"
    fi
}

function deleteHook {
    curl -s --user "api:$key" -X DELETE \
        "$mailgunApiWithDomain/webhooks/$1"
    echo ""
}

function reAddHook {
    curl -s --user "api:$key" \
        "$mailgunApiWithDomain/webhooks" \
        -F id="$1" \
        -F url="$backendUrl/mail/webhook/$1"
    echo ""
}

function deleteEvent {
    curl -s --user "api:$key" -X DELETE \
        "$mailgunApi/routes/$1"
    echo ""
}

function addEvent {
    curl -s --user "api:$key" \
        "$mailgunApi/routes" \
        -F priority=0 \
        -F expression="match_recipient(\".*@$emailDomain\")" \
        -F action="store(notify=\"$backendUrl/mail/event\")" \
        -F action="stop()"
    echo ""
}

function postNewMailgunWebhooks {
    for hook in ${webhooks[@]}; do
        deleteHook ${hook} && reAddHook ${hook}
    done
}

function postNewMailgunEvents {
    existingEvents=( $(curl -s --user "api:$key" "$mailgunApi/routes" |
     jq -r ".items | map(select(.expression == \"match_recipient(\\\".*@$emailDomain\\\")\")) | .[].id") )
    for hook in ${existingEvents[@]}; do
        deleteEvent ${hook}
    done
    addEvent
}

function showHelp {
    echo "Usage: API_KEY=[apiKey] $0 [emailDomain] [backendUrl]"
    echo " - [apiKey]:          Mailgun API key"
    echo " - [emailDomain]:     Email domain configured on Mailgun"
    echo " - [backendUrl]:      URL to gondor server"
    exit 1
}

getArgs $@ &&
    postNewMailgunEvents &&
    postNewMailgunWebhooks
