#!/usr/bin/env bash

here=`cd $(dirname $BASH_SOURCE); pwd`

function attachedRepl {
    keyfile=$here/dummy-for-repl.id_rsa
    chmod og-rw $keyfile
    echo "Connecting to gondorAppServer running in dev mode..."
    ssh localhost -p 7777 -i $keyfile
}

function standaloneRepl {
    echo "Starting a standalone REPL..."
    PWD=$here/../.. ./sbt gondorREPL/compile:run -Dconfig.file=local/local.conf
}

type=$1
shift
case $type in
    attached)
        attachedRepl
        ;;
    standalone)
        standaloneRepl
        ;;
    *)
        standaloneRepl
        ;;
esac
