#!/usr/bin/env bash

set -e
here=`cd $(dirname $BASH_SOURCE); pwd`
source $here/common.sh
$here/install-clis.sh

$here/stop.sh && $here/start.sh

# https://docs.couchbase.com/sync-gateway/2.1/sync-function-api.html#changing-the-sync-function
curl -X POST "http://sync-gateway.gondor.svc.kube-local.io:4985/sync_gateway/_offline" -H "accept: application/json"
curl -X POST "http://sync-gateway.gondor.svc.kube-local.io:4985/sync_gateway/_resync" -H "accept: application/json"
curl -X POST "http://sync-gateway.gondor.svc.kube-local.io:4985/sync_gateway/_online" -H "accept: application/json"
