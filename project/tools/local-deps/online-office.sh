#!/usr/bin/env bash

set -e

here=`cd $(dirname $BASH_SOURCE); pwd`
source $here/common.sh
$here/install-clis.sh

if isMinikube; then
    eval $(minikube docker-env --shell=bash)
fi

function runStart {
    doriath $STARGAZER_DORIATH_PARAMS --config $here/../../../ci/tasks/doriath/online-office.yml build &&
        rivendell $STARGAZER_RIVENDELL_PARAMS up $here/../../../ci/tasks/rivendell/run/online-office.yml
}

function runStop {
    rivendell $STARGAZER_RIVENDELL_PARAMS down $here/../../../ci/tasks/rivendell/run/online-office.yml
}

if [ $# -lt 1 ]; then
    echo "USAGE: $0 start|stop"
    exit 1
fi

case $1 in
    start)
        runStart
        ;;
    stop)
        runStop
        ;;
    *)
        echo "USAGE: $0 start|stop"
        exit 1
esac
