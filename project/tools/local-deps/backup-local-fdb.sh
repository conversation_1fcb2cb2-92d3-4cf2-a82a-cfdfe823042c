#!/usr/bin/env bash
set -euo pipefail

# FoundationDB Local Backup Script
#
# This script creates a backup of a local FoundationDB cluster with improved
# reliability and progress monitoring.
#
# Usage:
#   ./backup-local-fdb.sh [TAG]
#
# Arguments:
#   TAG    Optional backup tag. Defaults to snap-YYYYMMDD-HHMMSS
#
# Environment Variables:
#   CLUSTER_FILE      Path to FDB cluster file (default: /etc/fdb/fdb.cluster)
#   BASE_DIR          Backup destination directory (default: $HOME/fdb_backups)
#   BACKUP_TIMEOUT    Backup timeout in seconds (default: 300)
#   BACKUP_AGENT_PATH Path to backup_agent binary
#
# Features:
#   - Automatic backup agent management
#   - Progress monitoring with timeout
#   - Health checks before backup
#   - Proper error handling and cleanup
#   - Backup verification
#
# Require fdbbackup, installed with the standard fdbclient

# ---- configurable variables -------------------------------------------------
CLUSTER_FILE="${CLUSTER_FILE:-/etc/fdb/fdb.cluster}"
BASE_DIR="${BASE_DIR:-$HOME/fdb_backups}"
TAG="${1:-snap-$(date +%Y%m%d-%H%M%S)}"         # optional first arg = custom tag
URL="file://${BASE_DIR}"
BACKUP_TIMEOUT="${BACKUP_TIMEOUT:-300}"          # 5 minutes default timeout
BACKUP_AGENT_PATH="${BACKUP_AGENT_PATH:-/usr/local/foundationdb/backup_agent/backup_agent}"

# ---- input validation -------------------------------------------------------
validate_tag() {
    local tag="$1"
    # Allow alphanumeric characters, hyphens, underscores, and dots
    if [[ ! "$tag" =~ ^[a-zA-Z0-9._-]+$ ]]; then
        error "Invalid tag format: '$tag'. Only alphanumeric characters, dots, hyphens, and underscores are allowed."
    fi
    # Prevent excessively long tags
    if [[ ${#tag} -gt 100 ]]; then
        error "Tag too long: '$tag'. Maximum length is 100 characters."
    fi
    # Prevent tags that could be confused with command options
    if [[ "$tag" =~ ^- ]]; then
        error "Tag cannot start with hyphen: '$tag'"
    fi
}

# ---- helper functions -------------------------------------------------------
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $*"
}

error() {
    log "ERROR: $*" >&2
    exit 1
}

check_cluster_health() {
    log "Checking cluster health..."
    if ! fdbcli -C "$CLUSTER_FILE" --exec "status" >/dev/null 2>&1; then
        error "FoundationDB cluster is not accessible. Check cluster file: $CLUSTER_FILE"
    fi
    log "✅ Cluster is healthy"
}

check_backup_agent() {
    log "Checking backup agent status..."

    # Check if backup agent is already running
    if pgrep -f backup_agent >/dev/null 2>&1; then
        log "Backup agent is already running, testing responsiveness..."
    else
        log "No backup agent found, starting one..."
        start_backup_agent
    fi

    # Wait a bit for the agent to fully initialize
    sleep 3

    # Comprehensive backup agent health check
    local test_tag="health-check-$(date +%s)"
    local test_output
    local health_check_passed=false

    # Test 1: Check if agent can accept backup requests
    log "Testing backup agent responsiveness..."
    if test_output=$(fdbbackup start -C "$CLUSTER_FILE" -t "$test_tag" -d "$URL" --dryrun 2>&1); then
        if echo "$test_output" | grep -q "successfully submitted\|can be used to restore"; then
            log "✅ Backup agent accepts requests"

            # Test 2: Check if agent can report status
            log "Testing backup agent status reporting..."
            local status_output
            if status_output=$(fdbbackup status -C "$CLUSTER_FILE" 2>&1); then
                if ! echo "$status_output" | grep -q "Unable to connect\|Connection refused"; then
                    log "✅ Backup agent status reporting works"
                    health_check_passed=true
                else
                    warning "Backup agent status reporting issues: $status_output"
                fi
            else
                warning "Failed to get backup status: $status_output"
            fi
        else
            warning "Backup agent test failed: $test_output"
        fi
    else
        warning "Failed to test backup agent: $test_output"
    fi

    # Clean up the dry run
    fdbbackup abort -C "$CLUSTER_FILE" -t "$test_tag" >/dev/null 2>&1 || true

    if [[ "$health_check_passed" == "true" ]]; then
        log "✅ Backup agent health check passed"
    else
        error "Backup agent health check failed. The agent may not be functioning properly."
    fi
}

start_backup_agent() {
    if [[ ! -x "$BACKUP_AGENT_PATH" ]]; then
        error "Backup agent not found at: $BACKUP_AGENT_PATH"
    fi

    # Ensure log directory exists
    mkdir -p "$BASE_DIR"
    local agent_log="$BASE_DIR/backup_agent.log"

    log "Starting backup agent (logs: $agent_log)..."

    # Start backup agent with proper logging
    nohup "$BACKUP_AGENT_PATH" -C "$CLUSTER_FILE" >> "$agent_log" 2>&1 &
    local agent_pid=$!

    if [[ -z "$agent_pid" ]]; then
        error "Failed to start backup agent process"
    fi
    sleep 3  # Give it time to start

    # Verify the process is still running
    if ! kill -0 "$agent_pid" 2>/dev/null; then
        error "Backup agent process died immediately. Check logs at: $agent_log"
    fi

    if ! pgrep -f backup_agent >/dev/null 2>&1; then
        error "Failed to start backup agent. Check logs at: $agent_log"
    fi

    log "✅ Backup agent started (PID: $agent_pid)"
}

restart_backup_agent() {
    log "Stopping existing backup agent..."

    # Get PIDs of backup agent processes
    local pids
    pids=$(pgrep -f backup_agent || true)

    if [[ -n "$pids" ]]; then
        # Send TERM signal first
        pkill -TERM -f backup_agent || true

        # Wait for graceful shutdown (up to 10 seconds)
        local wait_count=0
        while [[ $wait_count -lt 10 ]] && pgrep -f backup_agent >/dev/null 2>&1; do
            sleep 1
            wait_count=$((wait_count + 1))
        done

        # Force kill if still running
        if pgrep -f backup_agent >/dev/null 2>&1; then
            log "Force killing backup agent processes..."
            pkill -KILL -f backup_agent || true
            sleep 2
        fi

        # Final verification
        if pgrep -f backup_agent >/dev/null 2>&1; then
            error "Failed to stop backup agent processes"
        fi

        log "✅ Backup agent processes stopped"
    fi

    start_backup_agent
}

cleanup_failed_backup() {
    local tag="$1"
    log "Cleaning up failed backup: $tag"
    fdbbackup abort -C "$CLUSTER_FILE" -t "$tag" >/dev/null 2>&1 || true
}

wait_for_backup() {
    local tag="$1"
    local timeout="$2"
    local start_time=$(date +%s)
    local last_status=""

    log "Waiting for backup to complete (timeout: ${timeout}s)..."

    while true; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))

        if [[ $elapsed -gt $timeout ]]; then
            error "Backup timed out after ${timeout} seconds"
        fi

        # Get backup status
        local status_output
        if status_output=$(fdbbackup status -C "$CLUSTER_FILE" -t "$tag" 2>&1); then
            if echo "$status_output" | grep -q "completed"; then
                log "✅ Backup completed successfully"
                return 0
            elif echo "$status_output" | grep -q "in progress"; then
                # Extract progress information
                local progress_info=$(echo "$status_output" | grep -E "(RangeBytes|LogBytes|snapshot)" | head -3)
                if [[ "$progress_info" != "$last_status" ]]; then
                    log "Progress: $progress_info"
                    last_status="$progress_info"
                fi
            elif echo "$status_output" | grep -q "error\|failed"; then
                error "Backup failed: $status_output"
            fi
        else
            error "Failed to get backup status: $status_output"
        fi

        sleep 5
    done
}

# ---- main script ------------------------------------------------------------

# Handle help option
if [[ "${1:-}" == "-h" || "${1:-}" == "--help" ]]; then
    cat << 'EOF'
FoundationDB Local Backup Script

Usage:
  ./backup-local-fdb.sh [TAG]
  ./backup-local-fdb.sh --help

Arguments:
  TAG    Optional backup tag. Defaults to snap-YYYYMMDD-HHMMSS

Environment Variables:
  CLUSTER_FILE      Path to FDB cluster file (default: /etc/fdb/fdb.cluster)
  BASE_DIR          Backup destination directory (default: $HOME/fdb_backups)
  BACKUP_TIMEOUT    Backup timeout in seconds (default: 300)
  BACKUP_AGENT_PATH Path to backup_agent binary

Examples:
  ./backup-local-fdb.sh                    # Create backup with auto-generated tag
  ./backup-local-fdb.sh my-backup          # Create backup with custom tag
  BACKUP_TIMEOUT=600 ./backup-local-fdb.sh # Create backup with 10-minute timeout

EOF
    exit 0
fi

mkdir -p "$BASE_DIR"

log "Starting FoundationDB backup"

# Validate input parameters
validate_tag "$TAG"

log "Tag: $TAG"
log "Destination: $URL"
log "Cluster file: $CLUSTER_FILE"

# Pre-flight checks
check_cluster_health
check_backup_agent

# Check if backup with this tag already exists
log "Checking for existing backup with tag: $TAG"
status_output=""
status_exit_code=0

if status_output=$(fdbbackup status -C "$CLUSTER_FILE" -t "$TAG" 2>&1); then
    status_exit_code=$?
    if echo "$status_output" | grep -q "in progress\|completed"; then
        error "Backup with tag '$TAG' already exists. Choose a different tag or abort the existing backup."
    fi
else
    status_exit_code=$?
    # If status command fails, check if it's a connectivity issue
    if echo "$status_output" | grep -q "Unable to connect\|Connection refused\|timeout"; then
        error "Cannot connect to FoundationDB cluster to check backup status. Check cluster connectivity."
    elif echo "$status_output" | grep -q "No previous backups found\|not found"; then
        # This is expected for new backups
        log "No existing backup found with tag '$TAG' - proceeding"
    else
        warning "Backup status check returned unexpected output: $status_output"
        log "Proceeding with backup creation..."
    fi
fi

# Start the backup
log "⏳ Starting backup..."
if ! fdbbackup start -C "$CLUSTER_FILE" -t "$TAG" -d "$URL"; then
    error "Failed to start backup"
fi

# Wait for completion with timeout and progress monitoring
if ! wait_for_backup "$TAG" "$BACKUP_TIMEOUT"; then
    cleanup_failed_backup "$TAG"
    exit 1
fi

# Verify the backup
log "🔍 Verifying backup metadata..."
if backup_info=$(fdbbackup describe -d "$URL" -C "$CLUSTER_FILE" 2>&1); then
    echo "$backup_info" | sed -e '1,3d'
    log "✅ Backup verification completed"
else
    log "⚠️  Backup completed but verification failed: $backup_info"
fi

log "🎉 Backup process completed successfully!"
log "Backup location: $URL"
