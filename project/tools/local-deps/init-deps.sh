#!/usr/bin/env bash

here=`cd $(dirname $BASH_SOURCE); pwd`

function installRootCA {
    echo "Installing Root CA $1"
    sudo security add-trusted-cert \
         -d -r trustRoot \
         -p ssl -p smime -p codeSign -p IPSec -p iChat -p basic -p swUpdate -p pkgSign -p pkinitClient -p pkinitServer -p eap \
         -k /Library/Keychains/System.keychain $1

}

function ensureRootCA {
    security verify-cert -q -L -c $1 \
             -p ssl -p smime -p codeSign -p IPSec -p iChat -p basic -p swUpdate -p pkgSign -p pkinitClient -p pkinitServer -p eap
    if [ $? -ne 0 ]; then
        echo "Root CA is not installed"
        installRootCA $1
    else
        echo "Root CA was already installed"
    fi
}

echo "Checking Root CA"
ensureRootCA $here/ca.cert.pem

echo "Enabling Ingress addon for minikube"
minikube addons enable ingress

