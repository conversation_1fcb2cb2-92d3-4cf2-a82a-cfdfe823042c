#!/usr/bin/env bash
set -euo pipefail

# FoundationDB Local Restore Script
#
# This script restores a FoundationDB cluster from a backup with improved
# reliability, validation, and progress monitoring.
#
# Usage:
#   ./restore-local-fdb.sh [OPTIONS] [BACKUP_PATH]
#
# Arguments:
#   BACKUP_PATH   Path to backup directory or backup URL. If not specified,
#                 shows available backups for selection.
#
# Options:
#   --list        List available backups and exit
#   --dry-run     Perform a trial run with no changes made
#   --incremental Perform incremental restore (doesn't clear existing data)
#   --keys RANGE  Restore only specific key ranges (format: "start end")
#   --help        Show this help and exit
#
# Environment Variables:
#   CLUSTER_FILE      Path to FDB cluster file (default: /etc/fdb/fdb.cluster)
#   BASE_DIR          Backup source directory (default: $HOME/fdb_backups)
#   RESTORE_TIMEOUT   Restore timeout in seconds (default: 600)
#   BACKUP_AGENT_PATH Path to backup_agent binary
#
# Features:
#   - Backup validation and metadata inspection
#   - Interactive backup selection
#   - Automatic backup agent management
#   - Progress monitoring with timeout
#   - Safety checks and confirmations
#   - Incremental restore support
#   - Dry run capability
#   - Key range selection
#
# Require fdbrestore, installed with the standard fdbclient

# ---- configurable variables -------------------------------------------------
CLUSTER_FILE="${CLUSTER_FILE:-/etc/fdb/fdb.cluster}"
BASE_DIR="${BASE_DIR:-$HOME/fdb_backups}"
RESTORE_TIMEOUT="${RESTORE_TIMEOUT:-600}"          # 10 minutes default timeout
BACKUP_AGENT_PATH="${BACKUP_AGENT_PATH:-/usr/local/foundationdb/backup_agent/backup_agent}"

# ---- script options ----------------------------------------------------------
DRY_RUN=false
INCREMENTAL=false
LIST_ONLY=false
KEY_RANGES=""
BACKUP_PATH=""

# ---- input validation -------------------------------------------------------
validate_key_ranges() {
    local ranges="$1"
    if [[ -z "$ranges" ]]; then
        return 0
    fi

    # Basic validation for key range format
    # Key ranges should be in format "start end" or multiple ranges separated by spaces
    # We'll be conservative and only allow printable ASCII characters
    if [[ ! "$ranges" =~ ^[[:print:][:space:]]+$ ]]; then
        error "Invalid key range format: contains non-printable characters"
    fi

    # Prevent command injection by checking for dangerous characters
    if [[ "$ranges" =~ [';|&$`'] ]]; then
        error "Invalid key range format: contains potentially dangerous characters (;|&\$\`)"
    fi

    # Check length to prevent excessively long input
    if [[ ${#ranges} -gt 1000 ]]; then
        error "Key range specification too long. Maximum length is 1000 characters."
    fi

    log "Key ranges validated: $ranges"
}

# ---- helper functions -------------------------------------------------------
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $*"
}

error() {
    log "ERROR: $*" >&2
    exit 1
}

warning() {
    log "WARNING: $*" >&2
}

check_cluster_health() {
    log "Checking cluster health..."
    if ! fdbcli -C "$CLUSTER_FILE" --exec "status" >/dev/null 2>&1; then
        error "FoundationDB cluster is not accessible. Check cluster file: $CLUSTER_FILE"
    fi
    log "✅ Cluster is healthy"
}

check_backup_agent() {
    log "Checking backup agent status..."

    # Check if backup agent process is running
    if ! pgrep -f backup_agent >/dev/null 2>&1; then
        log "⚠️  No backup agent found running. Starting backup agent..."
        start_backup_agent
        return
    fi

    log "✅ Backup agent is running"
}

start_backup_agent() {
    if [[ ! -x "$BACKUP_AGENT_PATH" ]]; then
        error "Backup agent not found at: $BACKUP_AGENT_PATH"
    fi

    log "Starting backup agent..."
    # Create a log file for backup agent to help with debugging
    local backup_agent_log="/tmp/backup_agent_$(date +%Y%m%d_%H%M%S).log"
    nohup "$BACKUP_AGENT_PATH" -C "$CLUSTER_FILE" --logdir /tmp >"$backup_agent_log" 2>&1 &
    local backup_agent_pid=$!

    sleep 5  # Give it more time to start

    # Check if the process is still running
    if ! kill -0 "$backup_agent_pid" 2>/dev/null; then
        log "Backup agent process died, checking log..."
        if [[ -f "$backup_agent_log" ]]; then
            log "Backup agent log:"
            tail -10 "$backup_agent_log" || true
        fi
        error "Failed to start backup agent (process died)"
    fi

    if ! pgrep -f backup_agent >/dev/null 2>&1; then
        error "Failed to start backup agent (process not found)"
    fi
    log "✅ Backup agent started (PID: $backup_agent_pid, log: $backup_agent_log)"
}

list_available_backups() {
    log "Available backups:"
    echo

    if [[ ! -d "$BASE_DIR" ]]; then
        echo "No backup directory found"
        return 1
    fi

    local backups=()
    local count=0

    # Find backup directories
    while IFS= read -r -d '' backup_dir; do
        if [[ -d "$backup_dir" && -d "$backup_dir/snapshots" ]]; then
            count=$((count + 1))
            backups+=("$backup_dir")

            local backup_name=$(basename "$backup_dir")
            # Only show size in general terms to avoid information disclosure
            local backup_size
            local size_bytes
            size_bytes=$(du -s "$backup_dir" 2>/dev/null | cut -f1 || echo "0")
            if [[ "$size_bytes" -gt 1048576 ]]; then
                backup_size="Large"
            elif [[ "$size_bytes" -gt 1024 ]]; then
                backup_size="Medium"
            else
                backup_size="Small"
            fi

            local backup_date=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M" "$backup_dir" 2>/dev/null || echo "unknown")

            printf "%2d) %-50s %8s  %s\n" "$count" "$backup_name" "$backup_size" "$backup_date"
        fi
    done < <(find "$BASE_DIR" -maxdepth 1 -type d -name "backup-*" -print0 | sort -z)

    if [[ $count -eq 0 ]]; then
        echo "No valid backups found"
        return 1
    fi

    echo
    echo "Total: $count backup(s) found"

    # Store backup list for selection
    printf '%s\n' "${backups[@]}" > /tmp/fdb_backup_list.$$
    return 0
}

select_backup_interactive() {
    if ! list_available_backups; then
        error "No backups available for selection"
    fi

    local max_selection
    max_selection=$(wc -l < /tmp/fdb_backup_list.$$)

    echo
    read -rp "Select backup number (1-$max_selection) or 'q' to quit: " selection

    if [[ "$selection" == "q" || "$selection" == "Q" ]]; then
        echo "Aborted."
        rm -f /tmp/fdb_backup_list.$$
        exit 0
    fi

    # Validate selection is a positive integer within range
    if ! [[ "$selection" =~ ^[1-9][0-9]*$ ]] || [[ "$selection" -gt "$max_selection" ]]; then
        rm -f /tmp/fdb_backup_list.$$
        error "Invalid selection: '$selection'. Must be a number between 1 and $max_selection."
    fi

    # Use printf to safely format the sed command
    BACKUP_PATH=$(sed -n "${selection}p" /tmp/fdb_backup_list.$$)
    rm -f /tmp/fdb_backup_list.$$

    if [[ -z "$BACKUP_PATH" ]]; then
        error "Failed to get backup path for selection $selection"
    fi

    log "Selected backup: $(basename "$BACKUP_PATH")"
}

validate_backup() {
    local backup_path="$1"

    log "Validating backup: $backup_path"

    # Check if backup path exists
    if [[ ! -d "$backup_path" ]]; then
        error "Backup directory does not exist: $backup_path"
    fi

    # Check for required backup structure
    if [[ ! -d "$backup_path/snapshots" ]]; then
        error "Invalid backup: missing snapshots directory in $backup_path"
    fi

    # Try to describe the backup
    local backup_url="file://$backup_path"
    local describe_output
    if describe_output=$(fdbbackup describe -d "$backup_url" -C "$CLUSTER_FILE" 2>&1); then
        log "✅ Backup validation successful"
        echo
        echo "Backup Details:"
        echo "$describe_output" | sed -e '1,3d'
        echo
        return 0
    else
        error "Backup validation failed: $describe_output"
    fi
}

get_database_status() {
    log "Getting current database status..."
    local status_output
    if status_output=$(fdbcli -C "$CLUSTER_FILE" --exec "status" 2>&1); then
        echo "$status_output" | grep -E "(Data:|Sum of key-value sizes|Disk space used)"
    else
        warning "Could not get database status: $status_output"
    fi
}

confirm_destructive_operation() {
    if [[ "$INCREMENTAL" == "true" ]]; then
        log "Incremental restore will add data to existing database"
        echo
        read -rp "Continue with incremental restore? [y/N] " ans
    else
        echo
        echo "⚠️  WARNING: This will COMPLETELY ERASE the current database!"
        echo
        get_database_status
        echo
        read -rp "Are you sure you want to proceed? Type 'YES' to confirm: " ans
        if [[ "$ans" != "YES" ]]; then
            echo "Aborted. (You must type 'YES' exactly to confirm)"
            exit 1
        fi
        return 0
    fi

    [[ $ans =~ ^[Yy]$ ]] || { echo "Aborted."; exit 1; }
}

stop_backup_operations() {
    log "Checking for active backup operations..."

    # Check for running backups
    local backup_status
    if backup_status=$(fdbbackup status -C "$CLUSTER_FILE" 2>&1); then
        if echo "$backup_status" | grep -qi "running\|active\|in progress"; then
            log "Found active backup operations, stopping them..."
            # Try to stop any running backups
            fdbbackup abort -C "$CLUSTER_FILE" >/dev/null 2>&1 || true
            sleep 2
        fi
    fi

    # Check for running or queued restores
    local restore_status
    if restore_status=$(fdbrestore status --dest-cluster-file "$CLUSTER_FILE" 2>&1); then
        if echo "$restore_status" | grep -qi "running\|active\|in progress\|queued"; then
            log "Found active/queued restore operations, stopping them..."

            # Extract restore tags and abort them
            local restore_tags
            restore_tags=$(echo "$restore_status" | grep -E "Tag:" | grep -oE "restore-[0-9-]+" || true)

            if [[ -n "$restore_tags" ]]; then
                while IFS= read -r tag; do
                    if [[ -n "$tag" ]]; then
                        log "Aborting restore: $tag"
                        fdbrestore abort --dest-cluster-file "$CLUSTER_FILE" -t "$tag" >/dev/null 2>&1 || true
                    fi
                done <<< "$restore_tags"
                sleep 2
            fi
        fi
    fi
}

check_database_locks() {
    log "Checking for database locks and conflicts..."

    # Stop any backup/restore operations that might be holding locks
    stop_backup_operations

    # Wait a moment for any transactions to complete
    sleep 1

    local status_output
    if status_output=$(fdbcli -C "$CLUSTER_FILE" --exec "status" 2>&1); then
        if echo "$status_output" | grep -qi "locked\|lock"; then
            warning "Database appears to be locked"
            # Extract lock UID if available
            local lock_uid
            if lock_uid=$(echo "$status_output" | grep -i "lock" | grep -oE '[0-9a-f]{16}' | head -1); then
                log "Attempting to unlock with UID: $lock_uid"
                if fdbcli -C "$CLUSTER_FILE" --exec "unlock $lock_uid" >/dev/null 2>&1; then
                    log "✅ Database unlocked"
                    sleep 2
                else
                    warning "Could not unlock database with UID: $lock_uid"
                fi
            else
                warning "Could not determine lock UID"
            fi
        fi
    fi
}

clear_database() {
    if [[ "$INCREMENTAL" == "true" ]]; then
        log "Skipping database clear for incremental restore"
        return 0
    fi

    # Additional safety check for database clear
    echo
    echo "🚨 FINAL WARNING: About to PERMANENTLY DELETE ALL DATA in the database!"
    echo "This operation cannot be undone."
    echo
    read -rp "Type 'DELETE ALL DATA' to confirm database clear: " clear_confirmation

    if [[ "$clear_confirmation" != "DELETE ALL DATA" ]]; then
        echo "Database clear cancelled. You must type 'DELETE ALL DATA' exactly."
        exit 1
    fi

    log "💣 Clearing existing database..."

    # Check for and handle database locks
    check_database_locks

    # Try multiple approaches to clear the database
    local clear_success=false
    local error_output=""
    local retry_count=0
    local max_retries=3

    while [[ "$clear_success" != "true" && $retry_count -lt $max_retries ]]; do
        retry_count=$((retry_count + 1))

        if [[ $retry_count -gt 1 ]]; then
            log "Retry attempt $retry_count of $max_retries..."
            sleep 2
        fi

        # Method 1: Use combined command with proper escaping
        log "Attempting database clear (method 1: combined command)..."
        local clear_cmd='writemode on; clearrange "" \xFF'
        if error_output=$(fdbcli -C "$CLUSTER_FILE" --exec "$clear_cmd" 2>&1); then
            clear_success=true
            log "✅ Database cleared using combined command"
            break
        else
            log "Method 1 failed: $error_output"

            # Check if it's a lock error and try to handle it
            if echo "$error_output" | grep -qi "locked\|1038"; then
                log "Database lock detected, attempting to resolve..."
                check_database_locks
                continue
            fi
        fi

        # Method 2: Alternative syntax with different end key
        log "Attempting database clear (method 2: alternative syntax)..."
        local clear_cmd='writemode on; clearrange "" \xff'
        if error_output=$(fdbcli -C "$CLUSTER_FILE" --exec "$clear_cmd" 2>&1); then
            clear_success=true
            log "✅ Database cleared using alternative syntax"
            break
        else
            log "Method 2 failed: $error_output"
        fi

        # Method 3: Use clear command instead of clearrange
        log "Attempting database clear (method 3: clear command)..."
        local clear_cmd='writemode on; clear "" \xFF'
        if error_output=$(fdbcli -C "$CLUSTER_FILE" --exec "$clear_cmd" 2>&1); then
            clear_success=true
            log "✅ Database cleared using clear command"
            break
        else
            log "Method 3 failed: $error_output"
        fi

        # Method 4: Try with force unlock
        if echo "$error_output" | grep -qi "locked\|1038"; then
            log "Attempting database clear (method 4: with force unlock)..."
            if fdbcli -C "$CLUSTER_FILE" --exec "unlock" >/dev/null 2>&1; then
                sleep 1
                local clear_cmd='writemode on; clearrange "" \xFF'
                if error_output=$(fdbcli -C "$CLUSTER_FILE" --exec "$clear_cmd" 2>&1); then
                    clear_success=true
                    log "✅ Database cleared after unlock"
                    break
                else
                    log "Method 4 failed: $error_output"
                fi
            fi
        fi
    done

    if [[ "$clear_success" != "true" ]]; then
        error "Failed to clear database after $max_retries attempts. Last error: $error_output"
    fi

    # Verify the clear operation worked
    log "Verifying database clear..."
    local key_count
    if key_count=$(fdbcli -C "$CLUSTER_FILE" --exec 'getrangekeys "" \xFF 1' 2>/dev/null | wc -l); then
        if [[ "$key_count" -gt 1 ]]; then
            warning "Database may not be completely cleared. Found $key_count keys remaining."
            # Try to get more information about remaining keys
            log "Checking remaining keys..."
            fdbcli -C "$CLUSTER_FILE" --exec 'getrangekeys "" \xFF 5' 2>/dev/null || true
        else
            log "✅ Database clear verified - no keys remaining"
        fi
    else
        log "Could not verify database clear, but clear command succeeded"
    fi

    log "✅ Database cleared"
}

cleanup_failed_restore() {
    local tag="$1"
    log "Cleaning up failed restore: $tag"
    fdbrestore abort --dest-cluster-file "$CLUSTER_FILE" -t "$tag" >/dev/null 2>&1 || true
}

restart_backup_agent() {
    log "Restarting backup agent to resolve queue issues..."

    # Kill existing backup agent
    if pgrep -f backup_agent >/dev/null 2>&1; then
        log "Stopping existing backup agent..."
        pkill -f backup_agent || true
        sleep 3
    fi

    # Start new backup agent
    start_backup_agent
}

wait_for_restore() {
    local tag="$1"
    local timeout="$2"
    local start_time=$(date +%s)
    local last_status=""
    local consecutive_failures=0
    local max_consecutive_failures=3
    local queued_time=0
    local max_queued_time=60  # Maximum time to wait in queued state

    log "Waiting for restore to complete (timeout: ${timeout}s)..."

    while true; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))

        if [[ $elapsed -gt $timeout ]]; then
            error "Restore timed out after ${timeout} seconds"
        fi

        # Get restore status with better error handling
        local status_output
        local status_exit_code

        if status_output=$(fdbrestore status --dest-cluster-file "$CLUSTER_FILE" -t "$tag" 2>&1); then
            status_exit_code=$?
            consecutive_failures=0

            # More robust status detection
            if [[ $status_exit_code -eq 0 ]]; then
                # Check for completion indicators
                if echo "$status_output" | grep -qE "(completed|finished|done|success)"; then
                    log "✅ Restore completed successfully"
                    return 0
                elif echo "$status_output" | grep -qE "(running|progress|active|in progress)"; then
                    queued_time=0  # Reset queued time if restore is running
                    # Extract and display progress information
                    local progress_info
                    progress_info=$(echo "$status_output" | grep -E "(progress|rate|bytes|percent|MB|GB)" | head -3 | tr '\n' ' ')
                    if [[ "$progress_info" != "$last_status" && -n "$progress_info" ]]; then
                        log "Progress: $progress_info"
                        last_status="$progress_info"
                    fi
                elif echo "$status_output" | grep -qE "queued"; then
                    queued_time=$((queued_time + 5))
                    if [[ $queued_time -eq 5 ]]; then
                        log "Restore is queued, waiting for backup agent to process..."
                    elif [[ $queued_time -ge $max_queued_time ]]; then
                        warning "Restore has been queued for ${queued_time}s, attempting to restart backup agent..."
                        restart_backup_agent
                        queued_time=0  # Reset timer after restart
                        sleep 10  # Give backup agent time to start processing
                    else
                        log "Still queued (${queued_time}s)..."
                    fi
                elif echo "$status_output" | grep -qE "(error|failed|abort|invalid)"; then
                    error "Restore failed: $status_output"
                fi
            fi
        else
            status_exit_code=$?
            consecutive_failures=$((consecutive_failures + 1))

            # Handle status command failures
            if echo "$status_output" | grep -qE "(not found|no restore|no active)"; then
                log "✅ Restore completed (no active restore found)"
                return 0
            elif echo "$status_output" | grep -qE "(connection|connect|timeout|refused)"; then
                warning "Connection issue while checking restore status: $status_output"
                if [[ $consecutive_failures -ge $max_consecutive_failures ]]; then
                    error "Too many consecutive connection failures. Cannot monitor restore progress."
                fi
            else
                warning "Failed to get restore status (attempt $consecutive_failures): $status_output"
                if [[ $consecutive_failures -ge $max_consecutive_failures ]]; then
                    error "Too many consecutive status check failures. Restore may have failed."
                fi
            fi
        fi

        sleep 5
    done
}

parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help|-h)
                show_help
                exit 0
                ;;
            --list)
                LIST_ONLY=true
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            --incremental)
                INCREMENTAL=true
                shift
                ;;
            --keys)
                if [[ -n "${2:-}" ]]; then
                    KEY_RANGES="$2"
                    validate_key_ranges "$KEY_RANGES"
                    shift 2
                else
                    error "--keys requires a key range argument"
                fi
                ;;
            -*)
                error "Unknown option: $1"
                ;;
            *)
                if [[ -z "$BACKUP_PATH" ]]; then
                    BACKUP_PATH="$1"
                else
                    error "Multiple backup paths specified: $BACKUP_PATH and $1"
                fi
                shift
                ;;
        esac
    done
}

show_help() {
    cat << 'EOF'
FoundationDB Local Restore Script

Usage:
  ./restore-local-fdb.sh [OPTIONS] [BACKUP_PATH]
  ./restore-local-fdb.sh --help

Arguments:
  BACKUP_PATH   Path to backup directory. If not specified, shows available backups for selection.

Options:
  --list        List available backups and exit
  --dry-run     Perform a trial run with no changes made
  --incremental Perform incremental restore (doesn't clear existing data)
  --keys RANGE  Restore only specific key ranges (format: "start end")
  --help        Show this help and exit

Environment Variables:
  CLUSTER_FILE      Path to FDB cluster file (default: /etc/fdb/fdb.cluster)
  BASE_DIR          Backup source directory (default: $HOME/fdb_backups)
  RESTORE_TIMEOUT   Restore timeout in seconds (default: 600)
  BACKUP_AGENT_PATH Path to backup_agent binary

Examples:
  ./restore-local-fdb.sh                                    # Interactive backup selection
  ./restore-local-fdb.sh /path/to/backup                    # Restore specific backup
  ./restore-local-fdb.sh --list                             # List available backups
  ./restore-local-fdb.sh --dry-run /path/to/backup          # Test restore without changes
  ./restore-local-fdb.sh --incremental /path/to/backup      # Incremental restore
  ./restore-local-fdb.sh --keys "start end" /path/to/backup # Restore specific key range

EOF
}
# ---- main script ------------------------------------------------------------

# Parse command line arguments
parse_arguments "$@"

# Handle list-only option
if [[ "$LIST_ONLY" == "true" ]]; then
    list_available_backups
    exit $?
fi

log "Starting FoundationDB restore"
log "Cluster file: $CLUSTER_FILE"
log "Backup directory: $BASE_DIR"

# Pre-flight checks
check_cluster_health
check_backup_agent

# Determine backup path
if [[ -z "$BACKUP_PATH" ]]; then
    log "No backup path specified, showing available backups..."
    select_backup_interactive
fi

# Convert relative path to absolute path
if [[ "$BACKUP_PATH" != /* ]]; then
    if [[ -d "$BASE_DIR/$BACKUP_PATH" ]]; then
        BACKUP_PATH="$BASE_DIR/$BACKUP_PATH"
    elif [[ -d "$BACKUP_PATH" ]]; then
        BACKUP_PATH="$(cd "$BACKUP_PATH" && pwd)"
    else
        error "Backup path not found: $BACKUP_PATH"
    fi
fi

# Validate the backup
validate_backup "$BACKUP_PATH"

# Prepare restore command
RESTORE_TAG="restore-$(date +%Y%m%d-%H%M%S)"
BACKUP_URL="file://$BACKUP_PATH"
RESTORE_CMD=(
    fdbrestore start
    --dest-cluster-file "$CLUSTER_FILE"
    -t "$RESTORE_TAG"
    -r "$BACKUP_URL"
)

# Add optional parameters
if [[ "$DRY_RUN" == "true" ]]; then
    RESTORE_CMD+=(--dryrun)
    log "🧪 Dry run mode enabled"
fi

if [[ "$INCREMENTAL" == "true" ]]; then
    RESTORE_CMD+=(--incremental)
    log "📈 Incremental restore mode enabled"
fi

if [[ -n "$KEY_RANGES" ]]; then
    RESTORE_CMD+=(-k "$KEY_RANGES")
    log "🔑 Key range specified: $KEY_RANGES"
fi

# Show restore summary
echo
log "Restore Summary:"
log "  Source: $BACKUP_PATH"
log "  Target: $CLUSTER_FILE"
log "  Mode: $([ "$INCREMENTAL" == "true" ] && echo "Incremental" || echo "Full")"
log "  Dry run: $([ "$DRY_RUN" == "true" ] && echo "Yes" || echo "No")"
if [[ -n "$KEY_RANGES" ]]; then
    log "  Key ranges: $KEY_RANGES"
fi
echo

# Confirmation (skip for dry run)
if [[ "$DRY_RUN" != "true" ]]; then
    confirm_destructive_operation
fi

# Clear database if not incremental and not dry run
if [[ "$DRY_RUN" != "true" ]]; then
    clear_database
fi

# Start the restore
log "⏳ Starting restore operation..."
log "Command: ${RESTORE_CMD[*]}"

if ! "${RESTORE_CMD[@]}"; then
    error "Failed to start restore operation"
fi

if [[ "$DRY_RUN" == "true" ]]; then
    log "✅ Dry run completed successfully"
    log "The restore would have been performed with the above parameters"
    exit 0
fi

# Wait for completion with progress monitoring
if ! wait_for_restore "$RESTORE_TAG" "$RESTORE_TIMEOUT"; then
    cleanup_failed_restore "$RESTORE_TAG"
    exit 1
fi

# Verify the restore
log "🔍 Verifying restore completion..."
get_database_status

log "🎉 Restore process completed successfully!"
log "Database has been restored from: $BACKUP_PATH"

# Cleanup
rm -f /tmp/fdb_backup_list.$$ 2>/dev/null || true
