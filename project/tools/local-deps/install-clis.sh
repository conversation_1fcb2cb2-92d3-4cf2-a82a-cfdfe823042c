#!/usr/bin/env bash

here=`cd $(dirname $BASH_SOURCE); pwd`

DORIATH_VERSION=1.6.0
RIVENDELL_VERSION=1.1.20
SERVERLESS_VERSION=1.32.0
HELM_VERSION=3.9.3

installTool=''
os=''

here=`cd $(dirname $BASH_SOURCE); pwd`

function checkEnv {
    if [[ ! -z "$SKIP_INSTALL_CLI" ]]; then
        exit 0
    fi
    case `uname` in
        Darwin)
            installTool=brew
            os=darwin
            ;;
        Linux)
            os=linux
            if which apt > /dev/null 2>&1; then
                installTool=apt
            elif which dnf > /dev/null 2>&1; then
                installTool=dnf
            fi
            ;;
    esac
}

function installSystemPackage {
    packageName=$1
    if [ -z "$installTool" ]; then
        echo "Cannot determine install tool, please install package '$packageName' by yourself"
        return 1
    fi
    case "$installTool" in
        brew)
            brew install $packageName
            ;;
        apt)
            sudo apt-get install -y $packageName
            ;;
        dnf)
            sudo dnf install -y $packageName
            ;;
        *)
            echo "Unknown install tool '$installTool', please install package '$packageName' by yourself"
            return 1
            ;;
    esac
}

function installGoPackage {
    if [ -z "$os" ]; then
        echo "Cannot determine install tool, please install package '${packageName}@${packageVersion}' by yourself"
        return 1
    fi
    packageName=$1
    packageVersion=$2
    wget -O /tmp/${packageName}.tar.gz https://github.com/anduintransaction/${packageName}/releases/download/v${packageVersion}/${packageName}-${packageVersion}-${os}-amd64.tar.gz &&
        cd /tmp &&
        tar xzf ${packageName}.tar.gz &&
        sudo mv ${packageName} /usr/local/bin &&
        rm -rf ${packageName}.tar.gz
}

function installWget {
    if ! which wget > /dev/null 2>&1; then
        echo "Installing wget"
        installSystemPackage wget
    fi
}

function installCurl {
    if ! which curl > /dev/null 2>&1; then
        echo "Installing curl"
        installSystemPackage curl
    fi
}

function installAws {
    if ! which aws > /dev/null 2>&1; then
        echo "Installing aws"
        installSystemPackage awscli
    fi
}

function installNode {
    if ! which node > /dev/null 2>&1; then
        echo "Please install nodejs version 14 or later and make sure you can run npm install -g without using root account"
        return 1
    fi
}

function installDoriath {
    needInstall=0
    if ! which doriath > /dev/null 2>&1; then
        needInstall=1
    else
        version=`doriath version 2>/dev/null | awk '{print $1}'`
        if [ "$version" != "dev-snapshot" ] && [ "$version" != "$DORIATH_VERSION" ]; then
            needInstall=1
        fi
    fi
    if [ "$needInstall" == "1" ]; then
        echo "Installing doriath $DORIATH_VERSION"
        installGoPackage doriath $DORIATH_VERSION
    fi
}

function installRivendell {
    needInstall=0
    if ! which rivendell > /dev/null 2>&1; then
        needInstall=1
    else
        version=`rivendell version 2>/dev/null | awk '{print $1}'`
        if [ "$version" != "dev-snapshot" ] && [ "$version" != "$RIVENDELL_VERSION" ]; then
            needInstall=1
        fi
    fi
    if [ "$needInstall" == "1" ]; then
        echo "Installing rivendell $RIVENDELL_VERSION"
        installGoPackage rivendell $RIVENDELL_VERSION
    fi
}

function installHelm {
    needInstall=0
    if ! which helm > /dev/null 2>&1; then
        needInstall=1
    else
        version=`helm version --short 2>/dev/null | awk -F'+' '{print $1}'`
        if [ "$version" != "v${HELM_VERSION}" ]; then
            needInstall=1
        fi
    fi
    if [ "$needInstall" == "1" ]; then
        echo "Installing helm $HELM_VERSION"
        wget -O /tmp/helm.tar.gz https://get.helm.sh/helm-v${HELM_VERSION}-${os}-amd64.tar.gz \
          && cd /tmp \
          && tar xzf helm.tar.gz \
          && sudo mv ${os}-amd64/helm /usr/local/bin \
          && rm -rf helm.tar.gz ${os}-amd64
    fi
}

function installFdbCli {
    $here/install-fdbcli.sh
}

checkEnv &&
    installWget &&
    installCurl &&
    installNode &&
    installDoriath &&
    installRivendell &&
    installHelm
