#!/usr/bin/env bash

# Backup/Restore data for gondor

function showHelp {
    echo "Usage: $0 backup|restore|clone|import|export|list|remove|clean"
    echo " - backup  [name]:                           to backup data"
    echo " - restore [name]:                           to restore data"
    echo " - clone   [from-namespace] [to-namespace]:  to clone data between namespace"
    echo " - import  [from-local-file] [to-namespace]: to import data from local file to namespace"
    echo " - dump    [from-namespace] [to-local-file]: to dump data from namespace to local file"
    echo " - list:                                     to list backups"
    echo " - remove  [name]:                           to remove a backup"
    echo " - clean:                                    to remove all backups"
    exit 1
}

function backup {
    if [ $# -lt 1 ]; then
        backupName=default
    else
        backupName=$1
    fi
    read -p "Backup '$NAMESPACE' to '$backupName'? [Y/n] " yn
    case $yn in
        [Nn]* )
            return
            ;;
    esac
    backupFolder=$backupRoot/$backupName
    if isMinikube; then
        runCommandOnMinikube sudo rm -rf $backupFolder &&
            runCommandOnMinikube sudo mkdir -p $backupRoot &&
            runCommandOnMinikube sudo cp -r $dataFolder $backupFolder &&
            echo "DONE"
    else
        runCommandWithDocker rm -rf $backupFolder &&
            runCommandWithDocker mkdir -p $backupRoot &&
            runCommandWithDocker cp -r $dataFolder $backupFolder &&
            echo "DONE"
    fi
}

function restore {
    if [ $# -lt 1 ]; then
        backupName=default
    else
        backupName=$1
    fi
    read -p "Restore '$backupName' to '$NAMESPACE'? [Y/n] " yn
    case $yn in
        [Nn]* )
            return
            ;;
    esac
    backupFolder=$backupRoot/$backupName
    test=0
    if isMinikube; then
        runCommandOnMinikube test -e $backupFolder > /dev/null 2>&1
        test=$?
    else
        test -e $backupFolder > /dev/null 2>&1
        test=$?
    fi
    if [ $test -ne 0 ]; then
        echo "Backup not found: $backupName"
        exit 1
    fi
    if [ "$FAST_IMPORT" == "true" ]; then
        if isMinikube; then
            runCommandOnMinikube sudo rm -rf $dataFolder &&
                runCommandOnMinikube sudo cp -r $backupFolder $dataFolder &&
                $here/restart.sh &&
                echo "DONE"
        else
            runCommandWithDocker rm -rf $dataFolder &&
                runCommandWithDocker cp -r $backupFolder $dataFolder &&
                $here/restart.sh &&
                echo "DONE"
        fi
    else
        if isMinikube; then
            $here/stop.sh &&
                runCommandOnMinikube sudo rm -rf $dataFolder &&
                runCommandOnMinikube sudo cp -r $backupFolder $dataFolder &&
                $here/start.sh &&
                echo "DONE"
        else
            $here/stop.sh &&
                runCommandWithDocker rm -rf $dataFolder &&
                runCommandWithDocker cp -r $backupFolder $dataFolder &&
                $here/start.sh &&
                echo "DONE"
        fi
    fi
}

function clone {
    if [ $# -lt 2 ]; then
        echo "Usage: $0 clone [from] [to]"
        exit 1
    fi
    fromFolder=$localDataDir/gondor/$1
    toFolder=$localDataDir/gondor/$2
    if isMinikube; then
        $here/stop.sh &&
            runCommandOnMinikube sudo rm -rf $toFolder &&
            runCommandOnMinikube sudo cp -r $fromFolder $toFolder &&
            $here/start.sh &&
            echo "DONE"
    else
        $here/stop.sh &&
            runCommandWithDocker rm -rf $toFolder &&
            runCommandWithDocker cp -r $fromFolder $toFolder &&
            $here/start.sh &&
            echo "DONE"
    fi
}

function import {
    if [ $# -lt 2 ]; then
        echo "Usage: $0 import [from-local-file] [to-namespace]"
        exit 1
    fi
    localFile=$1
    namespace=$2
    if [ ! -e $localFile ]; then
        echo "Local file not found: $localFile"
        exit 1
    fi
    dataFolder=$localDataDir/gondor
    if isMinikube; then
        echo "Extracting backup file" &&
            cp $localFile /tmp/exported-db.tar.gz &&
            cd /tmp &&
            tar xzf exported-db.tar.gz &&
            rm -f exported-db.tar.gz &&
            runCommandOnMinikube sudo rm -rf /tmp/dbimport &&
            runCommandOnMinikube sudo mkdir -p /tmp/dbimport &&
            runCommandOnMinikube sudo chown -R docker:docker /tmp/dbimport &&
            echo "Copying backup folder to VM" &&
            copyToMinikube exported-db /tmp/dbimport/$namespace &&
            rm -rf exported-db &&
            $here/stop.sh &&
            runCommandOnMinikube sudo rm -rf $dataFolder/$namespace &&
            runCommandOnMinikube sudo cp -r /tmp/dbimport/$namespace $dataFolder &&
            $here/start.sh &&
            echo "Successfully import db to $namespace"
    else
        echo "Extracting backup file" &&
            rm -rf /tmp/dbimport &&
            mkdir -p /tmp/dbimport &&
            cp $localFile /tmp/dbimport/exported-db.tar.gz &&
            cd /tmp/dbimport &&
            tar xzf exported-db.tar.gz &&
            rm -f exported-db.tar.gz &&
            $here/stop.sh &&
            echo "Copying" &&
            runCommandWithDocker rm -rf $dataFolder/$namespace &&
            runCommandWithDocker cp -r /tmp/dbimport/exported-db $dataFolder/$namespace &&
            $here/start.sh &&
            echo "Successfully import db to $namespace"
    fi
}

function dump {
    if [ $# -lt 2 ]; then
        echo "Usage: $0 dump [from-namespace] [to-local-folder]"
        echo "Example $0 dump gondor /path/to/folder"
        exit 1
    fi
    namespace=$1
    localFolder=$2
    filename=exported-db-${namespace}.tar.gz
    if isMinikube; then
        echo "Dumping data to VM" &&
            runCommandOnMinikube sudo rm -rf /tmp/dbdump &&
            runCommandOnMinikube sudo mkdir -p /tmp/dbdump &&
            runCommandOnMinikube sudo cp -r $localDataDir/gondor/$namespace /tmp/dbdump/exported-db &&
            runCommandOnMinikube sudo chown -R docker:docker /tmp/dbdump/exported-db &&
            rm -rf /tmp/exported-db /tmp/exported-db-${namespace}.tar.gz &&
            echo "Copying from VM to host machine" &&
            copyFromMinikube /tmp/dbdump/exported-db /tmp &&
            cd /tmp &&
            tar czf $filename exported-db &&
            rm -rf exported-db &&
            mkdir -p $localFolder &&
            mv /tmp/$filename $localFolder &&
            echo "Successfully dump db to $localFolder/$filename"
    else
        echo "Dumping data" &&
            runCommandWithDocker rm -rf /tmp/dbdump &&
            runCommandWithDocker mkdir -p /tmp/dbdump &&
            runCommandWithDocker cp -r $localDataDir/gondor/$namespace /tmp/dbdump/exported-db &&
            echo "Compressing" &&
            runCommandWithDocker sh -c "cd /tmp/dbdump; tar czf $filename exported-db; rm -rf exported-db " &&
            mkdir -p $localFolder &&
            cp /tmp/dbdump/$filename $localFolder &&
            echo "Successfully dump db to $localFolder/$filename"
    fi
}

function list {
    if isMinikube; then
        backupList=`runCommandOnMinikube sudo ls $backupRoot 2>/dev/null`
    else
        backupList=`runCommandWithDocker ls $backupRoot 2>/dev/null`
    fi
    for backup in $backupList; do
        echo $backup
    done
}

function remove {
    if [ $# -lt 1 ]; then
        backupName=default
    else
        backupName=$1
    fi
    read -p "Remove backup '$backupName' of '$NAMESPACE'? [Y/n] " yn
    case $yn in
        [Nn]* )
            return
            ;;
    esac
    if isMinikube; then
        runCommandOnMinikube sudo rm -rf $backupRoot/$backupName &&
            echo "DONE"
    else
        runCommandWithDocker rm -rf $backupRoot/$backupName &&
            echo "DONE"
    fi
}

function clean {
    read -p "Remove all backups of '$NAMESPACE'? [Y/n] " yn
    case $yn in
        [Nn]* )
            return
            ;;
    esac
    if isMinikube; then
        runCommandOnMinikube sudo rm -rf $backupRoot &&
            echo "DONE"
    else
        runCommandWithDocker rm -rf $backupRoot &&
            echo "DONE"
    fi
}

if [ $# -lt 1 ]; then
    showHelp
    exit 1
fi

command=$1
shift

here=`cd $(dirname $BASH_SOURCE); pwd`
source $here/common.sh

FAST_IMPORT=${FAST_IMPORT:-"false"}
localDataDir=`getLocalDataDir`
dataFolder=`getDataDir`
backupRoot=${dataFolder}-backup


$here/install-clis.sh
if [ $? -ne 0 ]; then
    exit 1
fi

case $command in
    help)
        showHelp
        ;;
    backup)
        backup $@
        ;;
    restore)
        restore $@
        ;;
    clone)
        clone $@
        ;;
    import)
        import $@
        ;;
    dump)
        dump $@
        ;;
    list)
        list $@
        ;;
    remove)
        remove $@
        ;;
    clean)
        clean $@
        ;;
    *)
        showHelp
        exit 1
        ;;
esac
