#!/usr/bin/env bash

set -e

here=`cd $(dirname $BASH_SOURCE); pwd`
source $here/../../../../ci/vars/common/aws-env.sh
source $here/common.sh

function runStart {
    rivendell $RIVENDELL_PARAMS upgrade $here/../../../../ci/tasks/rivendell/run/online-office.yml
}

function runStop {
    rivendell $RIVENDELL_PARAMS down $here/../../../../ci/tasks/rivendell/run/online-office.yml
}

if [ $# -lt 1 ]; then
    echo "USAGE: $0 start|stop"
    exit 1
fi

case $1 in
    start)
        runStart
        ;;
    stop)
        runStop
        ;;
    *)
        echo "USAGE: $0 start|stop"
        exit 1
esac
