#!/usr/bin/env bash

# Playground for infra team, if you dont know how to install k8s, please ignore this file.

here=`cd $(dirname $BASH_SOURCE); pwd`
source $here/../../../../ci/vars/common/aws-env.sh
source $here/common.sh

local_envs=("$here/../../../../.local.env" "$HOME/.config/stargazer/local.env")
for f in ${local_envs[@]}; do
    if test -f "$f"; then
        echo "Sourcing local env var at $f"
        source $f
    fi
done

function startServices {
    kubectl create ns $NAMESPACE || true
    kubectl label ns $NAMESPACE nukable='true' || true
    cat <<EOF | kubectl apply -f -
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: standard
provisioner: rancher.io/local-path
volumeBindingMode: WaitForFirstConsumer
reclaimPolicy: Delete
EOF
    rivendell $RIVENDELL_PARAMS $RIVENDELL_EXTRA_PARAMS upgrade $here/../../../../ci/tasks/rivendell/run/ecr-updater.yml
}

function start {
        startServices
}

start
