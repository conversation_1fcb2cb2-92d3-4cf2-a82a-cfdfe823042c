#!/usr/bin/env bash

here=`cd $(dirname $BASH_SOURCE); pwd`

export NAMESPACE=${NAMESPACE:-gondor}
export CRI_ENDPOINT=${CRI_ENDPOINT:-unix:///run/k3s/containerd/containerd.sock} # This assumes k3s as default.
export dockerImageVarFile=$here/../../../../ci/vars/common/docker-images.vars

defaultContext=`kubectl config current-context`
export context=${CONTEXT:-$defaultContext}
KEYCLOAK_DEV=${KEYCLOAK_DEV:-"false"}
CUSTOM_ROOT_URL=${CUSTOM_ROOT_URL:-""}
localDataDir="/var/kube-data"
GONDOR_SERVER_IP="**********"
GONDOR_SERVER_PORT="8080"

RIVENDELL_PARAMS=$(cat <<EOF
--namespace $NAMESPACE
--context $context
--variableFile $dockerImageVarFile
--variableFile $here/../../../../ci/vars/deployment-targets/local.vars
--variable deploymentEnvironment=staging
--variable deploymentTest=false
--variable stagingEnvironment=local-dev
--variable keycloakDev=$KEYCLOAK_DEV
--variable customRootUrl=$CUSTOM_ROOT_URL
--variable localDataDir=$localDataDir
--variable gondorServerIP=$GONDOR_SERVER_IP
--variable gondorServerPort=$GONDOR_SERVER_PORT
--variable ecrKeyId=$AWS_ACCESS_KEY_ID
--variable ecrKeySecret=$AWS_SECRET_ACCESS_KEY
--variable dockerEcrRegistry=988983267146.dkr.ecr.ap-east-1.amazonaws.com
--yes
EOF
)
export STARGAZER_RIVENDELL_PARAMS="$STARGAZER_RIVENDELL_PARAMS $STARGAZER_RIVENDELL_EXTRA_PARAMS"
