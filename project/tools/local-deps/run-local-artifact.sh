#!/usr/bin/env bash

here=`cd $(dirname $BASH_SOURCE); pwd`
root=$here/../../..
artifactFolder=$root/out/apps/gondor/gondorAppServer/universalPackageZip.dest
confFile=$root/local/application.conf

function runLocalArtifact {
    local zipFile=`ls -1 $artifactFolder | grep '\.zip' | head -n 1`
    if [ -z "$zipFile" ]; then
        echo "No artifact found"
        return 1
    fi
    echo "Running $zipFile"
    cd $artifactFolder &&
        rm -rf stargazer &&
        unzip $zipFile &&
        cp $confFile ./stargazer/conf &&
        cd ./stargazer/bin &&
        ./gondor-app-server
}

function cleanArtifact {
    echo "Cleaning all artifacts"
    rm -rf $artifactFolder
}

if [ $# -lt 1 ]; then
    echo "USAGE: $0 run|clean"
    exit 1
fi

case $1 in
    run)
        runLocalArtifact
        ;;
    clean)
        cleanArtifact
        ;;
    *)
        echo "USAGE: $0 run|clean"
        exit 1
        ;;
esac
