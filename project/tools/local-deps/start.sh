#!/usr/bin/env bash

set -e

here=`cd $(dirname $BASH_SOURCE); pwd`
source $here/../../../ci/vars/common/aws-env.sh
source $here/common.sh

$here/install-clis.sh
if isMinikube; then
    minikube addons enable default-storageclass
    eval $(minikube docker-env --shell=bash)
    kubectl config use-context minikube
    $here/install-local-provisioner.sh
fi

export FAKE_TIDB="true"

# Copy default zone files
echo "Adding DNS zone file for *.anduin.local domains"
ZONES_DIR=$HOME/.anduin-kube/zones
mkdir -p $ZONES_DIR
cp $here/dns-zones/db.* $ZONES_DIR/

if ! isMinikube; then
    ZONES_DIR_SLASH=/usr/local/share/coredns-zones
    rm -f $ZONES_DIR_SLASH/db.* || true
    cp $here/dns-zones/db.* $ZONES_DIR_SLASH || true
    chgrp slash ${ZONES_DIR_SLASH}/* 2>/dev/null || true
    chmod 775 ${ZONES_DIR_SLASH}/* 2>/dev/null || true
fi

aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 988983267146.dkr.ecr.us-east-1.amazonaws.com

# doriath $STARGAZER_DORIATH_PARAMS --config $here/../../../ci/tasks/doriath/deps-local.yml build
kubectl create ns $NAMESPACE || true
kubectl label ns $NAMESPACE nukable='true' || true
kubectl label --overwrite ns $NAMESPACE blackwood='true'
kubectl annotate --overwrite ns $NAMESPACE description="Local environment"

kubectl delete job wait-stargazer --namespace $NAMESPACE --context "$RIVENDELL_CONTEXT" --ignore-not-found=true
kubectl delete deployment kafka-local --namespace $NAMESPACE --context "$RIVENDELL_CONTEXT" --ignore-not-found=true

pushd $here/../../../ci/rivendell-v2
INFRA_ONLY=true ./node_modules/.bin/ts-node ./src/actions/interops.ts \
  --env=local \
  --var=stargazerSprintTag=$sidecarImageTag
popd
