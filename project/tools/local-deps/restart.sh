#!/usr/bin/env bash

set -e
here=`cd $(dirname $BASH_SOURCE); pwd`
source $here/common.sh
$here/install-clis.sh

kubectl delete job wait-stargazer --namespace $NAMESPACE --context "$RIVENDELL_CONTEXT" --ignore-not-found=true
rivendell $STARGAZER_RIVENDELL_PARAMS restart $here/../../../ci/tasks/rivendell/run/local.yml
rivendell $STARGAZER_RIVENDELL_PARAMS up $here/../../../ci/tasks/rivendell/run/wait.yml
rivendell --namespace $NAMESPACE --context "$RIVENDELL_CONTEXT" wait job wait-stargazer
