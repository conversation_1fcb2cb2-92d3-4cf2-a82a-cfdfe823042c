#!/usr/bin/env bash

here=`cd $(dirname $BASH_SOURCE); pwd`
local_envs=("$here/../../../.local.env" "$HOME/.config/stargazer/local.env")
for f in ${local_envs[@]}; do
  if test -f "$f"; then
    echo "Sourcing local env var at $f"
    source $f
  fi
done

function isMinikube {
    if systemctl status kubelet 2>/dev/null | grep -q running; then
        return 1
    else
        return 0
    fi
}

function findMinikubeIp {
    if [[ ! -z "$MINIKUBE_IP" ]]; then
        echo "$MINIKUBE_IP"
    else
        echo $(minikube ip)
    fi
}

function findKubernetesContext {
    if [[ ! -z "$KUBE_CONTEXT" ]]; then
        echo "$KUBE_CONTEXT"
    elif isMinikube; then
        echo "minikube"
    else
        kubectl config current-context
    fi
}

function findLocalDataDir {
    if [[ ! -z "$EXTERNAL_DATA_DIR" ]]; then
        echo "$EXTERNAL_DATA_DIR"
    elif isMinikube; then
        echo /mnt/sda1/var/data
    else
        echo /var/kube-data
    fi
}

function findGondorServerIp {
    if [[ ! -z "$GONDOR_SERVER_IP" ]]; then
        echo "$GONDOR_SERVER_IP"
    elif [ "$EXPERIMENTAL" -eq 1 ]; then
        # try to find ip of interface that is connected to the internet
        # example output: ******* via ********** dev wlan0 src **********8 uid 1000
        echo $(ip route get ******* | head -1 | cut -d' ' -f7)
    elif isMinikube; then
        echo "*************"
    else
        echo "**********"
    fi
}

export RIVENDELL_CONTEXT=${RIVENDELL_CONTEXT:-`findKubernetesContext`}
export NAMESPACE=${NAMESPACE:-gondor}
export KEYCLOAK_DEV=${KEYCLOAK_DEV:-"false"}
export CUSTOM_ROOT_URL=${CUSTOM_ROOT_URL:-""}

EXPERIMENTAL=${EXPERIMENTAL:-0}
dockerImageVarFile=$here/../../../ci/vars/common/docker-images.vars
sidecarImageTag=1.0-local
localDataDir=`findLocalDataDir`
MINIKUBE_IP=`findMinikubeIp`

export STARGAZER_DORIATH_PARAMS=$(cat <<EOF
--variableFile $dockerImageVarFile
--variable sidecarImageTag=$sidecarImageTag
--variable local=true
EOF
)

GONDOR_SERVER_IP=`findGondorServerIp`
GONDOR_SERVER_PORT=8080

STARGAZER_RIVENDELL_PARAMS=$(cat <<EOF
--namespace $NAMESPACE
--context $RIVENDELL_CONTEXT
--variableFile $dockerImageVarFile
--variableFile $here/../../../ci/vars/deployment-targets/local.vars
--variable ecrKeyId=$AWS_ACCESS_KEY_ID
--variable ecrKeySecret=$AWS_SECRET_ACCESS_KEY
--variable deploymentEnvironment=staging
--variable deploymentTest=false
--variable stagingEnvironment=local-dev
--variable sidecarImageTag=$sidecarImageTag
--variable keycloakDev=$KEYCLOAK_DEV
--variable customRootUrl=$CUSTOM_ROOT_URL
--variable localDataDir=$localDataDir
--variable gondorServerIP=$GONDOR_SERVER_IP
--variable gondorServerPort=$GONDOR_SERVER_PORT
--variable dockerEcrRegistry=988983267146.dkr.ecr.ap-east-1.amazonaws.com
--yes
EOF
)
STARGAZER_RIVENDELL_PARAMS="$STARGAZER_RIVENDELL_PARAMS $STARGAZER_RIVENDELL_EXTRA_PARAMS"


function runCommandOnMinikube {
  minikube ssh -- $@
}

function copyFromMinikube {
  minikube cp minikube:$1 $2
}

function copyToMinikube {
  minikube cp $1 minikube:$2
}

function runCommandWithDocker {
    docker run -it -v $localDataDir:$localDataDir -v /tmp:/tmp alpine:3.8 "$@"
}

function getLocalDataDir {
    echo $localDataDir
}

function getDataDir {
    echo $localDataDir/gondor/$NAMESPACE
}

function deleteDataFolder {
    dataDir=`getDataDir`
    if isMinikube; then
        runCommandOnMinikube "sudo rm -rf $dataDir"
    else
        runCommandWithDocker rm -rf $dataDir
    fi
}
