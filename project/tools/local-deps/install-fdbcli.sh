#!/usr/bin/env bash

FDBCLI_VERSION=6.3.15

installTool=''
os=''

function checkEnv {
    case `uname` in
        Darwin)
            installTool=brew
            os=darwin
            ;;
        Linux)
            os=linux
            if which apt > /dev/null 2>&1; then
                installTool=apt
            elif which dnf > /dev/null 2>&1; then
                installTool=dnf
             fi
            ;;
    esac
}

function removeFDBCLI {
    case "$os" in
        linux)
            case "$installTool" in
                apt)
                    sudo apt remove -y foundationdb-clients
                    ;;
                dnf)
                    sudo dnf remove -y foundationdb-clients
                    ;;
            esac
            ;;
    esac
}

function installFDBCLI {
    case "$os" in
        darwin)
            echo "Please download https://www.foundationdb.org/downloads/${FDBCLI_VERSION}/macOS/installers/FoundationDB-${FDBCLI_VERSION}.pkg then install the package"
            ;;
        linux)
            case "$installTool" in
                apt)
                    wget -O /tmp/foundationdb-clients.deb https://www.foundationdb.org/downloads/${FDBCLI_VERSION}/ubuntu/installers/foundationdb-clients_${FDBCLI_VERSION}-1_amd64.deb &&
                        sudo apt install -y /tmp/foundationdb-clients.deb
                    ;;
                dnf)
                    wget -O /tmp/foundationdb-clients.rpm https://www.foundationdb.org/downloads/${FDBCLI_VERSION}/rhel7/installers/foundationdb-clients-${FDBCLI_VERSION}-1.el7.x86_64.rpm &&
                        sudo dnf install -y /tmp/foundationdb-clients.rpm
                    ;;
            esac
            ;;
    esac
}

checkEnv
if [ "$1" == "--force" ]; then
    removeFDBCLI && installFDBCLI
else
    if ! which fdbcli > /dev/null 2>&1; then
        installFDBCLI
    fi
fi
