#!/usr/bin/env bash

export STARGAZER_SERVICES_BASE_URL=http://localhost:8080
export STARGAZER_SERVICES_SYNC_GATEWAY_INTERNAL_HOST=sync-gateway.gondor.svc.kube-local.io
export STARGAZER_SERVICES_SYNC_GATEWAY_INTERNAL_PUBLIC_HOST=sync-gateway.gondor.svc.kube-local.io
export STARGAZER_SERVICES_SYNC_GATEWAY_EXTERNAL_HOST=sync-gateway.gondor.svc.kube-local.io
export STARGAZER_SERVICES_SYNC_GATEWAY_EXTERNAL_SSL=false
export STARGAZER_SERVICES_SYNC_GATEWAY_EXTERNAL_PORT=80
export STARGAZER_SERVICES_COUCHBASE_INTERNAL_HOST=couchbase.gondor.svc.kube-local.io
export STARGAZER_SERVICES_KEYCLOAK_HOST=keycloak.gondor.svc.kube-local.io
export STARGAZER_SERVICES_KEYCLOAK_SSL=false
export STARGAZER_SERVICES_KEYCLOAK_PORT=80
export STARGAZER_SERVICES_KEYCLOAK_ADMIN_HOST=keycloak.gondor.svc.kube-local.io
export STARGAZER_SERVICES_KEYCLOAK_ADMIN_SSL=false
export STARGAZER_SERVICES_KEYCLOAK_ADMIN_PORT=80
export STARGAZER_MAIL_GUN_DOMAIN=dev.anduin.co
export STARGAZER_S3_DEFAULT_BUCKET=gondor-assets-document-dev
export STARGAZER_S3_BATCH_DOWNLOAD_BUCKET=gondor-batch-download-dev
export STARGAZER_S3_PUBLIC_BUCKET=gondor-public-document-dev
export STARGAZER_INTERCOM_APP_ID=xygdnwyv
export STARGAZER_KAFKA_BOOTSTRAP_SERVER=kafka-local.gondor.svc.kube:9092

export STARGAZER_SERVICES_COUCHBASE_USERNAME=Administrator
export STARGAZER_SERVICES_COUCHBASE_PASSWORD=password
export STARGAZER_SERVICES_COUCHBASE_RBAC_USERNAME=gondor
export STARGAZER_SERVICES_COUCHBASE_RBAC_PASSWORD=gondor
export STARGAZER_SERVICES_KEYCLOAK_ADMIN_USER=admin
export STARGAZER_SERVICES_KEYCLOAK_ADMIN_PASSWORD=password
export STARGAZER_MAIL_GUN_APIKEY=************************************
export STARGAZER_S3_ACCESS_KEY_ID=********************
export STARGAZER_S3_SECRET_ACCESS_KEY=OqyS8wcK6KAVu3uG6cB6J+6+Fri6i3F9VmVsbmpM
export STARGAZER_SERVICES_PORTAL_ADMIN_PASSWORD=bDIIyDx0
export STARGAZER_SERVICES_EXECUTIVE_ADMIN_PASSWORD=bDIIyDx0
export STARGAZER_SERVICES_JWT_TOKEN_KEY=a1aea9c8faa111bf7d4ecc4915edcd95
