#!/usr/bin/env bash

set -e

LOCAL_PROVISIONER_VERSION=v0.0.28

echo "Installing local provisioner $LOCAL_PROVISIONER_VERSION"

kubectl apply -f https://raw.githubusercontent.com/rancher/local-path-provisioner/${LOCAL_PROVISIONER_VERSION}/deploy/local-path-storage.yaml

if [ "${MINIKUBE_LOCAL_PATH_OVERRIDE_STANDARD_SC}" == "true" ]; then
  cat <<EOF | kubectl apply -f -
  apiVersion: storage.k8s.io/v1
  kind: StorageClass
  metadata:
    name: standard
  provisioner: rancher.io/local-path
  volumeBindingMode: WaitForFirstConsumer
  reclaimPolicy: Delete
EOF
fi
