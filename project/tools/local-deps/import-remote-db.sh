#!/usr/bin/env bash

function showHelp {
    echo "USAGE: $0 import [canary|internal|demo|minas-tirith] (backup to namespace gondor from latest backup)"
    echo "       $0 import [canary|internal|demo|minas-tirith] [namespace] (use latest backup)"
    echo "       $0 import [canary|internal|demo|minas-tirith] [namespace] [backup-name]"
    echo "       $0 cancel"
}

function executeImportJob {
    if [ "$environment" == "gondor-public" ]; then
        target=local-dev-public
    else
        target=local-dev
    fi
    $here/../../../ci/scripts/deploy/restore.sh $environment $target gondor $name
}

function fixKeycloakPassword {
    sql=$(cat <<EOF
update credential set secret_data = '{"value":"Tyx/FayORuwGpuOQttLRWTwf6nf5hBFzSojBVYdIZPMbarNGnq2dXLjp3103P5iAOtOkA4Fyu6Ku3lt5p+OKlw==","salt":"QTMmAKU0QSsSc2V8yTrrMQ=="}' where user_id IN (SELECT id from user_entity WHERE username = 'admin');
EOF
       )
    if isMinikube; then
        eval $(minikube docker-env --shell=bash)
        docker run -it -e PGHOST="postgres-keycloak-11.$namespace.svc.kube" -e PGDATABASE=keycloak -e PGUSER=keycloak-postgres-user -e PGPASSWORD=keycloak-postgres-password postgres:11.9 psql -c "$sql"
    else
        docker run -it --dns ********** --dns ******* -e PGHOST="postgres-keycloak-11.$namespace.svc.kube" -e PGDATABASE=keycloak -e PGUSER=keycloak-postgres-user -e PGPASSWORD=keycloak-postgres-password postgres:11.9 psql -c "$sql"
    fi
}

function waitForComplete {
    echo "Waiting for complete..." &&
        rivendell --namespace $namespace --context $context logs restore-couchbase &&
        rivendell --namespace $namespace --context $context logs restore-foundationdb &&
        rivendell --namespace $namespace --context $context logs restore-postgres-keycloak-11 &&
        rivendell --namespace $namespace --context $context logs restore-timescaledb &&
        rivendell --namespace $namespace --context $context logs restore-form &&
        echo "Done"
}

function cleanup {
    $here/../../../ci/scripts/deploy/restore-cleanup.sh $environment local-dev gondor latest
}

function doImport {
    executeImportJob &&
        waitForComplete &&
        fixKeycloakPassword &&
        $here/stop.sh &&
        $here/start.sh
    cleanup
}

if [ $# -lt 1 ]; then
    showHelp
    exit 1
fi

here=`cd $(dirname $BASH_SOURCE); pwd`
source $here/common.sh

command=$1
if [ "$2" == "local-dev" ]; then
    environment=$2
else
    environment=gondor-${2}
fi
namespace=${3:-gondor}
name=${4:-latest}
context=${RIVENDELL_CONTEXT:-minikube}

case $command in
    import)
        doImport
        ;;
    cancel)
        cleanup
        ;;
    *)
        showHelp
        exit 1
        ;;
esac
