#!/usr/bin/env bash

set -e

TIDB_OPERATOR_VERSION=v1.4.7
TIDB_OPERATOR_NAMESPACE=tidb-admin

echo "Installing tidb operator $TIDB_OPERATOR_VERSION"

kubectl replace -f https://raw.githubusercontent.com/pingcap/tidb-operator/${TIDB_OPERATOR_VERSION}/manifests/crd.yaml || true
helm repo add --force-update pingcap https://charts.pingcap.org
kubectl create ns $TIDB_OPERATOR_NAMESPACE || true
helm template --namespace $TIDB_OPERATOR_NAMESPACE tidb-local pingcap/tidb-operator --version ${TIDB_OPERATOR_VERSION} \
  | kubectl apply --namespace $TIDB_OPERATOR_NAMESPACE -f -
