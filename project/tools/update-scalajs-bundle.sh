#!/usr/bin/env bash
set -e
here=`cd $(dirname $BASH_SOURCE); pwd`

echo "running npm update on all top level projects"

appModules=$(cat <<EOF
jsAnduinBootstrap
gondorWebResources
gondorAppClient
olympianJS
pantheonJS
naryaJS
mayaAppJS
fundsubAppJS
signatureAppJS
dataroomAppJS
heimdallAppJS
pantheonWebWorker
lpprofileAppJS
EOF
)

sbtCommand=""
for module in $appModules; do
    sbtCommand="${module}/clearPackageJson;${module}/packageJson;${sbtCommand}"
done

UPDATE_YARN_LOCK=1 ./sbt "$sbtCommand"
echo "running npm update on all top level projects...DONE!"

echo "removing all node_modules"
find . -name node_modules -type d -exec rm -rf {} \; || :

echo "run yarn install"
yarn install
