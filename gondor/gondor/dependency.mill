// Copyright (C) 2014-2025 Anduin Transactions Inc.

package build.gondor.gondor

import anduin.build.AnduinVersions
import mill.scalalib.*

object Gondor {

  lazy val jvmDeps = Seq(
    mvn"jline:jline:${AnduinVersions.jline}",
    mvn"org.scala-lang.modules::scala-parser-combinators:${AnduinVersions.scala_parser}",
    mvn"commons-codec:commons-codec:${AnduinVersions.commonsCodec}",
    // parboiled
    mvn"org.parboiled::parboiled:${AnduinVersions.parboiled}"
      .exclude("com.chuusai" -> AnduinVersions.j2s("shapeless")),
    mvn"org.scodec::scodec-bits:${AnduinVersions.scodecBits}"
      .exclude("com.chuusai" -> AnduinVersions.j2sjs("shapeless"))
      .exclude("com.chuusai" -> AnduinVersions.j2s("shapeless")),
    mvn"org.apache.commons:commons-lang3:${AnduinVersions.apacheCommonLang}",
    // Test dependencies
    mvn"com.googlecode.owasp-java-html-sanitizer:owasp-java-html-sanitizer:${AnduinVersions.javaHtmlSanitizer}"
      .exclude("com.google.guava" -> "guava"),
    mvn"me.tongfei:progressbar:${AnduinVersions.progressBar}"
      .exclude("org.jline" -> "jline"),
    mvn"com.fasterxml.jackson.core:jackson-core:${AnduinVersions.jackson}",
    mvn"com.fasterxml.jackson.core:jackson-annotations:${AnduinVersions.jacksonAnnotations}",
    mvn"com.fasterxml.jackson.core:jackson-databind:${AnduinVersions.jacksonDatabind}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
  )

}
