package build.gondor.gondor

import build_.gondor.gondor.dependency_.Gondor
import build_.build.util_.*

import build.modules.{
  dataroom,
  investorProfile,
  dataextract,
  fundData,
  signature,
  fundsub,
  graphql,
  dynamicForm,
  gaia,
  dynamicFormTest,
  heimdall,
  ria,
  integplatform,
  greylin,
  brienne
}
import build.itools.{olympian, pantheon}
import build.apps.maya
import build.gondor.gondorTest
import build.platform.{stargazerTest, webModules}

import mill.*
import mill.scalajslib.api.*
import anduin.mill.*
import anduin.mill.jsdeps.*

object `package` extends AnduinCrossPlatformModule {

  object jvm extends JvmModule {

    override def mvnDeps = super.mvnDeps() ++ Gondor.jvmDeps

    override def moduleDeps = super.moduleDeps ++ Seq(
      dataroom.dataroomApp.jvm,
      investorProfile.investorProfileApp.jvm,
      dataextract.dataextractApp.jvm,
      fundData.fundDataApp.jvm,
      signature.signatureApp.jvm,
      fundsub.fundsubApp.jvm,
      olympian.jvm,
      pantheon.pantheon.jvm,
      dynamicFormTest.dynamicFormTest.jvm,
      maya.mayaApp.jvm,
      heimdall.heimdallApp.jvm,
      brienne.brienne,
      greylin.greylin.jvm,
      integplatform.integplatformApp.jvm,
      ria.riaApp.jvm
    )

    object test extends JvmTests with AnduinScalaPBModule {

      override def moduleDeps = super.moduleDeps ++ Seq(stargazerTest.jvm, gondorTest)

      // private val useForkEnvKey = Array("MILL_JVM_OPTS_PATH", "STARGAZER_CONFIG")

      // override def forkEnv = super.forkEnv().filter(item => useForkEnvKey.exists(key => item._1 == key))

    }

    object multiregionit extends AnduinZioTests with AnduinIntegTests {
      override def moduleDeps = super.moduleDeps ++ Seq(build.gondor.gondorTest, heimdall.heimdallApp.jvm.it, jvm)
    }

  }

  object js extends JsModule with ESBuildScalaJSApp {

    override def moduleDeps = super.moduleDeps ++ Seq(
      webModules,
      graphql.js,
      dataroom.dataroom.js,
      dataroom.dataroomIntegration.js,
      dynamicForm.dynamicForm.js,
      fundsub.fundsub.js,
      gaia.gaiaBuilder
    )

    object test extends JsTests {

      override def moduleKind: T[ModuleKind] = Task(ModuleKind.CommonJSModule)

      override def moduleDeps = super.moduleDeps ++ Seq(stargazerTest.js)
    }

  }

}
