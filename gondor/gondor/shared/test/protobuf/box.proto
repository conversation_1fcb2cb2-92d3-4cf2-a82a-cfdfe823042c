syntax = "proto3";

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.persistence.semiauto"
  single_file: true
  preserve_unknown_fields: false
  preamble: "sealed trait Sealed"
  preamble: "trait NonSealed"
  preamble: "final case class ThirdNonSealedItem(i: Int) extends NonSealed"
  preamble: "trait Level0"
  preamble: "final case class Level0ExtraItem(i: Int) extends Level0"
};

// Sealed box

message SealedBox {
  oneof item {
    FirstSealedItem first = 1;
    SecondSealedItem second = 2;
  }
}

message FirstSealedItem {
  option (scalapb.message).extends = "Sealed";
  string name = 1;
}

message SecondSealedItem {
  option (scalapb.message).extends = "Sealed";
  int32 age = 1;
}

// Non-sealed box

message NonSealedBox {
  oneof item {
    FirstNonSealedItem first = 1;
    SecondNonSealedItem second = 2;
  }
}

message FirstNonSealedItem {
  option (scalapb.message).extends = "NonSealed";
  string name = 1;
}

message SecondNonSealedItem {
  option (scalapb.message).extends = "NonSealed";
  int32 age = 1;
}

// Deep box

message Level0Box {
  oneof item {
    Level0Item first = 1;
    Level1Box second = 2;
  }
}

message Level0Item {
  option (scalapb.message).extends = "Level0";
  int32 i = 1;
}

message Level1Box {
  oneof item {
    Level1Item first = 1;
    Level2Box second = 2;
  }
}

message Level1Item {
  option (scalapb.message).extends = "Level0";
  int32 i = 1;
}

message Level2Box {
  oneof item {
    Level2FirstItem first = 1;
    Level2SecondItem second = 2;
  }
}

message Level2FirstItem {
  option (scalapb.message).extends = "Level0";
  int32 i = 1;
}

message Level2SecondItem {
  option (scalapb.message).extends = "Level0";
  int32 i = 1;
}
