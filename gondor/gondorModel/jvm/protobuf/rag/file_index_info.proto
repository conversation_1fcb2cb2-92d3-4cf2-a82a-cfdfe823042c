syntax = "proto3";

package anduin.rag;

import "scalapb/scalapb.proto";
import "rag/index_data.proto";
import "anduin/rag/index_state.proto";

option (scalapb.options) = {
  single_file: true
  import: "anduin.model.id.FileId"
};

message FileIndexInfo {
  string file_id = 1 [(scalapb.field).type = "FileId"];
  rag.IndexState filename_state = 2;
  rag.IndexState file_content_state = 3;
  rag.IndexData dms_info = 4 [(scalapb.field).required = true];
  rag.IndexData vespa_info_opt = 5;
}

message BatchFileIndexInfo {
  repeated rag.FileIndexInfo file_index_infos = 1;
}
