syntax = "proto3";

import "google/protobuf/wrappers.proto";
import "scalapb/scalapb.proto";
import "date_time.proto";
import "email_address.proto";
import "anduin/dms/feature.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.email"
  single_file: true
  import: "anduin.id.TransactionId"
  import: "anduin.model.common.emailaddress.EmailAddress"
  import: "anduin.model.common.user.UserId"
  import: "anduin.model.email.*"
  import: "anduin.model.id.*"
  import: "java.time.Instant"
  import: "anduin.dms.DmsFeatureProto"
};

message EmailPersonMessage {
  oneof id {
    EmailAddressMessage contact = 1 [(scalapb.field).type = "EmailAddress"];
    string user = 2 [(scalapb.field).type = "UserId"];
  }
}

enum EmailContactType {
  EmailContact = 0;
  ReportRecipientContact = 1;
}

message TransactionTag {
  string trxn_id = 1 [(scalapb.field).type = "Option[TransactionId]"];
}

message EmailTagValues {
  repeated string tags = 1 [(scalapb.field).collection_type = "Set"];
}

/** ===================== Couchbase models ===================== **/
// TODO: @hoangmai to remove these after migration
message EmailThreadModelMessage {
  reserved 5;

  // SMTP Message-ID of the email that initiated the thread.
  string first_smtp_message_id = 1;
  // The contact that sent the initiating email.
  EmailPersonMessage creator = 2;
  // List of all contacts in this thread. Note that this is problematic, as it would be visible to all users in
  // the thread (currently, all users in the transaction), but we accept this for now (similar to how "email contacts"
  // is the same for all users in the transaction)
  repeated EmailAddressMessage contacts = 3 [(scalapb.field).type = "EmailAddress"];

  string subject = 4;
}


message EmailAttachmentModelMessage {
  repeated string files = 1 [(scalapb.field).type = "FileId"];
  string sender = 2;
  InstantMessage timestamp = 3 [(scalapb.field).type = "Instant"];
}

// Model for emails received by Anduin-cc-email. This contains processed data. Raw emails, as they enter the system,
// are kept track of separately (without a corresponding protobuf message, as they are "raw").
message EmailModelMessage {
  reserved 9;

  message Body {
    // Text version of the email. If it has only HTML body, Mailgun will create a text representation.
    string plain = 1;
    // HTML version of the email, if it was multipart.
    google.protobuf.StringValue html = 2;
  }

  message Stripped {
    // Text version, without quoted parts (e.g. added by email clients when replying) and signature block.
    google.protobuf.StringValue text = 1;
    // HTML version, without quoted parts (e.g. added by email clients when replying) and signature block.
    google.protobuf.StringValue html = 2;
    // Signature block.
    google.protobuf.StringValue signature = 3;
  }

  // SMTP Message-ID of this email.
  string smtp_message_id = 1;
  // List of previous email ids in the reply chain. For example, if email A starts a thread, B is a reply to A, C is a
  // reply to B, then for C this field would be [A-ID, B-ID].
  repeated string parents = 2 [(scalapb.field).type = "EmailId"];
  EmailPersonMessage sender = 3;
  // Other contacts. This is effectively the 'Cc:' list. It does not contain Anduin-cc-email, which is the actual
  // receiver ('To:').
  repeated EmailAddressMessage receivers = 4 [(scalapb.field).type = "EmailAddress"];

  repeated EmailAddressMessage cc_receivers = 13 [(scalapb.field).type = "EmailAddress"];

  InstantMessage timestamp = 5 [(scalapb.field).type = "Instant"];

  string subject = 6;
  Body body = 7;
  // Extracted (by mailgun) content sub-parts.
  Stripped stripped = 8;

  // List of FileId pointing to the attachments.
  repeated string attachments = 12 [(scalapb.field).type = "FileId"];

  bool is_draft = 10;

  bool deleted = 11;
}

message EmailContactMessage {
  string first_name = 1;
  string last_name = 2;
  // If this is present, it should be used instead of first_name+last_name combination.
  string display_name = 3;
  string title = 4;
  // Email address.
  string address = 5;
}

// Model to store info of an email thread that related to a specific user.
message UserEmailThreadMessage {
  option (scalapb.message).companion_extends = "UserEmailThreadMessageCompanion";
  reserved 2;

  repeated string seen_emails = 1 [(scalapb.field).type = "EmailId", (scalapb.field).collection_type = "Set"];

  repeated TransactionTag trxn_tags = 3 [(scalapb.field).collection_type = "Set"];

  map<string, EmailTagValues> tag_map = 4 [(scalapb.field).key_type = "EmailTagKey"];

  map<string, EmailAddressMessageList> bounced_emails = 5 [(scalapb.field).key_type = "EmailId"];
}

/** ===================== Record layer models ===================== **/
message EmailThreadModel {
  reserved 5;

  string email_thread_id = 6 [(scalapb.field).type = "EmailThreadId"];
  // SMTP Message-ID of the email that initiated the thread.
  string first_smtp_message_id = 1;
  // The contact that sent the initiating email.
  EmailPersonMessage creator = 2;
  // List of all contacts in this thread. Note that this is problematic, as it would be visible to all users in
  // the thread (currently, all users in the transaction), but we accept this for now (similar to how "email contacts"
  // is the same for all users in the transaction)
  repeated EmailAddressMessage contacts = 3 [(scalapb.field).type = "EmailAddress"];

  string subject = 4;

}


message EmailAttachmentModel {
  string email_attachment_id = 4 [(scalapb.field).type = "EmailAttachmentId"];
  repeated string files = 1 [(scalapb.field).type = "FileId"];
  string sender = 2;
  InstantMessage timestamp = 3 [(scalapb.field).type = "Instant"];
  anduin.dms.DmsFeatureProto dms_feature = 5;
}

// Model for emails received by Anduin-cc-email. This contains processed data. Raw emails, as they enter the system,
// are kept track of separately (without a corresponding protobuf message, as they are "raw").
message EmailModel {
  reserved 9;

  message Body {
    // Text version of the email. If it has only HTML body, Mailgun will create a text representation.
    string plain = 1;
    // HTML version of the email, if it was multipart.
    google.protobuf.StringValue html = 2;
  }

  message Stripped {
    // Text version, without quoted parts (e.g. added by email clients when replying) and signature block.
    google.protobuf.StringValue text = 1;
    // HTML version, without quoted parts (e.g. added by email clients when replying) and signature block.
    google.protobuf.StringValue html = 2;
    // Signature block.
    google.protobuf.StringValue signature = 3;
  }

  string email_id = 14 [(scalapb.field).type = "EmailId"];
  // SMTP Message-ID of this email.
  string smtp_message_id = 1;
  // List of previous email ids in the reply chain. For example, if email A starts a thread, B is a reply to A, C is a
  // reply to B, then for C this field would be [A-ID, B-ID].
  repeated string parents = 2 [(scalapb.field).type = "EmailId"];
  EmailPersonMessage sender = 3;
  // Other contacts. This is effectively the 'Cc:' list. It does not contain Anduin-cc-email, which is the actual
  // receiver ('To:').
  repeated EmailAddressMessage receivers = 4 [(scalapb.field).type = "EmailAddress"];

  repeated EmailAddressMessage cc_receivers = 13 [(scalapb.field).type = "EmailAddress"];

  InstantMessage timestamp = 5 [(scalapb.field).type = "Instant"];

  string subject = 6;
  Body body = 7;
  // Extracted (by mailgun) content sub-parts.
  Stripped stripped = 8;

  // List of FileId pointing to the attachments.
  repeated string attachments = 12 [(scalapb.field).type = "FileId"];

  bool is_draft = 10;

  bool deleted = 11;

}

message EmailContactModel {
  string email_contact_id = 6 [(scalapb.field).type = "EmailContactId"];
  string first_name = 1;
  string last_name = 2;
  // If this is present, it should be used instead of first_name+last_name combination.
  string display_name = 3;
  string title = 4;
  // Email address.
  string address = 5;
}

// Model to store info of an email thread that related to a specific user.
message UserEmailThreadModel {
  option (scalapb.message).companion_extends = "UserEmailThreadModelCompanion";
  reserved 2;

  string user_email_thread_id = 6 [(scalapb.field).type = "UserEmailThreadId"];

  repeated string seen_emails = 1 [(scalapb.field).type = "EmailId", (scalapb.field).collection_type = "Set"];

  repeated TransactionTag trxn_tags = 3 [(scalapb.field).collection_type = "Set"];

  map<string, EmailTagValues> tag_map = 4 [(scalapb.field).key_type = "EmailTagKey"];

  map<string, EmailAddressMessageList> bounced_emails = 5 [(scalapb.field).key_type = "EmailId"];

}

message RecordTypeUnion {
  EmailThreadModel _EmailThreadModel = 1;
  EmailAttachmentModel _EmailAttachmentModel = 2;
  EmailModel _EmailModel = 3;
  EmailContactModel _EmailContactModel = 4;
  UserEmailThreadModel _UserEmailThreadModel = 5;
}