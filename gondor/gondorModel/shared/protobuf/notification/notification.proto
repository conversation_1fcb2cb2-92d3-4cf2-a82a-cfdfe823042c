syntax = "proto3";

package anduin.notification.record;

import "date_time.proto";
import "notification/id.proto";
import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";

option (scalapb.options) = {
    package_name: "anduin.protobuf.notification"
    single_file: true
    import: "anduin.notification.NotificationTypeMapper.given"
    import: "anduin.model.common.user.UserId"
    import: "anduin.model.id.stage.DataRoomWorkflowId"
    import: "anduin.model.id.FileId"
    import: "java.time.Instant"
    import: "anduin.id.notification.NotificationId"
    import: "anduin.id.fundsub.FundSubLpId"
    import: "anduin.id.fundsub.FundSubLpFormIdTrait"
    import: "anduin.id.issuetracker.IssueId"
};

enum NotificationType {
    UnspecifiedType = 0;
    DataRoomNewFile = 1;
    FormComment = 2;
    AmlKycComment = 3;
}

enum NotificationAction {
    UnspecifiedAction = 0;
    DataRoomNewFileNotifyHourly = 1;
    DataRoomNewFileNotifyDaily = 2;
    DataRoomNewFileNotifyWeekly = 3;
    FormCommentAddComment = 4;
    FormCommentAddReply = 5;
    FormCommentResolveComment = 6;
    FormCommentReopenComment = 7;
}

message NotificationData {
    oneof sealed_value {
        EmptyNotificationData empty_notification_data = 1;
        DataRoomNewFileNotificationData data_room_new_file_notification_data = 2;
        FormCommentNotificationData form_comment_notification_data = 3;
        AmlKycCommentNotificationData aml_kyc_comment_notification_data = 4;
    }
}

message EmptyNotificationData {}

message DataRoomNewFileNotificationData {
    string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
    repeated string file_ids = 2 [(scalapb.field).collection_type = "Set", (scalapb.field).type = "FileId"];
}

message FormCommentNotificationData {
    string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
    string lp_form_id = 2 [(scalapb.field).type = "FundSubLpFormIdTrait"];
    string issue_id = 3 [(scalapb.field).type = "IssueId"];
    google.protobuf.StringValue comment_id_opt = 4;
}

message AmlKycCommentNotificationData {
    string fund_sub_lp_id = 1 [(scalapb.field).type = "FundSubLpId"];
    string doctype = 2;
    string issue_id = 3 [(scalapb.field).type = "IssueId"];
    google.protobuf.StringValue comment_id_opt = 4;
}

message NotificationReceiverData {
    bool seen = 2;
    InstantMessage seen_at_opt = 3 [(scalapb.field).type = "Instant"];
}

message NotificationModel {
    NotificationIdMessage notification_id = 1 [(scalapb.field).type = "NotificationId"];
    NotificationType notification_type = 2;
    NotificationAction notification_action = 3;
    NotificationData notification_data = 4;
    InstantMessage created_at = 5 [(scalapb.field).type = "Instant"];
    string creator_opt = 6 [(scalapb.field).type = "Option[UserId]"];
    map<string, NotificationReceiverData> receiver_map = 7 [(scalapb.field).key_type = "UserId"];
}

message RecordTypeUnion {
    NotificationModel notification_model = 1;
}