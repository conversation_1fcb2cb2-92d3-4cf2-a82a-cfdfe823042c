syntax = "proto3";

package anduin.notification.space;

import "date_time.proto";
import "notification/id.proto";
import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";

option (scalapb.options) = {
    package_name: "anduin.protobuf.notification.space"
    single_file: true
    import: "anduin.model.id.notification.NotificationSpaceId"
    import: "anduin.notification.NotificationTypeMapper.given"
};

message NotificationSpaceModel {
    NotificationSpaceIdMessage notification_space_id = 1 [(scalapb.field).type = "NotificationSpaceId"];
}

message RecordTypeUnion {
    NotificationSpaceModel notification_space_model = 1;
}