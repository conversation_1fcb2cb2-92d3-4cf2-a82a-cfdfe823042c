syntax = "proto3";

package anduin.tag.v2;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.tag.v2"
  single_file: true
  import: "anduin.id.tag.TagListId"
  import: "java.time.Instant"
};

message TagListTrackingModel {
  string tag_list_id = 1 [(scalapb.field).type = "TagListId"];
  InstantMessage last_updated = 2 [(scalapb.field).type = "Instant"];
}