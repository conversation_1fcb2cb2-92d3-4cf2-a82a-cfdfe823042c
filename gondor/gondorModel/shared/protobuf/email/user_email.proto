syntax = "proto3";

package anduin.email.user;

import "scalapb/scalapb.proto";
import "email/email_topic.proto";

option (scalapb.options) = {
  package_name: "anduin.model.common.email"
  import: "anduin.model.common.user.UserId"
  single_file: true
};

message UserEmailModel {
  string user_id = 1 [(scalapb.field).type = "UserId"];
  repeated EmailTopic unsubscribed_email_topics = 4 [(scalapb.field).collection_type = "Set"];
}

message RecordTypeUnion {
  UserEmailModel _UserEmailModel = 1;
}