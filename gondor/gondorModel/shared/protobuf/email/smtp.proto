syntax = "proto3";

package anduin.email.smtp;

import "scalapb/scalapb.proto";
import "email_address.proto";

option (scalapb.options) = {
  single_file: true
};

message CustomEmailAddressModel {
  string name = 1;
  string address = 2;
}

message CustomSmtpServerConfigModel {
  CustomEmailAddressModel from = 1;
  string host = 2;
  int32 port = 3;
  string user_name = 4;
  string encrypted_password = 5;
  bool tls = 6;
}

