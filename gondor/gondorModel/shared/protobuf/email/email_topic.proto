syntax = "proto3";

import "scalapb/scalapb.proto";
import "anduin/ria/email/email_type.proto";

option (scalapb.options) = {
  package_name: "anduin.email.topic"
  import: "anduin.model.id.stage.DataRoomWorkflowId"
  import: "anduin.id.entity.EntityId"
  import: "anduin.id.TransactionId"
  import: "anduin.id.investmententity.InvestmentEntityId"
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.id.funddata.FundDataFirmId"
  import: "anduin.model.id.prismatic.PrismaticCustomerId"
  import: "anduin.id.ria.RiaEntityId"
  import: "anduin.id.ria.RiaFundGroupId"
  import: "anduin.id.investorportal.InvestorPortalId"
  single_file: true
};

message EmailTopic {
  reserved 812;
  oneof sealed_value {
    // Platform
    Other other = 1;
    InboxEmail inbox_email = 2;
    // DataRoom
    DataRoomFileUploadDigest data_room_file_upload_digest = 100;
    DataRoomJoinViaLinkInvitation data_room_join_via_link_invitation = 101;
    DataRoomCreated data_room_created = 102;
    DataRoomHomePageMessage data_room_home_page_message = 103;
    DataRoomInvitationAccepted data_room_invitation_accepted = 104;
    DataRoomInvitation data_room_invitation = 105;
    DataRoomManualNotification data_room_manual_notification = 106;
    DataRoomRequestAccess data_data_room_request_access = 107;
    DataRoomRejection data_room_rejection = 108;
    DataRoomSetIsArchived data_room_set_is_archived = 109;
    DataRoomUserRemovalAdmin data_room_user_removal_admin = 110;
    DataRoomUserRemovalUser data_room_user_removal_user = 111;
    DataRoomSimulatorWarning data_room_simulator_warning = 112;
    DataRoomStartSimulator data_room_start_simulator = 113;
    // Entity
    UserJoinedEntity user_joined_entity = 200;
    UserDeclinedEntityInvitation user_declined_entity_invitation = 201;
    UserRemovedFromEntity user_removed_from_entity = 202;
    DataRoomBillingSupport data_room_billing_support = 203;
    // Account
    LinkAccount link_account = 300;
    OTPAuthentication otp_authentication = 301;
    ProtectedLinkAccess protected_link_access = 302;
    ResetPassword reset_password = 303;
    SetupPassword setup_password = 304;
    SignupSupport signup_support = 305;
    SignupVerify signup_verify = 306;
    EnterpriseLinkAccount enterprise_link_account = 307;
    OneTimeLinkAuthentication one_time_link_authentication = 308;
    // Signature
    SignatureCancelCreator signature_cancel_creator = 400;
    SignatureCancelRecipient signature_cancel_recipient = 401;
    SignatureCompleteCreator signature_complete_creator = 402;
    SignatureCompleteRecipient signature_complete_recipient = 403;
    SignatureReassignRecipientCreator signature_reassign_recipient_creator = 404;
    SignatureReassignRecipientActor signature_reassign_recipient_actor = 405;
    SignatureReassignRecipientNewRecipient signature_reassign_recipient_new_recipient = 406;
    SignatureRecipientView signature_recipient_view = 407;
    SignatureRemindRecipient signature_remind_recipient = 408;
    SignatureRequestSignedCreator signature_request_signed_creator = 409;
    SignatureRequestSignedRecipient signature_request_signed_recipient = 410;
    SignatureSendRequestCollaborator signature_send_request_collaborator = 411;
    SignatureSendRequestCreator signature_send_request_creator = 412;
    SignatureSendRequestRecipient signature_send_request_recipient = 413;
    // Investment Entity
    InvestmentEntityCustomEmail investment_entity_custom_email = 500;
    InvestmentEntityDocumentExpiration investment_entity_document_expiration = 501;
    InvestmentEntityInvitationInvitee investment_entity_invitation_invitee = 502;
    InvestmentEntityUserRemoved investment_entity_user_removed = 503;
    InvestmentEntityWarningFundSubLowIaCoverage investment_entity_warning_fund_sub_low_ia_coverage = 504;
    // FundSub
    FundSubAcceptAccessPortal fund_sub_accept_access_portal = 600;
    FundSubFormCommentAssignmentForFundAdmin fund_sub_form_comment_assignment_for_fund_admin = 601;
    FundSubFormCommentForFundAdmin fund_sub_form_comment_for_fund_admin = 602;
    FundSubFormCommentForLp fund_sub_form_comment_for_lp = 603;
    FundSubFundActivity fund_sub_fund_activity = 604;
    FundSubInviteAdmin fund_sub_invite_admin = 605;
    FundSubInviteCollaborator fund_sub_invite_collaborator = 606;
    FundSubInviteLp fund_sub_invite_lp = 607;
    FundSubLpDuplicatedOrder fund_sub_lp_duplicated_order = 608;
    FundSubLpEvent fund_sub_lp_event = 609;
    FundSubLpSubmitSubscriptionDoc fund_sub_lp_submit_subscription_doc = 610;
    FundSubLpSubmitSupportingDoc fund_sub_lp_submit_supporting_doc = 611;
    FundSubNotifyEmailBounced fund_sub_notify_email_bounced = 612;
    FundSubRemindLpCompleteForm fund_sub_remind_lp_complete_form = 613;
    FundSubRemindLpSignAgain fund_sub_remind_lp_sign_again = 614;
    FundSubRemindSupportingDoc fund_sub_remind_supporting_doc = 615;
    FundSubRemoveCollaborator fund_sub_remove_collaborator = 616;
    FundSubRemoveFundSubUser fund_sub_remove_fund_sub_user = 617;
    FundSubRequestAccessPortal fund_sub_request_access_portal = 618;
    FundSubRequestLpChange fund_sub_request_lp_change = 619;
    FundSubRequestSupportingDoc fund_sub_request_supporting_doc = 620;
    FundSubSendCountersigned fund_sub_send_countersigned = 621;
    FundSubSendCustomEmail fund_sub_send_custom_email = 622;
    FundSubSubscribeBlankOrderViaInvitation fund_sub_subscribe_blank_order_via_invitation = 623;
    FundSubUpdateCountersignDoc fund_sub_update_countersign_doc = 624;
    FundSubUploadReferenceDoc fund_sub_upload_reference_doc = 625;
    FundSubCountersignedDocsAdded fund_sub_countersigned_docs_added = 626;
    FundSubCountersignedDocsDistributed fund_sub_countersigned_docs_distributed = 627;
    FundSubDataExtractRequestsReadyForReviewForGp fund_sub_data_extract_requests_ready_for_review_for_gp = 628;
    FundSubSimulatorVerification fund_sub_simulator_verification = 629;
    FundSubSimulatorWarning fund_sub_simulator_warning = 630;
    FundSubAdditionalDocumentReadyForReviewReport fund_sub_additional_document_ready_for_review_report = 631;
    FundSubApproveSoftReview fund_sub_approve_soft_review = 632;
    FundSubCancelSoftReview fund_sub_cancel_soft_review = 633;
    FundSubDocumentReadyForReview fund_sub_document_ready_for_review = 634;
    FundSubDocumentReviewAssignment fund_sub_document_review_assignment = 635;
    FundSubDocumentReviewUnassignment fund_sub_document_review_unassignment = 636;
    FundSubUnsignedDocumentReadyForReview fund_sub_unsigned_document_ready_for_review = 637;
    FundSubNewLpReport fund_sub_new_lp_report = 638;
    FundSubNewDocumentsUploadByLp fund_sub_new_documents_upload_by_lp = 639;
    FundSubCancelRequestChangeOnSupportingDoc fund_sub_cancel_request_change_on_supporting_doc = 640;
    FundSubRequestChangeOnSupportingDoc fund_sub_request_change_on_supporting_doc = 641;
    FundSubDoneBatchSigning fund_sub_done_batch_signing = 642;
    FundSubDoneSigningRequester fund_sub_done_signing_requester = 643;
    FundSubDoneSigningSigner fund_sub_done_signing_signer = 644;
    FundSubReassignRequester fund_sub_reassign_requester = 645;
    FundSubReassignSigner fund_sub_reassign_signer = 646;
    FundSubCancelSignatureRequest fund_sub_cancel_signature_request = 647;
    FundSubRemindBatchCountersign fund_sub_remind_batch_countersign = 648;
    FundSubRemindSignatureRequest fund_sub_remind_signature_request = 649;
    FundSubSendBatchCountersign fund_sub_send_batch_countersign = 650;
    FundSubSendSignatureRequestSigner fund_sub_send_signature_request_signer = 651;
    FundSubSignaturePagesReusedToSigners fund_sub_signature_pages_reused_to_signers = 663;
    FundSubMarkSubDocRequestCompeteSigner fund_sub_mark_sub_doc_request_compete_signer = 652;
    FundSubMarkSubDocRequestCompleteRequester fund_sub_mark_sub_doc_request_complete_requester = 653;
    FundSubRecipientFailedDocusignAuthenticationRequester fund_sub_recipient_failed_docusign_authentication_requester = 654;
    FundSubAmlKycDocumentReviewAssignment fund_sub_aml_kyc_document_review_assignment = 655;
    FundSubAmlKycDocumentReviewUnassignment fund_sub_aml_kyc_document_review_unassignment = 656;
    FundSubDocumentHasNoReviewer fund_sub_document_has_no_reviewer = 657;
    BrienneBrokenUrlNotification brienne_broken_url_notification = 658;
    FundSubInviteAdvisor fund_sub_invite_advisor = 659;
    FundSubRevokeAdvisorInvitation fund_sub_revoke_advisor_invitation = 660;
    FundSubMarkSubscriptionAsComplete fund_sub_mark_subscription_as_complete = 661;
    FundSubTest fund_sub_test = 662;

    // Portal
    PortalInviteNewUserToEntity portal_invite_new_user_to_entity = 700;
    // FundData
    FundDataAssessmentDueDateNotification fund_data_assessment_due_date_notification = 800;
    FundDataAutoSyncDowngradeAdmin fund_data_auto_sync_downgrade_admin = 801;
    FundDataDocumentExpirationNotification fund_data_document_expiration_notification = 802;
    FundDataIEProfileConflictNotification fund_data_ie_profile_conflict_notification = 803;
    FundDataInviteMember fund_data_invite_member = 804;
    FundDataLeaveFundData fund_data_leave_fund_data = 805;
    FundDataRemindMember fund_data_remind_member = 806;
    FundDataRemoveMember fund_data_remove_member = 807;
    FundDataRequestCanceled fund_data_request_canceled = 808;
    FundDataRequestRemoveCollaborator fund_data_request_remove_collaborator = 809;
    FundDataRequestSent fund_data_request_sent = 810;
    FundDataRequestSubmitted fund_data_request_submitted = 811;
    FundDataRemoveGuest fund_data_remove_guest = 813;
    FundDataDocDistributionNotification fund_data_doc_distribution_notification = 814;
    FundDataCustomEmail fund_data_custom_email = 815;
    FundDataLandingPageLimitExceeded fund_data_landing_page_limit_exceeded = 816;
    // Integration Platform
    IntegPlatformInviteMember integ_platform_invite_member = 900;

    // Ria
    RiaEntityEmailTopic ria_entity_email_topic = 901;
    RiaFundGroupEmailTopic ria_fund_group_email_topic = 902;
  }
}

message Other {}

message InboxEmail {
  string transaction_id_opt = 1 [(scalapb.field).type = "Option[TransactionId]"];
}

message DataRoomFileUploadDigest {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomJoinViaLinkInvitation {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomCreated {}


message DataRoomHomePageMessage {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomInvitationAccepted {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomInvitation {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomManualNotification {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomRejection {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomRequestAccess {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomSetIsArchived {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomUserRemovalAdmin {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomUserRemovalUser {
  string data_room_workflow_id = 1 [(scalapb.field).type = "DataRoomWorkflowId"];
}

message DataRoomSimulatorWarning {}

message DataRoomStartSimulator {}

message UserJoinedEntity {
  string entity_id = 1 [(scalapb.field).type = "EntityId"];
}

message UserDeclinedEntityInvitation {
  string entity_id = 1 [(scalapb.field).type = "EntityId"];
}

message UserRemovedFromEntity {
  string entity_id = 1 [(scalapb.field).type = "EntityId"];
}

message DataRoomBillingSupport {
  string entity_id = 1;
}

message LinkAccount {}

message OTPAuthentication {}

message ProtectedLinkAccess {}

message ResetPassword {}

message SetupPassword {}

message SignupSupport {}

message SignupVerify {}

message EnterpriseLinkAccount {}

message OneTimeLinkAuthentication {}

message SignatureCancelCreator {}

message SignatureCancelRecipient {}

message SignatureCompleteCreator {}

message SignatureCompleteRecipient {}

message SignatureReassignRecipientActor {}

message SignatureReassignRecipientCreator {}

message SignatureReassignRecipientNewRecipient {}

message SignatureRecipientView {}

message SignatureRemindRecipient {}

message SignatureRequestSignedCreator {}

message SignatureRequestSignedRecipient {}

message SignatureSendRequestCollaborator {}

message SignatureSendRequestCreator {}

message SignatureSendRequestRecipient {}

message InvestmentEntityCustomEmail {
  string investment_entity_id = 1 [(scalapb.field).type = "InvestmentEntityId"];
}

message InvestmentEntityDocumentExpiration {
  string investment_entity_id = 1 [(scalapb.field).type = "InvestmentEntityId"];
}

message InvestmentEntityInvitationInvitee {
  string investment_entity_id = 1 [(scalapb.field).type = "InvestmentEntityId"];
}

message InvestmentEntityUserRemoved {
  string investment_entity_id = 1 [(scalapb.field).type = "InvestmentEntityId"];
}

message InvestmentEntityWarningFundSubLowIaCoverage {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubAcceptAccessPortal {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubFormCommentAssignmentForFundAdmin {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubFormCommentForFundAdmin {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubFormCommentForLp {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubFundActivity {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubInviteAdmin {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubInviteCollaborator {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubInviteLp {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubLpDuplicatedOrder {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubLpEvent {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubLpSubmitSubscriptionDoc {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubLpSubmitSupportingDoc {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubNotifyEmailBounced {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubRemindLpCompleteForm {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubRemindLpSignAgain {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubRemindSupportingDoc {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubRemoveCollaborator {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubRemoveFundSubUser {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubRequestAccessPortal {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubRequestLpChange {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubRequestSupportingDoc {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubSendCountersigned {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubSendCustomEmail {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubSubscribeBlankOrderViaInvitation {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubUpdateCountersignDoc {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubUploadReferenceDoc {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubCountersignedDocsAdded {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubCountersignedDocsDistributed {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubDataExtractRequestsReadyForReviewForGp {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubSimulatorVerification {}

message FundSubSimulatorWarning {}

message FundSubAdditionalDocumentReadyForReviewReport {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubApproveSoftReview {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubCancelSoftReview {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubDocumentReadyForReview {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubDocumentReviewAssignment {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubDocumentReviewUnassignment {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubUnsignedDocumentReadyForReview {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubNewLpReport {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubNewDocumentsUploadByLp {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubCancelRequestChangeOnSupportingDoc {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubRequestChangeOnSupportingDoc {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubDoneBatchSigning {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubDoneSigningRequester {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubDoneSigningSigner {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubReassignRequester {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubReassignSigner {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubCancelSignatureRequest {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubRemindBatchCountersign {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubRemindSignatureRequest {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubSendBatchCountersign {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubSendSignatureRequestSigner {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubSignaturePagesReusedToSigners {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubMarkSubDocRequestCompeteSigner {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubMarkSubDocRequestCompleteRequester {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubRecipientFailedDocusignAuthenticationRequester {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubAmlKycDocumentReviewAssignment {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubAmlKycDocumentReviewUnassignment {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubDocumentHasNoReviewer {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubInviteAdvisor {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubRevokeAdvisorInvitation {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubMarkSubscriptionAsComplete {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message FundSubTest {
  string fund_sub_id = 1 [(scalapb.field).type = "FundSubId"];
}

message BrienneBrokenUrlNotification {}

message PortalInviteNewUserToEntity {}

message FundDataAssessmentDueDateNotification {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
}

message FundDataAutoSyncDowngradeAdmin {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
}

message FundDataDocumentExpirationNotification {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
}

message FundDataIEProfileConflictNotification {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
}

message FundDataInviteMember {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
}

message FundDataLeaveFundData {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
}

message FundDataRemindMember {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
}

message FundDataRemoveMember {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
}

message FundDataRequestCanceled {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
}

message FundDataRequestRemoveCollaborator {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
}

message FundDataRequestSent {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
}

message FundDataRequestSubmitted {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
}

message FundDataRemoveGuest {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
}

message FundDataDocDistributionNotification {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
  string investor_portal_id = 2 [(scalapb.field).type = "InvestorPortalId"];
}

message FundDataCustomEmail {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
}

message FundDataLandingPageLimitExceeded {
  string firm_id = 1 [(scalapb.field).type = "FundDataFirmId"];
}

message IntegPlatformInviteMember {
  string prismatic_customer_id = 1 [(scalapb.field).type = "PrismaticCustomerId"];
}

message RiaEntityEmailTopic {
  string ria_entity_id = 1 [(scalapb.field).type = "RiaEntityId"];
  RiaEmail.RiaEntityEmailType ria_entity_email_type = 2;
}

message RiaFundGroupEmailTopic {
  string ria_fund_group_id = 1 [(scalapb.field).type = "RiaFundGroupId"];
  RiaEmail.RiaFundGroupEmailType ria_fund_group_email_type = 2;
}