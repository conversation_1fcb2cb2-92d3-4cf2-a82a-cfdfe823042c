syntax = "proto3";

package orgbilling;

import "scalapb/scalapb.proto";

import "orgbilling/dataroom/dataroom.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.orgbilling"
  single_file: true
  import: "anduin.id.entity.EntityId"
};

message EntityBillingStatus {
  reserved 2 to 4;
  string entity_id = 1 [(scalapb.field).type = "EntityId"];
  orgbilling.dataroom.DataRoomPackage data_room_package = 5;
}

message RecordTypeUnion {
  EntityBillingStatus _EntityBillingStatus = 1;
}
