syntax = "proto3";

package orgbilling.dataroom;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.orgbilling.dataroom"
  single_file: true
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.entity.EntityId"
};

enum DataRoomPremiumFeatureProto {
  ViewOnly = 0;
  Insights = 1;
  TermsOfAccess = 2;
  Watermark = 3;
  HomePage = 4;
  Whitelabel = 5;
}

message DataRoomPackage {
  oneof sealed_value {
    DataRoomPro data_room_pro = 1;
    DataRoomBusiness data_room_business = 2;
  }
}

message DataRoomPro {
  int64 exp_date = 1;
  int32 extra_seat = 2;
  repeated DataRoomPremiumFeatureProto extra_features = 3 [(scalapb.field).collection_type="Set"];
}

message DataRoomBusiness {
  int64 exp_date = 1;
  int32 extra_seat = 2;
  bool is_trial = 3;
}

message ChangeDataRoomPlanLog {
  reserved 3;

  InstantMessage change_date = 1 [(scalapb.field).type = "java.time.Instant"];
  string entity_id = 2 [(scalapb.field).type = "EntityId"];
  DataRoomPackage data_room_package = 5;
  string actor = 4 [(scalapb.field).type = "Option[UserId]"];
}

message RecordTypeUnion {
  ChangeDataRoomPlanLog _ChangeDataRoomPlanLog = 1;
}

