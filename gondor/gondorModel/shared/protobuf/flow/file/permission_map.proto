syntax = "proto3";

package flow.file;

import "scalapb/scalapb.proto";
import "flow/file/file_folder_permissions.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.flow.file"
  single_file: true
  import: "anduin.model.common.user.UserId"
  import: "anduin.model.id.*"
};

message FileFolderPermissionMap {
  map<string, FileFolderPermission> user_permissions = 1 [(scalapb.field).key_type = "UserId"];
  map<string, FileFolderPermission> team_permissions = 2 [(scalapb.field).key_type = "TeamId"];
}

message RevokedPermissionSet {
  repeated string revoked_users = 1 [(scalapb.field).type = "UserId", (scalapb.field).collection_type = "Set"];
  repeated string revoked_teams = 2 [(scalapb.field).type = "TeamId", (scalapb.field).collection_type = "Set"];
}
