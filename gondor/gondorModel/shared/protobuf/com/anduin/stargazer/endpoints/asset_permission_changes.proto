syntax = "proto3";

package com.anduin.stargazer.endpoints;

import "scalapb/scalapb.proto";
import "flow/file/file_folder_permissions.proto";

option (scalapb.options) = {
  single_file: true
  import: "scalapb.TypeMapper"
  import: "anduin.model.id.*"
  import: "anduin.model.id.dms.DmsChannel"
  import: "anduin.protobuf.flow.file.FileFolderPermission"
  import: "anduin.radix.RadixId"
  preamble: "trait FileFolderPermissionOptCompanion {"
  preamble: "  implicit val mapper: TypeMapper[FileFolderPermissionOpt, Option[FileFolderPermission]] = {"
  preamble: "    TypeMapper[FileFolderPermissionOpt, Option[FileFolderPermission]] {"
  preamble: "      _.value.permission"
  preamble: "    } {"
  preamble: "      _.fold(FileFolderPermissionOpt())(FileFolderPermissionOpt().withPermission)"
  preamble: "    }"
  preamble: "  }"
  preamble: "}"
  preamble: "trait AssetPermissionChangesCompanion { self: AssetPermissionChanges.type =>"
  preamble: "  val empty: AssetPermissionChanges = AssetPermissionChanges(Map(), Map(), Map())"
  preamble: "  def allFoldersWithRootChannel[K <: RadixId: DmsChannel]("
  preamble: "    channel: K,"
  preamble: "    permission: FileFolderPermission = FileFolderPermission.Own"
  preamble: "  ): AssetPermissionChanges = {"
  preamble: "    AssetPermissionChanges(Map(), Map(), Map(FolderId.channelSystemFolderId(channel) -> Some(permission)))"
  preamble: "  }"
  preamble: "  def isEmpty(changes: AssetPermissionChanges): Boolean = {"
  preamble: "    changes.folderPermissions.isEmpty &&"
  preamble: "      changes.filePermissions.isEmpty &&"
  preamble: "      changes.recursivePermissions.isEmpty"
  preamble: "  }"
  preamble: "}"
};

message FileFolderPermissionOpt {
  option (scalapb.message).type = "Option[FileFolderPermission]";
  option (scalapb.message).companion_extends = "FileFolderPermissionOptCompanion";
  oneof value {
    flow.file.FileFolderPermission permission = 1;
  }
}

message AssetPermissionChanges {
  option (scalapb.message).companion_extends = "AssetPermissionChangesCompanion";
  map<string, FileFolderPermissionOpt> folder_permissions = 1 [(scalapb.field).key_type = "FolderId"];
  map<string, FileFolderPermissionOpt> file_permissions = 2 [(scalapb.field).key_type = "FileId"];
  map<string, FileFolderPermissionOpt> recursive_permissions = 3 [(scalapb.field).key_type = "FolderId"];
}
