
syntax = "proto3";

package anduin.protobuf.fundsub.comment;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
    package_name: "anduin.protobuf.fundsub.comment"
    single_file: true
    import: "anduin.id.fundsub.FundSubLpFormIdTrait"
};

message FormQuestionData {
    string fund_sub_lp_form_id = 1 [(scalapb.field).type = "FundSubLpFormIdTrait"];
    string field_alias = 2;
    string field_description = 3;
    string toc_section = 4;
}

message AmlKycDocData {
    string doctype = 1;
}

message TopicData {
    oneof sealed_value {
        FormQuestionData form_question_data = 1;
        AmlKycDocData aml_kyc_doc_data = 2;
    }
}
