syntax = "proto3";

package anduin.dms.upload.error;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  single_file: true
  import: "anduin.workflow.flow.*"
  import: "anduin.circe.generic.semiauto.CirceCodec"
  import: "anduin.model.codec.ProtoCodecs.given"
  preamble: "sealed trait FileUploadError extends TemporalFlowEventError with TemporalFlowQueryError derives CirceCodec.WithDefaultsAndTypeName"
};

message FileUploadGeneralError {
  option (scalapb.message).extends = "FileUploadError";
  string message = 1;
}

message FileUploadInternalError {
  option (scalapb.message).extends = "FileUploadError";
  string message = 1;
}

message FileUploadExternalError {
  option (scalapb.message).extends = "FileUploadError";
  string message = 1;
}

message FileNotFoundError {
  option (scalapb.message).extends = "FileUploadError";
  string message = 1;
}

message FileTooLargeError {
  option (scalapb.message).extends = "FileUploadError";
  string message = 1;
}

message InvalidFileNameError {
  option (scalapb.message).extends = "FileUploadError";
  string message = 1;
}

message InvalidFileTypeError {
  option (scalapb.message).extends = "FileUploadError";
  string message = 1;
}

message EmptyFileError {
  option (scalapb.message).extends = "FileUploadError";
  string message = 1;
}
