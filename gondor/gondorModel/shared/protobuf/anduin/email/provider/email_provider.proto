syntax = "proto3";

package anduin.email.provider;

import "date_time.proto";
import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.email.provider"
  single_file: true
  import: "anduin.model.id.email.provider.EmailProviderId"
  import: "anduin.radix.RadixId"
  import: "anduin.model.common.user.UserId"
  import: "java.time.Instant"
};

message EmailProviderModel {
  string id = 1 [(scalapb.field).type = "EmailProviderId"];
  string name = 2;
  string from_name = 3;
  string from_address = 4;
  string host = 5;
  int32 port = 6;
  string user_name = 7;
  string encrypted_password = 8;
  bool tls = 9;
  InstantMessage created_at = 10 [(scalapb.field).type = "Instant"];
  string created_by = 11 [(scalapb.field).type = "Option[UserId]"];
}

message EmailProviderBinding {
  string binding_key = 1;
  string id = 2 [(scalapb.field).type = "EmailProviderId"];
  string resource_id = 3 [(scalapb.field).type = "RadixId"];
}

message RecordTypeUnion {
  EmailProviderModel _EmailProviderModel = 1;
  EmailProviderBinding _EmailProviderBinding = 2;
}