syntax = "proto3";

package anduin.email.sending;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.email.sending"
  single_file: true
  import: "anduin.model.id.email.sending.EmailSendingTaskId"
  import: "anduin.model.id.email.sending.EmailSendingProviderAttemptId"
  import: "anduin.model.id.email.provider.EmailProviderId"
  import: "anduin.model.id.email.GeneratedEmailId"
  import: "anduin.radix.RadixId"
};

enum EmailSendingTaskStatus {
  UNKNOWN = 0; // Dont touch this, record layer does not work well with zero value in enum
  PENDING = 1;
  SENT = 2;
  FAILED = 3;
  PROVIDER_FAILED = 4;
  PERMANENT_FAILED = 5;
}

enum EmailSendingTaskPriority {
  PRIORITY_UNKNOWN = 0;
  LOW = 1;
  MEDIUM = 2;
  HIGH = 3;
  URGENT = 4;
  THROTTLED = 5;
}

message EmailSendingTask {
  string id = 1 [(scalapb.field).type = "EmailSendingTaskId"];
  string space_id = 2 [(scalapb.field).type = "RadixId"];
  string provider_id = 3 [(scalapb.field).type = "EmailProviderId"];
  EmailSendingTaskStatus status = 4;
  EmailSendingTaskPriority priority = 5;
  int64 created_at = 6;
  int64 updated_at = 7;
  string generated_email_id = 8 [(scalapb.field).type = "GeneratedEmailId"];
  int32 provider_attempts = 9;
  int32 system_attempts = 10;
  string last_attempt_message = 11;
  string sender = 12;
  repeated string receivers = 13;
  repeated string cc_receivers = 14;
  repeated string bcc_receivers = 15;
  string subject = 16;
}

message EmailSendingProviderAttempt {
  string id = 1 [(scalapb.field).type = "EmailSendingProviderAttemptId"];
  string provider_id = 2 [(scalapb.field).type = "EmailProviderId"];
  int64 created_at = 3;
}

enum EmailSendingProviderStatusEnum {
  PROVIDER_UNKNOWN = 0;
  PROVIDER_OK = 1;
  PROVIDER_TEMPORARY_FAILED = 2;
  PROVIDER_PERMANENT_FAILED = 3;
}

message EmailSendingProviderStatus {
  string id = 1 [(scalapb.field).type = "EmailProviderId"];
  EmailSendingProviderStatusEnum status = 2;
  int32 failed_count = 3;
  int64 last_updated_at = 4;
}

// While we can use a count index on the email sending task model above, it's
// more efficient to maintain a separate model for that because the count index
// will keep the count even when the queue is empty (count = 0). So when scanning the index,
// we will have to go through all the empty queues. By maintaining a separate model, we can
// avoid that by delete the record when the queue is empty. The tradeoff is that we have to
// synchronize the two models, but that's not a big deal.
message EmailSendingQueueStat {
  string prefix = 1;
  string space_id = 2 [(scalapb.field).type = "RadixId"];
  EmailSendingTaskPriority priority = 3;
  int32 count = 4;
}

// Temporal parameters
message SendUrgentEmailParams {
  string email_sending_task_id = 1 [(scalapb.field).type = "EmailSendingTaskId"];
}

message SendUrgentEmailResponse {
}

message MarkUrgentEmailAsPermanentFailedParams {
  string email_sending_task_id = 1 [(scalapb.field).type = "EmailSendingTaskId"];
}

message MarkUrgentEmailAsPermanentFailedResponse {
}

message EmailSendingScheduleParams {
}

message EmailSendingScheduleResponse {
}

message CreateSchedulePlanParams {
}

message ScheduleBin {
  map<string, int32> space_ids = 1 [(scalapb.field).key_type = "RadixId"];
}

message SchedulePlan {
  EmailSendingTaskPriority priority = 1;
  repeated ScheduleBin bins = 2;
}

message CreateSchedulePlanResponse {
  repeated SchedulePlan plans = 1;
}

message ExecuteSchedulePlanParams {
  EmailSendingTaskPriority priority = 1;
  map<string, int32> space_ids = 2 [(scalapb.field).key_type = "RadixId"];
}

enum EmailScheduleExecutionStatus {
  SEND_UNKNOWN = 0;
  SEND_OK = 1;
  SEND_FAILED = 2;
  SEND_PROVIDER_FAILED = 3;
  SEND_THROTTLED = 4;
}

message EmailScheduleExecutionResult {
  EmailScheduleExecutionStatus status = 1;
  int32 count = 2;
}

message ExecuteSchedulePlanResponse {
  repeated EmailScheduleExecutionResult results = 1;
}

message AutoFixParams {
}

message AutoFixResponse {
}

message CollectGarbageParams {
}

message CollectGarbageResponse {
}

message ProviderAutoRescueParams {
}

message ProviderAutoRescueResponse {
}

message RecordTypeUnion {
  EmailSendingTask _EmailSendingTask = 1;
  EmailSendingProviderAttempt _EmailSendingProviderAttempt = 2;
  EmailSendingProviderStatus _EmailSendingProviderStatus = 3;
  EmailSendingQueueStat _EmailSendingQueueStat = 4;
}