syntax = "proto3";

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.ria.email"
  single_file: true
};

message RiaEmail {
  enum RiaEntityEmailType {
    RiaEntityCreated = 0;
    RiaEntityUserInvited = 1;
    RiaEntityUserInvitationRevoked = 2;
    RiaEntityUserRoleUpdated = 3;
    RiaEntityUserRemoved = 4;
  }
  enum RiaFundGroupEmailType {
    RiaFundGroupCreatedWithoutAdmin = 0;
    RiaFundGroupUserInvited = 1;
    RiaFundGroupUserInvitationRevoked = 2;
    RiaFundGroupUserRoleUpdated = 3;
    RiaFundGroupUserRemoved = 4;
    RiaFundGroupDisableCreateOrder = 5;
    RiaFundGroupLinked = 6;
    RiaFundGroupUnlinked = 7;
  }
  oneof riaEmailType {
    RiaEntityEmailType ria_entity_email_type = 1;
    RiaFundGroupEmailType ria_fund_group_email_type = 2;
  }
}

