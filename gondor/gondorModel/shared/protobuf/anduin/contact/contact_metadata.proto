syntax = "proto3";

package anduin.contact;

import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.contact.metadata"
  import: "anduin.id.fundsub.FundSubContactGroupId"
  single_file: true
  lenses: false
};

message ContactMetadata {
  oneof sealed_value {
    // Basic data
    ContactMetaBasic contact_metadata_basic = 1;
    // Other engagements definition here
    FundSubContactMetaData fs_contact_metadata = 2;
  }
}

message ContactMetaBasic {
  string message = 1;
}

message FundSubContactMetaData {
  string id = 1 [(scalapb.field).type = "FundSubContactGroupId"];
}