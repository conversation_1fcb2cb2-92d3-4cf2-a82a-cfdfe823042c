package build.gondor.gondorModel

import scala.collection.immutable.Seq

import build_.gondor.gondorModel.dependency_.*
import build_.build.util_.*
import build.platform.{stargazerId, stargazerModel}
import mill.*
import mill.scalalib.*

object `package` extends StargazerModelCrossPlatformModule {

  object jvm extends JvmModelModule with SharedModule {

    override def moduleDeps = super.moduleDeps ++ Seq(
      stargazerId.jvm,
      stargazerModel.jvm
    )

  }

  object js extends JsModelModule with SharedModule {

    override def moduleDeps = super.moduleDeps ++ Seq(
      stargazerId.js,
      stargazerModel.js
    )

  }

  trait SharedModule extends ScalaModule {
    override def mvnDeps = super.mvnDeps() ++ InboxModel.sharedDeps
    override def scalacOptions = super.scalacOptions() ++ Seq("-Xmax-inlines:128")
  }

}
