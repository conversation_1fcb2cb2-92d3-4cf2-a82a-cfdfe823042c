// Copyright (C) 2014-2025 Anduin Transactions Inc.
package build.gondor.gondorModel

import anduin.build.AnduinVersions
import mill.scalalib.*

object InboxModel {

  lazy val sharedDeps = Seq(
    mvn"com.lihaoyi::sourcecode::${AnduinVersions.sourcecode}",
    mvn"com.lihaoyi::geny::${AnduinVersions.geny}",
    mvn"com.lihaoyi::scalatags::${AnduinVersions.scalatags}"
      .exclude("org.scala-js" -> AnduinVersions.j2sjs("scalajs-dom"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("sourcecode"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("sourcecode"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("geny"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("geny")),
    // TODO: @binh remove this as it is abandoned
    mvn"com.github.japgolly.scalacss::core::${AnduinVersions.scalaCss}"
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("scalatags"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("scalatags"))
  )

}
