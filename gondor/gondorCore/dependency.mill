// Copyright (C) 2014-2025 Anduin Transactions Inc.
package build.gondor.gondorCore

import build_.build.versions_.{AnduinCopyRuntime, AnduinGaia}
import build_.build.dependency_.CommonDependencies
import anduin.build.AnduinVersions
import mill.scalalib.*

object GondorCore {

  lazy val sharedDeps = Seq(
    mvn"com.lihaoyi::geny::${AnduinVersions.geny}",
    mvn"com.lihaoyi::scalatags::${AnduinVersions.scalatags}"
      .exclude("org.scala-js" -> AnduinVersions.j2s("scalajs-dom"))
      .exclude("org.scala-js" -> AnduinVersions.j2sjs("scalajs-dom"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("sourcecode"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("sourcecode"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("geny"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2sjs("geny"))
  )

  lazy val jvmDeps = Seq(
    mvn"com.github.curious-odd-man:rgxgen:2.0",
    mvn"com.googlecode.libphonenumber:libphonenumber:${AnduinVersions.libphonenumer}",
    mvn"net.datafaker:datafaker:${AnduinVersions.datafaker}"
      .exclude("com.github.curious-odd-man" -> "rgxgen")
      .exclude("com.googlecode.libphonenumber" -> "libphonenumber")
      .exclude("org.yaml" -> "snakeyaml"),
    mvn"commons-io:commons-io:${AnduinVersions.common_io}",
    mvn"joda-time:joda-time:${AnduinVersions.jodaTime}"
      .exclude("joda-time" -> "joda-convert"),
    mvn"org.joda:joda-convert:${AnduinVersions.jodaConvert}",
    mvn"com.opengamma.strata:strata-basics:${AnduinVersions.opengamma}"
      .exclude("com.opengamma.strata" -> "strata-collect"),
    mvn"com.opengamma.strata:strata-collect:${AnduinVersions.opengamma}"
      .exclude("com.google.guava" -> "guava")
      .exclude("joda-time" -> "joda-time")
      .exclude("org.joda" -> "joda-convert"),
    // TODO: @binh update protobuf and then mysql-connector-j
    mvn"com.mysql:mysql-connector-j:8.4.0"
      .exclude("com.google.protobuf" -> "protobuf-java")
  )

  lazy val jsDeps = Seq(
    mvn"be.doeraene::url-dsl::${AnduinVersions.urlDsl}",
    mvn"com.raquo::waypoint::${AnduinVersions.waypoint}"
      .exclude("com.raquo" -> AnduinVersions.j2sjs("laminar"))
      .exclude("com.raquo" -> AnduinVersions.j2sjs("airstream"))
      .exclude("be.doeraene" -> AnduinVersions.j2sjs("url-dsl"))
      .exclude("org.scala-js" -> AnduinVersions.j2sjs("scalajs-dom"))
  )

  lazy val npmDeps = Seq(
    "react" -> AnduinVersions.npm.react,
    "react-dom" -> AnduinVersions.npm.reactDom
  )

}

object LibreOfficeServerless {

  lazy val jvmDeps = Seq(
    mvn"software.amazon.eventstream:eventstream:${AnduinVersions.eventStream}",
    mvn"com.fasterxml.jackson.core:jackson-core:${AnduinVersions.jackson}",
    mvn"com.fasterxml.jackson.core:jackson-annotations:${AnduinVersions.jacksonAnnotations}",
    mvn"com.fasterxml.jackson.core:jackson-databind:${AnduinVersions.jacksonDatabind}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-core"),
    mvn"software.amazon.awssdk.crt:aws-crt:${AnduinVersions.awsCrt}",
    mvn"com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:${AnduinVersions.jacksonCbor}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind"),
    mvn"com.fasterxml.jackson.dataformat:jackson-dataformat-toml:${AnduinVersions.jacksonCbor}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind"),
    mvn"software.amazon.awssdk:lambda:${AnduinVersions.awsSdk2Version}"
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("software.amazon.awssdk" -> "http-client-spi")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams-http")
      .exclude("software.amazon.awssdk" -> "netty-nio-client")
      .exclude("software.amazon.awssdk" -> "apache-client")
      .exclude("software.amazon.awssdk.crt" -> "aws-crt")
      .excludeOrg("com.fasterxml.jackson.core")
      .excludeName("commons-codec"),
    mvn"software.amazon.awssdk:apache-client:${AnduinVersions.awsSdk2Version}"
      .exclude("commons-codec" -> "commons-codec")
      .exclude("software.amazon.awssdk" -> "http-client-spi")
      .exclude("software.amazon.awssdk" -> "annotations")
      .exclude("software.amazon.awssdk" -> "utils")
      .exclude("software.amazon.awssdk" -> "metrics-spi")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .exclude("org.apache.httpcomponents" -> "httpclient")
      .exclude("software.amazon.awssdk.crt" -> "aws-crt"),
    mvn"software.amazon.awssdk:netty-nio-client:${AnduinVersions.awsSdk2Version}"
      .exclude("commons-codec" -> "commons-codec")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams-http")
      .exclude("software.amazon.awssdk" -> "http-client-spi")
      .exclude("software.amazon.awssdk" -> "annotations")
      .exclude("software.amazon.awssdk" -> "utils")
      .exclude("software.amazon.awssdk" -> "metrics-spi")
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("io.netty" -> "netty-all")
      .exclude("io.netty" -> "netty-codec-http")
      .exclude("io.netty" -> "netty-codec-http2")
      .exclude("io.netty" -> "netty-codec")
      .exclude("io.netty" -> "netty-transport")
      .exclude("io.netty" -> "netty-common")
      .exclude("io.netty" -> "netty-buffer")
      .exclude("io.netty" -> "netty-handler")
      .exclude("io.netty" -> "netty-resolver")
      .exclude("io.netty" -> "netty-transport-native-epoll")
      .exclude("io.netty" -> "netty-transport-classes-epoll")
      .exclude("org.reactivestreams" -> "reactive-streams")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams")
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .exclude("org.apache.httpcomponents" -> "httpclient")
      .exclude("software.amazon.awssdk.crt" -> "aws-crt"),
    mvn"software.amazon.awssdk:metrics-spi:${AnduinVersions.awsSdk2Version}"
      .exclude("software.amazon.awssdk" -> "annotations")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("org.reactivestreams" -> "reactive-streams")
      .exclude("software.amazon.awssdk.crt" -> "aws-crt"),
    mvn"software.amazon.awssdk:utils:${AnduinVersions.awsSdk2Version}"
      .exclude("software.amazon.awssdk" -> "annotations")
      .exclude("software.amazon.awssdk" -> "utils")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("software.amazon.awssdk.crt" -> "aws-crt"),
    mvn"org.reactivestreams:reactive-streams:${AnduinVersions.reactiveStream}",
    mvn"com.typesafe.netty:netty-reactive-streams:${AnduinVersions.typesafeNetty}"
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("io.netty" -> "netty-handler")
      .exclude("org.reactivestreams" -> "reactive-streams"),
    mvn"com.typesafe.netty:netty-reactive-streams-http:${AnduinVersions.typesafeNetty}"
      .exclude("io.netty" -> "netty-codec-http")
      .exclude("io.netty" -> "netty-handler")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams"),
    mvn"io.netty:netty-buffer:${AnduinVersions.netty}",
    mvn"io.netty:netty-codec-http:${AnduinVersions.netty}",
    mvn"io.netty:netty-handler:${AnduinVersions.netty}",
    mvn"io.netty:netty-handler-proxy:${AnduinVersions.netty}",
    mvn"io.netty:netty-resolver-dns:${AnduinVersions.netty}",
    mvn"io.netty:netty-transport-native-epoll:${AnduinVersions.netty}",
    mvn"io.netty:netty-transport-classes-epoll:${AnduinVersions.netty}",
    mvn"io.netty:netty-codec-http2:${AnduinVersions.netty}",
    mvn"software.amazon.awssdk:auth:${AnduinVersions.awsSdk2Version}"
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("commons-codec" -> "commons-codec")
      .exclude("software.amazon.awssdk" -> "http-client-spi")
      .exclude("org.reactivestreams" -> "reactive-streams")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams-http")
      .exclude("software.amazon.awssdk.crt" -> "aws-crt")
      .excludeOrg("com.fasterxml.jackson.core")
      .excludeName("commons-codec"),
    mvn"software.amazon.awssdk:core:${AnduinVersions.awsSdk2Version}"
      .exclude("org.slf4j" -> "slf4j-api"),
    mvn"software.amazon.awssdk:services:${AnduinVersions.awsSdk2Version}"
      .exclude("commons-codec" -> "commons-codec")
      .exclude("software.amazon.awssdk" -> "apache-client")
      .exclude("org.apache.httpcomponents" -> "httpclient")
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .exclude("software.amazon.awssdk" -> "http-client-spi")
      .exclude("software.amazon.awssdk" -> "netty-nio-client")
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("io.netty" -> "netty-all")
      .exclude("io.netty" -> "netty-codec-http")
      .exclude("io.netty" -> "netty-codec-http2")
      .exclude("io.netty" -> "netty-codec")
      .exclude("io.netty" -> "netty-transport")
      .exclude("io.netty" -> "netty-common")
      .exclude("io.netty" -> "netty-buffer")
      .exclude("io.netty" -> "netty-handler")
      .exclude("io.netty" -> "netty-transport-native-epoll")
      .exclude("io.netty" -> "netty-transport-classes-epoll")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams-http")
      .exclude("software.amazon.awssdk.crt" -> "aws-crt")
      .excludeOrg("com.fasterxml.jackson.core")
      .excludeName("commons-codec"),
    mvn"software.amazon.awssdk:http-client-spi:${AnduinVersions.awsSdk2Version}"
      .exclude("software.amazon.eventstream" -> "eventstream")
      .exclude("io.netty" -> "netty-all")
      .exclude("io.netty" -> "netty-codec-http")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("io.netty" -> "netty-handler")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("software.amazon.awssdk" -> "annotations")
      .exclude("org.reactivestreams" -> "reactive-streams")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams-http")
      .exclude("software.amazon.awssdk.crt" -> "aws-crt")
      .excludeOrg("com.fasterxml.jackson.core")
      .excludeName("commons-codec"),
    mvn"software.amazon.awssdk:annotations:${AnduinVersions.awsSdk2Version}"
      .exclude("software.amazon.eventstream" -> "eventstream")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("software.amazon.awssdk.crt" -> "aws-crt"),
    mvn"com.anduin.stargazer::loki:${AnduinGaia.version}"
      .exclude("org.yaml" -> "snakeyaml")
      .exclude("io.circe" -> AnduinVersions.j2s("circe-core"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-parser"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-generic"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
  )

}

object StorageService {

  lazy val jvmDeps = CommonDependencies.googleHttpJVM ++ Seq(
    mvn"commons-codec:commons-codec:${AnduinVersions.commonsCodec}",
    mvn"commons-io:commons-io:${AnduinVersions.common_io}",
    mvn"org.apache.httpcomponents:httpcore:${AnduinVersions.httpCore}",
    mvn"org.apache.httpcomponents:httpclient:${AnduinVersions.httpClient}"
      .exclude("commons-codec" -> "commons-codec")
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .exclude("commons-logging" -> "commons-logging")
      .exclude("commons-configuration" -> "commons-configuration"),
    mvn"software.amazon.awssdk.crt:aws-crt:${AnduinVersions.awsCrt}",
    mvn"software.amazon.awssdk:auth:${AnduinVersions.awsSdk2Version}"
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("software.amazon.awssdk" -> "http-client-spi")
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind")
      .exclude("org.reactivestreams" -> "reactive-streams")
      .exclude("software.amazon.awssdk.crt" -> "aws-crt"),
    mvn"software.amazon.awssdk:s3:${AnduinVersions.awsSdk2Version}"
      .exclude("software.amazon.awssdk" -> "apache-client")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind")
      .exclude("com.fasterxml.jackson.dataformat" -> "jackson-dataformat-cbor")
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .exclude("org.apache.httpcomponents" -> "httpclient")
      .excludeOrg("io.netty")
      .exclude("org.reactivestreams" -> "reactive-streams")
      .exclude("io.netty" -> "netty-all")
      .exclude("software.amazon.awssdk" -> "http-client-spi")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams-http")
      .exclude("software.amazon.awssdk.crt" -> "aws-crt"),
    mvn"software.amazon.awssdk:textract:${AnduinVersions.awsSdk2Version}"
      .exclude("software.amazon.awssdk" -> "apache-client")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind")
      .exclude("com.fasterxml.jackson.dataformat" -> "jackson-dataformat-cbor")
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .exclude("org.apache.httpcomponents" -> "httpclient")
      .excludeOrg("io.netty")
      .exclude("org.reactivestreams" -> "reactive-streams")
      .exclude("io.netty" -> "netty-all")
      .exclude("software.amazon.awssdk" -> "http-client-spi")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams-http")
      .exclude("software.amazon.awssdk.crt" -> "aws-crt"),
    mvn"software.amazon.awssdk:sts:${AnduinVersions.awsSdk2Version}"
      .exclude("software.amazon.awssdk" -> "apache-client")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind")
      .exclude("com.fasterxml.jackson.dataformat" -> "jackson-dataformat-cbor")
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .exclude("org.apache.httpcomponents" -> "httpclient")
      .excludeOrg("io.netty")
      .exclude("org.reactivestreams" -> "reactive-streams")
      .exclude("io.netty" -> "netty-all")
      .exclude("software.amazon.awssdk" -> "http-client-spi")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams-http")
      .exclude("software.amazon.awssdk.crt" -> "aws-crt"),
    mvn"software.amazon.awssdk:cloudfront:${AnduinVersions.awsSdk2Version}"
      .exclude("software.amazon.awssdk" -> "apache-client")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind")
      .exclude("com.fasterxml.jackson.dataformat" -> "jackson-dataformat-cbor")
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .exclude("org.apache.httpcomponents" -> "httpclient")
      .excludeOrg("io.netty")
      .exclude("org.reactivestreams" -> "reactive-streams")
      .exclude("io.netty" -> "netty-all")
      .exclude("software.amazon.awssdk" -> "http-client-spi")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams-http")
      .exclude("software.amazon.awssdk.crt" -> "aws-crt"),
    mvn"software.amazon.awssdk:http-client-spi:${AnduinVersions.awsSdk2Version}"
      .exclude("software.amazon.eventstream" -> "eventstream")
      .exclude("io.netty" -> "netty-all")
      .exclude("io.netty" -> "netty-codec-http")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("software.amazon.awssdk" -> "annotations")
      .exclude("org.reactivestreams" -> "reactive-streams")
      .exclude("software.amazon.awssdk.crt" -> "aws-crt"),
    mvn"dev.zio::zio-aws-core:${AnduinVersions.zioAws}"
      .exclude("software.amazon.awssdk" -> "aws-core")
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-interop-reactivestreams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-prelude"))
      .exclude("software.amazon.awssdk.crt" -> "aws-crt"),
    mvn"dev.zio::zio-aws-textract:${AnduinVersions.zioAws}"
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-mock"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio-mock"))
      .exclude("software.amazon.awssdk" -> "textract")
      .exclude("software.amazon.awssdk" -> "aws-core")
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-aws-core"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-prelude"))
      .exclude("software.amazon.awssdk.crt" -> "aws-crt"),
    mvn"dev.zio::zio-aws-netty:${AnduinVersions.zioAws}"
      .exclude("software.amazon.awssdk" -> "netty-nio-client")
      .exclude("software.amazon.awssdk" -> "aws-core")
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-aws-core"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-prelude"))
      .exclude("software.amazon.awssdk.crt" -> "aws-crt"),
    mvn"javax.annotation:javax.annotation-api:${AnduinVersions.javaxAnnotation}",
    mvn"io.grpc:grpc-context:${AnduinVersions.grpc}",
    mvn"io.opencensus:opencensus-api:${AnduinVersions.opencensus}"
      .exclude("io.grpc" -> "grpc-context"),
    mvn"io.opencensus:opencensus-contrib-http-util:${AnduinVersions.opencensus}"
      .exclude("io.opencensus" -> "opencensus-api")
      .exclude("com.google.guava" -> "guava"),
    mvn"com.google.api.grpc:proto-google-common-protos:${AnduinVersions.protoCommon}"
      .exclude("com.google.protobuf" -> "protobuf-java"),
    mvn"com.google.auto.value:auto-value-annotations:${AnduinVersions.autoValueAnnotations}",
    mvn"com.google.code.gson:gson:${AnduinVersions.gson}"
      .exclude("com.google.errorprone" -> "error_prone_annotations"),
    mvn"com.google.http-client:google-http-client-gson:${AnduinVersions.googleJackson}"
      .exclude("io.grpc" -> "grpc-context")
      .exclude("com.google.code.gson" -> "gson")
      .exclude("com.google.j2objc" -> "j2objc-annotations")
      .exclude("com.google.http-client" -> "google-http-client"),
    mvn"com.google.http-client:google-http-client-apache-v2:${AnduinVersions.googleJackson}"
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .exclude("org.apache.httpcomponents" -> "httpclient")
      .exclude("com.google.http-client" -> "google-http-client"),
    mvn"com.google.http-client:google-http-client-jackson2:${AnduinVersions.googleJackson}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("com.google.j2objc" -> "j2objc-annotations")
      .exclude("com.google.http-client" -> "google-http-client"),
    mvn"com.google.http-client:google-http-client:${AnduinVersions.googleJackson}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("com.google.j2objc" -> "j2objc-annotations")
      .exclude("com.google.errorprone" -> "error_prone_annotations")
      .exclude("io.opencensus" -> "opencensus-api")
      .exclude("io.opencensus" -> "opencensus-contrib-http-util")
      .exclude("com.google.guava" -> "guava")
      .exclude("org.apache.httpcomponents" -> "httpclient")
      .exclude("io.grpc" -> "grpc-context")
      .exclude("org.apache.httpcomponents" -> "httpcore"),
    mvn"com.fasterxml.jackson.core:jackson-core:${AnduinVersions.jackson}",
    mvn"com.fasterxml.jackson.core:jackson-annotations:${AnduinVersions.jacksonAnnotations}",
    mvn"com.fasterxml.jackson.core:jackson-databind:${AnduinVersions.jacksonDatabind}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations"),
    mvn"com.google.oauth-client:google-oauth-client:${AnduinVersions.googleOAuthClient1}"
      .exclude("com.google.guava" -> "guava")
      .exclude("com.google.http-client" -> "google-http-client-gson")
      .exclude("com.google.http-client" -> "google-http-client"),
    mvn"com.google.api-client:google-api-client:${AnduinVersions.googleApiClient}"
      .exclude("com.google.auth" -> "google-auth-library-oauth2-http")
      .exclude("com.google.auth" -> "google-auth-library-credentials")
      .exclude("com.google.guava" -> "guava")
      .exclude("commons-codec" -> "commons-codec")
      .exclude("com.google.oauth-client" -> "google-oauth-client")
      .exclude("com.google.http-client" -> "google-http-client-gson")
      .exclude("com.google.http-client" -> "google-http-client-apache-v2")
      .exclude("com.google.http-client" -> "google-http-client")
      .exclude("org.apache.httpcomponents" -> "httpclient")
      .exclude("org.apache.httpcomponents" -> "httpcore"),
    mvn"com.google.http-client:google-http-client-apache-v2:${AnduinVersions.googleJackson}"
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .exclude("org.apache.httpcomponents" -> "httpclient")
      .exclude("com.google.http-client" -> "google-http-client"),
    mvn"com.google.auth:google-auth-library-oauth2-http:${AnduinVersions.googleOauthClient}"
      .exclude("com.google.errorprone" -> "error_prone_annotations")
      .exclude("com.google.auto.value" -> "auto-value-annotations")
      .exclude("com.google.code.findbugs" -> "jsr305")
      .exclude("com.google.guava" -> "guava")
      .exclude("com.google.code.gson" -> "gson")
      .exclude("com.google.http-client" -> "google-http-client-gson")
      .exclude("com.google.http-client" -> "google-http-client"),
    mvn"com.google.apis:google-api-services-drive:${AnduinVersions.gdrive}"
      .exclude("com.google.api-client" -> "google-api-client"),
    mvn"org.bitbucket.b_c:jose4j:${AnduinVersions.jose4j}"
      .exclude("org.slf4j" -> "slf4j-api"),
    mvn"com.box:box-java-sdk:${AnduinVersions.boxSdk}"
      .exclude("org.bitbucket.b_c" -> "jose4j")
      .exclude("org.bouncycastle" -> "bcmail-jdk18on")
      .exclude("org.bouncycastle" -> "bcprov-jdk18on")
      .exclude("org.bouncycastle" -> "bcpkix-jdk18on")
      .exclude("com.github.luben" -> "zstd-jni")
      .exclude("com.squareup.okhttp3" -> "okhttp")
      .exclude("org.slf4j" -> "slf4j-api"),
    mvn"com.dropbox.core:dropbox-core-sdk:${AnduinVersions.dropbox}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-core"),
    mvn"software.amazon.awssdk:cloudfront:${AnduinVersions.awsSdk2Version}"
      .exclude("software.amazon.awssdk" -> "apache-client")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind")
      .exclude("com.fasterxml.jackson.dataformat" -> "jackson-dataformat-cbor")
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .exclude("org.apache.httpcomponents" -> "httpclient")
      .excludeOrg("io.netty")
      .exclude("org.reactivestreams" -> "reactive-streams")
      .exclude("io.netty" -> "netty-all")
      .exclude("software.amazon.awssdk" -> "http-client-spi")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams-http")
      .exclude("software.amazon.awssdk.crt" -> "aws-crt"),
    mvn"software.amazon.awssdk:dynamodb:${AnduinVersions.awsSdk2Version}"
      .exclude("software.amazon.awssdk" -> "apache-client")
      .exclude("software.amazon.awssdk" -> "http-client-spi")
      .exclude("software.amazon.eventstream" -> "eventstream")
      .exclude("software.amazon" -> "flow")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind")
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("org.apache.httpcomponents" -> "httpclient")
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .excludeOrg("io.netty")
      .exclude("io.netty" -> "netty-all")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams-http"),
    mvn"software.amazon.awssdk:cloudwatchlogs:${AnduinVersions.awsSdk2Version}"
      .exclude("software.amazon.awssdk" -> "apache-client")
      .exclude("software.amazon.awssdk" -> "http-client-spi")
      .exclude("software.amazon.eventstream" -> "eventstream")
      .exclude("software.amazon" -> "flow")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind")
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("org.apache.httpcomponents" -> "httpclient")
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .excludeOrg("io.netty")
      .exclude("io.netty" -> "netty-all")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams-http")
  )

}

object DocumentContent {

  lazy val jvmDeps = Seq(
    mvn"org.apache.commons:commons-compress:${AnduinVersions.commonCompress}"
      .exclude("commons-codec" -> "commons-codec")
      .exclude("org.apache.commons" -> "commons-lang3")
      .exclude("commons-io" -> "commons-io"),
    mvn"org.apache.httpcomponents:httpcore:${AnduinVersions.httpCore}",
    mvn"org.apache.httpcomponents:httpclient:${AnduinVersions.httpClient}"
      .exclude("commons-codec" -> "commons-codec")
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .exclude("commons-logging" -> "commons-logging")
      .exclude("commons-configuration" -> "commons-configuration"),
    mvn"com.google.code.gson:gson:${AnduinVersions.gson}"
      .exclude("com.google.errorprone" -> "error_prone_annotations"),
    mvn"com.sun.activation:jakarta.activation:2.0.1",
    mvn"org.slf4j:jcl-over-slf4j:${AnduinVersions.slf4j_api}"
      .exclude("org.slf4j" -> "slf4j-api"),
    mvn"org.apache.commons:commons-math3:${AnduinVersions.commonMath}",
    mvn"org.apache.poi:poi:${AnduinVersions.poi}"
      .exclude("org.apache.logging.log4j" -> "log4j-api")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("org.slf4j" -> "jcl-over-slf4j")
      .exclude("org.apache.commons" -> "commons-collections4")
      .exclude("commons-codec" -> "commons-codec")
      .exclude("commons-io" -> "commons-io")
      .exclude("org.apache.commons" -> "commons-math3"),
    mvn"org.apache.xmlgraphics:batik-all:${AnduinVersions.batik_codec}"
      .exclude("commons-logging" -> "commons-logging")
      .exclude("commons-configuration" -> "commons-configuration")
      .exclude("commons-io" -> "commons-io")
      .exclude("javax.ws.rs" -> "javax.ws.rs-api")
      .exclude("xml-apis" -> "xml-apis")
      .exclude("org.apache.xmlgraphics" -> "xmlgraphics-commons"),
    mvn"org.apache.santuario:xmlsec:${AnduinVersions.xmlsec}"
      .exclude("jakarta.xml.bind" -> "jakarta.xml.bind-api")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("commons-codec" -> "commons-codec")
      .exclude("com.fasterxml.woodstox" -> "woodstox-core"),
    mvn"com.fasterxml.woodstox:woodstox-core:${AnduinVersions.woodstockCore}",
    mvn"com.beust:jcommander:${AnduinVersions.jcommander}",
    mvn"jakarta.xml.bind:jakarta.xml.bind-api:${AnduinVersions.jakartaXmlBind}"
      .exclude("com.sun.activation" -> "jakarta.activation"),
    mvn"org.apache.logging.log4j:log4j-api:${AnduinVersions.log4j}",
    mvn"org.apache.poi:poi-ooxml:${AnduinVersions.poi_ooxml}"
      .exclude("xml-apis" -> "xml-apis")
      .exclude("javax.ws.rs" -> "javax.ws.rs-api")
      .exclude("jakarta.xml.bind" -> "jakarta.xml.bind-api")
      .exclude("org.apache.poi" -> "poi")
      .exclude("commons-io" -> "commons-io")
      .exclude("com.fasterxml.woodstox" -> "woodstox-core")
      .exclude("org.apache.commons" -> "commons-compress")
      .exclude("org.apache.xmlgraphics" -> "batik-all")
      .exclude("org.apache.pdfbox" -> "pdfbox")
      .exclude("org.bouncycastle" -> "bcmail-jdk18on")
      .exclude("org.bouncycastle" -> "bcprov-jdk18on")
      .exclude("org.bouncycastle" -> "bcpkix-jdk18on")
      .exclude("commons-codec" -> "commons-codec")
      .exclude("org.apache.commons" -> "commons-collections4")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("org.apache.santuario" -> "xmlsec")
      .exclude("org.apache.logging.log4j" -> "log4j-api"),
    mvn"org.apache.poi:poi-ooxml-full:${AnduinVersions.poi_ooxml}"
      .exclude("org.apache.logging.log4j" -> "log4j-api"),
    mvn"org.apache.poi:poi-scratchpad:${AnduinVersions.poi}"
      .exclude("org.apache.logging.log4j" -> "log4j-api")
      .exclude("commons-codec" -> "commons-codec")
      .exclude("commons-io" -> "commons-io")
      .exclude("org.apache.poi" -> "poi"),
    mvn"org.apache.pdfbox:pdfbox:${AnduinVersions.pdfbox}"
      .exclude("commons-logging" -> "commons-logging")
      .exclude("xml-apis" -> "xml-apis")
      .exclude("javax.ws.rs" -> "javax.ws.rs-api")
      .exclude("org.apache.poi" -> "poi")
      .exclude("org.apache.poi" -> "poi-ooxml"),
    mvn"commons-io:commons-io:${AnduinVersions.common_io}",
    mvn"org.apache.commons:commons-lang3:${AnduinVersions.apacheCommonLang}",
    mvn"com.drewnoakes:metadata-extractor:${AnduinVersions.metaDataExtractor}",
    mvn"com.github.tototoshi::scala-csv:${AnduinVersions.scalaCsv}",
    mvn"dev.zio::zio-aws-core:${AnduinVersions.zioAws}"
      .exclude("software.amazon.awssdk" -> "aws-core")
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-interop-reactivestreams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-prelude")),
    mvn"dev.zio::zio-aws-sqs:${AnduinVersions.zioAws}"
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-mock"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio-mock"))
      .exclude("software.amazon.awssdk" -> "sqs")
      .exclude("software.amazon.awssdk" -> "aws-core")
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-aws-core"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-prelude"))
      .exclude("software.amazon.awssdk.crt" -> "aws-crt"),
    mvn"dev.zio::zio-sqs:${AnduinVersions.zioSQS}"
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-aws-sqs"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-aws-netty"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-aws-core"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-prelude")),
    mvn"software.amazon.awssdk:sqs:${AnduinVersions.awsSdk2Version}"
      .exclude("software.amazon.awssdk" -> "apache-client")
      .exclude("software.amazon.awssdk" -> "http-client-spi")
      .exclude("software.amazon.eventstream" -> "eventstream")
      .exclude("software.amazon" -> "flow")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind")
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("org.apache.httpcomponents" -> "httpclient")
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .excludeOrg("io.netty")
      .exclude("io.netty" -> "netty-all")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams-http")
      .exclude("software.amazon.awssdk.crt" -> "aws-crt")
  )

}

object Dms {

  lazy val jvmDeps = Seq(
    mvn"org.apache.httpcomponents:httpcore:${AnduinVersions.httpCore}",
    mvn"org.apache.httpcomponents:httpclient:${AnduinVersions.httpClient}"
      .exclude("commons-codec" -> "commons-codec")
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .exclude("commons-logging" -> "commons-logging")
      .exclude("commons-configuration" -> "commons-configuration"),
    mvn"com.fasterxml.jackson.core:jackson-databind:${AnduinVersions.jacksonDatabind}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations"),
    mvn"com.fasterxml.jackson.core:jackson-annotations:${AnduinVersions.jacksonAnnotations}",
    mvn"com.fasterxml.jackson.dataformat:jackson-dataformat-cbor:${AnduinVersions.jacksonCbor}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind"),
    mvn"joda-time:joda-time:${AnduinVersions.jodaTime}",
    mvn"org.scala-lang.modules::scala-xml:${AnduinVersions.scala_xml}",
    mvn"software.amazon.awssdk:auth:${AnduinVersions.awsSdk2Version}"
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("software.amazon.awssdk" -> "http-client-spi")
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind")
      .exclude("org.reactivestreams" -> "reactive-streams")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams")
      .exclude("software.amazon.awssdk" -> "netty-nio-client")
      .exclude("software.amazon.awssdk" -> "apache-client"),
    mvn"software.amazon.awssdk:apache-client:${AnduinVersions.awsSdk2Version}"
      .exclude("commons-codec" -> "commons-codec")
      .exclude("software.amazon.awssdk" -> "http-client-spi")
      .exclude("software.amazon.awssdk" -> "annotations")
      .exclude("software.amazon.awssdk" -> "utils")
      .exclude("software.amazon.awssdk" -> "metrics-spi")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .exclude("org.apache.httpcomponents" -> "httpclient"),
    mvn"software.amazon.awssdk:netty-nio-client:${AnduinVersions.awsSdk2Version}"
      .exclude("com.typesafe.netty" -> "netty-reactive-streams-http")
      .exclude("software.amazon.awssdk" -> "http-client-spi")
      .exclude("software.amazon.awssdk" -> "annotations")
      .exclude("software.amazon.awssdk" -> "utils")
      .exclude("software.amazon.awssdk" -> "metrics-spi")
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("io.netty" -> "netty-all")
      .exclude("io.netty" -> "netty-codec-http")
      .exclude("io.netty" -> "netty-codec-http2")
      .exclude("io.netty" -> "netty-codec")
      .exclude("io.netty" -> "netty-transport")
      .exclude("io.netty" -> "netty-common")
      .exclude("io.netty" -> "netty-buffer")
      .exclude("io.netty" -> "netty-handler")
      .exclude("io.netty" -> "netty-resolver")
      .exclude("io.netty" -> "netty-transport-native-epoll")
      .exclude("io.netty" -> "netty-transport-classes-epoll")
      .exclude("org.reactivestreams" -> "reactive-streams")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams")
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .exclude("org.apache.httpcomponents" -> "httpclient"),
    mvn"software.amazon.awssdk:metrics-spi:${AnduinVersions.awsSdk2Version}"
      .exclude("software.amazon.awssdk" -> "annotations")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("org.reactivestreams" -> "reactive-streams"),
    mvn"software.amazon.awssdk:utils:${AnduinVersions.awsSdk2Version}"
      .exclude("software.amazon.awssdk" -> "annotations")
      .exclude("software.amazon.awssdk" -> "utils")
      .exclude("org.slf4j" -> "slf4j-api"),
    mvn"org.reactivestreams:reactive-streams:${AnduinVersions.reactiveStream}",
    mvn"com.typesafe.netty:netty-reactive-streams:${AnduinVersions.typesafeNetty}"
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("io.netty" -> "netty-handler")
      .exclude("org.reactivestreams" -> "reactive-streams"),
    mvn"com.typesafe.netty:netty-reactive-streams-http:${AnduinVersions.typesafeNetty}"
      .exclude("io.netty" -> "netty-codec-http")
      .exclude("io.netty" -> "netty-handler")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams"),
    mvn"io.netty:netty-buffer:${AnduinVersions.netty}",
    mvn"io.netty:netty-codec-http:${AnduinVersions.netty}",
    mvn"io.netty:netty-handler:${AnduinVersions.netty}",
    mvn"io.netty:netty-handler-proxy:${AnduinVersions.netty}",
    mvn"io.netty:netty-resolver-dns:${AnduinVersions.netty}",
    mvn"io.netty:netty-transport-native-epoll:${AnduinVersions.netty}",
    mvn"io.netty:netty-transport-classes-epoll:${AnduinVersions.netty}",
    mvn"io.netty:netty-codec-http2:${AnduinVersions.netty}",
    mvn"software.amazon.awssdk:http-client-spi:${AnduinVersions.awsSdk2Version}"
      .exclude("software.amazon.eventstream" -> "eventstream")
      .exclude("io.netty" -> "netty-all")
      .exclude("io.netty" -> "netty-codec-http")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("software.amazon.awssdk" -> "annotations")
      .exclude("org.reactivestreams" -> "reactive-streams"),
    mvn"software.amazon.awssdk:annotations:${AnduinVersions.awsSdk2Version}"
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("software.amazon.eventstream" -> "eventstream"),
    mvn"software.amazon:flow:${AnduinVersions.awsFlow}",
    mvn"commons-io:commons-io:${AnduinVersions.common_io}",
    mvn"xml-apis:xml-apis:${AnduinVersions.xml_apis}",
    mvn"javax.ws.rs:javax.ws.rs-api:${AnduinVersions.javaxws}",
    // Dropbox
    mvn"com.dropbox.core:dropbox-core-sdk:${AnduinVersions.dropbox}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-core"),
    // better_files
    mvn"com.github.pathikrit::better-files:${AnduinVersions.better_files}",
    mvn"net.java.dev.jna:jna:${AnduinVersions.jna}",
    mvn"com.levigo.jbig2:levigo-jbig2-imageio:${AnduinVersions.jbig2}",
    mvn"com.github.jai-imageio:jai-imageio-core:${AnduinVersions.jaiImageIO}",
    mvn"com.github.jai-imageio:jai-imageio-jpeg2000:${AnduinVersions.jaiJpeg2k}"
      .exclude("com.github.jai-imageio" -> "jai-imageio-core"),
    mvn"commons-io:commons-io:${AnduinVersions.common_io}",
    mvn"commons-lang:commons-lang:${AnduinVersions.common_lang}",
    mvn"org.apache.commons:commons-lang3:${AnduinVersions.apacheCommonLang}",
    mvn"commons-configuration:commons-configuration:${AnduinVersions.apacheCommonConfig}"
      .exclude("commons-logging" -> "commons-logging"),
    mvn"commons-logging:commons-logging:${AnduinVersions.commonsLogging}",
    // batik for manipulating SVG files
    mvn"org.apache.xmlgraphics:batik-transcoder:${AnduinVersions.batik_transcoder}"
      .exclude("commons-logging" -> "commons-logging")
      .exclude("commons-configuration" -> "commons-configuration")
      .exclude("commons-io" -> "commons-io")
      .exclude("xml-apis" -> "xml-apis")
      .exclude("javax.ws.rs" -> "javax.ws.rs-api")
      .exclude("org.apache.xmlgraphics" -> "xmlgraphics-commons"),
    mvn"org.apache.xmlgraphics:batik-codec:${AnduinVersions.batik_codec}"
      .exclude("commons-logging" -> "commons-logging")
      .exclude("commons-io" -> "commons-io")
      .exclude("xml-apis" -> "xml-apis")
      .exclude("javax.ws.rs" -> "javax.ws.rs-api")
      .exclude("org.apache.xmlgraphics" -> "xmlgraphics-commons"),
    mvn"org.apache.xmlgraphics:fop:${AnduinVersions.fop}"
      .exclude("org.apache.ant" -> "ant")
      .exclude("commons-logging" -> "commons-logging")
      .exclude("commons-configuration" -> "commons-configuration")
      .exclude("commons-io" -> "commons-io")
      .exclude("javax.ws.rs" -> "javax.ws.rs-api")
      .exclude("xml-apis" -> "xml-apis")
      .exclude("org.apache.xmlgraphics" -> "batik-svg-dom")
      .exclude("org.apache.xmlgraphics" -> "batik-anim")
      .exclude("org.apache.xmlgraphics" -> "batik-bridge")
      .exclude("org.apache.xmlgraphics" -> "batik-awt-util")
      .exclude("org.apache.xmlgraphics" -> "batik-gvt")
      .exclude("org.apache.xmlgraphics" -> "batik-transcoder")
      .exclude("org.apache.xmlgraphics" -> "batik-extension")
      .exclude("org.apache.xmlgraphics" -> "batik-ext")
      .exclude("org.apache.xmlgraphics" -> "xmlgraphics-commons")
      .exclude("org.apache.pdfbox" -> "fontbox"),
    mvn"org.apache.xmlgraphics:xmlgraphics-commons:${AnduinVersions.xmlgraphics_commons}"
      .exclude("commons-logging" -> "commons-logging")
      .exclude("commons-configuration" -> "commons-configuration")
      .exclude("commons-io" -> "commons-io")
      .exclude("javax.ws.rs" -> "javax.ws.rs-api")
      .exclude("xml-apis" -> "xml-apis"),
    // scala-arm
    mvn"org.scodec::scodec-bits:${AnduinVersions.scodecBits}"
      .exclude("com.chuusai" -> AnduinVersions.j2sjs("shapeless"))
      .exclude("com.chuusai" -> AnduinVersions.j2s("shapeless")),
    mvn"org.bitbucket.b_c:jose4j:${AnduinVersions.jose4j}"
      .exclude("org.slf4j" -> "slf4j-api"),
    mvn"org.scala-lang.modules::scala-parser-combinators:${AnduinVersions.scala_parser}"
  )

}

object Notification {

  lazy val sharedDeps = Seq(
    mvn"org.scodec::scodec-bits::${AnduinVersions.scodecBits}"
      .excludeOrg("com.chuusai")
  )

}

object Mailer {

  lazy val sharedDeps = Seq(
    mvn"org.scodec::scodec-bits::${AnduinVersions.scodecBits}"
      .excludeOrg("com.chuusai")
  )

  lazy val jvmDeps = Seq(
    // logging
    mvn"org.jsoup:jsoup:${AnduinVersions.jsoup}",
    mvn"com.sun.mail:jakarta.mail:${AnduinVersions.javamail}",
    mvn"com.sun.mail:dsn:${AnduinVersions.javamail}",
    mvn"software.amazon.eventstream:eventstream:${AnduinVersions.eventStream}",
    mvn"com.fasterxml.jackson.core:jackson-core:${AnduinVersions.jackson}",
    mvn"com.fasterxml.jackson.core:jackson-annotations:${AnduinVersions.jacksonAnnotations}",
    mvn"com.fasterxml.jackson.core:jackson-databind:${AnduinVersions.jacksonDatabind}"
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-core"),
    mvn"software.amazon.awssdk.crt:aws-crt:${AnduinVersions.awsCrt}",
    mvn"dev.zio::zio-aws-core:${AnduinVersions.zioAws}"
      .exclude("software.amazon.awssdk" -> "aws-core")
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-interop-reactivestreams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-prelude")),
    mvn"dev.zio::zio-aws-sqs:${AnduinVersions.zioAws}"
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-mock"))
      .exclude("dev.zio" -> AnduinVersions.j2sjs("zio-mock"))
      .exclude("software.amazon.awssdk" -> "sqs")
      .exclude("software.amazon.awssdk" -> "aws-core")
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-aws-core"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-prelude"))
      .exclude("software.amazon.awssdk.crt" -> "aws-crt"),
    mvn"dev.zio::zio-aws-netty:${AnduinVersions.zioAws}"
      .exclude("software.amazon.awssdk" -> "netty-nio-client")
      .exclude("software.amazon.awssdk" -> "aws-core")
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-aws-core"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-prelude"))
      .exclude("software.amazon.awssdk.crt" -> "aws-crt"),
    mvn"dev.zio::zio-sqs:${AnduinVersions.zioSQS}"
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-aws-sqs"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-aws-netty"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-aws-core"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-streams"))
      .exclude("dev.zio" -> AnduinVersions.j2s("zio-prelude")),
    mvn"software.amazon.awssdk:ses:${AnduinVersions.awsSdk2Version}"
      .exclude("software.amazon.awssdk" -> "apache-client")
      .exclude("software.amazon.awssdk" -> "http-client-spi")
      .exclude("software.amazon.eventstream" -> "eventstream")
      .exclude("software.amazon" -> "flow")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind")
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("org.apache.httpcomponents" -> "httpclient")
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .excludeOrg("io.netty")
      .exclude("io.netty" -> "netty-all")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams-http")
      .exclude("software.amazon.awssdk.crt" -> "aws-crt"),
    mvn"software.amazon.awssdk:sesv2:${AnduinVersions.awsSdk2Version}"
      .exclude("software.amazon.awssdk" -> "apache-client")
      .exclude("software.amazon.awssdk" -> "http-client-spi")
      .exclude("software.amazon.eventstream" -> "eventstream")
      .exclude("software.amazon" -> "flow")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind")
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("org.apache.httpcomponents" -> "httpclient")
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .excludeOrg("io.netty")
      .exclude("io.netty" -> "netty-all")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams-http")
      .exclude("software.amazon.awssdk.crt" -> "aws-crt"),
    mvn"software.amazon.awssdk:sqs:${AnduinVersions.awsSdk2Version}"
      .exclude("software.amazon.awssdk" -> "apache-client")
      .exclude("software.amazon.awssdk" -> "http-client-spi")
      .exclude("software.amazon.eventstream" -> "eventstream")
      .exclude("software.amazon" -> "flow")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("com.fasterxml.jackson.core" -> "jackson-annotations")
      .exclude("com.fasterxml.jackson.core" -> "jackson-databind")
      .exclude("com.fasterxml.jackson.core" -> "jackson-core")
      .exclude("org.apache.httpcomponents" -> "httpclient")
      .exclude("org.apache.httpcomponents" -> "httpcore")
      .excludeOrg("io.netty")
      .exclude("io.netty" -> "netty-all")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams-http")
      .exclude("software.amazon.awssdk.crt" -> "aws-crt"),
    mvn"org.reactivestreams:reactive-streams:${AnduinVersions.reactiveStream}",
    mvn"com.typesafe.netty:netty-reactive-streams:${AnduinVersions.typesafeNetty}"
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("io.netty" -> "netty-handler")
      .exclude("org.reactivestreams" -> "reactive-streams"),
    mvn"com.typesafe.netty:netty-reactive-streams-http:${AnduinVersions.typesafeNetty}"
      .exclude("io.netty" -> "netty-codec-http")
      .exclude("io.netty" -> "netty-handler")
      .exclude("com.typesafe.netty" -> "netty-reactive-streams"),
    mvn"io.netty:netty-buffer:${AnduinVersions.netty}",
    mvn"io.netty:netty-codec-http:${AnduinVersions.netty}",
    mvn"io.netty:netty-handler:${AnduinVersions.netty}",
    mvn"io.netty:netty-handler-proxy:${AnduinVersions.netty}",
    mvn"io.netty:netty-resolver-dns:${AnduinVersions.netty}",
    mvn"io.netty:netty-transport-native-epoll:${AnduinVersions.netty}",
    mvn"io.netty:netty-codec-http2:${AnduinVersions.netty}",
    mvn"software.amazon.awssdk:http-client-spi:${AnduinVersions.awsSdk2Version}"
      .exclude("software.amazon.eventstream" -> "eventstream")
      .exclude("io.netty" -> "netty-codec-http")
      .exclude("org.slf4j" -> "slf4j-api")
      .exclude("software.amazon.awssdk" -> "annotations"),
    mvn"software.amazon.awssdk:annotations:${AnduinVersions.awsSdk2Version}"
      .exclude("software.amazon.eventstream" -> "eventstream")
      .exclude("org.slf4j" -> "slf4j-api"),
    mvn"software.amazon:flow:${AnduinVersions.awsFlow}",
    mvn"com.googlecode.owasp-java-html-sanitizer:owasp-java-html-sanitizer:${AnduinVersions.javaHtmlSanitizer}"
      .exclude("com.google.guava" -> "guava")
  )

}

object ActionLogger {

  lazy val jvmDeps = Seq(
    mvn"io.netty:netty-common:${AnduinVersions.netty}"
  )

}

object Inbox {

  lazy val sharedDeps = Seq(
    mvn"org.scodec::scodec-bits::${AnduinVersions.scodecBits}"
      .exclude("com.chuusai" -> AnduinVersions.j2sjs("shapeless"))
      .exclude("com.chuusai" -> AnduinVersions.j2s("shapeless"))
  )

}

object IssueTracker {

  lazy val jvmDeps = Seq(
    mvn"org.apache.commons:commons-text:${AnduinVersions.commonsText}"
      .exclude("org.apache.commons" -> "commons-lang3"),
    mvn"org.apache.commons:commons-lang3:${AnduinVersions.apacheCommonLang}",
    mvn"joda-time:joda-time:${AnduinVersions.jodaTime}"
      .exclude("joda-time" -> "joda-convert"),
    mvn"org.joda:joda-convert:${AnduinVersions.jodaConvert}",
    mvn"com.github.nscala-time::nscala-time:${AnduinVersions.nScalaTime}"
      .exclude("joda-time" -> "joda-time")
      .exclude("org.joda" -> "joda-convert"),
    mvn"net.ruippeixotog::scala-scraper:${AnduinVersions.scalaScrapper}"
      .exclude("com.github.nscala-time" -> AnduinVersions.j2s("nscala-time"))
      .exclude("org.apache.commons" -> "commons-lang3")
      .exclude("org.apache.commons" -> "commons-text")
      .exclude("commons-logging" -> "commons-logging")
      .exclude("org.apache.httpcomponents" -> "httpclient")
      .exclude("org.apache.httpcomponents" -> "httpmime")
      .exclude("commons-io" -> "commons-io")
      .exclude("org.jsoup" -> "jsoup")
      .exclude("commons-codec" -> "commons-codec")
  )

}

object Copy {

  lazy val sharedDeps = Seq(
    mvn"com.anduin::anduin-copy-runtime::${AnduinCopyRuntime.version}"
      .exclude("io.circe" -> AnduinVersions.j2s("circe-core"))
      .exclude("io.circe" -> AnduinVersions.j2sjs("circe-core"))
      .exclude("io.circe" -> AnduinVersions.j2s("circe-generic"))
      .exclude("io.circe" -> AnduinVersions.j2sjs("circe-generic"))
      .exclude("org.typelevel" -> AnduinVersions.j2sjs("cats-core"))
      .exclude("org.typelevel" -> AnduinVersions.j2s("cats-core"))
  )

  lazy val jvmDeps = Seq(
    mvn"com.typesafe:config:${AnduinVersions.typesafeConfig}",
    mvn"io.circe::circe-parser:${AnduinVersions.circe}"
      .exclude("io.circe" -> AnduinVersions.j2s("circe-core"))
      .exclude("io.circe" -> AnduinVersions.j2sjs("circe-core"))
      .exclude("org.typelevel" -> AnduinVersions.j2sjs("cats-core"))
      .exclude("org.typelevel" -> AnduinVersions.j2s("cats-core"))
  )

}

object Vespa {

  lazy val jvmDeps = Seq(
    mvn"com.yahoo.vespa:client:${AnduinVersions.vespa}"
  )

}
