// Copyright (C) 2014-2025 Anduin Transactions Inc.

package build.gondor.gondorCore

import build_.build.dependency_.CommonDependencies
import build_.gondor.gondorCore.dependency_.*
import build_.build.util_.*
import build.gondor.{gondorModel, gondorTest}
import build.modules.{bifrost, brienne, dataextract, dataroom, fundData, fundsub, graphql, heimdall, rohan}
import build.platform.stargazer
import mill.*
import mill.scalalib.*

import anduin.mill.*
import anduin.mill.AnduinProtoJsonSchemaModule.*
import anduin.mill.jsdeps.*
import io.github.hoangmaihuy.mill.caliban.*

object `package` extends AnduinCrossPlatformModule {

  object jvm extends JvmModule with SharedModule with AnduinCalibanSourceGenModule with AnduinProtoJsonSchemaModule {

    def protoJsonSchemaPluginPath: T[String] = {
      s"${Task.env.getOrElse("HOME", "$HOME")}/go/bin/protoc-gen-jsonschema"
    }

    def protoJsonSchemaOptions: T[Seq[ProtoJsonSchemaOption]] = Task {
      Seq(
        ProtoJsonSchemaOption.EnumAsStringOnly,
        ProtoJsonSchemaOption.JsonFieldNames,
        ProtoJsonSchemaOption.Messages(Seq("ActionEventModel"))
      )
    }

    def protoJsonSchemaSources: T[Seq[PathRef]] = Task.Sources(
      moduleDir / "protobuf" / "actionlogger" / "actionlogger.proto"
    )

    def generateActionEventSchema: T[PathRef] = Task {
      mill.api.BuildCtx.withFilesystemCheckerDisabled {
        val schemaScripsPath = Task.ctx().workspace / "ci" / "scripts" / "build" / "schemas"
        val jsonSchemaPath = generateProtoJsonSchema().path / "ActionEventModel.json"
        val s3ActionEventSchemaPath = schemaScripsPath / "actionevents.json"
        try {
          val mergeCommand = Seq(
            "jq",
            "-s",
            ".[0] * .[1]",
            s"$jsonSchemaPath",
            s"${s3ActionEventSchemaPath}"
          )
          Task.log.info(s"Merging action event schema with command: ${mergeCommand.mkString(" ")}")
          os.proc(mergeCommand)
            .call(
              cwd = Task.ctx().workspace,
              env = Task.env,
              stdout = Task.dest / "merged.json",
              stderr = os.Inherit
            )
          val json2SparkPath = schemaScripsPath / "json2spark"
          val json2SparkCommand = Seq(
            "scala-cli",
            s"${json2SparkPath}",
            "--",
            s"${Task.dest / "merged.json"}"
          )
          Task.log.info(s"Generating action event schema with command: ${json2SparkCommand.mkString(" ")}")
          os.proc(json2SparkCommand)
            .call(
              cwd = Task.ctx().workspace,
              env = Task.env,
              stdout = Task.dest / "ddl.txt",
              stderr = os.Inherit
            )
        } catch {
          case e: Exception =>
            throw new RuntimeException("Error generating action event schema", e)
        }
      }
      PathRef(Task.dest / "ddl.txt")
    }

    override def calibanSourcePath =
      Task.Source(moduleDir / "src" / "anduin" / "storageservice" / "zot" / "graphql")

    override def calibanFileSettings =
      Task {
        Lib
          .findSourceFiles(Seq(calibanSourcePath()), extensions = Seq("graphql"))
          .map(filePath => filePath.relativeTo(calibanSourcePath().path))
          .map { fileRelPath =>
            CalibanFileSettings.forFile(fileRelPath)(
              _.withClientName(s"${fileRelPath.baseName}")
                .withPackageName("anduin.storageservice.zot.graphql")
                .withGenView(false)
            )
          }
      }

    override def mvnDeps = super.mvnDeps() ++
      GondorCore.jvmDeps ++
      StorageService.jvmDeps ++
      LibreOfficeServerless.jvmDeps ++
      DocumentContent.jvmDeps ++
      ActionLogger.jvmDeps ++
      Mailer.jvmDeps ++
      IssueTracker.jvmDeps ++
      Vespa.jvmDeps ++
      CommonDependencies.calibanDeps

    override def moduleDeps = super.moduleDeps ++ Seq(
      gondorModel.jvm,
      fundsub.fundsubModel.jvm,
      dataroom.dataroomModel.jvm,
      rohan.rohanCommon.jvm,
      heimdall.heimdallCore.jvm,
      fundData.fundDataModel.jvm,
      brienne.brienneModel
    )

    object it extends AnduinZioTests with AnduinIntegTests with AnduinScalaPBModule {
      override def moduleDeps = super.moduleDeps ++ Seq(heimdall.heimdall.jvm, gondorTest)
    }

  }

  object js extends JsModule with ESBuildScalaJSApp with SharedModule {

    override def jsDeps = super.jsDeps() ++ JsDeps(GondorCore.npmDeps*)

    override def mvnDeps = super.mvnDeps() ++ GondorCore.jsDeps

    override def moduleDeps = super.moduleDeps ++ Seq(
      gondorModel.js,
      fundsub.fundsubModel.js,
      dataroom.dataroomModel.js,
      stargazer.js,
      graphql.js,
      heimdall.heimdall.js,
      fundData.fundDataModel.js,
      bifrost.js,
      dataextract.dataextractModel.js
    )

  }

  trait SharedModule extends AnduinPlatformScalaPBModule {

    override def mvnDeps = super.mvnDeps() ++
      GondorCore.sharedDeps ++
      Mailer.sharedDeps ++
      Inbox.sharedDeps ++
      Copy.sharedDeps

    override def scalacOptions = super.scalacOptions() ++ Seq("-Xmax-inlines:128")
  }

}
