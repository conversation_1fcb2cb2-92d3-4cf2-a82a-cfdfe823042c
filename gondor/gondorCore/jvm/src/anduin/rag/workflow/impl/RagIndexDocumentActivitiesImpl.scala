// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.rag.workflow.impl

import anduin.dms.DmsFeature
import anduin.dms.service.FileService
import anduin.dms.tracking.DmsTrackingActivityType
import anduin.documentservice.filepage.FilePageConverterService
import anduin.documentservice.pdf.PdfFileService
import anduin.rag.*
import anduin.rag.EmbeddingService.EmbeddingModel.TitanG1
import anduin.rag.workflow.*
import anduin.workflow.TemporalWorkflowService
import anduin.workflow.TemporalWorkflowService.runActivity
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.utils.ZIOUtils

final case class RagIndexDocumentActivitiesImpl(
  backendConfig: GondorBackendConfig,
  ragIndexService: RagIndexService,
  embeddingsService: EmbeddingService,
  fileService: FileService,
  pdfFileService: PdfFileService,
  filePageConverterService: FilePageConverterService,
  ragTextractService: RagTextractService,
  ragTextService: RagTextProcessService
)(
  using val temporalWorkflowService: TemporalWorkflowService
) extends RagIndexDocumentActivities {

  override def getFileIndexState(
    params: GetFileIndexStateParams
  ): GetFileIndexStateResponse = {
    val task = ragIndexService
      .batchGetFileIndexState(
        fileIds = Seq(params.fileId),
        actor = params.actor
      )(
        using DmsFeature.fromProto(params.dmsFeature)
      )
      .map(_.headOption)
      .flatMap(ZIOUtils.fromOption)
      .map(GetFileIndexStateResponse(_))
    task.runActivity
  }

  override def convertToImages(
    params: ConvertToImagesParams
  ): ConvertToImagesResponse = {
    val task = filePageConverterService
      .convertToImages(
        fileId = params.fileId,
        actor = params.actor,
        purpose = DmsTrackingActivityType.Internal,
        httpContextOpt = None
      )(
        using DmsFeature.fromProto(params.dmsFeature)
      )
      .map(ConvertToImagesResponse(_))

    task.runActivity
  }

  override def getImageFileStorageId(params: GetImageFileStorageIdParams): GetImageFileStorageIdResponse = {
    val task = for {
      storageId <- fileService.getFileStorageId(
        fileId = params.fileId,
        actor = params.actor,
        purpose = DmsTrackingActivityType.Internal,
        httpContextOpt = None
      )(
        using DmsFeature.fromProto(params.dmsFeature)
      )
    } yield GetImageFileStorageIdResponse(storageId)

    task.runActivity
  }

  override def generateVisualEmbeddings(params: GenerateVisualEmbeddingsParams): GenerateVisualEmbeddingsResponse = {
    val task = embeddingsService
      .generateVisualEmbedding(
        params.actor,
        params.imageStorageIds,
        TitanG1
      )
      .map(GenerateVisualEmbeddingsResponse(_))

    task.runActivity
  }

  override def storeVisualEmbeddings(params: StoreVisualEmbeddingsParams): StoreVisualEmbeddingsResponse = {
    val task = ragIndexService
      .saveVisualEmbeddingsToVespa(
        params.indexInfo,
        params.visualEmbeddings
      )
      .map(_ => StoreVisualEmbeddingsResponse())

    task.runActivity
  }

  override def indexKeywordFileContent(
    params: IndexKeywordFileContentParams
  ): IndexKeywordFileContentResponse = {
    val task = ragIndexService
      .syncDocumentChunkDocuments(
        params.indexInfo,
        params.actor,
        params.setTagsOpt.map(_.tags)
      )(
        using DmsFeature.fromProto(params.dmsFeature)
      )
      .as(IndexKeywordFileContentResponse())

    task.runActivity
  }

  override def syncVespaFileState(params: SyncVespaFileStateParams): SyncVespaFileStateResponse = {
    val task = ragIndexService
      .batchSyncFileStateDocument(
        Seq(params.indexInfo),
        params.setTagsOpt.map(_.tags)
      )
      .map(_ => SyncVespaFileStateResponse())

    task.runActivity
  }

}
