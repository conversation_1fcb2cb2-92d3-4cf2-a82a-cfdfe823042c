// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.rag.workflow.impl

import io.temporal.api.enums.v1.ParentClosePolicy

import anduin.dms.DmsFeatureProto
import anduin.radix.RadixId
import anduin.rag.SearchIndexState
import anduin.rag.workflow.*
import anduin.rag.workflow.impl.RagUpdateIndexWorkflowImpl.batchSize
import anduin.workflow.TemporalWorkflowService.{newActivityStub, newChildWorkflowStubWithOptionsCustomizer}
import anduin.workflow.common.TemporalWorkflowImplCompanion
import anduin.workflow.{WorkflowImpl, WorkflowTask}

class RagUpdateIndexWorkflowImpl extends RagUpdateIndexWorkflow {

  private val activities = newActivityStub[RagUpdateIndexActivities]

  private def setChannelState(
    dmsFeatureProto: DmsFeatureProto,
    channel: RadixId,
    state: SearchIndexState
  ): WorkflowTask[Unit] = {
    WorkflowTask
      .executeActivity(
        activities.updateChannelIndexState(
          UpdateChannelIndexStateParams(
            dmsFeature = dmsFeatureProto,
            channel = channel,
            state = state
          )
        )
      )
      .unit
  }

  private def runRagBatchIndexDocumentParams(
    params: RagBatchIndexDocumentParams
  ): WorkflowTask[Unit] = {
    val stub = newChildWorkflowStubWithOptionsCustomizer[RagBatchIndexDocumentWorkflow](options =>
      options.withParentClosePolicy(ParentClosePolicy.PARENT_CLOSE_POLICY_ABANDON)
    )
    WorkflowTask.executeChildWorkflow(stub.run(params)).unit
  }

  override def runAsync(params: RagUpdateIndexParams): WorkflowTask[RagUpdateIndexResponse] = {
    val workflow = for {
      _ <- WorkflowTask.succeed(
        scribe.info(
          s"Start update index workflow for channel ${params.channel.idString}"
        )
      )
      _ <- setChannelState(params.dmsFeature, params.channel, SearchIndexState.Indexing)
      fileIndexesStorageKeys <- WorkflowTask
        .executeActivity(
          activities.scanFileIndexes(
            RagScanFileIndexesParams(
              dmsFeature = params.dmsFeature,
              channel = params.channel,
              actor = params.actor,
              forceReindex = params.forceReindex
            )
          )
        )
        .map(_.fileIndexesStorageKeys)
      folderIndexesStorageKey <- WorkflowTask
        .executeActivity(
          activities.scanFolderIndexes(
            RagScanFolderIndexesParams(
              dmsFeature = params.dmsFeature,
              channel = params.channel,
              actor = params.actor,
              forceReindex = params.forceReindex
            )
          )
        )
        .map(_.folderIndexesStorageKey)
      _ <- WorkflowTask.executeActivity(
        activities.updateFolderIndex(
          RagUpdateFolderIndexParams(folderIndexesStorageKey)
        )
      )
      _ <- WorkflowTask.foreach(fileIndexesStorageKeys) { storageKey =>
        {
          for {
            fileIndexInfos <- WorkflowTask
              .executeActivity(
                activities.getScanFileIndexesResult(
                  RagGetScanFileIndexesResultParams(storageKey)
                )
              )
              .map(_.fileIndexInfos)
            _ <- WorkflowTask.foreach(fileIndexInfos.grouped(batchSize).toSeq) { batch =>
              runRagBatchIndexDocumentParams(
                RagBatchIndexDocumentParams(
                  dmsFeature = params.dmsFeature,
                  actor = params.actor,
                  fileIndexInfos = batch.toSeq,
                  forced = params.forceReindex
                )
              )
            }
          } yield ()
        }
      }
      _ <- setChannelState(
        params.dmsFeature,
        params.channel,
        SearchIndexState.FilenameKeywordIndexed
      )
    } yield RagUpdateIndexResponse()

    workflow.catchAll(error =>
      setChannelState(params.dmsFeature, params.channel, SearchIndexState.Error)
        .flatMap(_ => WorkflowTask.fail(error))
    )
  }

  override def run(params: RagUpdateIndexParams): RagUpdateIndexResponse = {
    runAsync(params).getOrThrow
  }

}

object RagUpdateIndexWorkflowImpl
    extends TemporalWorkflowImplCompanion[RagUpdateIndexWorkflow, RagUpdateIndexWorkflowImpl] {

  val instance = WorkflowImpl.derived[RagUpdateIndexWorkflow, RagUpdateIndexWorkflowImpl]

  private val batchSize = 100
}
