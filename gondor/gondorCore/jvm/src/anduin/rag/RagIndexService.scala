// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.rag

import java.time.Instant
import java.time.temporal.ChronoUnit

import zio.{Task, ZIO}

import anduin.dms.DmsFeature
import anduin.dms.channel.DmsChannelModelStoreOperations
import anduin.dms.service.FileService
import anduin.fdb.record.FDBRecordDatabase
import anduin.model.common.user.UserId
import anduin.model.id.{FileId, FolderId}
import anduin.radix.RadixId
import anduin.rag.RagIndexService.*
import anduin.rag.Tensor1D
import anduin.rag.vespa.model.{DocumentChunk, FileState, FolderState, VisualEmbedding}
import anduin.rag.vespa.{DocumentChunkStore, FileStateStore, FolderStateStore, VisualEmbeddingStore}
import anduin.rag.workflow.*
import anduin.storage.ProtoStorageService
import anduin.vespa.VespaService
import anduin.workflow.TemporalWorkflowService
import com.anduin.stargazer.service.GondorBackendConfig
import com.anduin.stargazer.service.utils.ZIOUtils

final case class RagIndexService(
  backendConfig: GondorBackendConfig,
  embeddingService: EmbeddingService,
  fileService: FileService,
  protoStorageService: ProtoStorageService,
  ragTextProcessService: RagTextProcessService,
  temporalWorkflowService: TemporalWorkflowService
)(
  using vespaService: VespaService
) {

  private[rag] def uploadFileIndexesToS3(
    fileIndexes: Seq[FileIndexInfo]
  ): Task[String] = {
    protoStorageService.put(BatchFileIndexInfo(fileIndexes))
  }

  private[rag] def uploadFolderIndexesToS3(
    folderIndexes: Seq[FolderIndexInfo]
  ): Task[String] = {
    protoStorageService.put(BatchFolderIndexInfo(folderIndexes))
  }

  private[rag] def getFileIndexesFromS3(
    storageKey: String
  ): Task[Seq[FileIndexInfo]] = {
    protoStorageService.get[BatchFileIndexInfo](storageKey).map(_.fileIndexInfos)
  }

  private[rag] def getFolderIndexesFromS3(
    storageKey: String
  ): Task[Seq[FolderIndexInfo]] = {
    protoStorageService.get[BatchFolderIndexInfo](storageKey).map(_.folderIndexInfos)
  }

  private[rag] def saveVisualEmbeddingsToVespa(
    indexInfo: FileIndexInfo,
    embeddings: List[Tensor1D]
  ): Task[Unit] = {
    for {
      _ <- VisualEmbeddingStore.deleteAllDocsOfFileId(indexInfo.fileId) // clean up old indexes
      createdAtEpoch <- ZIOUtils.fromOption(indexInfo.dmsInfo.createdAt.map(_.getEpochSecond))
      lastModifiedEpoch <- ZIOUtils.fromOption(indexInfo.dmsInfo.lastModified.map(_.getEpochSecond))
      _ <- VisualEmbeddingStore.upsertBatch(
        embeddings.zipWithIndex.map { case (embedding, index) =>
          VisualEmbedding(
            id = s"${indexInfo.fileId.idString}_$index",
            embedding = embedding.asList,
            fileId = indexInfo.fileId.idString,
            pageIndex = index,
            fileName = RagTextUtils.normalizeText(indexInfo.dmsInfo.name),
            createdAt = createdAtEpoch,
            lastModified = lastModifiedEpoch,
            isDeleted = indexInfo.dmsInfo.isDeleted
          )
        },
        indexInfo.fileId.channel
      )
    } yield ()
  }

  private def syncFolderStateDocument(
    indexInfo: FolderIndexInfo,
    setTagsOpt: Option[Seq[String]]
  ): Task[Unit] = {
    indexInfo.state match {
      case IndexState.Missing | IndexState.ContentOutdated =>
        for {
          extractKeywordResp <- ragTextProcessService.extractKeywordsFromText(indexInfo.dmsInfo.name)
          prefixes <- RagTextUtils
            .tokenize(
              extractKeywordResp.normalizedText,
              lowercase = true,
              removeAccent = true,
              withAllTokensPrefixes = true
            )
            .map(_.mkString(" "))
          _ <- FolderStateStore.upsert(
            indexInfo.folderId,
            FolderState(
              id = indexInfo.folderId.idString,
              folderId = indexInfo.folderId,
              folderName = extractKeywordResp.normalizedText,
              folderNameKeywords = extractKeywordResp.keywords,
              folderNameKeywordsFlatten = extractKeywordResp.keywords.map(_.lemmatized).mkString(" "),
              folderNameTokensPrefixesFlatten = prefixes,
              createdUserId = indexInfo.dmsInfo.createdUserId,
              createdAt = indexInfo.dmsInfo.getCreatedAt.getEpochSecond,
              lastModified = indexInfo.dmsInfo.getLastModified.getEpochSecond,
              isDeleted = indexInfo.dmsInfo.isDeleted,
              tags = setTagsOpt.getOrElse(indexInfo.vespaInfoOpt.map(_.tags).getOrElse(Seq.empty))
            )
          )
        } yield ()
      case IndexState.MetadataOutdated =>
        FolderStateStore.update(
          indexInfo.folderId,
          FolderState.Partial(
            tags = setTagsOpt,
            createdUserId = Some(indexInfo.dmsInfo.createdUserId),
            createdAt = indexInfo.dmsInfo.createdAt.map(_.getEpochSecond),
            lastModified = indexInfo.dmsInfo.lastModified.map(_.getEpochSecond),
            isDeleted = Some(indexInfo.dmsInfo.isDeleted)
          )
        )
      case IndexState.Synced =>
        ZIOUtils.when(setTagsOpt.isDefined) {
          FolderStateStore.update(
            indexInfo.folderId,
            FolderState.Partial(tags = setTagsOpt)
          )
        }
      case IndexState.Unrecognized(_) =>
        ZIO.fail(
          new RuntimeException("Unknown index state")
        )
    }
  }

  private[rag] def batchSyncFolderStateDocument(
    indexInfos: Seq[FolderIndexInfo],
    setTagsOpt: Option[Seq[String]]
  ): Task[Unit] = {
    ZIOUtils.foreachParN(parallelism)(indexInfos)(indexInfo => syncFolderStateDocument(indexInfo, setTagsOpt)).unit
  }

  private def syncFileStateDocument(
    indexInfo: FileIndexInfo,
    setTagsOpt: Option[Seq[String]]
  ): Task[Unit] = {
    indexInfo.filenameState match {
      case IndexState.Missing | IndexState.ContentOutdated =>
        for {
          extractKeywordsResp <- ragTextProcessService.extractKeywordsFromText(indexInfo.dmsInfo.name)
          prefixes <- RagTextUtils
            .tokenize(
              extractKeywordsResp.normalizedText,
              lowercase = true,
              removeAccent = true,
              withAllTokensPrefixes = true
            )
            .map(_.mkString(" "))
          _ <- FileStateStore.upsert(
            indexInfo.fileId,
            FileState(
              id = indexInfo.fileId.idString,
              fileId = indexInfo.fileId,
              filename = extractKeywordsResp.normalizedText,
              filenameKeywords = extractKeywordsResp.keywords,
              filenameKeywordsFlatten = extractKeywordsResp.keywords.map(_.lemmatized).mkString(" "),
              filenameTokensPrefixesFlatten = prefixes,
              tags = setTagsOpt.getOrElse(indexInfo.vespaInfoOpt.map(_.tags).getOrElse(Seq.empty)),
              createdUserId = indexInfo.dmsInfo.createdUserId,
              createdAt = indexInfo.dmsInfo.getCreatedAt.getEpochSecond,
              lastModified = indexInfo.dmsInfo.getLastModified.getEpochSecond,
              isDeleted = indexInfo.dmsInfo.isDeleted
            )
          )
        } yield ()
      case IndexState.MetadataOutdated =>
        FileStateStore.update(
          indexInfo.fileId,
          FileState.Partial(
            tags = setTagsOpt,
            createdUserId = Some(indexInfo.dmsInfo.createdUserId),
            createdAt = indexInfo.dmsInfo.createdAt.map(_.getEpochSecond),
            lastModified = indexInfo.dmsInfo.lastModified.map(_.getEpochSecond),
            isDeleted = Some(indexInfo.dmsInfo.isDeleted)
          )
        )
      case IndexState.Synced =>
        ZIOUtils.when(setTagsOpt.isDefined) {
          FileStateStore.update(
            indexInfo.fileId,
            FileState.Partial(tags = setTagsOpt)
          )
        }
      case IndexState.Unrecognized(_) =>
        ZIO.fail(
          new RuntimeException("Unknown index state")
        )
    }
  }

  private[rag] def batchSyncFileStateDocument(
    indexInfos: Seq[FileIndexInfo],
    setTagsOpt: Option[Seq[String]]
  ): Task[Unit] = {
    ZIOUtils.foreachParN(parallelism)(indexInfos)(syncFileStateDocument(_, setTagsOpt)).unit
  }

  private[rag] def syncDocumentChunkDocuments(
    indexInfo: FileIndexInfo,
    actor: UserId,
    setTagsOpt: Option[Seq[String]] = None
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    val fileId = indexInfo.fileId
    indexInfo.fileContentState match {
      case IndexState.Missing | IndexState.ContentOutdated =>
        for {
          pageTextsKeywords <- ragTextProcessService.extractKeywordsFromPdfPageTexts(fileId, actor)
          createdAtEpoch <- ZIOUtils.fromOption(indexInfo.dmsInfo.createdAt.map(_.getEpochSecond))
          lastModifiedEpoch <- ZIOUtils.fromOption(indexInfo.dmsInfo.lastModified.map(_.getEpochSecond))
          bigramWordsPerPage <- ZIO.foreach(pageTextsKeywords)(
            ragTextProcessService.getAllBigramWordsFromExtractKeywordsResult
          )
          docs = pageTextsKeywords.zipWithIndex
            .zip(bigramWordsPerPage)
            .map({ case ((extractKeywordResult, pageIndex), bigramWords) =>
              DocumentChunk(
                id = s"${fileId.idString}_$pageIndex",
                fileId = fileId,
                filename = RagTextUtils.normalizeText(indexInfo.dmsInfo.name),
                pageIndex = pageIndex,
                content = extractKeywordResult.normalizedText,
                contentKeywords = extractKeywordResult.keywords,
                contentKeywordsFlatten = extractKeywordResult.keywords.map(_.lemmatized).mkString(" "),
                contentNounPhrasesFlatten = bigramWords.mkString(" "),
                tags = setTagsOpt.getOrElse(indexInfo.vespaInfoOpt.map(_.tags).getOrElse(Seq.empty)),
                createdUserId = indexInfo.dmsInfo.createdUserId,
                createdAt = createdAtEpoch,
                lastModified = lastModifiedEpoch,
                isDeleted = indexInfo.dmsInfo.isDeleted
              )
            })
          _ <- DocumentChunkStore.saveFileChunks(fileId, docs)
        } yield ()
      case IndexState.MetadataOutdated =>
        DocumentChunkStore.updateAllChunksOfFile(
          indexInfo.fileId,
          DocumentChunk.Partial(
            tags = setTagsOpt,
            filename = Some(RagTextUtils.normalizeText(indexInfo.dmsInfo.name)),
            createdUserId = Some(indexInfo.dmsInfo.createdUserId),
            createdAt = indexInfo.dmsInfo.createdAt.map(_.getEpochSecond),
            lastModified = indexInfo.dmsInfo.lastModified.map(_.getEpochSecond),
            isDeleted = Some(indexInfo.dmsInfo.isDeleted)
          )
        )
      case IndexState.Synced =>
        ZIOUtils.when(setTagsOpt.isDefined) {
          DocumentChunkStore.updateAllChunksOfFile(
            indexInfo.fileId,
            DocumentChunk.Partial(tags = setTagsOpt)
          )
        }
      case IndexState.Unrecognized(_) =>
        ZIO.fail(
          new RuntimeException("Unknown index state")
        )
    }
  }

  private def batchGetDmsFileIndexInfo(
    fileIds: Seq[FileId],
    actor: UserId
  )(
    using dmsFeature: DmsFeature
  ): Task[Map[FileId, IndexData]] = {
    for {
      infos <- fileService
        .batchGetFileInfos(
          fileIds,
          actor,
          includeDeleted = true
        )
      indexes <- ZIOUtils.foreachParN(parallelism)(infos) { info =>
        for {
          creator <- ZIOUtils.fromOption(info.creator)
        } yield (
          info.itemId,
          IndexData(
            name = info.name,
            createdUserId = creator,
            createdAt = info.createdAt.map(_.truncatedTo(ChronoUnit.SECONDS)),
            lastModified = info.lastModifiedTime.orElse(info.createdAt).map(_.truncatedTo(ChronoUnit.SECONDS)),
            isDeleted = info.deletedAt.isDefined
          )
        )
      }
    } yield indexes.toMap
  }

  private def batchGetDmsFolderIndexInfo(
    folderIds: Seq[FolderId],
    actor: UserId
  )(
    using dmsFeature: DmsFeature
  ): Task[Map[FolderId, IndexData]] = {
    for {
      infos <- fileService
        .batchGetFolderInfos(
          folderIds,
          actor,
          includeDeleted = true
        )
      indexes <- ZIOUtils.foreachParN(parallelism)(infos) { info =>
        for {
          creator <- ZIOUtils.fromOption(info.creator)
        } yield (
          info.fileManagerLocation.folderId,
          IndexData(
            name = info.name,
            createdUserId = creator,
            createdAt = info.createdAt.map(_.truncatedTo(ChronoUnit.SECONDS)),
            lastModified = info.lastModifiedTime.orElse(info.createdAt).map(_.truncatedTo(ChronoUnit.SECONDS)),
            isDeleted = info.deletedAt.isDefined
          )
        )
      }
    } yield indexes.toMap
  }

  private def getVespaFileIndexInfoOpt(
    fileId: FileId
  ): Task[Option[IndexData]] = {
    for {
      doc <- FileStateStore.getDocumentOpt(fileId)
    } yield doc.map(doc =>
      IndexData(
        name = doc.filename,
        createdUserId = doc.createdUserId,
        createdAt = Some(Instant.ofEpochSecond(doc.createdAt)),
        lastModified = Some(Instant.ofEpochSecond(doc.lastModified)),
        isDeleted = doc.isDeleted,
        tags = doc.tags
      )
    )
  }

  private def getVespaFolderIndexInfoOpt(
    folderId: FolderId
  ): Task[Option[IndexData]] = {
    for {
      doc <- FolderStateStore.getDocumentOpt(folderId)
    } yield doc.map(doc =>
      IndexData(
        name = doc.folderName,
        createdUserId = doc.createdUserId,
        createdAt = Some(Instant.ofEpochSecond(doc.createdAt)),
        lastModified = Some(Instant.ofEpochSecond(doc.lastModified)),
        isDeleted = doc.isDeleted,
        tags = doc.tags
      )
    )
  }

  private def getFileFolderNameIndexState(
    dmsInfo: IndexData,
    vespaInfoOpt: Option[IndexData]
  ): IndexState = {
    vespaInfoOpt.fold(IndexState.Missing) {
      case vespaInfo if vespaInfo.name != dmsInfo.name                 => IndexState.ContentOutdated
      case vespaInfo if vespaInfo.lastModified != dmsInfo.lastModified => IndexState.MetadataOutdated
      case vespaInfo if vespaInfo.createdAt != dmsInfo.createdAt       => IndexState.MetadataOutdated
      case vespaInfo if vespaInfo.isDeleted != dmsInfo.isDeleted       => IndexState.MetadataOutdated
      case _                                                           => IndexState.Synced
    }
  }

  private def getFileContentIndexState(
    dmsInfo: IndexData,
    vespaInfoOpt: Option[IndexData]
  ): IndexState = {
    vespaInfoOpt.fold(IndexState.Missing) {
      case vespaInfo if vespaInfo.lastModified != dmsInfo.lastModified => IndexState.ContentOutdated
      case vespaInfo if vespaInfo.name != dmsInfo.name                 => IndexState.MetadataOutdated
      case vespaInfo if vespaInfo.createdAt != dmsInfo.createdAt       => IndexState.MetadataOutdated
      case vespaInfo if vespaInfo.isDeleted != dmsInfo.isDeleted       => IndexState.MetadataOutdated
      case _                                                           => IndexState.Synced
    }
  }

  private[rag] def batchGetFileIndexState(
    fileIds: Seq[FileId],
    actor: UserId
  )(
    using dmsFeature: DmsFeature
  ): Task[Seq[FileIndexInfo]] = {
    for {
      batchDmsInfos <- batchGetDmsFileIndexInfo(fileIds, actor)
      indexStates <- ZIOUtils.foreachParN(parallelism)(batchDmsInfos) { (fileId, dmsInfo) =>
        for {
          vespaInfoOpt <- getVespaFileIndexInfoOpt(fileId)
        } yield FileIndexInfo(
          fileId,
          getFileFolderNameIndexState(dmsInfo, vespaInfoOpt),
          getFileContentIndexState(dmsInfo, vespaInfoOpt),
          dmsInfo,
          vespaInfoOpt
        )
      }
    } yield indexStates.toSeq
  }

  private[rag] def batchGetFolderIndexState(
    folderIds: Seq[FolderId],
    actor: UserId
  )(
    using dmsFeature: DmsFeature
  ): Task[Seq[FolderIndexInfo]] = {
    for {
      batchDmsInfo <- batchGetDmsFolderIndexInfo(folderIds, actor)
      indexStates <- ZIOUtils.foreachParN(parallelism)(batchDmsInfo) { (folderId, dmsInfo) =>
        for {
          vespaInfoOpt <- getVespaFolderIndexInfoOpt(folderId)
        } yield FolderIndexInfo(
          folderId,
          getFileFolderNameIndexState(dmsInfo, vespaInfoOpt),
          dmsInfo,
          vespaInfoOpt
        )
      }
    } yield indexStates.toSeq

  }

  def batchSyncFolderIndex(
    folderIds: Seq[FolderId],
    actor: UserId,
    setTagsOpt: Option[Seq[String]] = None,
    forced: Boolean = false
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    for {
      indexStates <- batchGetFolderIndexState(folderIds, actor).map(
        _.map(indexState => Option.when(forced)(indexState.withState(IndexState.Missing)).getOrElse(indexState))
      )
      _ <- batchSyncFolderStateDocument(indexStates, setTagsOpt)
    } yield ()
  }

  def batchDeleteFolderIndexes(
    folderIds: Seq[FolderId],
    actor: UserId,
    isPermanent: Boolean = false
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    for {
      permissionCheckResult <- fileService
        .batchCheckFolderPermission(folderIds, actor, includeDeleted = true)
      _ <- ZIO.foreach(folderIds)(folderId =>
        ZIOUtils.validate(permissionCheckResult.contains(folderId))(
          new RuntimeException(s"User $actor does not have access to folder $folderId")
        )
      )
      _ <- ZIOUtils.foreachParN(parallelism)(folderIds) { folderId =>
        FolderStateStore.delete(folderId, isPermanent)
      }
    } yield ()
  }

  private def syncFileIndex(
    fileId: FileId,
    actor: UserId,
    setTagsOpt: Option[Seq[String]] = None,
    forced: Boolean = false
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    temporalWorkflowService
      .runAsync[
        RagIndexDocumentParams,
        RagIndexDocumentResponse,
        RagIndexDocumentWorkflow
      ](
        RagIndexDocumentParams(
          dmsFeature = dmsFeature.toProto,
          fileId = fileId,
          actor = actor,
          setTagsOpt = setTagsOpt.map(SetTagsParams(_)),
          forced = forced
        )
      )
      .unit
  }

  def batchSyncFileIndex(
    fileIds: Seq[FileId],
    actor: UserId,
    setTagsOpt: Option[Seq[String]] = None,
    forced: Boolean = false
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    ZIOUtils.foreachParN(parallelism)(fileIds)(fileId => syncFileIndex(fileId, actor, setTagsOpt, forced)).unit
  }

  def batchDeleteFileIndexes(
    fileIds: Seq[FileId],
    actor: UserId,
    isPermanent: Boolean = false
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    for {
      permissionCheckResult <- fileService
        .batchCheckFilePermission(fileIds, actor, includeDeleted = true)
      _ <- ZIO.foreach(fileIds)(fileId =>
        ZIOUtils.validate(permissionCheckResult.contains(fileId))(
          new RuntimeException(s"User $actor does not have access to file $fileId")
        )
      )
      _ <- ZIOUtils.foreachParN(parallelism)(fileIds) { fileId =>
        FileStateStore.delete(fileId, isPermanent) *>
          DocumentChunkStore.deleteAllChunksOfFile(fileId, isPermanent)
      }
    } yield ()
  }

  def startUpdateIndexWorkflow(
    channel: RadixId,
    actor: UserId,
    forceReindex: Boolean = false
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    temporalWorkflowService
      .runAsync[
        RagUpdateIndexParams,
        RagUpdateIndexResponse,
        RagUpdateIndexWorkflow
      ](
        RagUpdateIndexParams(
          dmsFeature = dmsFeature.toProto,
          channel = channel,
          actor = actor,
          forceReindex = forceReindex
        )
      )
      .unit
  }

  def getChannelIndexState(
    channel: RadixId
  )(
    using dmsFeature: DmsFeature
  ): Task[SearchIndexState] = {
    dmsFeature
      .getDmsChannel(channel)
      .fold(ZIO.succeed(SearchIndexState.NotIndexed))(dmsChannel =>
        FDBRecordDatabase.transact(DmsChannelModelStoreOperations.Production)(
          _.getOpt(channel)(
            using dmsChannel
          ).map(_.fold(SearchIndexState.NotIndexed)(_.searchIndexState))
        )
      )
  }

  private[rag] def updateChannelIndexState(
    channel: RadixId,
    state: SearchIndexState
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    dmsFeature
      .getDmsChannel(channel)
      .fold(ZIO.none)(dmsChannel =>
        FDBRecordDatabase.transact(DmsChannelModelStoreOperations.Production)(
          _.upsert(channel, _.copy(searchIndexState = state))(
            using dmsChannel
          )
        )
      )
      .unit
  }

}

object RagIndexService {
  private val parallelism = 4
}
