// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.rag.vespa

import ai.vespa.client.dsl.{A, Q}
import zio.{Task, ZIO}

import anduin.model.id.FileId
import anduin.radix.RadixId
import anduin.rag.vespa.model.DocumentChunk
import anduin.vespa.Models.{QueryRankingParams, QueryStreamingParams, VespaNamespace, VespaQueryRequest}
import anduin.vespa.VespaService
import anduin.vespa.VespaService.QueryModelWithScoreResult
import com.anduin.stargazer.service.utils.ZIOUtils

private[rag] object DocumentChunkStore {
  given vespaNamespace: VespaNamespace = VespaNamespace.Default

  private val parallelism = 4

  private def upsertChunk(
    doc: DocumentChunk,
    channel: RadixId
  )(
    using vespaService: VespaService
  ): Task[Unit] = {
    vespaService.saveDocument(doc, channel.idString)
  }

  private def updateChunk(
    docId: String,
    channel: RadixId,
    updateData: DocumentChunk.Partial
  )(
    using vespaService: VespaService
  ): Task[Unit] = {
    vespaService
      .updateDocument[DocumentChunk, DocumentChunk.Partial](
        docId,
        updateData,
        channel.idString
      )
  }

  private def deleteChunk(
    docId: String,
    channel: RadixId,
    isPermanent: Boolean = false
  )(
    using vespaService: VespaService
  ): Task[Unit] = {
    if (isPermanent) {
      vespaService.deleteDocument[DocumentChunk](docId, channel.idString)
    } else {
      updateChunk(docId, channel, DocumentChunk.Partial(isDeleted = Some(true)))
    }
  }

  private def getChunkOpt(
    docId: String,
    channel: RadixId
  )(
    using vespaService: VespaService
  ): Task[Option[DocumentChunk]] = {
    vespaService.getDocumentOption[DocumentChunk](docId, channel.idString)
  }

  private def getAllChunkIdsOfFile(
    fileId: FileId
  )(
    using vespaService: VespaService
  ): Task[Seq[String]] = {
    val yql = Q
      .select(DocumentChunk.Field.Id.name)
      .from(DocumentChunk.schema)
      .where(DocumentChunk.Field.Id.name)
      .contains(A.a("exact", true), fileId.idString)
      .build()
    val streaming = QueryStreamingParams(groupname = Some(fileId.channel.idString))

    for {
      partialDocs <- vespaService.queryAllModel[DocumentChunk, DocumentChunk.Partial](
        VespaQueryRequest(yql, streaming = Some(streaming))
      )
      ids <- ZIO.foreach(partialDocs)(doc => ZIOUtils.fromOption(doc.id))
    } yield ids
  }

  def saveFileChunks(
    fileId: FileId,
    chunks: Seq[DocumentChunk]
  )(
    using vespaService: VespaService
  ): Task[Unit] = {
    for {
      _ <- ZIOUtils.validate(chunks.forall(_.fileId == fileId))(
        new IllegalArgumentException(s"Inconsistent fileIds, All chunks must have fileId = ${fileId.idString}")
      )
      _ <- deleteAllChunksOfFile(fileId, isPermanent = true)
      _ <- ZIOUtils.foreachParN(parallelism)(chunks)(upsertChunk(_, fileId.channel)).unit
    } yield ()
  }

  def updateAllChunksOfFile(
    fileId: FileId,
    updateData: DocumentChunk.Partial
  )(
    using vespaService: VespaService
  ): Task[Unit] = {
    for {
      docIds <- getAllChunkIdsOfFile(fileId)
      _ <- ZIOUtils.foreachParN(parallelism)(docIds)(updateChunk(_, fileId.channel, updateData))
    } yield ()
  }

  def deleteAllChunksOfFile(
    fileId: FileId,
    isPermanent: Boolean = false
  )(
    using vespaService: VespaService
  ): Task[Unit] = {
    for {
      docIds <- getAllChunkIdsOfFile(fileId)
      _ <- ZIOUtils.foreachParN(4)(docIds)(id =>
        deleteChunk(
          id,
          fileId.channel,
          isPermanent
        )
      )
    } yield ()
  }

  private def buildKeywordSearchQuery(
    channel: RadixId,
    query: String,
    selectFields: List[DocumentChunk.Field],
    limit: Int = 10,
    offset: Int = 0,
    isDeleted: Boolean = false
  ): VespaQueryRequest = {
    if (selectFields.isEmpty) {
      throw new IllegalArgumentException(s"'selectFields' must be non-empty")
    }

    val builder =
      Q.select(selectFields.head.name, selectFields.drop(1).map(_.name)*)
        .from(DocumentChunk.schema)
        .where(Q.ui(A.defaultIndex(DocumentChunk.FieldSet.KeywordSearch.name), query))
    Option
      .when(isDeleted)(())
      .fold(builder.and(DocumentChunk.Field.IsDeleted.name).isFalse)(_ =>
        builder.and(DocumentChunk.Field.IsDeleted.name).isTrue
      )

    VespaQueryRequest(
      builder.build().drop(4),
      hits = Some(limit),
      streaming = Some(QueryStreamingParams(groupname = Some(channel.idString))),
      ranking = Some(QueryRankingParams(profile = Some(DocumentChunk.RankingProfile.BM25.name))),
      offset = Some(offset)
    )
  }

  def keywordSearch(
    channel: RadixId,
    query: String,
    limit: Int = 10,
    offset: Int = 0,
    isDeleted: Boolean = false
  )(
    using vespaService: VespaService
  ): Task[QueryModelWithScoreResult[DocumentChunk.Partial]] = {
    val fieldList = List(
      DocumentChunk.Field.FileId,
      DocumentChunk.Field.Filename,
      DocumentChunk.Field.PageIndex,
      DocumentChunk.Field.Content,
      DocumentChunk.Field.ContentKeywords,
      DocumentChunk.Field.LastModified,
      DocumentChunk.Field.CreatedUserId
    )
    if (query.trim.isEmpty) {
      ZIO.succeed(QueryModelWithScoreResult(0, List.empty))
    } else {
      vespaService.executeQueryModelWithScore[DocumentChunk.Partial](
        buildKeywordSearchQuery(channel, query, fieldList, limit, offset, isDeleted)
      )
    }
  }

}
