package anduin.rag

import io.circe.Decoder
import io.circe.parser.decode
import io.circe.syntax.*
import zio.{Task, ZIO}

import anduin.model.common.user.UserId
import anduin.model.document.DocumentStorageId
import anduin.rag.RagKeywordService.{ExtractKeywordsResult, s3Access}
import anduin.rag.model.{Keyword, NounPhrase}
import anduin.serverless.functions.ExtractKeywordsServerless
import anduin.serverless.models.ExtractKeywords.{
  ExtractKeywordsRequest,
  ExtractKeywordsViaS3Request,
  ExtractKeywordsResult as ServerlessExtractKeywordsResult
}
import anduin.serverless.utils.ServerlessUtils
import anduin.storageservice.s3.S3Service
import anduin.utils.stream.ZStreamIOUtils
import com.anduin.stargazer.service.utils.ZIOUtils

final case class RagKeywordService(
  s3Service: S3Service,
  extractKeywordsServerless: ExtractKeywordsServerless
) {

  def extractKeywordsText(
    text: String
  ): Task[ExtractKeywordsResult] = {
    for {
      resp <- extractKeywordsServerless.extractKeywordsDirect(
        ExtractKeywordsRequest(text)
      )
      _ <- ZIOUtils.validate(resp.statusCode == 200) {
        new RuntimeException(s"Extract keywords failed: ${resp.message}")
      }
      result <- ExtractKeywordsResult.fromServerlessResult(resp.result)
    } yield result
  }

  private def readResultFromS3[T](
    s3Service: S3Service,
    storageId: DocumentStorageId
  )(
    using decoder: Decoder[T]
  ): Task[Option[T]] = {
    ZIO.scoped {
      for {
        isExisted <- s3Service.checkExisted(storageId, s3Access.bucket)
        result <- Option
          .when(isExisted)(
            s3Service
              .downloadFromS3Scoped(s3Access.bucket, storageId.id)
              .flatMap(ZStreamIOUtils.toString)
              .map(decode[T](_).toOption)
          )
          .getOrElse(
            ZIO.none
          )
      } yield result
    }
  }

  private def extractKeywordsViaS3WithoutCache(
    text: String,
    actor: UserId,
    s3InputId: DocumentStorageId,
    s3OutputId: DocumentStorageId
  ): Task[ExtractKeywordsResult] = {
    for {
      _ <- s3Service.uploadStreamToS3(
        storageId = s3InputId,
        verifiedUser = actor,
        content = ZStreamIOUtils.fromString(ExtractKeywordsRequest(text).asJson.noSpaces),
        contentType = "text/plain",
        bucket = s3Access.bucket
      )
      resp <- extractKeywordsServerless.extractKeywordsViaS3(
        ExtractKeywordsViaS3Request(
          s3Access = s3Access,
          s3InputKey = s3InputId.id,
          s3OutputKey = s3OutputId.id,
          isBatch = false
        )
      )
      _ <- ZIOUtils.validate(resp.statusCode == 200) {
        new RuntimeException(s"Extract keywords failed: ${resp.message}")
      }
      serverlessResult <- readResultFromS3[ServerlessExtractKeywordsResult](s3Service, s3OutputId).flatMap(
        ZIOUtils.fromOption
      )
      result <- ExtractKeywordsResult.fromServerlessResult(serverlessResult)
    } yield result
  }

  def extractKeywordsViaS3(
    text: String,
    actor: UserId,
    s3InputId: DocumentStorageId,
    s3OutputId: DocumentStorageId,
    useCache: Boolean = true
  ): Task[ExtractKeywordsResult] = {
    if (useCache) {
      for {
        resultOpt <- readResultFromS3[ServerlessExtractKeywordsResult](s3Service, s3OutputId)
        result <- resultOpt.fold(
          extractKeywordsViaS3WithoutCache(text, actor, s3InputId, s3OutputId)
        )(ExtractKeywordsResult.fromServerlessResult)
      } yield result
    } else {
      extractKeywordsViaS3WithoutCache(text, actor, s3InputId, s3OutputId)
    }
  }

  private def batchExtractKeywordsViaS3WithoutCache(
    texts: List[String],
    actor: UserId,
    s3InputId: DocumentStorageId,
    s3OutputId: DocumentStorageId
  ): Task[List[ExtractKeywordsResult]] = {
    for {
      _ <- s3Service.uploadStreamToS3(
        storageId = s3InputId,
        verifiedUser = actor,
        content = ZStreamIOUtils.fromString(texts.map(ExtractKeywordsRequest.apply).asJson.noSpaces),
        contentType = "text/plain",
        bucket = s3Access.bucket
      )
      resp <- extractKeywordsServerless.extractKeywordsViaS3(
        ExtractKeywordsViaS3Request(
          s3Access = s3Access,
          s3InputKey = s3InputId.id,
          s3OutputKey = s3OutputId.id,
          isBatch = true
        )
      )
      _ <- ZIOUtils.validate(resp.statusCode == 200) {
        new RuntimeException(s"Extract keywords failed: ${resp.message}")
      }
      serverlessResults <- readResultFromS3[List[ServerlessExtractKeywordsResult]](s3Service, s3OutputId)
        .flatMap(ZIOUtils.fromOption)
      result <- ZIO.foreach(serverlessResults)(ExtractKeywordsResult.fromServerlessResult)
    } yield result
  }

  def batchExtractKeywordsViaS3(
    texts: List[String],
    actor: UserId,
    s3InputId: DocumentStorageId,
    s3OutputId: DocumentStorageId,
    useCache: Boolean = true
  ): Task[List[ExtractKeywordsResult]] = {
    if (useCache) {
      for {
        resultOpt <- readResultFromS3[List[ServerlessExtractKeywordsResult]](s3Service, s3OutputId)
        result <- resultOpt.fold(
          batchExtractKeywordsViaS3WithoutCache(texts, actor, s3InputId, s3OutputId)
        )(ZIO.foreach(_)(ExtractKeywordsResult.fromServerlessResult))
      } yield result
    } else {
      batchExtractKeywordsViaS3WithoutCache(texts, actor, s3InputId, s3OutputId)
    }
  }

}

object RagKeywordService {
  private val s3Access = ServerlessUtils.getS3Access()

  final case class ExtractKeywordsResult(
    keywords: List[Keyword],
    normalizedText: String,
    nounPhrases: List[NounPhrase]
  )

  object ExtractKeywordsResult {

    def fromServerlessResult(result: ServerlessExtractKeywordsResult): Task[ExtractKeywordsResult] = {
      // fix the different in string indexing between Python & Scala
      val positionMap = RagTextUtils.getCodePointToPositionMap(result.normalizedText)
      for {
        keywords <- ZIO.foreach(result.keywords) { keyword =>
          ZIOUtils
            .fromOption(positionMap.get(keyword.position))
            .map(pos =>
              Keyword(
                keyword.text,
                pos,
                keyword.lemmatized
              )
            )
        }
      } yield ExtractKeywordsResult(
        keywords,
        result.normalizedText,
        result.nounPhrases.map { nounPhrase =>
          NounPhrase(
            nounPhrase.text,
            nounPhrase.lemmatized
          )
        }
      )
    }

  }

}
