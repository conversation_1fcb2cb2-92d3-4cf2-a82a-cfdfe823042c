// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.rag

import scala.collection.immutable.TreeMap

import zio.{Task, ZIO}

import anduin.dms.DmsFeature
import anduin.dms.tracking.DmsTrackingActivityType.Internal
import anduin.documentcontent.pdf.PDFTextExtractor
import anduin.documentservice.pdf.PdfFileService
import anduin.model.common.user.UserId
import anduin.model.document.DocumentStorageId
import anduin.model.id.FileId
import anduin.model.textract.block.{Block, BlockType}
import anduin.rag.RagKeywordService.ExtractKeywordsResult
import anduin.rag.RagTextProcessService.*
import anduin.rag.model.{HighLightRange, Keyword, TextWithHighlights}
import anduin.serverless.functions.MuPDFServerless
import anduin.serverless.utils.ServerlessUtils
import anduin.storageservice.s3.S3Service
import com.anduin.stargazer.service.utils.ZIOUtils

final case class RagTextProcessService(
  pdfFileService: PdfFileService,
  ragTextractService: RagTextractService,
  ragKeywordService: RagKeywordService,
  muPDFServerless: MuPDFServerless,
  s3Service: S3Service
) {

  private def getPdfFileStorageId(
    fileId: FileId,
    actor: UserId
  )(
    using dmsFeature: DmsFeature
  ): Task[DocumentStorageId] = {
    pdfFileService
      .getPdfStorageIdOrConvert(
        actor,
        fileId,
        purpose = Internal,
        password = None,
        httpContext = None,
        includeDeleted = true
      )
      .map(_._1)
  }

  private def postprocessExtractKeywordsResult(
    normalizedText: String,
    extractedResult: ExtractKeywordsResult
  ): Task[ExtractKeywordsResult] = {
    for {
      keywords <- ZIO.foreach(extractedResult.keywords) { keyword =>
        for {
          normalizedLemma <- RagTextUtils.normalizeTextZIO(
            keyword.lemmatized,
            removeAccent = true,
            removePunctuation = true
          )
        } yield keyword.copy(lemmatized = normalizedLemma)
      }
      nounPhrases <- ZIO.foreach(extractedResult.nounPhrases) { nounPhrase =>
        for {
          normalizedLemma <- RagTextUtils.normalizeTextZIO(
            nounPhrase.lemmatized,
            removeAccent = true,
            removePunctuation = true
          )
        } yield nounPhrase.copy(lemmatized = normalizedLemma)
      }
    } yield extractedResult.copy(
      keywords = keywords,
      normalizedText = normalizedText,
      nounPhrases = nounPhrases
    )
  }

  private[rag] def getAllBigramWordsFromExtractKeywordsResult(
    extractedResult: ExtractKeywordsResult
  ): Task[List[String]] = {
    ZIO
      .foreach(extractedResult.nounPhrases) { keyword =>
        ZIO.succeed(RagTextUtils.getAllBigramWords(keyword.lemmatized))
      }
      .map(_.flatten)
  }

  private[rag] def extractKeywordsFromText(text: String): Task[ExtractKeywordsResult] = {
    for {
      // normalize & lowercase for better keyword extraction
      normalizedText <- RagTextUtils.normalizeTextZIO(text)
      resp <- ragKeywordService.extractKeywordsText(normalizedText.toLowerCase)
      result <- postprocessExtractKeywordsResult(normalizedText, resp)
    } yield result
  }

  private[rag] def extractKeywordsFromPdfPageTexts(
    fileId: FileId,
    actor: UserId,
    useCache: Boolean = true
  )(
    using dmsFeature: DmsFeature
  ): Task[List[ExtractKeywordsResult]] = {
    for {
      storageId <- getPdfFileStorageId(fileId, actor)
      (inputId, outputId) = getPdfExtractedKeywordsCacheStorageId(storageId)
      texts <- extractPageTextsFromPdf(fileId, actor, useCache)

      // normalize & lowercase before extraction
      normalizedTexts <- ZIO.foreach(texts)(RagTextUtils.normalizeTextZIO(_))
      s3result <- ragKeywordService.batchExtractKeywordsViaS3(
        normalizedTexts.map(_.toLowerCase),
        actor,
        inputId,
        outputId,
        useCache
      )
      results <- ZIO.foreach(s3result.zip(normalizedTexts)) { case (result, text) =>
        postprocessExtractKeywordsResult(text, result)
      }
    } yield results
  }

  private[rag] def extractPageTextsFromPdf(
    fileId: FileId,
    actor: UserId,
    useCache: Boolean = true
  )(
    using dmsFeature: DmsFeature
  ): Task[List[String]] = {
    def runWithoutCache(inputStorageId: DocumentStorageId, outputStorageId: DocumentStorageId): Task[List[String]] = {
      for {
        extractResp <- PDFTextExtractor.runExtractTextPerPages(
          muPDFServerless,
          s3Access,
          inputStorageId,
          outputStorageId
        )
        result <- extractResp.fold(
          err => ZIO.succeed(List.empty[String]),
          _ =>
            PDFTextExtractor
              .getExtractTextPerPagesResult(s3Service, s3Access, outputStorageId)
              .flatMap(ZIOUtils.fromOption)
        )
      } yield result
    }

    for {
      fileStorageId <- getPdfFileStorageId(fileId, actor)
      outputStorageId = getPdfExtractedTextsCacheStorageId(fileStorageId)
      cachedResult <- PDFTextExtractor.getExtractTextPerPagesResult(s3Service, s3Access, outputStorageId)
      result <- cachedResult.fold(
        runWithoutCache(fileStorageId, outputStorageId)
      )(result =>
        if (useCache) {
          ZIO.succeed(result)
        } else {
          runWithoutCache(fileStorageId, outputStorageId)
        }
      )

    } yield result
  }

  private def getTextractWordBlocks(
    fileId: FileId,
    actor: UserId,
    pageRangeOpt: Option[(Int, Int)] = None
  )(
    using dmsFeature: DmsFeature
  ): Task[List[Block]] = {
    ragTextractService
      .getTextractBlocks(fileId, actor, pageRangeOpt)
      .map(_.filter(_.blockType.contains(BlockType.Word)))
  }

  private def getTextOfAllBlocks(
    blocks: List[Block]
  ): String = {
    blocks.flatMap(_.text).map(_.trim).mkString(wordSplittingChar)
  }

  private def buildKeywordBlockMapping(
    blocks: List[Block],
    keywords: List[Keyword]
  ): Map[Keyword, Block] = {
    val positionBlockMap = TreeMap[Int, Block](
      blocks
        .flatMap(_.text)
        .map(_.length)
        .scanLeft(0)((acc, length) => acc + length + wordSplittingChar.length)
        .dropRight(1)
        .zip(blocks)*
    )

    // some keywords may be in the middle of a block, we will also map it to the long block (i.e. 100-year-old)
    keywords.zip {
      keywords
        .map(keyword =>
          positionBlockMap
            .rangeTo(keyword.startPosition)
            .lastOption
            .fold(throw new RuntimeException(s"Failed to find block for keyword"))((blockPos, block) =>
              Option
                .when(
                  block.text
                    .map(
                      _.substring(
                        keyword.startPosition - blockPos,
                        keyword.startPosition - blockPos + keyword.text.length
                      )
                    )
                    .contains(keyword.text)
                )(block)
                .getOrElse(
                  throw new RuntimeException(s"Keyword and block text do not match")
                )
            )
        )
    }.toMap
  }

  def getKeywordBlockMapping(
    fileId: FileId,
    actor: UserId,
    pageRangeOpt: Option[(Int, Int)] = None
  )(
    using dmsFeature: DmsFeature
  ): Task[Map[Keyword, Block]] = {
    for {
      blocks <- ragTextractService.getTextractBlocks(fileId, actor, pageRangeOpt)
      fullText <- ZIO.attempt(getTextOfAllBlocks(blocks))
      keywords <- extractKeywordsFromText(fullText).map(_.keywords)
      mapping <- ZIO.attempt(buildKeywordBlockMapping(blocks, keywords))
    } yield mapping
  }

  def getPlainTextPerPageTextract(
    fileId: FileId,
    actor: UserId
  )(
    using dmsFeature: DmsFeature
  ): Task[List[(page: Int, text: String)]] = {
    for {
      blocks <- getTextractWordBlocks(fileId, actor)
      pageBlocks <- ZIO.foreach(blocks)(block => ZIOUtils.fromOption(block.page).map(page => (page, block)))
    } yield pageBlocks
      .groupBy((page, _) => page)
      .toList
      .sortBy((page, _) => page)
      .map((page, pageBlocks) => (page, getTextOfAllBlocks(pageBlocks.map(_._2))))
  }

  def getFullPlainTextTextract(
    fileId: FileId,
    actor: UserId,
    pageRangeOpt: Option[(Int, Int)] = None
  )(
    using dmsFeature: DmsFeature
  ): Task[String] = {
    for {
      blocks <- getTextractWordBlocks(fileId, actor, pageRangeOpt)
      fullText <- ZIO.attempt(getTextOfAllBlocks(blocks))
    } yield fullText
  }

  private[rag] def findContentSnippetInternal(
    query: String,
    queryKeywords: List[Keyword],
    content: String,
    contentKeywords: List[Keyword],
    maxSnippetLength: Option[Int] = Some(maxSnippetLength),
    keepEmptyMatch: Boolean = true
  ): Task[TextWithHighlights] = {
    for {
      queryTokensSet <- RagTextUtils
        .tokenize(query, removeAccent = true)
        .map(tokens => (tokens ++ queryKeywords.map(_.text.toLowerCase) ++ queryKeywords.map(_.lemmatized)).toSet)
      contentTokens <- RagTextUtils
        .tokenize(content, lowercase = true, includePunctuation = true)
        .map(tokens =>
          tokens
            .zip(tokens.scanLeft(0)((acc, pos) => acc + pos.length).dropRight(1))
            .map((token, pos) =>
              (
                token,
                pos,
                contentKeywords
                  .find(k => k.startPosition == pos)
                  .fold(snippetNonKeywordWeight)(_ => snippetKeywordWeight),
                contentKeywords
                  .find(k => k.startPosition == pos)
                  .fold("")(_.lemmatized)
              )
            )
            .map((token, pos, weight, lemma) =>
              (
                token.length,
                pos,
                Option
                  .when(
                    queryTokensSet.contains(RagTextUtils.normalizeText(token, removeAccent = true))
                      || queryTokensSet.contains(lemma)
                  )(
                    weight
                  )
                  .getOrElse(0)
              )
            )
        )
      windowOpt <- ZIO.attempt(
        maxSnippetLength.fold(Some(contentTokens))(maxLength =>
          findMaxSumWindowOpt(contentTokens, (_, _, weight) => weight, maxLength)
        )
      )
      snippet <- windowOpt.fold(ZIO.succeed(TextWithHighlights.Empty))(window =>
        if (window.map((_, _, weight) => weight).sum == 0 && !keepEmptyMatch) {
          ZIO.succeed(TextWithHighlights.Empty)
        } else {
          for {
            startPos <- ZIOUtils.fromOption(window.headOption.map((_, pos, _) => pos))
            endPos <- ZIOUtils.fromOption(window.lastOption.map((wordLength, pos, _) => pos + wordLength))
          } yield TextWithHighlights(
            content.substring(startPos, endPos), {
              window.flatMap((wordLength, pos, weight) =>
                Option.when(weight > 0)(
                  HighLightRange(
                    startPos = pos - startPos,
                    endPos = pos - startPos + wordLength
                  )
                )
              )
            }
          )
        }
      )
    } yield snippet
  }

  def clearAllCache: Task[Unit] = {
    s3Service.deleteDirectory(s3Access.bucket, pdfExtractedTextsCacheDir) *>
      s3Service.deleteDirectory(s3Access.bucket, pdfExtractedKeywordCacheDir)
  }

}

object RagTextProcessService {
  private val wordSplittingChar = " "
  private val maxSnippetLength = 80
  private val snippetKeywordWeight = 4
  private val snippetNonKeywordWeight = 1

  private val s3Access = ServerlessUtils.getS3Access()
  private val pdfExtractedTextsCacheDir = "cache_pdf_extracted_text"
  private val pdfExtractedKeywordCacheDir = "cache_extracted_keywords"

  private def findMaxSumWindowOpt[T](
    arr: List[T],
    weightFunc: T => Int,
    maxWindowSize: Int
  ): Option[List[T]] = {
    if (arr.isEmpty || maxWindowSize <= 0) {
      None
    } else if (arr.size <= maxWindowSize) {
      Some(arr)
    } else {
      val weights = arr.map(weightFunc)
      val initialSum = weights.take(maxWindowSize).sum
      val (_, _, maxIndex) = (1 to arr.size - maxWindowSize).foldLeft((initialSum, initialSum, 0)) {
        case ((sum, maxSum, maxIndex), index) =>
          val newSum = sum - weights(index - 1) + weights(index + maxWindowSize - 1)
          if (newSum > maxSum) {
            (newSum, newSum, index)
          } else {
            (newSum, maxSum, maxIndex)
          }
      }
      Some(arr.slice(maxIndex, maxIndex + maxWindowSize))
    }
  }

  private def getPdfExtractedTextsCacheStorageId(storageId: DocumentStorageId): DocumentStorageId = {
    DocumentStorageId(s"$pdfExtractedTextsCacheDir/${storageId.id}")
  }

  private def getPdfExtractedKeywordsCacheStorageId(storageId: DocumentStorageId)
    : (input: DocumentStorageId, output: DocumentStorageId) = {
    DocumentStorageId(s"$pdfExtractedKeywordCacheDir/input/${storageId.id}") ->
      DocumentStorageId(s"$pdfExtractedKeywordCacheDir/output/${storageId.id}")
  }

}
