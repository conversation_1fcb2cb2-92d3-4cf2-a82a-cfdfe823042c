// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.batchaction

import java.time.Duration
import scala.reflect.ClassTag

import io.temporal.api.enums.v1.ParentClosePolicy
import io.temporal.failure.ActivityFailure
import zio.temporal.activity.{IsActivity, ZActivityOptions, ZActivityStub}
import zio.temporal.workflow.*
import zio.temporal.{ZRetryOptions, workflowMethod}

import anduin.workflow.{TemporalQueue, WorkflowImpl}

trait BatchActionWorkflow {

  @workflowMethod
  def execute(param: BatchActionWorkflowParam): ResponseUnit

}

trait BatchActionWorkflowImpl[
  ItemWorkflow <: BatchActionItemWorkflow: {ClassTag, IsWorkflow},
  Activities <: BatchActionActivities: {ClassTag, IsActivity}
] extends BatchActionWorkflow {

  def queue: TemporalQueue = TemporalQueue.Default

  val childWorkflowRunTimeout: Duration = Duration.ofMinutes(10)
  val activityStartToCloseTimeout: Duration = Duration.ofMinutes(2)

  private val activities = ZWorkflow
    .newActivityStub[Activities](
      ZActivityOptions
        .withStartToCloseTimeout(activityStartToCloseTimeout)
        .withScheduleToCloseTimeout(Duration.ofMinutes(10))
        .withTaskQueue(queue.queueName)
        .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(1))
    )

  override def execute(param: BatchActionWorkflowParam): ResponseUnit = {
    val itemActionWorkflow = ZWorkflow
      .newChildWorkflowStub[ItemWorkflow](
        ZChildWorkflowOptions
          .withWorkflowId(ZWorkflow.randomUUID.toString)
          .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(1))
          .withWorkflowRunTimeout(childWorkflowRunTimeout)
          .withParentClosePolicy(ParentClosePolicy.PARENT_CLOSE_POLICY_TERMINATE)
      )

    param.batchActionItemIds.map { itemId =>
      ZChildWorkflowStub.execute(itemActionWorkflow.execute(BatchActionItemWorkflowParam(itemId, param.actionType)))
    }

    try {
      ZActivityStub.execute(activities.postExecute(param))
    } catch {
      case e: ActivityFailure =>
        scribe.error(s"Catch activity failure ${e.getCause}", e)
        ZActivityStub.execute(
          activities.markPostExecuteAsFailed(
            BatchActionMarkActionFailParam(
              param.batchActionId,
              BatchActionItemServerError(e.getCause.getMessage)
            )
          )
        )
    }
  }

}

trait BatchActionWorkflowImplCompanion[
  Interface <: BatchActionWorkflow: {ClassTag, IsWorkflow},
  Implementation <: Interface: {ClassTag, ExtendsWorkflow, IsConcreteClass, HasPublicNullaryConstructor}
] {

  def queue: TemporalQueue = TemporalQueue.Default

  lazy val instance = WorkflowImpl[Interface, Implementation](queue)

}
