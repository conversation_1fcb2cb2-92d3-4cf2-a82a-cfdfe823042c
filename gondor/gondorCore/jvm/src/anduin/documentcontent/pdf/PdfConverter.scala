// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.documentcontent.pdf

import scala.annotation.unused

import zio.{Task, ZIO}

import anduin.serverless.common.ServerlessModels.{S3Access, ServerlessTimeoutError}
import anduin.serverless.functions.LibreOfficeServerless
import anduin.serverless.models.LibreOffice.{PdfConvertRequest, PdfConvertResponse}
import com.anduin.stargazer.service.utils.ZIOUtils

object PdfConverter {

  trait PdfConvertException extends Exception

  val internalErrorMessage = "Internal error"

  object InternalErrorException extends PdfConvertException {
    override def getMessage: String = "Serverless internal error"
  }

  object TimeoutException extends PdfConvertException {
    override def getMessage: String = "Serverless timed out"
  }

  def convertToPdfViaServerless(
    libreOfficeServerless: LibreOfficeServerless,
    s3Access: S3Access,
    inputStorageId: String,
    outputStorageId: String,
    @unused password: Option[String] = None // Not in used for now
  ): Task[PdfConvertResponse] = {
    for {
      resp <- libreOfficeServerless
        .convertToPdf(
          PdfConvertRequest(
            s3Access = s3Access,
            inputKey = inputStorageId,
            outputKey = outputStorageId
          )
        )
        .catchSome { case _: ServerlessTimeoutError =>
          ZIO.fail(TimeoutException)
        }
      _ <- ZIOUtils.unless(resp.message != internalErrorMessage)(ZIO.fail(InternalErrorException))
    } yield resp
  }

}
