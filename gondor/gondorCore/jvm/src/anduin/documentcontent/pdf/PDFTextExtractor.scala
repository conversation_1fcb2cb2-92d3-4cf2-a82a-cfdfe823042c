// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.documentcontent.pdf

import io.circe.parser.decode
import zio.{Task, ZIO}

import anduin.model.document.DocumentStorageId
import anduin.serverless.common.ServerlessModels.S3Access
import anduin.serverless.functions.{DocumentSplitServerless, MuPDFServerless}
import anduin.serverless.models.DocSplit.*
import anduin.serverless.models.MuPdf.{
  ExtractTextPerPagesRequest,
  ExtractTextPerPagesResponse,
  InvalidOrDamagedPdfErrorStr,
  InvalidPasswordErrorStr
}
import anduin.storageservice.s3.S3Service
import anduin.utils.stream.ZStreamIOUtils

object PDFTextExtractor {

  def getPageRangesByTextPattern(
    documentSplitServerless: DocumentSplitServerless,
    storageId: DocumentStorageId,
    s3Access: S3Access,
    textPattern: String
  ): Task[GetPageRangesByTextPatternResponse] = {
    documentSplitServerless
      .getPageRangesByTextPattern(
        GetPageRangesByTextPatternRequest(
          storageId = storageId.id,
          s3Access = s3Access,
          textPattern = textPattern
        )
      )
  }

  def extractTextWithPageRanges(
    documentSplitServerless: DocumentSplitServerless,
    storageId: DocumentStorageId,
    s3Access: S3Access,
    pageRanges: List[PageRange],
    pageArea: ExtractTextWithPageRangesRequest.PageArea
  ): Task[ExtractTextWithPageRangesResponse] = {
    documentSplitServerless
      .extractTextWithPageRanges(
        ExtractTextWithPageRangesRequest(
          storageId = storageId.id,
          s3Access = s3Access,
          pageRanges = pageRanges,
          pageArea = pageArea
        )
      )
  }

  trait ExtractTextFromPdfException extends Exception

  object InvalidPasswordException extends ExtractTextFromPdfException {
    override def getMessage: String = InvalidPasswordErrorStr
  }

  object InvalidOrDamagedPdfException extends ExtractTextFromPdfException {
    override def getMessage: String = InvalidOrDamagedPdfErrorStr
  }

  def getExtractTextPerPagesResult(
    s3Service: S3Service,
    s3Access: S3Access,
    outputStorageId: DocumentStorageId
  ): Task[Option[List[String]]] = {
    ZIO.scoped {
      for {
        existed <- s3Service.checkExisted(outputStorageId, s3Access.bucket)
        texts <-
          if (!existed) {
            ZIO.succeed(None)
          } else {
            s3Service
              .downloadFromS3Scoped(s3Access.bucket, outputStorageId.id)
              .flatMap(ZStreamIOUtils.toString)
              .flatMap(jsonStr => ZIO.fromEither(decode[List[String]](jsonStr)).map(Some.apply))
          }
      } yield texts
    }
  }

  def runExtractTextPerPages(
    muPdfServerless: MuPDFServerless,
    s3Access: S3Access,
    pdfStorageId: DocumentStorageId,
    outputStorageId: DocumentStorageId,
    passwordOpt: Option[String] = None
  ): Task[Either[ExtractTextFromPdfException, ExtractTextPerPagesResponse]] = {
    for {
      response <- muPdfServerless.extractTextPerPage(
        ExtractTextPerPagesRequest(
          s3Access = s3Access,
          pdfStorageId = pdfStorageId.id,
          passwordOpt = passwordOpt,
          outputStorageId = outputStorageId.id
        )
      )
      result <- response.error match {
        case None                              => ZIO.succeed(Right(response))
        case Some(InvalidPasswordErrorStr)     => ZIO.succeed(Left(InvalidPasswordException))
        case Some(InvalidOrDamagedPdfErrorStr) => ZIO.succeed(Left(InvalidOrDamagedPdfException))
        case Some(e)                           => ZIO.fail(new RuntimeException(e))
      }
    } yield result
  }

}
