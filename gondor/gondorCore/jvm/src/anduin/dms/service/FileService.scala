// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.dms.service

import java.io.InputStream
import java.time.Instant

import squants.information.{Bytes, Information}
import sttp.model.MediaType
import zio.stream.Stream
import zio.{Scope, Task, ZIO}

import anduin.account.profile.UserProfileService
import anduin.dms.*
import anduin.dms.file.*
import anduin.dms.file.FileStateVersionOperations.FileStateVersionData
import anduin.dms.file.event.FileEventStoreOperations
import anduin.dms.file.state.{FileState, FileStateStoreOperations}
import anduin.dms.file.version.state.FileVersionModel
import anduin.dms.file.viewertracking.DmsViewerTrackingService
import anduin.dms.folder.*
import anduin.dms.folder.event.FolderEventStoreOperations
import anduin.dms.folder.state.FolderStateStoreOperations
import anduin.dms.service.DmsPermissionService.ModifyPermissionsResult
import anduin.dms.service.FileService.{FileIdWithVersionOpt, PermissionModMode, parallelism}
import anduin.dms.tracking.DmsTrackingActivityType
import anduin.dms.upload.{FileUploadHandler, FileUploadService, FileUploadUtils}
import anduin.fdb.record.model.{RecordIO, RecordReadIO}
import anduin.fdb.record.{FDBCluster, FDBCommonDatabase, FDBOperations, FDBRecordDatabase}
import anduin.id.upload.BatchUploadId
import anduin.id.user.UserRestrictedId
import anduin.model.common.user.UserId
import anduin.model.document.DocumentStorageId
import anduin.model.id.*
import anduin.model.id.dms.DmsChannel
import anduin.protobuf.flow.file.{FileFolderPermission, FileFolderPermissionMap}
import anduin.radix.RadixId
import anduin.refined.Refined
import anduin.service.{AuthenticatedRequestContext, CommonResponse, GeneralServiceException, RequestContext}
import anduin.storageservice.common.FileContentOrigin
import anduin.storageservice.s3.S3Service
import anduin.storageservice.zip.ZipService
import anduin.team.TeamService
import anduin.util.FilenameUtils
import com.anduin.stargazer.endpoints.*
import com.anduin.stargazer.service.FileServiceEndpoints.{
  UploadFilesToUserTemporaryFolderParams,
  UploadFilesToUserTemporaryFolderResp
}
import com.anduin.stargazer.service.file.tracking.*
import com.anduin.stargazer.service.utils.ZIOUtils
import com.anduin.stargazer.service.{GondorBackendConfig, GondorConfig}

final case class FileService(
  gondorConfig: GondorConfig,
  backendConfig: GondorBackendConfig,
  zipService: ZipService,
  s3Service: S3Service,
  teamService: TeamService,
  dmsViewerTrackingService: DmsViewerTrackingService,
  dmsPermissionService: DmsPermissionService,
  dmsDeleteService: DmsDeleteService,
  fileUploadService: FileUploadService,
  userProfileService: UserProfileService
) {

  private type UserOrTeamId = Either[UserId, TeamId]

  private val s3Config = s3Service.s3Config

  private val maxUserBatchSize = 256

  private val maxFileBatchSize = 1000

  val s3Bucket: String = s3Config.bucket

  val uploadFilesToUserTemporaryFolderHandler: FileUploadHandler =
    FileUploadHandler[UploadFilesToUserTemporaryFolderParams, UploadFilesToUserTemporaryFolderResp](
      createUploadFilesToUserTempFolder,
      completeUploadFilesToUserTempFolder
    )

  private def validateUploadUserTempFiles(
    input: FileUploadHandler.CreateInput[UploadFilesToUserTemporaryFolderParams]
  ) = {
    def validateSingleFileTask = ZIOUtils.validate(input.files.size == 1)(
      GeneralServiceException(s"Upload files to user temp folder expected one file, got ${input.files}")
    )
    input.params.purpose match {
      case UploadFilesToUserTemporaryFolderParams.Purpose.CreateDataRoomWithTermsOfAccess => validateSingleFileTask
      case UploadFilesToUserTemporaryFolderParams.Purpose.ModifyDataRoomTermsOfAccess     => validateSingleFileTask
      case UploadFilesToUserTemporaryFolderParams.Purpose.ImportFundDataDocuments         => ZIO.unit
    }
  }

  private def createUploadFilesToUserTempFolder(
    input: FileUploadHandler.CreateInput[UploadFilesToUserTemporaryFolderParams]
  ): Task[BatchUploadId] = {
    val actor = input.actor
    val files = input.files
    for {
      _ <- ZIO.logInfo(s"$actor batch uploads ${files.length} files to their temporary folder")
      _ <- validateUploadUserTempFiles(input)
      _ <- fileUploadService.createDirectUploadUnsafe(
        input.batchUploadId,
        input.params.apiName,
        Some(input.params.toJsonString),
        files,
        emptyFolders = Seq.empty,
        actor = actor
      )
    } yield input.batchUploadId
  }

  private def completeUploadFilesToUserTempFolder(
    input: FileUploadHandler.CompleteInput[UploadFilesToUserTemporaryFolderParams]
  ): Task[UploadFilesToUserTemporaryFolderResp] = {
    val actor = input.actor
    for {
      temporaryFolderId <- createUserTemporaryFolderIfNeeded(actor)(
        using DmsFeature.Public
      )
      filesInfo <- ZIOUtils.foreachParN(4)(input.files) { file =>
        for {
          fileName <- FileUploadUtils.fileNameTask(file.filePath)
          fileId <- uploadFile(
            parentFolderId = temporaryFolderId,
            fileName = fileName,
            content = FileContentOrigin.FromStorageId(file.storageId, file.bucket),
            uploader = actor
          )(
            using DmsFeature.Public
          )
          fileSize <- getFileSize(fileId)
          parentFolderOpt = FilenameUtils.getParentFolderName(file.filePath)
        } yield FileInfo(
          itemId = fileId,
          name = fileName,
          fileSize = Some(fileSize),
          parentFolderName = parentFolderOpt
        )
      }
      _ <- fileUploadService.completeDirectUpload(input.batchUploadId, actor)
    } yield UploadFilesToUserTemporaryFolderResp(files = filesInfo.toList)
  }

  def modifyFolderPermissions(
    actor: UserId,
    folderId: FolderId,
    changes: PermissionChanges,
    mode: PermissionModMode = PermissionModMode.Validate()
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    for {
      _ <- FDBRecordDatabase.transact(DmsPermissionOperations.Production) { permissionOps =>
        for {
          _ <- RecordIO.validate {
            val hasNewUserOwner = changes.permissionMap.userPermissions.exists(_._2 == FileFolderPermission.Own)
            val hasNewTeamOwner = changes.permissionMap.teamPermissions.exists(_._2 == FileFolderPermission.Own)
            mode == PermissionModMode.Unsafe || !hasNewUserOwner && !hasNewTeamOwner
          } {
            new RuntimeException("Must change folder permissions recursively if adding new owners")
          }
          updatedState <- permissionOps.modifyFolderPermissions(
            actor,
            folderId,
            changes,
            mode
          )
        } yield updatedState
      }
    } yield ()
  }

  def modifyFilePermissions(
    actor: UserId,
    fileId: FileId,
    changes: PermissionChanges,
    mode: PermissionModMode = PermissionModMode.Validate()
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    for {
      _ <- FDBRecordDatabase.transact(DmsPermissionOperations.Production) {
        _.modifyFilePermissions(
          actor,
          fileId,
          changes,
          mode
        )
      }
    } yield ()
  }

  def modifyPermissionsRecursively(
    actor: UserId,
    rootFolderId: FolderId,
    changes: PermissionChanges,
    mode: PermissionModMode,
    actorIp: Option[String] = None,
    metadata: Map[String, String] = Map.empty
  )(
    using dmsFeature: DmsFeature
  ): Task[ModifyPermissionsResult] = {
    dmsPermissionService.modifyPermissionsRecursively(
      actor,
      rootFolderId,
      changes,
      mode,
      actorIp,
      metadata
    )
  }

  def modifyAssetPermissionsRecursively(
    actor: UserId,
    changes: Map[UserOrTeamId, AssetPermissionChanges],
    mode: PermissionModMode,
    shouldIgnoreDeletedFiles: Boolean = false,
    actorIp: Option[String],
    modifyFolderPermissionMetadata: Map[FolderId, Map[String, String]] = Map.empty,
    modifyFilePermissionMetadata: Map[FileId, Map[String, String]] = Map.empty
  )(
    using dmsFeature: DmsFeature
  ): Task[ModifyPermissionsResult] = {
    dmsPermissionService.modifyAssetPermissionsRecursively(
      actor = actor,
      changes = changes,
      mode = mode,
      shouldIgnoreDeletedFiles = shouldIgnoreDeletedFiles,
      actorIp,
      modifyFolderPermissionMetadata,
      modifyFilePermissionMetadata
    )
  }

  def modifyWatermarkExceptionsUnsafe(
    fileIds: Seq[FileId],
    isWatermarkException: Boolean,
    actor: UserId,
    actorIp: Option[String]
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    for {
      _ <- FDBRecordDatabase.transact(FileFlowOperations.Production) { fileFlowOps =>
        RecordIO.parTraverseN(4)(fileIds) { fileId =>
          fileFlowOps.modifyWatermarkExceptionUnsafe(
            fileId,
            isWatermarkException,
            actor,
            actorIp
          )
        }
      }
    } yield ()
  }

  def getFileStorageId(
    actor: UserId,
    fileId: FileId,
    purpose: DmsTrackingActivityType,
    httpContextOpt: Option[RequestContext],
    includeDeleted: Boolean = false,
    versionIndexOpt: Option[Int] = None,
    requiredPermissionOpt: Option[FileFolderPermission] = None
  )(
    using dmsFeature: DmsFeature
  ): Task[DocumentStorageId] = {
    getFileStorageIdWithSessionTimestamp(
      actor,
      fileId,
      purpose,
      httpContextOpt,
      includeDeleted,
      versionIndexOpt,
      requiredPermissionOpt
    ).map(_._1)
  }

  def getFileStorageIdWithSessionTimestamp(
    actor: UserId,
    fileId: FileId,
    purpose: DmsTrackingActivityType,
    httpContextOpt: Option[RequestContext],
    includeDeleted: Boolean = false,
    versionIndexOpt: Option[Int] = None,
    requiredPermissionOpt: Option[FileFolderPermission] = None
  )(
    using dmsFeature: DmsFeature
  ): Task[(DocumentStorageId, Option[Instant])] = {
    for {
      versionIndex <- versionIndexOpt.fold(getCurrentVersion(actor, includeDeleted)(fileId))(
        ZIO.succeed
      )
      storageId <- FDBRecordDatabase.transact(FileStateVersionOperations.Production) {
        _.getStorageId(
          actor = actor,
          fileId = fileId,
          purpose = purpose,
          versionIndexOpt = Some(versionIndex),
          includeDeleted = includeDeleted,
          requiredPermissionOpt = requiredPermissionOpt
        )
      }
      sessionOpt <- ZIOUtils.whenOption(dmsFeature.enabledViewerTracking) {
        dmsViewerTrackingService.recordFirstEventReturnSession(
          actor,
          fileId,
          versionIndex,
          httpContextOpt.flatMap(_.getClientIP),
          purpose
        )
      }
    } yield storageId -> sessionOpt
  }

  // Folder Apis
  /** Creates a system folder for a channel.
    *
    * @param permissionOpts
    *   If ``permissionOpts`` is empty, default permissions are inherited from the folder channel.
    */
  def createSystemFolderForChannel[FC <: RadixId: DmsChannel](
    channel: FC,
    folderName: String,
    creator: UserId,
    permissionOpts: FileFolderPermissionMap
  )(
    using dmsFeature: DmsFeature
  ): Task[FolderId] = {
    FDBRecordDatabase.transact(AddFolderOperations.Production) {
      _.createSystemFolderForChannel(
        channel,
        folderName,
        creator,
        permissionOpts
      )
    }
  }

  /** Creates a user folder for a channel.
    *
    * @param permissionOpts
    *   If ``permissionOpts`` is empty, default permissions are inherited from the parent.
    */
  def createEmptyFolder(
    parentFolderId: FolderId,
    folderName: String,
    creator: UserId,
    suffixToUse: Option[Refined[FolderId]] = None,
    permissionOpts: Option[FileFolderPermissionMap] = None,
    setAuthorAsOwner: Boolean = false
  )(
    using dmsFeature: DmsFeature
  ): Task[FolderId] = {
    for {
      folderId <- FDBRecordDatabase.transact(AddFolderOperations.Production) {
        _.createEmptyFolder(
          parentFolderId,
          folderName,
          creator,
          suffixToUse,
          permissionOpts,
          setAuthorAsOwner
        )
      }
      _ <- modifyFolderLastUpdatedAt(creator)(parentFolderId)
    } yield folderId
  }

  def createEmptyFolderUnsafe(
    parentFolderId: FolderId,
    folderName: String,
    creator: UserId,
    suffixToUse: Option[Refined[FolderId]] = None,
    permissionOpts: Option[FileFolderPermissionMap] = None,
    setAuthorAsOwner: Boolean = false
  )(
    using dmsFeature: DmsFeature
  ): Task[FolderId] = {
    for {
      folderId <- FDBRecordDatabase.transact(AddFolderOperations.Production) {
        _.createEmptyFolderUnsafe(
          parentFolderId,
          folderName,
          creator,
          suffixToUse,
          permissionOpts,
          setAuthorAsOwner
        )
      }
    } yield folderId
  }

  def copyFileInSameFeature(
    actor: UserId,
    originFileId: FileId,
    folderId: FolderId,
    ctx: Option[RequestContext],
    fileNameOpt: Option[String] = None,
    includeDeleted: Boolean = false
  )(
    using dmsFeature: DmsFeature
  ): Task[FileId] = {
    copyFile(
      actor,
      originFileId,
      folderId,
      ctx,
      fileNameOpt,
      includeDeleted
    )(
      using sourceFeature = dmsFeature,
      targetFeature = dmsFeature
    )
  }

  def copyFile(
    actor: UserId,
    originFileId: FileId,
    folderId: FolderId,
    ctx: Option[RequestContext],
    fileNameOpt: Option[String] = None,
    includeDeleted: Boolean = false
  )(
    using sourceFeature: DmsFeature,
    targetFeature: DmsFeature
  ): Task[FileId] = {
    for {
      oldStorageId <- getFileStorageId(
        actor,
        originFileId,
        DmsTrackingActivityType.Internal,
        ctx,
        includeDeleted
      )(
        using sourceFeature
      )
      fileName <- fileNameOpt.fold[Task[String]](
        getFileName(actor, includeDeleted)(originFileId)(
          using sourceFeature
        )
      ) { fileName =>
        ZIO.succeed(FilenameUtils.autoSanitizeName(fileName))
      }
      fileContentOrigin = FileContentOrigin.FromStorageId(oldStorageId, s3Bucket)
      newFileId <- uploadFile(
        folderId,
        fileName,
        fileContentOrigin,
        actor,
        uploaderIp = ctx.flatMap(_.getClientIP)
      )(
        using targetFeature
      )
      _ <- modifyFolderLastUpdatedAt(actor)(folderId)(
        using targetFeature
      )
    } yield newFileId
  }

  def updateFileName(
    actor: UserId,
    fileId: FileId,
    newName: String,
    actorIp: Option[String] = None
  )(
    using dmsFeature: DmsFeature
  ): Task[(String, CommonResponse)] = {
    for {
      oldName <- getFileName(actor)(fileId)
      res <-
        if (FilenameUtils.getExtension(oldName) == FilenameUtils.getExtension(newName)) {
          for {
            _ <- FDBRecordDatabase.transact(RenameFileOperations.Production) {
              _.updateFileName(
                actor,
                fileId,
                newName,
                actorIp
              )
            }
          } yield CommonResponse(ok = true, message = "")
        } else {
          ZIO.succeed(
            CommonResponse(ok = false, message = "New file name should keep file extension")
          )
        }
    } yield oldName -> res
  }

  def updateFolderName(
    actor: UserId,
    folderId: FolderId,
    newName: String
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    for {
      _ <- FDBRecordDatabase.transact(RenameFolderOperations.Production) {
        _.updateFolderName(
          actor,
          folderId,
          newName
        )
      }
    } yield ()
  }

  def deleteFilesAndFolders(
    actor: UserId,
    folderIds: Seq[FolderId],
    fileIds: Seq[FileId]
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    dmsDeleteService.deleteFilesAndFolders(
      actor,
      folderIds,
      fileIds
    )
  }

  def deleteFilesAndFoldersRecursively(
    actor: UserId,
    folderIds: Seq[FolderId],
    fileIds: Seq[FileId],
    isPermanent: Boolean = false,
    actorIp: Option[String] = None
  )(
    using dmsFeature: DmsFeature
  ): Task[(deletedFiles: Seq[FileId], deletedFolders: Seq[FolderId])] = {
    dmsDeleteService.deleteFilesAndFoldersRecursively(
      actor = actor,
      folderIds = folderIds,
      fileIds = fileIds,
      isPermanent = isPermanent,
      actorIp = actorIp
    )
  }

  def deleteFilesAndFoldersRecursivelyUnsafe(
    actor: UserId,
    folderIds: Seq[FolderId],
    fileIds: Seq[FileId],
    actorIp: Option[String] = None
  )(
    using dmsFeature: DmsFeature
  ): Task[(deletedFiles: Seq[FileId], deletedFolders: Seq[FolderId])] = {
    dmsDeleteService.deleteFilesAndFoldersRecursivelyUnsafe(
      actor,
      folderIds,
      fileIds,
      actorIp
    )
  }

  def undeleteFilesAndFolders(
    actor: UserId,
    folderIds: Seq[FolderId],
    fileIds: Seq[FileId]
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    for {
      _ <- FDBRecordDatabase.transact(DmsDeleteOperations.Production) {
        _.restoreFilesAndFolders(
          actor,
          folderIds,
          fileIds
        )
      }
    } yield ()
  }

  private def moveTempFileToChecksumFile(
    tempStorageId: DocumentStorageId,
    destBucket: String,
    checksumSHA256: String
  ): Task[DocumentStorageId] = {
    val checksumStorageId = DocumentStorageId.generateChecksumStorageId(checksumSHA256)
    for {
      _ <- ZIO.logInfo(s"Moving temporary file $tempStorageId to checksum file $checksumStorageId in bucket $destBucket")
      _ <- s3Service.copyObject(
        tempStorageId,
        destBucket,
        checksumStorageId,
        destBucket
      )
      _ <- ZIOUtils.when(tempStorageId != checksumStorageId)(
        s3Service.deleteObjects(
          destBucket,
          Seq(tempStorageId.id)
        )
      )
    } yield checksumStorageId
  }

  private def computeChecksumSHA256(
    storageId: DocumentStorageId,
    bucket: String
  ): Task[String] = {
    s3Service.computeSHA256ViaServerless(
      storageId,
      bucket
    )
  }

  private def uploadNewFileToS3(
    actor: UserId,
    fileId: FileId,
    fileName: String,
    source: Stream[Throwable, Byte],
    contentType: MediaType,
    destBucket: String
  ): Task[DocumentStorageId] = {
    val tempStorageId = s3Service.generateRandomStorageId(Some(fileId), fileName)
    for {
      _ <- s3Service.uploadStreamToS3(
        tempStorageId,
        actor,
        source,
        contentType.toString(),
        destBucket
      )
      checksumSHA256 <- computeChecksumSHA256(tempStorageId, destBucket)
      checksumStorageId <- moveTempFileToChecksumFile(
        tempStorageId,
        destBucket,
        checksumSHA256
      )
    } yield checksumStorageId
  }

  // Mark as public for migration purpose
  def copyFromNonChecksumStorageIdFile(
    sourceStorageId: DocumentStorageId,
    sourceBucket: String,
    destBucket: String
  ): Task[DocumentStorageId] = {
    for {
      checksumSHA256 <- computeChecksumSHA256(sourceStorageId, sourceBucket)
      checksumStorageId = DocumentStorageId.generateChecksumStorageId(checksumSHA256)
      _ <- s3Service.copyObject(
        sourceStorageId,
        sourceBucket,
        checksumStorageId,
        destBucket
      )
    } yield checksumStorageId
  }

  private def uploadFileContentOriginToS3[A](
    actor: UserId,
    content: FileContentOrigin,
    fileId: FileId,
    fileName: String,
    destBucket: String
  )(
    postUploadTask: (DocumentStorageId, Long) => Task[A]
  ): Task[A] = {
    val uploadTask =
      content match {
        case FileContentOrigin.FromSource(source, contentType) =>
          uploadNewFileToS3(actor, fileId, fileName, source, contentType, destBucket)

        case fromId: FileContentOrigin.FromStorageId =>
          if (fromId.storageId.isChecksumStorageId) {
            if (fromId.bucket == destBucket) {
              ZIO.attempt(fromId.storageId)
            } else {
              val destStorageId = fromId.storageId
              s3Service
                .copyObject(
                  fromId.storageId,
                  fromId.bucket,
                  destStorageId,
                  destBucket
                )
                .map(_ => destStorageId)
            }
          } else {
            copyFromNonChecksumStorageIdFile(
              fromId.storageId,
              fromId.bucket,
              destBucket
            )
          }
      }

    for {
      storageId <- uploadTask
      metadataOpt <- s3Service.getObjectMetadataOpt(destBucket, storageId.id)
      byteSize = metadataOpt.fold(0L)(_.contentLength)
      res <- postUploadTask(storageId, byteSize)
    } yield res
  }

  def uploadNewVersion(
    actor: UserId,
    fileId: FileId,
    content: FileContentOrigin,
    fileNameOpt: Option[String], // None to keep latest version file name
    metadata: Map[String, String] = Map.empty,
    actorIp: Option[String] = None,
    originalFileNameOpt: Option[String] = None
  )(
    using dmsFeature: DmsFeature
  ): Task[Int] = {
    uploadNewVersionInternal(
      actor,
      fileId,
      content,
      fileNameOpt,
      metadata,
      actorIp,
      originalFileNameOpt
    ).map(_._2.versionIndex)
  }

  def uploadNewVersionInternal(
    actor: UserId,
    fileId: FileId,
    content: FileContentOrigin,
    fileNameOpt: Option[String], // None to keep latest version file name
    metadata: Map[String, String] = Map.empty,
    actorIp: Option[String] = None,
    originalFileNameOpt: Option[String] = None
  )(
    using dmsFeature: DmsFeature
  ): Task[(FileState, FileVersionModel)] = {
    for {
      fileName <- fileNameOpt.fold(
        getFileName(actor)(fileId)
      )(ZIO.succeed(_))
      (fileState, fileVersion) <- uploadFileContentOriginToS3(
        actor,
        content,
        fileId,
        fileName,
        s3Bucket
      ) { (newStorageId, newFileSize) =>
        FDBRecordDatabase.transact(UploadNewVersionOperations.Production) {
          _.uploadNewVersion(
            actor,
            fileId,
            fileName,
            newStorageId,
            newFileSize,
            metadata,
            actorIp,
            originalFileNameOpt
          )
        }
      }
      _ <- modifyFileLastUpdatedAt(actor)(fileId)
    } yield (fileState, fileVersion)
  }

  def uploadNewVersionFromFile(
    fileId: FileId,
    fromFileId: FileId,
    actor: UserId,
    ctx: Option[AuthenticatedRequestContext]
  )(
    using dmsFeature: DmsFeature
  ): Task[Int] = {
    for {
      sourceStorageId <- getFileStorageId(
        actor,
        fileId = fromFileId,
        purpose = DmsTrackingActivityType.Internal,
        httpContextOpt = ctx
      )
      versionIndex <- uploadNewVersion(
        actor,
        fileId,
        FileContentOrigin.FromStorageId(sourceStorageId, s3Bucket),
        fileNameOpt = None
      )
    } yield versionIndex
  }

  def uploadFile(
    parentFolderId: FolderId,
    fileName: String,
    content: FileContentOrigin,
    uploader: UserId,
    metadata: Map[String, String] = Map.empty,
    permissionOpt: Option[FileFolderPermissionMap] = None,
    setAuthorAsOwner: Boolean = false,
    uploaderIp: Option[String] = None,
    actionType: DmsUploadActionType = DmsUploadActionType.Upload,
    originalFileIdOpt: Option[FileId] = None,
    originalFileNameOpt: Option[String] = None
  )(
    using dmsFeature: DmsFeature
  ): Task[FileId] = {
    uploadFileInternal(
      parentFolderId,
      fileName,
      content,
      uploader,
      metadata,
      permissionOpt,
      setAuthorAsOwner,
      uploaderIp,
      actionType,
      originalFileIdOpt,
      originalFileNameOpt
    ).map(_._1)
  }

  def uploadFileInternal(
    parentFolderId: FolderId,
    fileName: String,
    content: FileContentOrigin,
    uploader: UserId,
    metadata: Map[String, String] = Map.empty,
    permissionOpt: Option[FileFolderPermissionMap] = None,
    setAuthorAsOwner: Boolean = false,
    uploaderIp: Option[String] = None,
    actionType: DmsUploadActionType = DmsUploadActionType.Upload,
    originalFileIdOpt: Option[FileId] = None,
    originalFileNameOpt: Option[String] = None
  )(
    using dmsFeature: DmsFeature
  ): Task[(FileId, FileState, FileVersionModel)] = {
    for {
      fileId <- ZIO.attempt {
        FileIdFactory.generate(parentFolderId)
      }
      sanitizedName = FilenameUtils.autoSanitizeName(fileName)
      (_, fileState, fileVersion) <- uploadFileContentOriginToS3(
        uploader,
        content,
        fileId,
        sanitizedName,
        s3Bucket
      ) { (storageId, fileSize) =>
        FDBRecordDatabase.transact(AddFileOperations.Production) {
          _.addFile(
            uploader,
            fileId,
            sanitizedName,
            storageId,
            fileSize,
            permissionOpt,
            setAuthorAsOwner,
            uploaderIp,
            metadata,
            actionType,
            originalFileIdOpt,
            originalFileNameOpt
          )
        }
      }
      _ <- ZIO.logInfo(s"Uploaded successfully to file service, fileId = $fileId")
    } yield (fileId, fileState, fileVersion)
  }

  /** Legacy API. Avoid using this. Use [[uploadFile]] instead.
    */
  def uploadFileToFolder(
    fileId: FileId,
    fileName: String,
    content: FileContentOrigin,
    uploader: UserId,
    metadata: Map[String, String] = Map.empty,
    permissionOpt: Option[FileFolderPermissionMap] = None
  )(
    using dmsFeature: DmsFeature
  ): Task[FileId] = {
    val sanitizedName = FilenameUtils.autoSanitizeName(fileName)
    for {
      _ <- uploadFileContentOriginToS3(
        uploader,
        content,
        fileId,
        sanitizedName,
        s3Bucket
      ) { (storageId, fileSize) =>
        FDBRecordDatabase.transact(AddFileOperations.Production) {
          _.addFile(
            actor = uploader,
            fileId = fileId,
            fileName = sanitizedName,
            storageId = storageId,
            fileSize = fileSize,
            permissionOpt = permissionOpt,
            metadata = metadata
          )
        }
      }
      _ <- ZIO.logInfo(s"Uploaded successfully to file service, fileId = $fileId")
    } yield fileId
  }

  def getFileSource(
    actor: UserId,
    fileId: FileId
  )(
    using dmsFeature: DmsFeature
  ): ZIO[Scope, Throwable, Stream[Throwable, Byte]] = {
    for {
      storageId <- getFileStorageId(
        actor,
        fileId,
        DmsTrackingActivityType.Internal,
        None
      )
      fileSourceAndMetaData <- s3Service.downloadFromS3Scoped(
        s3Bucket,
        storageId.id
      )
    } yield fileSourceAndMetaData
  }

  def getFileChecksumSHA256(
    actor: UserId,
    fileId: FileId,
    includeDeleted: Boolean = false,
    versionIndexOpt: Option[Int] = None
  )(
    using dmsFeature: DmsFeature
  ): Task[String] = {
    for {
      fileInfo <- FDBRecordDatabase.transact(FileStateVersionOperations.Production)(
        _.getFileInfo(actor, fileId, versionIndexOpt, includeDeleted = includeDeleted)
      )
      checksum <- fileInfo.checksumSHA256.fold[Task[String]](
        computeChecksumSHA256(fileInfo, actor)
      )(ZIO.succeed(_))
    } yield checksum
  }

  private def computeChecksumSHA256(
    fileInfo: FileStateVersionData,
    actor: UserId
  )(
    using dmsFeature: DmsFeature
  ): Task[String] = {
    val fileId = fileInfo.fileId
    for {
      _ <- ZIO.logInfo(s"User $actor is computing checksum for $fileId")
      checksumOpt = fileInfo.storageId.getChecksumSHA256Opt
      checksum <- checksumOpt.fold(
        for {
          computedChecksum <- computeChecksumSHA256(fileInfo.storageId, s3Bucket)
          _ <- FDBRecordDatabase.transact(FileFlowOperations.Production)(
            _.modifyChecksumSHA256(
              actor,
              fileId,
              Some(computedChecksum),
              Some(fileInfo.versionIndex)
            )
          )
        } yield computedChecksum
      )(ZIO.succeed(_))
    } yield checksum
  }

  def useFileInputStream[A](
    actor: UserId,
    fileId: FileId,
    httpContext: Option[AuthenticatedRequestContext]
  )(
    use: InputStream => Task[A]
  )(
    using dmsFeature: DmsFeature
  ): Task[A] = {
    for {
      storageId <- getFileStorageId(
        actor,
        fileId,
        DmsTrackingActivityType.Internal,
        httpContext
      )
      res <- s3Service.useS3InputStream(
        s3Bucket,
        storageId.id
      )(use)
    } yield res
  }

  /** Create a temporary folder that should be invisible for everyone. This is a place to store intermediate files that
    * should not be exposed to users.
    */
  def createUserTemporaryFolderIfNeeded(
    actor: UserId,
    suffix: Refined[FolderId] = FolderId.DefaultTemporaryPart,
    folderName: String = "tmp"
  )(
    using dmsFeature: DmsFeature
  ): Task[FolderId] = {
    val channel = UserRestrictedId(actor)
    val channelRootFolderId = FolderId.channelSystemFolderId(channel)
    for {
      isRootFolderExisting <- existFolder(actor)(channelRootFolderId)
      _ <- ZIOUtils.unless(isRootFolderExisting) {
        createSystemFolderForChannel(
          channel,
          channel.idString,
          actor,
          FileFolderPermissionMap().addUserPermissions(actor -> FileFolderPermission.Own)
        )
      }
      subFolderId = channelRootFolderId.addSuffix(suffix)
      isSubFolderExisting <- existFolder(actor)(subFolderId)
      _ <- ZIOUtils.unless(isSubFolderExisting) {
        createEmptyFolder(
          parentFolderId = channelRootFolderId,
          folderName = folderName,
          creator = actor,
          suffixToUse = Some(suffix)
        )
      }
    } yield subFolderId
  }

  def getFileCreated(
    actor: UserId,
    includeDeleted: Boolean = false
  )(
    fileId: FileId
  )(
    using dmsFeature: DmsFeature
  ): Task[CreatedInfo] = {
    FDBRecordDatabase.transact(FileFlowOperations.Production) {
      _.getCreated(actor, fileId, includeDeleted)
    }
  }

  def getFolderCreated(
    actor: UserId
  )(
    folderId: FolderId
  )(
    using dmsFeature: DmsFeature
  ): Task[CreatedInfo] = {
    FDBRecordDatabase.transact(FolderFlowOperations.Production) {
      _.getCreated(actor, folderId)
    }
  }

  def getFolderName(
    actor: UserId,
    includeDeleted: Boolean = false
  )(
    folderId: FolderId
  )(
    using dmsFeature: DmsFeature
  ): Task[String] = {
    FDBRecordDatabase.transact(FolderStateStoreOperations.Production) {
      _.getName(
        actor,
        folderId,
        includeDeleted
      )
    }
  }

  def batchGetFolderName(
    actor: UserId,
    includeDeleted: Boolean = false
  )(
    folderId: Seq[FolderId]
  )(
    using dmsFeature: DmsFeature
  ): Task[Map[FolderId, String]] = {
    FDBRecordDatabase.transact(FolderStateStoreOperations.Production) { folderStateOps =>
      RecordIO
        .parTraverseN(parallelism)(folderId.distinct) { folderId =>
          folderStateOps
            .getName(actor, folderId, includeDeleted)
            .map(folderId -> _)
        }
        .map(_.toMap)
    }
  }

  def getCurrentVersion(
    actor: UserId,
    includeDeleted: Boolean = false
  )(
    fileId: FileId
  )(
    using dmsFeature: DmsFeature
  ): Task[Int] = {
    FDBRecordDatabase.transact(FileStateStoreOperations.Production) {
      _.getState(
        actor = actor,
        id = fileId,
        includeDeleted = includeDeleted
      ).map(_.versionIndex)
    }
  }

  def getFileName(
    actor: UserId,
    includeDeleted: Boolean = false
  )(
    fileId: FileId,
    versionIndexOpt: Option[Int] = None
  )(
    using dmsFeature: DmsFeature
  ): Task[String] = {
    FDBRecordDatabase.transact(FileStateVersionOperations.Production) {
      _.getFileInfo(
        actor = actor,
        fileId = fileId,
        versionIndexOpt = versionIndexOpt,
        includeDeleted = includeDeleted
      ).map(_.name)
    }
  }

  def getLatestFilesName(
    actor: UserId,
    includeDeleted: Boolean = false
  )(
    fileIds: Seq[FileId]
  )(
    using dmsFeature: DmsFeature
  ): Task[Map[FileId, String]] = {
    FDBRecordDatabase
      .transact(FileStateVersionOperations.Production) { ops =>
        RecordIO.parTraverseN(parallelism)(fileIds) { fileId =>
          ops
            .getFileInfo(
              actor = actor,
              fileId = fileId,
              includeDeleted = includeDeleted
            )
            .map(fileInfo => fileId -> fileInfo.name)
        }
      }
      .map(_.toMap)
  }

  def getFileStates(
    fileList: Seq[FileIdWithVersionOpt],
    actor: UserId,
    requiredPermission: FileFolderPermission = FileFolderPermission.ViewOnly,
    includeDeleted: Boolean = false
  )(
    using DmsFeature
  ): Task[Seq[FileStateVersionData]] = {
    ZIO
      .foreach(fileList.grouped(maxFileBatchSize).toSeq) { files =>
        FDBRecordDatabase
          .transact(FileStateVersionOperations.Production) { fileOps =>
            RecordIO
              .parTraverseN(parallelism)(files) { (fileId, versionIndexOpt) =>
                fileOps
                  .getFileInfoOpt(
                    actor = actor,
                    fileId = fileId,
                    versionIndexOpt = versionIndexOpt,
                    requiredPermission = requiredPermission,
                    includeDeleted = includeDeleted
                  )
              }
              .map(_.flatten)
          }
      }
      .map(_.flatten)
  }

  def getFileStateMap(
    actor: UserId,
    files: Seq[FileId],
    purpose: DmsTrackingActivityType,
    includeDeleted: Boolean = false
  )(
    using dmsFeature: DmsFeature
  ): Task[Map[FileId, FileStateVersionData]] = {
    getFileStates(
      files.map(_ -> Option.empty[Int]),
      actor,
      purpose.getRequiredPermisison,
      includeDeleted
    ).map(_.map(s => s.fileId -> s).toMap)
  }

  def getFileInfo(
    actor: UserId,
    fileId: FileId,
    purpose: DmsTrackingActivityType,
    includeDeleted: Boolean = false,
    versionIndexOpt: Option[Int] = None
  )(
    using dmsFeature: DmsFeature
  ): Task[FileStateVersionData] = {
    val requiredPermission = purpose match {
      case DmsTrackingActivityType.View     => FileFolderPermission.ViewOnly
      case DmsTrackingActivityType.Download => FileFolderPermission.Read
      case DmsTrackingActivityType.Internal => FileFolderPermission.ViewOnly
    }
    FDBRecordDatabase.transact(FileStateVersionOperations.Production) {
      _.getFileInfo(
        actor,
        fileId,
        versionIndexOpt,
        requiredPermission,
        includeDeleted
      )
    }
  }

  def getFolderCreatedAt(
    actor: UserId
  )(
    folderId: FolderId
  )(
    using dmsFeature: DmsFeature
  ): Task[Option[Instant]] = {
    FDBRecordDatabase.transact(FolderFlowOperations.Production) {
      _.getCreated(actor, folderId).map(_.timestamp)
    }
  }

  def getFileCreatedAt(
    actor: UserId,
    includeDeleted: Boolean = false
  )(
    fileId: FileId
  )(
    using dmsFeature: DmsFeature
  ): Task[Option[Instant]] = {
    FDBRecordDatabase.transact(FileFlowOperations.Production) {
      _.getCreated(
        actor,
        fileId,
        includeDeleted
      ).map(_.timestamp)
    }
  }

  def getFolderCreatedBy(
    actor: UserId
  )(
    folderId: FolderId
  )(
    using dmsFeature: DmsFeature
  ): Task[UserId] = {
    FDBRecordDatabase.transact(FolderFlowOperations.Production) {
      _.getCreated(actor, folderId).map(_.actor)
    }
  }

  def getFileCreatedBy(
    actor: UserId,
    includeDeleted: Boolean = false
  )(
    fileId: FileId
  )(
    using dmsFeature: DmsFeature
  ): Task[UserId] =
    for {
      startTime <- ZIO.succeed(System.currentTimeMillis())
      result <- FDBRecordDatabase.transact(FileFlowOperations.Production) {
        _.getCreated(
          actor,
          fileId,
          includeDeleted
        ).map(_.actor)
      }
      endTime <- ZIO.succeed(System.currentTimeMillis())
      _ <- ZIO.logInfo(s"Get file created by time: ${endTime - startTime}")
    } yield result

  def getFolderPermissionMap(
    actor: UserId,
    includeDeleted: Boolean = false
  )(
    folderId: FolderId
  )(
    using dmsFeature: DmsFeature
  ): Task[FileFolderPermissionMap] = {
    FDBRecordDatabase.transact(FolderStateStoreOperations.Production) {
      _.getPermissionMap(
        actor,
        folderId,
        includeDeleted
      )
    }
  }

  def getFilePermissionMap(
    actor: UserId,
    includeDeleted: Boolean = false
  )(
    fileId: FileId
  )(
    using dmsFeature: DmsFeature
  ): Task[FileFolderPermissionMap] = {
    FDBRecordDatabase.transact(FileStateStoreOperations.Production) {
      _.getPermissionMap(
        actor,
        fileId,
        includeDeleted
      )
    }
  }

  def getFolderTeamPermission(
    actor: UserId,
    teamId: TeamId
  )(
    folderId: FolderId
  )(
    using dmsFeature: DmsFeature
  ): Task[Option[FileFolderPermission]] = {
    getFolderPermissionMap(actor)(folderId).map(_.teamPermissions.get(teamId))
  }

  def getFolderMaxTeamPermission(
    actor: UserId,
    teamIds: Set[TeamId]
  )(
    folderId: FolderId
  )(
    using DmsFeature
  ): Task[Option[FileFolderPermission]] = {
    getFolderPermissionMap(actor)(folderId).map { permissionMap =>
      teamIds.flatMap(permissionMap.teamPermissions.get).maxByOption(_.value)
    }
  }

  def batchGetFolderTeamPermission(
    actor: UserId,
    teamIds: Set[TeamId]
  )(
    folderId: FolderId
  )(
    using dmsFeature: DmsFeature
  ): Task[Map[TeamId, FileFolderPermission]] = {
    getFolderPermissionMap(actor)(folderId).map { folderPermission =>
      val teamPermission = folderPermission.teamPermissions
      teamIds
        .map { teamId => teamId -> teamPermission.get(teamId) }
        .collect { case (teamId, Some(permission)) =>
          teamId -> permission
        }
        .toMap
    }
  }

  def getFileTeamPermission(
    actor: UserId,
    teamId: TeamId
  )(
    fileId: FileId
  )(
    using dmsFeature: DmsFeature
  ): Task[Option[FileFolderPermission]] = {
    getFilePermissionMap(actor)(fileId).map(_.teamPermissions.get(teamId))
  }

  def getFileMaxTeamPermission(
    actor: UserId,
    teamIds: Set[TeamId]
  )(
    fileId: FileId
  )(
    using dmsFeature: DmsFeature
  ): Task[Option[FileFolderPermission]] = {
    getFilePermissionMap(actor)(fileId).map { permissionMap =>
      teamIds.flatMap(permissionMap.teamPermissions.get).maxByOption(_.value)
    }
  }

  def batchGetFileTeamPermission(
    actor: UserId,
    teamIds: Set[TeamId]
  )(
    fileId: FileId
  )(
    using dmsFeature: DmsFeature
  ): Task[Map[TeamId, FileFolderPermission]] = {
    getFilePermissionMap(actor)(fileId).map { filePermission =>
      val teamPermission = filePermission.teamPermissions
      teamIds
        .map { teamId => teamId -> teamPermission.get(teamId) }
        .collect { case (teamId, Some(permission)) =>
          teamId -> permission
        }
        .toMap
    }
  }

  def getFolderPermission(
    actor: UserId
  )(
    folderId: FolderId
  )(
    using dmsFeature: DmsFeature
  ): Task[Option[FileFolderPermission]] = {
    FDBRecordDatabase.transact(FolderStateStoreOperations.Production) {
      _.getPermission(actor, folderId)
    }
  }

  def batchGetFolderPermission(
    actor: UserId,
    userIds: Set[UserId]
  )(
    folderId: FolderId
  )(
    using dmsFeature: DmsFeature
  ): Task[Map[UserId, FileFolderPermission]] = {
    ZIO
      .foreach(userIds.grouped(maxUserBatchSize).toList) { batchUserIds =>
        FDBRecordDatabase.transact(FolderStateStoreOperations.Production) {
          _.batchGetPermission(actor, batchUserIds, folderId)
        }
      }
      .map(_.flatten.toMap)
  }

  def getFilePermission(
    actor: UserId,
    includeDeleted: Boolean = false
  )(
    fileId: FileId
  )(
    using dmsFeature: DmsFeature
  ): Task[Option[FileFolderPermission]] = {
    FDBRecordDatabase.transact(FileStateStoreOperations.Production) {
      _.getPermission(
        actor,
        fileId,
        includeDeleted
      )
    }
  }

  def batchGetFilePermission(
    actor: UserId,
    userIds: Set[UserId]
  )(
    fileId: FileId
  )(
    using dmsFeature: DmsFeature
  ): Task[Map[UserId, FileFolderPermission]] = {
    ZIO
      .foreach(userIds.grouped(maxUserBatchSize).toList) { batchUserIds =>
        FDBRecordDatabase.transact(FileStateStoreOperations.Production) {
          _.batchGetPermission(actor, batchUserIds, fileId)
        }
      }
      .map(_.flatten.toMap)
  }

  def getFolderDeletedOpt(
    actor: UserId
  )(
    folderId: FolderId
  )(
    using dmsFeature: DmsFeature
  ): Task[Option[DeleteRecord]] = {
    FDBRecordDatabase.transact(FolderStateStoreOperations.Production) {
      _.getDeletedOpt(actor, folderId)
    }
  }

  def getFileDeletedOpt(
    actor: UserId
  )(
    fileId: FileId
  )(
    using dmsFeature: DmsFeature
  ): Task[Option[DeleteRecord]] = {
    FDBRecordDatabase.transact(FileStateStoreOperations.Production) {
      _.getDeletedOpt(actor, fileId)
    }
  }

  def getSubFolders(
    actor: UserId,
    includeDeleted: Boolean = false
  )(
    parentFolderId: FolderId
  )(
    using dmsFeature: DmsFeature
  ): Task[Seq[FolderId]] = {
    FDBRecordDatabase.transact(FolderStateStoreOperations.Production) {
      _.getSubfolderIds(
        actor,
        parentFolderId,
        includeDeleted = includeDeleted
      )
    }
  }

  def getSubFolderNameMap(
    actor: UserId,
    includeDeleted: Boolean = false
  )(
    parentFolderId: FolderId
  )(
    using dmsFeature: DmsFeature
  ): Task[Map[FolderId, String]] = {
    FDBRecordDatabase.transact(FolderStateStoreOperations.Production) {
      _.getSubfolderNameMap(
        actor,
        parentFolderId,
        includeDeleted = includeDeleted
      )
    }
  }

  def getFiles(
    actor: UserId,
    includeDeleted: Boolean = false
  )(
    parentFolderId: FolderId
  )(
    using dmsFeature: DmsFeature
  ): Task[Seq[FileId]] = {
    FDBRecordDatabase.transact(FileStateStoreOperations.Production) {
      _.getFileIds(
        actor,
        parentFolderId,
        includeDeleted = includeDeleted
      )
    }
  }

  def getFilesMetadataMap(
    actor: UserId,
    includeDeleted: Boolean = false
  )(
    parentFolderId: FolderId
  )(
    using dmsFeature: DmsFeature
  ): Task[Map[FileId, Map[String, String]]] = {
    FDBRecordDatabase.transact(FileStateStoreOperations.Production) {
      _.getFilesMetadataMap(
        actor,
        parentFolderId,
        includeDeleted = includeDeleted
      )
    }
  }

  def getFileNameMap(
    actor: UserId,
    includeDeleted: Boolean = false
  )(
    parentFolderId: FolderId
  )(
    using dmsFeature: DmsFeature
  ): Task[Map[FileId, String]] = {
    FDBRecordDatabase.transact(FileStateStoreOperations.Production) {
      _.getFileNameMap(
        actor,
        parentFolderId,
        includeDeleted = includeDeleted
      )
    }
  }

  def getFolderOrder(
    actor: UserId,
    folderId: FolderId
  )(
    using dmsFeature: DmsFeature
  ): Task[Map[RadixId, Long]] = {
    for {
      order <- FDBRecordDatabase.transact(DmsFolderOrderOperations.Production) {
        _.getFolderOrder(actor, folderId)
      }
    } yield order
  }

  def getDirectChildrenFolders(
    actor: UserId,
    parentFolder: FolderId
  )(
    using dmsFeature: DmsFeature
  ): Task[Seq[FolderId]] = {
    for {
      folderOrder <- getFolderOrder(actor, parentFolder)
      childrenFolders = folderOrder.toList.collect { case (folderId: FolderId, _) =>
        folderId
      }
    } yield childrenFolders
  }

  def getFolderNamesUnsafe(
    folderIds: Seq[FolderId]
  )(
    using dmsFeature: DmsFeature
  ): Task[Map[FolderId, String]] = {
    FDBRecordDatabase.transact(FolderStateStoreOperations.Production) { folderStateOps =>
      RecordIO
        .parTraverseN(4)(folderIds) { folderId =>
          folderStateOps
            .getNameUnsafe(folderId)
            .map { folderName =>
              folderId -> folderName
            }
        }
        .map(_.toMap)
    }
  }

  def getFolderOrders(
    actor: UserId,
    folderIds: Seq[FolderId]
  )(
    using dmsFeature: DmsFeature
  ): Task[Map[RadixId, Long]] = {
    FDBRecordDatabase
      .transact(DmsFolderOrderOperations.Production) { orderOps =>
        RecordIO.parTraverseN(64)(folderIds) { folderId =>
          orderOps.getFolderOrder(actor, folderId)
        }
      }
      .map(_.reduce(_ ++ _))
  }

  def getFolderIndex(
    actor: UserId
  )(
    folderId: FolderId
  )(
    using dmsFeature: DmsFeature
  ): Task[Option[Long]] = {
    ZIOUtils
      .traverseOption(folderId.parentFolder) { parentFolderId =>
        getFolderOrder(actor, parentFolderId).map(_.get(folderId))
      }
      .map(_.flatten)
  }

  def getFileIndex(
    actor: UserId
  )(
    fileId: FileId
  )(
    using dmsFeature: DmsFeature
  ): Task[Option[Long]] = {
    getFolderOrder(actor, fileId.folder).map(_.get(fileId))
  }

  def changeFolderOrder(
    actor: UserId,
    folderId: FolderId,
    order: Map[RadixId, Long]
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    for {
      _ <- FDBRecordDatabase.transact(DmsFolderOrderOperations.Production) {
        _.changeFolderOrder(
          actor,
          folderId,
          order
        )
      }
    } yield ()
  }

  def existFolder(
    actor: UserId,
    requiredPermission: FileFolderPermission = FileFolderPermission.ViewOnly,
    includeDeleted: Boolean = false
  )(
    folderId: FolderId
  )(
    using dmsFeature: DmsFeature
  ): Task[Boolean] = {
    FDBRecordDatabase.transact(FolderStateStoreOperations.Production) {
      _.exist(
        actor,
        folderId,
        requiredPermission,
        includeDeleted
      )
    }
  }

  def existFile(
    actor: UserId,
    requiredPermission: FileFolderPermission = FileFolderPermission.ViewOnly,
    includeDeleted: Boolean = false
  )(
    fileId: FileId
  )(
    using dmsFeature: DmsFeature
  ): Task[Boolean] = {
    FDBRecordDatabase.transact(FileStateStoreOperations.Production) {
      _.exist(
        actor,
        fileId,
        requiredPermission,
        includeDeleted
      )
    }
  }

  def validateFolder(
    actor: UserId,
    requiredPermission: FileFolderPermission
  )(
    folderId: FolderId
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    FDBRecordDatabase.transact(FolderStateStoreOperations.Production) {
      _.validate(
        actor,
        folderId,
        requiredPermission
      )
    }
  }

  def validateFile(
    actor: UserId,
    requiredPermission: FileFolderPermission
  )(
    fileId: FileId
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    FDBRecordDatabase.transact(FileStateStoreOperations.Production) {
      _.validate(
        actor,
        fileId,
        requiredPermission
      )
    }
  }

  def getFileSize(fileId: FileId): Task[Information] = {
    FDBRecordDatabase.transact(DmsStorageTrackingOperations.Production) {
      _.getFileSize(fileId).map(Bytes(_))
    }
  }

  def getBatchChannelsLastUpdated(
    channelIds: Set[RadixId]
  )(
    using FDBCluster
  ): Task[Map[RadixId, Instant]] = {
    FDBCommonDatabase().read(FDBOperations[(FileEventStoreOperations, FolderEventStoreOperations)].Production) {
      case (fileOps, folderOps) =>
        RecordReadIO
          .parTraverseN(parallelism)(channelIds) { channelId =>
            for {
              fileLastUpdatedAt <- fileOps.getChannelLastUpdatedTimestamp(channelId)
              folderLastUpdatedAt <- folderOps.getChannelLastUpdatedTimestamp(channelId)
            } yield {
              val lastUpdatedOpt = Ordering[Option[Instant]].max(
                fileLastUpdatedAt,
                folderLastUpdatedAt
              )
              lastUpdatedOpt.map(channelId -> _)
            }
          }
          .map(_.flatten.toMap)
    }
  }

  def getChannelLastUpdatedTimestamp(channelId: RadixId): Task[Option[Instant]] = {
    for {
      fileLastUpdatedAt <- FDBRecordDatabase.transact(FileEventStoreOperations.Production) {
        _.getChannelLastUpdatedTimestamp(channelId)
      }
      folderLastUpdatedAt <- FDBRecordDatabase.transact(FolderEventStoreOperations.Production) {
        _.getChannelLastUpdatedTimestamp(channelId)
      }
    } yield {
      Ordering[Option[Instant]].max(fileLastUpdatedAt, folderLastUpdatedAt)
    }
  }

  def getFolderLastUpdatedAt(
    actor: UserId,
    includeDeleted: Boolean = false
  )(
    folderId: FolderId
  )(
    using dmsFeature: DmsFeature
  ): Task[Option[Instant]] = {
    FDBRecordDatabase.transact(FolderStateStoreOperations.Production)(
      _.getLastUpdatedAt(actor, folderId, includeDeleted)
    )
  }

  def getFileLastUpdatedAt(
    actor: UserId,
    includeDeleted: Boolean = false
  )(
    fileId: FileId
  )(
    using dmsFeature: DmsFeature
  ): Task[Option[Instant]] = {
    FDBRecordDatabase.transact(FileStateStoreOperations.Production)(
      _.getLastUpdatedAt(
        actor,
        fileId,
        includeDeleted
      )
    )
  }

  def modifyFolderLastUpdatedAt(
    actor: UserId
  )(
    folderId: FolderId
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    FDBRecordDatabase.transact(FolderFlowOperations.Production)(
      _.modifyLastUpdatedAtRecursively(actor, folderId)
    )
  }

  def modifyFileLastUpdatedAt(
    actor: UserId
  )(
    fileId: FileId
  )(
    using dmsFeature: DmsFeature
  ): Task[Unit] = {
    FDBRecordDatabase.transact(
      FDBOperations[(FileFlowOperations, FolderFlowOperations)].Production
    ) { case (fileFlowOps, folderFlowOps) =>
      for {
        _ <- fileFlowOps.modifyLastUpdatedAt(actor, fileId)
        _ <- folderFlowOps.modifyLastUpdatedAtRecursively(actor, fileId.folder)
      } yield ()
    }
  }

  def getAllVersions(
    actor: UserId,
    fileId: FileId,
    requiredPermission: FileFolderPermission = FileFolderPermission.ViewOnly
  )(
    using dmsFeature: DmsFeature
  ): Task[(FileState, List[FileVersionModel])] = {
    FDBRecordDatabase.transact(FileStateVersionOperations.Production)(
      _.getAllFileVersions(actor, fileId, requiredPermission = requiredPermission)
    )
  }

  // ---------------------------------------------------- UNSAFE API ----------------------------------------------------

  def getFolderNameUnsafe(
    folderId: FolderId
  )(
    using dmsFeature: DmsFeature
  ): Task[String] = {
    FDBRecordDatabase.transact(FolderStateStoreOperations.Production) {
      _.getNameUnsafe(folderId)
    }
  }

  def getFileNameUnsafe(
    fileId: FileId,
    includeDeleted: Boolean = false
  )(
    using DmsFeature,
    FDBCluster
  ): Task[String] = {
    FDBCommonDatabase().read(FileStateStoreOperations.Production) {
      _.getNameUnsafe(fileId, includeDeleted)
    }
  }

  def getMultipleFileNameAndDeletedStateUnsafe(
    fileIds: List[FileId]
  )(
    using dmsFeature: DmsFeature
  ): Task[Map[FileId, (String, Boolean)]] = {
    for {
      fileStates <- ZIOUtils.foreachGrouped(fileIds.toSet, 500) { batchFileIds =>
        FDBRecordDatabase.transact(FileStateStoreOperations.Production) { fileStateOps =>
          RecordIO.parTraverseN(4)(batchFileIds) { fileId =>
            fileStateOps.getNameAndDeletedStateUnsafe(fileId).map(fileState => fileId -> fileState)
          }
        }
      }
    } yield fileStates.toMap
  }

  def batchGetFileNamesUnsafe(
    fileIds: Seq[FileId]
  )(
    using dmsFeature: DmsFeature
  ): Task[Map[FileId, String]] = {
    FDBRecordDatabase
      .transact(FileStateStoreOperations.Production) { ops =>
        RecordIO.parTraverseN(4)(fileIds) { fileId =>
          ops.getNameUnsafe(fileId).map(fileId -> _)
        }
      }
      .map(_.toMap)
  }

  def getMultipleFolderNameAndDeletedStateUnsafe(
    folderIds: List[FolderId]
  )(
    using dmsFeature: DmsFeature
  ): Task[Map[FolderId, (String, Boolean)]] = {
    for {
      folderStates <- ZIOUtils.foreachGrouped(folderIds.toSet, 500) { batchFolderIds =>
        FDBRecordDatabase.transact(FolderStateStoreOperations.Production) { folderStateOps =>
          RecordIO.parTraverseN(4)(batchFolderIds) { folderId =>
            folderStateOps.getNameAndDeletedStateUnsafe(folderId).map { folderState =>
              folderId -> folderState
            }
          }
        }
      }
    } yield folderStates.toMap
  }

  def getFileCreatedByUnsafe(
    fileId: FileId
  )(
    using DmsFeature,
    FDBCluster
  ): Task[UserId] = {
    FDBCommonDatabase().read(FileEventStoreOperations.Production) {
      _.getFileCreatedUnsafe(fileId).map(_.actor)
    }
  }

  def getFileCreatedAtUnsafe(
    fileId: FileId
  )(
    using DmsFeature,
    FDBCluster
  ): Task[Option[Instant]] = {
    FDBCommonDatabase().read(FileEventStoreOperations.Production) {
      _.getFileCreatedUnsafe(fileId).map(_.timestamp)
    }
  }

  private def batchGetFolderOrders(
    folderIds: Set[FolderId],
    actor: UserId
  )(
    using DmsFeature
  ): Task[Map[FolderId, Map[RadixId, Long]]] = {
    FDBRecordDatabase
      .transact(DmsFolderOrderOperations.Production) { folderOrderOps =>
        RecordIO.traverse(folderIds) { folderId =>
          folderOrderOps.getFolderOrder(actor, folderId).map(folderId -> _)
        }
      }
      .map(_.toMap)
  }

  def batchGetFolderInfos(
    folderIds: Seq[FolderId],
    actor: UserId,
    includeDeleted: Boolean = false
  )(
    using dmsFeature: DmsFeature
  ): Task[Seq[FolderInfo]] = {
    val parentFolders = folderIds.flatMap(_.parentFolder).toSet
    for {
      parentFolderOrderMap <- batchGetFolderOrders(parentFolders, actor)
      folderInfos <- ZIO
        .foreach(folderIds.grouped(maxFileBatchSize).toList) { batchFolderIds =>
          FDBRecordDatabase.transact(
            FDBOperations[((FolderStateStoreOperations, FileStateStoreOperations), FolderFlowOperations)].Production
          ) { case ((folderStateOps, fileStateOps), folderFlowOps) =>
            RecordIO.traverse(batchFolderIds) { folderId =>
              for {
                state <- folderStateOps.getState(actor, folderId, includeDeleted = includeDeleted)
                name = state.name
                index = folderId.parentFolder.flatMap { parentFolder =>
                  parentFolderOrderMap.get(parentFolder).flatMap(_.get(folderId))
                }
                createdInfo <- folderFlowOps.getCreated(actor, folderId, includeDeleted)
                lastModifiedTime = state.lastUpdatedAt.orElse(createdInfo.timestamp)
                permission <- folderStateOps.getPermission(actor, folderId, includeDeleted)
                subFileIds <- fileStateOps.getFileIds(actor, folderId, includeDeleted = includeDeleted)
                subFolderIds <- folderStateOps.getSubfolderIds(actor, folderId, includeDeleted = includeDeleted)
              } yield FolderInfo(
                FileManagerLocation.Folder(None, folderId),
                name,
                lastModifiedTime,
                creator = Some(createdInfo.actor),
                createdAt = createdInfo.timestamp,
                createdBy = None, // populate below
                deletedAt = state.deletedOpt.flatMap(_.at),
                files = None,
                folders = None,
                subFolderCount = subFolderIds.size,
                fileCount = subFileIds.size,
                userPermission = permission,
                index = index,
                allFileIds = None
              )
            }
          }
        }
        .map(_.flatten)
      creatorUserIds = folderInfos.flatMap(_.creator).toSet
      creatorInfos <- ZIO
        .foreach(creatorUserIds.grouped(maxUserBatchSize).toList) { batchUserIds =>
          userProfileService.batchGetUserInfos(batchUserIds)
        }
        .map(_.flatten.toMap)
    } yield folderInfos.map(info => info.copy(createdBy = info.creator.flatMap(creatorInfos.get)))
  }

  def batchGetFileInfos(
    fileIds: Seq[FileId],
    actor: UserId,
    includeDeleted: Boolean = false
  )(
    using DmsFeature
  ): Task[Seq[FileInfo]] = {
    val parentFolders = fileIds.map(_.folder).toSet
    for {
      parentFolderOrderMap <-
        if (includeDeleted) {
          ZIO.succeed(Map.empty[FolderId, Map[RadixId, Long]])
        } else {
          batchGetFolderOrders(parentFolders, actor)
        }
      parentFolderInfoMap <- batchGetFolderInfos(parentFolders.toSeq, actor, includeDeleted).map { folderInfos =>
        folderInfos.map(info => info.itemId -> info).toMap
      }
      fileInfos <- ZIO
        .foreach(fileIds.grouped(maxFileBatchSize).toList) { batchFileIds =>
          FDBRecordDatabase.transact(
            FDBOperations[((FileStateStoreOperations, FileFlowOperations), DmsStorageTrackingOperations)].Production
          ) { case ((fileStateOps, fileFlowOps), fileTrackingOps) =>
            RecordIO.traverse(batchFileIds) { fileId =>
              for {
                state <- fileStateOps.getState(actor, fileId, includeDeleted = includeDeleted)
                name = state.name
                index = parentFolderOrderMap.get(fileId.parent).flatMap(_.get(fileId))
                createdInfo <- fileFlowOps.getCreated(actor, fileId, includeDeleted)
                lastModifiedTime = state.lastUpdatedAt.orElse(createdInfo.timestamp)
                permission <- fileStateOps.getPermission(actor, fileId, includeDeleted)
                size <- fileTrackingOps.getFileSize(fileId).map(Bytes(_))
                parentFolderInfoOpt = parentFolderInfoMap.get(fileId.folder)
              } yield FileInfo(
                itemId = fileId,
                name = name,
                lastModifiedTime = lastModifiedTime,
                creator = Some(createdInfo.actor),
                createdBy = None,
                createdAt = createdInfo.timestamp,
                fileSize = Some(size),
                userPermission = permission,
                index = index,
                parentFolderName = parentFolderInfoOpt.map(_.name),
                parentFolderPermission = parentFolderInfoOpt.flatMap(_.userPermission),
                deletor = state.deletedOpt.map(_.actor),
                deletedBy = None, // populate below
                deletedAt = state.deletedOpt.flatMap(_.at)
              )
            }
          }
        }
        .map(_.flatten)
      allUserIds = fileInfos.flatMap(info => info.creator ++ info.deletor).toSet
      userInfos <- ZIO
        .foreach(allUserIds.grouped(maxUserBatchSize).toList) { batchUserIds =>
          userProfileService.batchGetUserInfos(batchUserIds)
        }
        .map(_.flatten.toMap)
    } yield fileInfos.map(info =>
      info.copy(createdBy = info.creator.flatMap(userInfos.get), deletedBy = info.deletor.flatMap(userInfos.get))
    )
  }

  def batchCheckFolderPermission(
    folderIds: Seq[FolderId],
    actor: UserId,
    requiredPermission: FileFolderPermission = FileFolderPermission.ViewOnly,
    includeDeleted: Boolean = false
  )(
    using dmsFeature: DmsFeature
  ): Task[Set[FolderId]] = {
    ZIO
      .foreach(folderIds.grouped(maxFileBatchSize).toList) { batchFolderIds =>
        FDBRecordDatabase.transact(FolderStateStoreOperations.Production) { folderStateStoreOps =>
          RecordIO.parTraverseN(parallelism)(batchFolderIds) { folderId =>
            folderStateStoreOps.getStateOpt(
              actor,
              folderId,
              requiredPermission,
              includeDeleted
            )
          }
        }
      }
      .map(_.flatten.flatten.flatMap(_.folderId).toSet)
  }

  def batchCheckFilePermission(
    fileIds: Seq[FileId],
    actor: UserId,
    requiredPermission: FileFolderPermission = FileFolderPermission.ViewOnly,
    includeDeleted: Boolean = false
  )(
    using dmsFeature: DmsFeature
  ): Task[Set[FileId]] = {
    ZIO
      .foreach(fileIds.grouped(maxFileBatchSize).toList) { batchFileIds =>
        FDBRecordDatabase.transact(FileStateStoreOperations.Production) { fileStateStoreOps =>
          RecordIO.parTraverseN(parallelism)(batchFileIds) { fileId =>
            fileStateStoreOps.getStateOpt(
              actor,
              fileId,
              requiredPermission,
              includeDeleted
            )
          }
        }
      }
      .map(_.flatten.flatten.flatMap(_.fileId).toSet)
  }

}

object FileService {

  sealed trait PermissionModMode derives CanEqual

  object PermissionModMode {

    sealed trait Requirement derives CanEqual

    object Requirement {

      final case class Fixed(permission: FileFolderPermission) extends Requirement

      case object Highest extends Requirement
    }

    sealed trait OnFailure derives CanEqual

    object OnFailure {

      /** If the requirement is not fulfilled, throw an exception.
        */
      case object ThrowException extends OnFailure

      /** When the requirement is not fulfilled, if the requirement is Fixed, best effort to avoid an exception is doing
        * nothing. Otherwise, if the requirement is Highest, filter out invalid changes and apply the rest.
        */
      case object BestEffort extends OnFailure
    }

    /** Permission checking required
      */
    final case class Validate(
      requirement: Requirement = Requirement.Highest,
      onFailure: OnFailure = OnFailure.ThrowException
    ) extends PermissionModMode

    /** No permission checking applied
      */
    case object Unsafe extends PermissionModMode
  }

  private val parallelism = 50

  type FileIdWithVersionOpt = (FileId, Option[Int])

}
