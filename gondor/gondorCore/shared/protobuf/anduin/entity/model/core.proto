syntax = "proto3";

package anduin.entity.model;

import "date_time.proto";
import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";
import "document.proto";
import "email_address.proto";
import "email/smtp.proto";
import "anduin/dataroom/email_template_type.proto";

option (scalapb.options) = {
  package_name: "anduin.entity.model"
  single_file: true
  import: "anduin.dms.DocumentStorageIdMapper.given"
  import: "anduin.id.entity.EntityId"
  import: "anduin.id.entity.EntityRestrictedId"
  import: "anduin.id.entity.EntitySignatureId"
  import: "anduin.id.entity.EmailDomainId"
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.model.id.TeamId"
  import: "anduin.model.common.user.UserId"
  import: "anduin.model.document.DocumentStorageId"
  import: "anduin.id.signature.SignatureModuleId"
  import: "anduin.model.common.emailaddress.EmailAddress"
  import: "anduin.model.id.email.provider.EmailProviderId"
};

enum EntityType {
  Investor = 0;
  LawFirm = 1;
  Company = 2;
  ThirdParty = 3;
  NaturalPerson = 4;
  PortalAdmin = 5;
  PersonalEntity = 6;
}

// We need set value for role increased by the level of this role
enum EntityRole {
  Member = 0;
  SuperAdmin = 2;
}

enum EntityAccessLevel {
  Official = 0;
  Restricted = 1;
  Confidential = 2;
  BoardMember = 3;
  Shareholder = 4;
}

message EntityMemberInfo {
  reserved 2, 3;

  EntityRole role = 1;
  ZonedDateTimeMessage joined_date = 4 [(scalapb.field).type = "java.time.ZonedDateTime"];
  bool default_approver = 5;
}

message EntityInvitedMemberInfo {
  reserved 2;
  EntityRole entity_role = 1;
}

message EmailDomainModel {
  string email_domain_id = 1 [(scalapb.field).type = "EmailDomainId"];
  string entityId = 2 [(scalapb.field).type = "Option[EntityId]"];
}

message EntityModel {
  string entity_id = 1 [(scalapb.field).type = "EntityId"];
  string name = 2;
  string alias = 3;
  EntityType entity_type = 4;
  repeated google.protobuf.StringValue email_domains = 5;
  DocumentStorageIdMessage logoStorageId = 6 [(scalapb.field).type = "DocumentStorageId"];
  DocumentStorageIdMessage longLogoStorageId = 7 [(scalapb.field).type = "DocumentStorageId"];
  string customer_tracking_id = 8;

  enum EntityTrackingType {
    Internal = 0;
    Production = 1;
  }
  EntityTrackingType entity_tracking_type = 9;
}

message EntityRestrictedModel {
  reserved 4;
  string entity_restricted_id = 1 [(scalapb.field).type = "EntityRestrictedId"];
  map<string, EntityMemberInfo> members_info = 2 [(scalapb.field).key_type = "UserId"];
  repeated string fund_sub_ids = 3 [(scalapb.field).type = "FundSubId"];
  string entity_team_id = 5 [(scalapb.field).type = "TeamId"];
  map<string, EntityInvitedMemberInfo> invitation_info = 6 [(scalapb.field).key_type = "UserId"];
  InstantMessage created_date = 7 [(scalapb.field).type = "java.time.Instant"];
  EntityDataRoomEmailConfigs data_room_email_configs = 8;
}

message EntitySignatureModel {
  string entity_signature_id = 1 [(scalapb.field).type = "EntitySignatureId"];
  map<string, string> signatureModules = 2 [(scalapb.field) = {key_type: "UserId" value_type: "SignatureModuleId"}];
  map<string, InstantMessage> lastRequests = 3 [(scalapb.field) = {key_type: "UserId" value_type: "java.time.Instant"}];
}

message EntityDataRoomEmailConfigs {
  reserved 18;
  // Note: sender_address_name_part is used for data rooms of the entity, and is set at entity level
  bool sender_name_enabled = 1;
  string sender_name = 2;
  bool sender_address_name_part_enabled = 3;
  string sender_address_name_part = 4;
  bool custom_email_reply_enabled = 5;
  repeated EmailAddressMessage reply_to_emails = 6 [(scalapb.field).type = "EmailAddress"];
  repeated dataroom.email.DataRoomEmailTemplateType custom_email_reply_template_types = 7 [(scalapb.field).collection_type = "Set"];
  bool reply_to_sender_enabled = 8;
  repeated dataroom.email.DataRoomEmailTemplateType reply_to_sender_template_types = 9 [(scalapb.field).collection_type = "Set"];
  bool custom_email_cc_enabled = 10;
  repeated EmailAddressMessage cc_emails = 11 [(scalapb.field).type = "EmailAddress"];
  repeated dataroom.email.DataRoomEmailTemplateType custom_cc_template_types = 12 [(scalapb.field).collection_type = "Set"];
  bool custom_email_bcc_enabled = 14;
  repeated EmailAddressMessage bcc_emails = 15 [(scalapb.field).type = "EmailAddress"];
  repeated dataroom.email.DataRoomEmailTemplateType custom_bcc_template_types = 16 [(scalapb.field).collection_type = "Set"];
  bool enable_custom_smtp = 17;
  string custom_smtp_provider_id = 19 [(scalapb.field).type = "Option[EmailProviderId]"];
  bool allow_disabling_invitation_email = 13;
}

message RecordTypeUnion {
  EntityModel _EntityModel = 1;
  EntityRestrictedModel _EntityRestrictedModel = 2;
  EmailDomainModel _EmailDomainModel = 3;
  EntitySignatureModel _EntitySignatureModel = 4;
}

enum FundSubBatchActionTrackingType {
  SendCustomEmail = 0;
  UpdateCustomValue = 1;
  ResendInvitation = 2;
  RemindCompleteSubscriptionDoc = 3;
  RemindCompleteSupportingDoc = 4;
  AddFullyExecutedDoc = 5;
  DistributedExecutedDoc = 6;
  ConvertOfflineInvestor = 7;
  CounterSign = 8;
  MarkSupportingDocAsProvided = 9;
  ApproveSupportingDoc = 10;
}