// Copyright (C) 2014-2025 Anduin Transactions Inc.

package build.apps.gondor

import mill.scalalib.*

import anduin.build.AnduinVersions

object GondorAppServer {

  lazy val jvmDeps = Seq(
    mvn"javax.ws.rs:javax.ws.rs-api:${AnduinVersions.javaxws}",
    mvn"com.lihaoyi:fansi:${AnduinVersions.fansi}"
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("sourcecode")),
    mvn"com.lihaoyi::fastparse:${AnduinVersions.fastParse}"
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("sourcecode"))
      .exclude("com.lihaoyi" -> AnduinVersions.j2s("geny")),
    mvn"com.lihaoyi::sourcecode:${AnduinVersions.sourcecode}",
    mvn"com.lihaoyi::geny:${AnduinVersions.geny}",
    mvn"net.java.dev.jna:jna:${AnduinVersions.jna}",
    mvn"org.javassist:javassist:${AnduinVersions.javaAssist}"
  )

  lazy val testDeps = Seq(
    mvn"com.softwaremill.sttp.tapir::tapir-sttp-stub-server:${AnduinVersions.tapirVersion}"
      .exclude("com.softwaremill.sttp.tapir" -> AnduinVersions.j2s("tapir-server"))
      .exclude("com.softwaremill.sttp.tapir" -> AnduinVersions.j2s("tapir-sttp-client"))
  )

}
