package build.apps.gondor

import build_.build.util_.*
import build_.build.dependency_.CommonDependencies
import build_.apps.gondor.settings_.GondorSettings
import build_.apps.gondor.dependency_.GondorAppServer
import build.gondor.gondor
import build.platform.stargazerTest

import mill.util.VcsVersion
import mill.*

import anduin.build.*
import anduin.mill.*
import anduin.mill.jsdeps.*
import anduin.mill.utils.*
import mill.javalib.dependency.DependencyUpdatesModule
import io.github.hoangmaihuy.mill.packager.archetypes.JavaAppPackagingModule
import io.github.hoangmaihuy.missinglink.{*, given}

object `package` extends Module {

  object gondorAppServer
      extends AnduinScalaModule
      with DependencyUpdatesModule
      with JavaAppPackagingModule
      with MissinglinkCheckModule
      with AnduinBuildEnv {

    override def maintainer = "<EMAIL>"
    override def artifactName = "gondor-app-server"
    override def packageVersion = VcsVersion.vcsState().format()

    override def topLevelDirectory = Some("stargazer")

    override def universalMappings = Task {
      mill.api.BuildCtx.withFilesystemCheckerDisabled {
        val clientDir = clientDirectory()
        val webMappings = os.walk(clientDir).map { path =>
          PathRef(path) -> os.sub / "web" / path.subRelativeTo(clientDir)
        }
        super.universalMappings() ++ webMappings
      }
    }

    override def moduleDeps = super.moduleDeps ++ Seq(gondor.jvm)

    override def mvnDeps = super.mvnDeps() ++ CommonDependencies.zioTestDeps

    override def CONFIG_SETTINGS = Task {
      super.CONFIG_SETTINGS() ++ Seq(
        "-Xmx3g",
        "-Xms1G",
        "-XX:NativeMemoryTracking=summary"
        // "-Dio.netty.leakDetection.level=PARANOID" // Enable this to hunt netty mem leak
      )
    }

    override def mainClass = Task {
      Task.env
        .get("STARGAZER_MAIN_CLASS")
        .orElse(Some("com.anduin.stargazer.apps.stargazer.GondorServiceApp"))
    }

    override def missinglinkIgnoreDestinationPackages = GondorSettings.missinglinkIgnoreDestinationPackages
    override def missinglinkIgnoreSourcePackages = GondorSettings.missinglinkIgnoreSourcePackages
    override def missinglinkExcludedDependencies = GondorSettings.missinglinkExcludedDependencies

    object test extends AnduinScalaTests {

      private def universalResources = Task.Sources(moduleDir / os.up / "universal")

      override def resources = super.resources() ++ universalResources()
      override def mvnDeps = super.mvnDeps() ++ GondorAppServer.testDeps
      override def moduleDeps = super.moduleDeps ++ Seq(stargazerTest.jvm)
    }

    override def runBackgroundLogToConsole: Boolean = false

    def dir: T[os.Path] = Task {
      Task.dest
    }

  }

  object gondorAppClient extends AnduinScalaJSModule with AnduinWebClientModule {
    override def webModule = AnduinWebModules.Gondor
    override def moduleDeps = super.moduleDeps ++ Seq(gondor.js)
    override def mainClass = Some("com.anduin.stargazer.client.StargazerMain")
  }

}
