package build.apps.gondor

import io.github.hoangmaihuy.missinglink.*

import anduin.build.AnduinVersions

object GondorSettings {

  lazy val missinglinkIgnoreDestinationPackages = Seq(
    IgnoredPackage("anduin.entity.model"),
    IgnoredPackage("com.google.protobuf"),
    IgnoredPackage("org.eclipse.jetty.npn"),
    IgnoredPackage("javax.xml.bind"),
    IgnoredPackage("io.netty.util.internal.logging"),
    IgnoredPackage("com.typesafe.sslconfig.ssl"),
    IgnoredPackage("javax.activation"),
    IgnoredPackage("okhttp3.internal.platform.android.*"),
    IgnoredPackage("okhttp3.internal.platform.Android10Platform"),
    IgnoredPackage("okhttp3.internal.platform.AndroidPlatform"),
    IgnoredPackage("okhttp3.internal.platform.OpenJSSEPlatform"),
    IgnoredPackage("sttp.client3.armeria"),
    IgnoredPackage("software.amazon.awssdk.auth.credentials"),
    IgnoredPackage("software.amazon.awssdk.services.sqs"),
    IgnoredPackage("com.fasterxml.jackson.databind")
  )

  lazy val missinglinkIgnoreSourcePackages = Seq(
    IgnoredPackage("com.github.benmanes.caffeine.cache"),
    IgnoredPackage("org.flywaydb.core.internal"),
    IgnoredPackage("shapeless"),
    IgnoredPackage("org.slf4j"),
    IgnoredPackage("okhttp3.internal.platform.android", ignoreSubpackages = true),
    IgnoredPackage("okhttp3.internal.platform", ignoreSubpackages = true),
    IgnoredPackage("net.ruippeixotog.scalascraper.dsl"),
    IgnoredPackage("org.apache.commons.codec.binary"),
    IgnoredPackage("com.datastax.driver"),
    IgnoredPackage("sttp.client3.armeria"),
    IgnoredPackage("sttp.tapir.server.armeria"),
    IgnoredPackage("net.datafaker.service"),
    IgnoredPackage("liquibase.util", ignoreSubpackages = true)
  )

  lazy val missinglinkExcludedDependencies = Seq(
    DependencyFilter(organization = "io.getquill", name = AnduinVersions.j2s("quill-engine")),
    DependencyFilter(organization = "io.getquill", name = AnduinVersions.j2s("quill-util")),
    DependencyFilter(organization = "commons-logging", name = "commons-logging"),
    DependencyFilter(organization = "org.slf4j", name = "jcl-over-slf4j"),
    DependencyFilter(organization = "javax.activation", name = "javax.activation-api"),
    DependencyFilter(organization = "jakarta.activation", name = "jakarta.activation"),
    DependencyFilter(organization = "jakarta.activation", name = "jakarta.activation-api"),
    DependencyFilter(organization = "org.apache.xmlbeans", name = "xmlbeans"),
    DependencyFilter(organization = "io.grpc", name = "grpc-netty-shaded"),
    DependencyFilter(organization = "org.bouncycastle", name = "bcprov-jdk18on"),
    DependencyFilter(organization = "org.bouncycastle", name = "bcpkix-jdk18on"),
    DependencyFilter(organization = "org.bouncycastle", name = "bcutil-jdk18on"),
    DependencyFilter(organization = "io.netty"), // TODO: more specific
    DependencyFilter(organization = "com.zaxxer", name = "HikariCP"),
    DependencyFilter(organization = "io.opentelemetry", name = "opentelemetry-semconv"),
    DependencyFilter(organization = "io.opentelemetry", name = "opentelemetry-api"),
    DependencyFilter(organization = "io.opentelemetry", name = "opentelemetry-context"),
    DependencyFilter(organization = "io.opentelemetry", name = "opentelemetry-sdk-trace"),
    DependencyFilter(organization = "io.temporal", name = "temporal-serviceclient"),
    DependencyFilter(organization = "org.apache.logging.log4j", name = "log4j-api"),
    DependencyFilter(organization = "net.spy", name = "spymemcached"),
    DependencyFilter(organization = "io.prometheus", name = "prometheus-metrics-core"),
    DependencyFilter(organization = "com.google.crypto.tink", name = "tink")
  )

}
