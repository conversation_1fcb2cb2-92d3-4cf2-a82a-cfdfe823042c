// Copyright (C) 2014-2025 Anduin Transactions Inc.
package build.apps.maya

import build_.build.util_.*
import build.modules.narya

import mill.*
import anduin.build.*
import anduin.mill.*

object `package` extends Module {

  object mayaApp extends AnduinCrossPlatformModule {

    object jvm extends JvmModule {
      override def moduleDeps = super.moduleDeps ++ Seq(narya.jvm)
    }

    object js extends JsModule with AnduinWebClientModule {
      override def webModule = AnduinWebModules.Maya
      override def mainClass = Some("anduin.maya.client.MayaMainApp")
      override def moduleDeps = super.moduleDeps ++ Seq(narya.js)
    }

  }

}
