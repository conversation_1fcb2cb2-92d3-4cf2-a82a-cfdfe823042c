package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

func init() {
	hostName, _ = os.Hostname()
	if hostName == "" {
		hostName = "unknown"
	}
}

var (
	javaPid  int
	jcmdPath string
	hostName string

	jvmCommittedGauge = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "gondor_memory_jvm_committed",
		Help: "Result of VM.native_memory",
		ConstLabels: prometheus.Labels{
			"hostname": hostName,
		},
	})
	rssGauge = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "gondor_memory_rss",
		Help: "Result of ps -o rss",
		ConstLabels: prometheus.Labels{
			"hostname": hostName,
		},
	})
	mmapGauge = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "gondor_memory_mmap",
		Help: "Result of pmap",
		ConstLabels: prometheus.Labels{
			"hostname": hostName,
		},
	})
	otherGauge = prometheus.NewGauge(prometheus.GaugeOpts{
		Name: "gondor_memory_other",
		Help: "Rest of memory",
		ConstLabels: prometheus.Labels{
			"hostname": hostName,
		},
	})
)

type dataPoint struct {
	jvmCommitted float64
	rss          float64
	mmap         float64
}

func (p dataPoint) possibleLeaked() float64 {
	return p.rss - p.jvmCommitted - p.mmap
}

func getJavaPid(ctx context.Context) (int, error) {
	cmd := exec.CommandContext(ctx, "pidof", "java")
	out, err := cmd.Output()
	if err != nil {
		return 0, err
	}
	pid, err := strconv.ParseInt(strings.TrimSpace(string(out)), 10, 32)
	if err != nil {
		return 0, err
	}
	return int(pid), nil
}

func collectFloat(ctx context.Context, name string, args ...string) (float64, error) {
	cmd := exec.CommandContext(ctx, name, args...)
	out, err := cmd.Output()
	if err != nil {
		return 0, err
	}
	res, err := strconv.ParseFloat(strings.TrimSpace(string(out)), 64)
	if err != nil {
		return 0, err
	}
	return res, nil
}

func collectInfo(ctx context.Context) (*dataPoint, error) {
	jvmCommitted, err := collectFloat(
		ctx,
		"su", "anduin", "-c",
		fmt.Sprintf(`%s %d VM.native_memory | grep -P "Total.*committed=" | grep -o -P "(?<=committed=)[0-9]+(?=KB)"`, jcmdPath, javaPid),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to collect jvm committed memory. ERR: %v", err)
	}

	rss, err := collectFloat(ctx, "ps", "--no-header", "-o", "rss", fmt.Sprintf("%d", javaPid))
	if err != nil {
		return nil, fmt.Errorf("failed to collect rss memory. ERR: %v", err)
	}

	mmap, err := collectFloat(
		ctx,
		"su", "anduin", "-c",
		fmt.Sprintf(`pmap -X %d | head -n -2 | awk '{ if (NR > 2 && $5 >0 ) sum += $7 } END { print sum }'`, javaPid),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to collect mmap memory. ERR: %v", err)
	}

	return &dataPoint{
		jvmCommitted: jvmCommitted,
		rss:          rss,
		mmap:         mmap,
	}, nil
}

func waitForJavaPid(ctx context.Context) int {
	for {
		javaPid, err := getJavaPid(ctx)
		if err == nil && javaPid != 0 {
			return javaPid
		}
		log.Printf("[WARN] failed to get java pid. ERR: %v", err)
		time.Sleep(time.Second)
	}
}

func main() {
	ctx := context.Background()
	httpPort := os.Getenv("JVM_MEMORY_PORT")
	if httpPort == "" {
		httpPort = "4444"
	}

	var err error
	javaPid = waitForJavaPid(ctx)
	log.Printf("java pid is: %d", javaPid)

	jcmdPath, err = exec.LookPath("jcmd")
	if err != nil {
		log.Fatalf("failed to find jcmd bin. ERR: %v", err)
	}
	log.Printf("jcmd bin: %s", jcmdPath)

	// http server
	go func() {
		reg := prometheus.NewRegistry()
		reg.MustRegister(jvmCommittedGauge, rssGauge, mmapGauge, otherGauge)

		http.Handle("/metrics", promhttp.HandlerFor(reg, promhttp.HandlerOpts{Registry: reg}))
		addr := fmt.Sprintf(":%s", httpPort)
		log.Printf("HTTP metrics served at `%s`", addr)
		log.Fatal(http.ListenAndServe(addr, nil))
	}()

	// main loop
	for {
		time.Sleep(15 * time.Second)

		p, err := collectInfo(ctx)
		if err != nil {
			log.Printf("[WARN] failed to collect metrics. ERR: %v", err)
			continue
		}
		jvmCommittedGauge.Set(float64(p.jvmCommitted))
		rssGauge.Set(p.rss)
		mmapGauge.Set(p.mmap)
		otherGauge.Set(p.possibleLeaked())
	}
}
