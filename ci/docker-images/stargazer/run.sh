#!/usr/bin/env bash

set -e

function generate_svix_token() {
  local now=$(date +%s)
  if [ -z "$STARGAZER_SERVICES_SVIX_JWT_SECRET" ]; then
    exit 1
  fi
  jwt encode \
    --alg=HS256 \
    --secret="$STARGAZER_SERVICES_SVIX_JWT_SECRET" \
    --exp=`expr $now + 1814400` \
    --nbf=$now \
    --iss=svix-server \
    --sub=org_23rb8YdGqMT0qIzpgGwdXfHirMu
}

# Source vault environments
if [ -d /vault/secrets ]; then
  for fname in /vault/secrets/*.sh; do
    . $fname
  done
fi

# Tweak ulimit
if [ "$ANDUIN_ENABLE_FARGATE" = "true" ]; then
  ulimit -n 65535
  ulimit -u 65535
fi

# Generate SVIX dynamic token
export STARGAZER_SVIX_TOKEN=$(generate_svix_token)
if [ -z "$STARGAZER_SVIX_TOKEN" ]; then
  echo "Failed to generate svix token"
  exit 1
fi

mkdir -p /tmp/fdb-trace
chown -R anduin:anduin /tmp/fdb-trace

# Running gondor
cd /opt/anduin/stargazer/bin
exec gosu anduin:anduin $@
