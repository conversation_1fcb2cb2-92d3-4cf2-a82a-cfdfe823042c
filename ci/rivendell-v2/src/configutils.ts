import { Finder } from "@anduintransaction/rivendell";
import * as path from "path";
import * as merge from "deepmerge";
import * as parseArgs from "minimist";

type EnvType = "local" | "integ" | "staging" | "demo" | "production";

export const ConfigUtils = {
  getAwsRegion(cfg: any) {
    return Finder.requiredString(cfg, ["awsRegion"], "us-east-1");
  },
  isAwsUs(cfg: any) {
    return this.getAwsRegion(cfg).startsWith("us");
  },
  isAwsEu(cfg: any) {
    return this.getAwsRegion(cfg).startsWith("eu");
  },
  getDockerGithubImage(cfg: any, image: string) {
    return Finder.requiredString(cfg, ["dockerGithubImages", image]);
  },
  getDockerPublicPrefix(cfg: any) {
    return `${this.getDockerEcrRepoRoot(cfg)}/docker-hub`;
  },
  getDockerPublicImage(cfg: any, image: string, skipPullThrough = false) {
    const imageName = Finder.requiredString(cfg, ["dockerPublicImages", image]);
    if (skipPullThrough) return imageName;

    const dockerPrefix = this.getDockerPublicPrefix(cfg);
    const fullImageName = imageName.split("/").length > 1
      ? imageName
      : `library/${imageName}`;
    return `${dockerPrefix}/${fullImageName}`;
  },
  getEcrRegion(cfg: any) {
    return Finder.optionalString(cfg, ["dockerEcrRegion"]);
  },
  getDockerEcrRepoRoot(cfg: any, overrideRegion?: string) {
    const region = overrideRegion ||
      this.getEcrRegion(cfg) ||
      this.getAwsRegion(cfg);
    const accountId = Finder.requiredString(cfg, ["awsAccountId"]);
    const useRegionalEcr = Finder
      .requiredBool(cfg, ["dockerUseRegionalEcr"], false);
    return useRegionalEcr
      ? `${accountId}.dkr.ecr.${region}.amazonaws.com`
      : `${accountId}.dkr.ecr.us-east-1.amazonaws.com`;
  },
  getDockerEcrImage(cfg: any, image: string) {
    const ecrRepo = this.getDockerEcrRepoRoot(cfg);
    const imageName = Finder.requiredString(cfg, ["dockerEcrImages", image]);
    return `${ecrRepo}/${imageName}`;
  },
  getStargazerImage(cfg: any) {
    const ecrRepo = this.getDockerEcrRepoRoot(cfg);
    const imageName = Finder.requiredString(cfg, ["stargazerImage"]);
    const imageTag = Finder.requiredString(cfg, ["stargazerSprintTag"]);
    return `${ecrRepo}/${imageName}:${imageTag}`;
  },
  getKubeCtx(cfg: any) {
    return Finder.requiredString(cfg, ["kubeCtx"], "");
  },
  getK8sNamespace(cfg: any) {
    const namespace = Finder.optionalString(cfg, ["k8sNamespace"]);
    if (namespace !== undefined && namespace !== "") return namespace;
    return this.getEnvName(cfg);
  },
  getK8sServiceSuffix(cfg: any) {
    return Finder.required(cfg, ["k8sServiceSuffix"], "");
  },
  isProduction(cfg: any) {
    return this.getEnvType(cfg) === "production";
  },
  isStaging(cfg: any) {
    return this.getEnvType(cfg) === "staging";
  },
  isLocal(cfg: any) {
    return this.getEnvType(cfg) === "local" || this.getEnvType(cfg) === "integ";
  },
  isInteg(cfg: any) {
    return this.getEnvType(cfg) === "integ";
  },
  isEnvNukable(cfg: any) {
    return Finder.requiredBool(cfg, ["envNukable"], false);
  },
  getEnvName(cfg: any) {
    return Finder.requiredString(cfg, ["envName"]);
  },
  getEnvType(cfg: any): EnvType {
    return Finder.requiredString(cfg, ["envType"]) as EnvType;
  },
  isPersistData(cfg: any): boolean {
    return Finder.requiredBool(cfg, ["persistData"], true);
  },
};

export async function loadConfig(env: string) {
  const cfgPath = path.join(
    __dirname,
    `./configs/environments/config.${env}.ts`,
  );
  const { default: cfg } = await import(cfgPath);
  return merge(cfg, { envName: env });
}

function objFrom(paths: string[], value: any): any {
  if (paths.length === 0) return {};
  const [head, ...rest] = paths;
  if (rest.length === 0) return { [head]: value };
  return { [head]: objFrom(rest, value) };
}

type VarItem = {
  raw: string;
  valueType: string;
};

/**
 * loadConfigFromCli
 * support 4 kind of flags
 *    - --env                 : specify environment to deploy
 *    - --var=<key>=<val>     : specify string var
 *    - --varNum=<key>=<val>  : specify number var
 *    - --varBool=<key>=<val> : specify boolean var
 */
export async function loadConfigFromCli(args: string[]) {
  const parsed = parseArgs(args);
  const varStringArgs = Finder.getAsArray<string>(parsed, ["var"], []);
  const varNumberArgs = Finder.getAsArray<string>(parsed, ["varNum"], []);
  const varBoolArgs = Finder.getAsArray<string>(parsed, ["varBool"], []);
  const varArgs: VarItem[] = [
    ...varStringArgs.map((s) => ({ raw: s, valueType: "string" })),
    ...varNumberArgs.map((s) => ({ raw: s, valueType: "number" })),
    ...varBoolArgs.map((s) => ({ raw: s, valueType: "boolean" })),
  ];

  const env = Finder.requiredString(parsed, ["env"]);
  let cfg = await loadConfig(env);
  for (const varArg of varArgs) {
    const raw = varArg.raw;
    const key = raw.substring(0, raw.indexOf("="));
    const paths = key.split(".");
    const valueRaw = raw.slice(key.length + 1);
    let value: any;
    switch (varArg.valueType) {
      case "string":
        value = valueRaw;
        break;
      case "number":
        value = parseInt(valueRaw, 10);
        break;
      case "boolean":
        value = valueRaw.trim().toLowerCase() === "true";
        break;
      default:
        break;
    }
    const override = objFrom(paths, value);
    cfg = merge(cfg, override);
  }
  return cfg;
}
