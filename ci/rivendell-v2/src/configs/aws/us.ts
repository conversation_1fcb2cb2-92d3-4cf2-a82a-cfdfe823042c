export default {
  awsRegion: "us-east-1",
  pyroscopeAddress: "http://pyroscope.anduin.local",
  wafAclArn:
    "arn:aws:wafv2:us-east-1:988983267146:regional/webacl/Imperva/65a56ac4-c6fb-4d34-8153-ed04fb581b99",
  cfCertArn:
    "arn:aws:acm:us-east-1:988983267146:certificate/7faf52c9-1eca-439f-8810-c20806375659",
  cfTruststoreArn:
    "arn:aws:elasticloadbalancing:us-east-1:988983267146:truststore/cloudflare/73e3e60b447fca9e",
  LITE_LLM_API_KEY: "sk-zP8CHhANSiiuXSlTKUzIvw",
  LITE_LLM_API_BASE: "https://litellm.us-east-1.anduin.app",
};
