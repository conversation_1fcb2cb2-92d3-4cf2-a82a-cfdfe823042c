import * as merge from "deepmerge";
import commonCfg from "../common";
import otherCfg from "../server-other";
import regionCfg from "../aws/us";

export default merge.all([commonCfg, regionCfg, otherCfg, {
  envType: "demo",
  envNukable: true,
  vaultAwsRole: "fs-prod",
  vaultKubeRole: "fs-prod-v2",
  vaultCliAwsRole: "cli-fs-prod",
  vaultCliKubeRole: "fs-prod-v2",
  gondorFeatures: {
    topo: "simulator",
    ses: false,
  },
  vespa: true,
}]);
