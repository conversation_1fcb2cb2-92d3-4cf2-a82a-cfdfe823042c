import * as merge from "deepmerge";
import commonCfg from "../common";
import otherCfg from "../server-other";
import regionCfg from "../aws/us";

export default merge.all([commonCfg, regionCfg, otherCfg, {
  envType: "staging",
  awsDbBackupBucket: "stargazer-db-backup-test",
  vaultAwsRole: "gondor-internal",
  vaultKubeRole: "gondor-internal-v2",
  vaultCliAwsRole: "cli-gondor-internal",
  vaultCliKubeRole: "gondor-internal-v2",
  gondorFeatures: {
    topo: "slim",
    ses: true,
  },
  vespa: true,
}]);
