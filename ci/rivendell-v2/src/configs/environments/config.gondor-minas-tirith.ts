import * as merge from "deepmerge";
import commonCfg from "../common";
import otherCfg from "../server-other";
import regionCfg from "../aws/us";

export default merge.all([commonCfg, regionCfg, otherCfg, {
  envType: "staging",
  awsDbBackupBucket: "stargazer-db-backup-test",
  vaultAwsRole: "gondor-minas",
  vaultKubeRole: "gondor-minas-v2",
  vaultCliAwsRole: "cli-gondor-minas",
  vaultCliKubeRole: "gondor-minas-v2",
  gondorFeatures: {
    topo: "slim",
    ses: true,
  },
  vespa: true,
}]);
