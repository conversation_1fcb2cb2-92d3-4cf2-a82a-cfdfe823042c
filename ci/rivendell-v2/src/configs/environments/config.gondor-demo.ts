import * as merge from "deepmerge";
import commonCfg from "../common";
import otherCfg from "../server-other";
import regionCfg from "../aws/us";

export default merge.all([commonCfg, regionCfg, otherCfg, {
  envType: "demo",
  awsDbBackupBucket: "stargazer-db-backup-test",
  vaultAwsRole: "gondor-demo",
  vaultKubeRole: "gondor-demo-v2",
  vaultCliAwsRole: "cli-gondor-demo",
  vaultCliKubeRole: "gondor-demo-v2",
  gondorFeatures: {
    topo: "slim",
    ses: true,
  },
  vespa: true,
}]);
