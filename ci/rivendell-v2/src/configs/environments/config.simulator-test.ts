import * as merge from "deepmerge";
import commonCfg from "../common";
import otherCfg from "../server-other";
import regionCfg from "../aws/us";

export default merge.all([commonCfg, regionCfg, otherCfg, {
  envType: "staging",
  envNukable: true,
  vaultAwsRole: "fs-test",
  vaultKubeRole: "fs-test-v2",
  vaultCliAwsRole: "cli-fs-test",
  vaultCliKubeRole: "fs-test-v2",
  gondorFeatures: {
    topo: "simulator",
    ses: false,
  },
  vespa: true,
}]);
