import * as merge from "deepmerge";
import commonCfg from "../common";
import regionCfg from "../aws/ap";

export default merge.all([commonCfg, regionCfg, {
  stargazerImage: "stargazer-canary",

  dockerEcrRegion: "ap-east-1",
  envType: "local",
  envNukable: true,
  persistData: true,
  pvClass: "local-path",
  keycloakDev: false,
  vault: false,
  onlyoffice: true,
  temporalCassandraReplicationFactor: 1,
  temporalEsEnabled: false,
  temporalEsHost: "",
  dockerPullSecrets: ["ecr-creds"],
  gondorFeatures: {
    topo: "local",
    ses: false,
  },
  vespa: true,
}]);
