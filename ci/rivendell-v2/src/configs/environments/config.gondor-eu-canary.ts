import * as merge from "deepmerge";
import commonCfg from "../common";
import otherCfg from "../server-other";
import regionCfg from "../aws/eu";

export default merge.all([commonCfg, regionCfg, otherCfg, {
  envType: "staging",
  awsDbBackupBucket: "stargazer-eu-db-backup-test",
  vaultAwsRole: "gondor-eu-canary",
  vaultKubeRole: "gondor-eu-canary",
  vaultCliAwsRole: "cli-gondor-eu-canary",
  vaultCliKubeRole: "gondor-eu-canary",
  gondorFeatures: {
    topo: "slim",
    ses: true,
  },
  vespa: true,
}]);
