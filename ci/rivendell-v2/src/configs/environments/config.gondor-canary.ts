import * as merge from "deepmerge";
import commonCfg from "../common";
import otherCfg from "../server-other";
import regionCfg from "../aws/us";

export default merge.all([commonCfg, regionCfg, otherCfg, {
  envType: "staging",
  awsDbBackupBucket: "stargazer-db-backup-test",
  vaultAwsRole: "gondor-canary",
  vaultKubeRole: "gondor-canary-v2",
  vaultCliAwsRole: "cli-gondor-canary",
  vaultCliKubeRole: "gondor-canary-v2",
  gondorFeatures: {
    topo: "slim",
    ses: true,
  },
  enableProfiler: true,
  vespa: true,
}]);
