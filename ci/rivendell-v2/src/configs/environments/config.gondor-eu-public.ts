import * as merge from "deepmerge";
import commonCfg from "../common";
import regionCfg from "../aws/eu";

export default merge.all([commonCfg, regionCfg, {
  envType: "production",
  awsDbBackupBucket: "stargazer-eu-db-backup",
  persistData: true,
  pvClass: "aws-standard",
  local: false,
  keycloakDev: false,
  onlyoffice: true,
  temporalCassandraReplicationFactor: 3,
  temporalEsEnabled: true,
  temporalEsHost: "temporal-es:80",
  gondorFeatures: {
    topo: "standard",
    ses: true,
  },
  vault: true,
  vaultAwsRole: "gondor-eu-public",
  vaultKubeRole: "gondor-eu-public-v2",
  vaultCliAwsRole: "cli-gondor-eu-public",
  vaultCliKubeRole: "gondor-eu-public-v2",
  vespa: true,
  useLegacyFoundationDb: true,
}]);
