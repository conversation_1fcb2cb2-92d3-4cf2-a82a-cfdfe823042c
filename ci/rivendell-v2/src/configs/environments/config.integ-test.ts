import * as merge from "deepmerge";
import commonCfg from "../common";
import regionCfg from "../aws/us";

export default merge.all([commonCfg, regionCfg, {
  envType: "integ",
  envNukable: true,
  persistData: false,
  pvClass: "gp3",
  keycloakDev: false,
  vault: false,
  onlyoffice: false,
  temporalCassandraReplicationFactor: 1,
  temporalEsEnabled: false,
  temporalEsHost: "",
  vespa: true,
}]);
