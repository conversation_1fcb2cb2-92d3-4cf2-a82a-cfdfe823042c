import * as merge from "deepmerge";
import regionCfg from "../aws/us";
import commonCfg from "../common";
import otherCfg from "../server-other";

const isRiaSandbox =
  (process.env["RIA_SANDBOX"] || "").toLocaleLowerCase() === "true";

export default merge.all([commonCfg, regionCfg, otherCfg, {
  envType: "staging",
  envNukable: true,
  vaultAwsRole: "gondor-feature",
  vaultKubeRole: "gondor-feature-v2",
  vaultCliAwsRole: "cli-gondor-feature",
  vaultCliKubeRole: "gondor-feature-v2",
  gondorFeatures: {
    topo: isRiaSandbox ? "simulator" : "slim",
    ses: false,
  },
  vespa: true,
}]);
