import * as merge from "deepmerge";
import commonCfg from "../common";
import regionCfg from "../aws/ap";

export default merge.all([commonCfg, regionCfg, {
  dockerEcrRegion: "ap-east-1",
  envType: "local",
  envNukable: true,
  k8sNamespace: "gondor", // always deploy to gondor
  persistData: true,
  pvClass: "local-path",
  keycloakDev: false,
  vault: false,
  onlyoffice: false,
  temporalCassandraReplicationFactor: 1,
  temporalEsEnabled: false,
  temporalEsHost: "",
  dockerPullSecrets: ["ecr-creds"],
  vespa: true,
}]);
