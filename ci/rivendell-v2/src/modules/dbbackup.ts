import { Context, K8sObject } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import * as k8s from "@utils/k8s";

import fdb from "@app/dbbackup/foundationdb";
import form from "@app/dbbackup/form";
import dgraph from "@app/dbbackup/dgraph";
import postgres from "@app/dbbackup/postgres";

export default async function (ctx: Context): Promise<K8sObject[]> {
  if (
    ConfigUtils.isLocal(ctx.configs) ||
    ConfigUtils.isEnvNukable(ctx.configs)
  ) {
    return [];
  }
  return await k8s.combineGenerators(fdb, form, dgraph, postgres)(ctx);
}
