import { Context, K8sObject, Module } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import * as k8s from "@utils/k8s";

import cluster from "@app/tidb/cluster";
import setupJob from "@app/tidb/setupJob";
import setupCdcJob from "@app/tidb/setupCdcJob";
import * as fake from "@app/tidb/fake";

async function serverDeployment(ctx: Context): Promise<K8sObject[]> {
  if (ConfigUtils.isProduction(ctx.configs)) return [];
  return await k8s.combineGenerators(cluster)(ctx);
}

export default function (_ctx: Context): Module[] {
  const commonDeps = ["appconfig", "appsecret"];

  const useFakeTidb =
    (process.env["FAKE_TIDB"] || "").toLocaleLowerCase() === "true";

  return [
    new Module("tidb", {
      deps: commonDeps,
      generator: useFakeTidb ? fake.server : serverDeployment,
    }),
    new Module("tidb-setup", {
      deps: ["tidb"],
      generator: useFakeTidb ? fake.setupJob : setupJob,
    }),
    new Module("tidb-setup-changefeed", {
      deps: ["tidb"],
      generator: useFakeTidb ? fake.setupCdcJob : setupCdcJob,
    }),
  ];
}
