import { Context, Module, SourceGenerator } from "@anduintransaction/rivendell";
import { appConfig, appSecret } from "./appconfig";
import dbbackup from "./dbbackup";
import dgraph from "./dgraph";
import edgedb from "./edgedb";
import foundationdb from "./foundationdb";
import * as gondor from "./gondor";
import kafka from "./kafka";
import keycloak from "./keycloak";
import memcached from "./memcached";
import nats from "./nats";
import onlyoffice from "./onlyoffice";
import sharedPostgres from "./shared-postgres";
import svix from "./svix";
import temporal from "./temporal";
import tyk from "./tyk";
import vespa from "./vespa";
import tidb from "./tidb";
import openfga from "./openfga";
import infraWait from "./wait";
import mocksaml from "./mocksaml";
import mockoauth from "./mockoauth";

// @ts-ignore
function envBlacklistGenerator(
  envs: string[],
  generator: SourceGenerator,
): SourceGenerator {
  return (ctx: Context) => {
    if (envs.includes(ctx.env)) {
      return Promise.resolve([]);
    }
    return generator(ctx);
  };
}

// @ts-ignore
function envWhitelistGenerator(
  envs: string[],
  generator: SourceGenerator,
): SourceGenerator {
  return (ctx: Context) => {
    if (envs.includes(ctx.env)) {
      return generator(ctx);
    }
    return Promise.resolve([]);
  };
}

const commonDeps = ["appconfig", "appsecret"];
function isBoolEnv(key: string) {
  return (process.env[key] || "").toLocaleLowerCase() === "true";
}

export async function loadModules(ctx: Context) {
  const isInfraOnly = isBoolEnv("INFRA_ONLY");
  const isMigrateOnly = isBoolEnv("GONDOR_MIGRATE_ONLY");
  const isHotfixOnly = isBoolEnv("GONDOR_HOTFIX_ONLY");
  const isUpgradeOnly = isBoolEnv("GONDOR_UPGRADE_ONLY");
  const isSmokeTestOnly = isBoolEnv("GONDOR_SMOKE_TEST_ONLY");

  const modules = {
    infra: infraWait(ctx, [
      new Module("appconfig", {
        generator: appConfig,
      }),
      new Module("appsecret", {
        generator: appSecret,
      }),
      new Module("edgedb", {
        deps: commonDeps,
        generator: edgedb,
      }),
      new Module("memcached", {
        deps: commonDeps,
        generator: memcached,
      }),
      new Module("mocksaml", {
        deps: commonDeps,
        generator: mocksaml,
      }),
      new Module("mockoauth", {
        deps: commonDeps,
        generator: mockoauth,
      }),
      new Module("shared-postgres", {
        deps: commonDeps,
        generator: sharedPostgres,
      }),
      new Module("svix", {
        deps: ["shared-postgres"],
        generator: svix,
        waits: [{
          kind: "job",
          name: "init-shared-postgres",
          timeout: 600,
        }],
      }),
      new Module("dbbackup", {
        deps: commonDeps,
        generator: dbbackup,
      }),
      new Module("kafka", {
        deps: commonDeps,
        generator: kafka,
      }),
      new Module("foundationdb", {
        deps: commonDeps,
        generator: foundationdb,
      }),
      new Module("keycloak", {
        deps: commonDeps,
        generator: keycloak,
      }),
      new Module("nats", {
        deps: commonDeps,
        generator: nats,
      }),
      new Module("tyk", {
        deps: commonDeps,
        generator: tyk,
      }),
      ...temporal(ctx),
      ...vespa(ctx),
      new Module("onlyoffice", {
        deps: commonDeps,
        generator: onlyoffice,
      }),
      new Module("dgraph", {
        deps: commonDeps,
        generator: dgraph,
      }),
      ...tidb(ctx),
      ...openfga(ctx),
    ]),

    migrate: [
      new Module("gondor-migrate", {
        deps: [],
        generator: gondor.migrate("migrate"),
      }),
      new Module("gondor-hotfix-migrate", {
        deps: [],
        generator: gondor.migrate("hotfix"),
      }),
    ],

    smokeTest: [
      new Module("gondor-smoke-test", {
        deps: [],
        generator: gondor.smokeTest,
      }),
    ],

    gondor: [
      new Module("gondor-initializer", {
        deps: ["wait-stargazer"],
        generator: gondor.initializer,
        waits: [{
          kind: "job",
          name: "wait-stargazer",
          timeout: 600,
        }],
      }),
      new Module("gondor", {
        deps: ["gondor-initializer"],
        generator: gondor.service,
        waits: [{
          kind: "job",
          name: "gondor-initializer",
          timeout: 900,
        }],
      }),
    ],
  };

  if (isInfraOnly) {
    return modules.infra;
  }
  if (isSmokeTestOnly) {
    return modules.smokeTest;
  }
  if (isMigrateOnly) {
    return modules.migrate.filter((m) => m.name === "gondor-migrate");
  }
  if (isHotfixOnly) {
    return modules.migrate.filter((m) => m.name === "gondor-hotfix-migrate");
  }
  if (isUpgradeOnly) {
    return [...modules.infra, ...modules.gondor];
  }

  return Object.values(modules).flat();
}
