import { Context, Module } from "@anduintransaction/rivendell";

import { database, server, setup } from "@app/openfga";

export default function (ctx: Context): Module[] {
  const commonDeps = ["appconfig", "appsecret"];

  return [
    new Module("openfga-database", {
      deps: commonDeps,
      generator: database(ctx),
    }),
    new Module("openfga", {
      deps: ["openfga-database"],
      generator: server(ctx),
    }),
    new Module("openfga-setup", {
      deps: ["openfga"],
      generator: setup(ctx),
    }),
  ];
}
