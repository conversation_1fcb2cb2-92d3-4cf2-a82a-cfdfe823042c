import {
  Context,
  K8sObject,
  SourceGenerator,
} from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import * as k8s from "@utils/k8s";

import * as com from "@app/common";
import server from "@app/foundationdb/server";
import backupAgent from "@app/foundationdb/backupAgent";
import stats from "@app/foundationdb/stats";
import drAgent, { DrAgentSpec, initDrJob } from "@app/foundationdb/drAgent";
import * as fdbCommon from "@app/foundationdb/common";

export default async function (ctx: Context): Promise<K8sObject[]> {
  const isProduction = ConfigUtils.isProduction(ctx.configs);
  const primaryName = "foundationdb"; // need to comply with gondor config
  const readonlyName = `${primaryName}-readonly`; // need to comply with gondor config

  const generators: SourceGenerator[] = [backupAgent, stats];
  if (!isProduction) {
    generators.push(server(primaryName));
  }

  const fdbReadonlyFeature = fdbCommon.isEnableFdbReadonly(ctx);
  if (fdbReadonlyFeature & fdbCommon.FdbReadonlyCondition.EnabledServer) {
    generators.push(server(readonlyName));
  }
  if (fdbReadonlyFeature & fdbCommon.FdbReadonlyCondition.EnabledAgent) {
    const getEnv = (prefix: string, envName: string) => {
      return com.env.envValueFromGondorConfig(`${prefix}_${envName}`);
    };
    const fdbEnv = (envName: string) =>
      getEnv("STARGAZER_SERVICES_FOUNDATIONDB", envName);
    const fdbReadEnv = (envName: string) =>
      getEnv("STARGAZER_SERVICES_FOUNDATIONDB_READ_ONLY", envName);

    const drSpec: DrAgentSpec = {
      name: "fdb-dr-readonly",
      source: {
        host: fdbEnv("HOST"),
        port: fdbEnv("PORT"),
        descriptor: fdbEnv("DESCRIPTION"),
      },
      dest: {
        host: fdbReadEnv("HOST"),
        port: fdbReadEnv("PORT"),
        descriptor: fdbReadEnv("DESCRIPTION"),
      },
      replicas: isProduction ? 3 : 1,
    };
    generators.push(drAgent(drSpec), initDrJob(drSpec));
  }

  return k8s.combineGenerators(...generators)(ctx);
}
