import { Context, K8sObject } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import * as k8s from "@utils/k8s";

import gateway from "@app/tyk/gateway";
import pump from "@app/tyk/pump";
import redis from "@app/tyk/redis";

export default async function (ctx: Context): Promise<K8sObject[]> {
  const generator = ConfigUtils.isProduction(ctx.configs)
    ? k8s.combineGenerators(gateway, pump)
    : k8s.combineGenerators(redis, gateway, pump);
  return generator(ctx);
}
