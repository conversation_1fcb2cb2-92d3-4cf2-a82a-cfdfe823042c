import { Finder } from "@anduintransaction/rivendell";
import { Context, K8sObject } from "@anduintransaction/rivendell";
import * as k8s from "@utils/k8s";

import config from "@app/onlyoffice/configmap";
import postgresl from "@app/onlyoffice/postgresql";
import redis from "@app/onlyoffice/redis";
import rabbitmq from "@app/onlyoffice/rabbitmq";
import docServer from "@app/onlyoffice/docServer";

function isEnabled(ctx: Context) {
  return Finder.requiredBool(ctx.configs, ["onlyoffice"], false);
}

export default async function (ctx: Context): Promise<K8sObject[]> {
  if (!isEnabled(ctx)) {
    return [];
  }

  return await k8s.combineGenerators(
    config,
    docServer,
    postgresl,
    redis,
    rabbitmq,
  )(ctx);
}
