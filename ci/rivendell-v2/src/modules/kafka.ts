import { Context, K8sObject } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import * as k8s from "@utils/k8s";

import broker from "@app/kafka/server";
import exporter from "@app/kafka/exporter";

export default async function (ctx: Context): Promise<K8sObject[]> {
  const generator = ConfigUtils.isProduction(ctx.configs)
    ? exporter
    : k8s.combineGenerators(broker, exporter);
  return generator(ctx);
}
