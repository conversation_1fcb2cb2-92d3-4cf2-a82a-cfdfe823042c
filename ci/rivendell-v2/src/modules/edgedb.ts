import { Context, K8sObject } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import * as k8s from "@utils/k8s";
import pg from "@app/edgedb/postgresql";
import edgedb from "@app/edgedb/edgedb";

export default async function (ctx: Context): Promise<K8sObject[]> {
  const generator = ConfigUtils.isProduction(ctx.configs)
    ? edgedb
    : k8s.combineGenerators(pg, edgedb);
  return generator(ctx);
}
