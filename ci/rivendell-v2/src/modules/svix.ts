import { Context, K8sObject } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import * as k8s from "@utils/k8s";
import redis from "@app/svix/redis";
import backend from "@app/svix/backend";

export default async function (ctx: Context): Promise<K8sObject[]> {
  const generator = ConfigUtils.isProduction(ctx.configs)
    ? backend
    : k8s.combineGenerators(redis, backend);
  return generator(ctx);
}
