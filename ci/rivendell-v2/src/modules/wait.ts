import { Context, Module } from "@anduintransaction/rivendell";

import * as gondor from "./gondor";
import { isVespaEnabled } from "./vespa";

// this is the final step to wait for all infra deploy done
export default function (ctx: Context, deps: Module[]): Module[] {
  const stargazerWait = [{
    kind: "job",
    name: "init-keycloak",
    timeout: 600,
  }, {
    kind: "job",
    name: "temporal-post-setup",
    timeout: 600,
  }, {
    kind: "job",
    name: "tidb-setup",
    timeout: 600,
  }];
  if (isVespaEnabled(ctx)) {
    stargazerWait.push({
      kind: "job",
      name: "vespa-post-setup",
      timeout: 600,
    });
  }

  return [
    ...deps,

    // put wait stargazer at bottom and depends on all other
    new Module("wait-stargazer", {
      deps: deps.map((m) => m.name),
      generator: gondor.waitStargazer,
      waits: stargazerWait,
    }),
  ];
}
