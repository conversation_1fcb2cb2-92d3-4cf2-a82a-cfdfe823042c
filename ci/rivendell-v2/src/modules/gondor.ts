import {
  Context,
  K8sObject,
  SourceGenerator,
} from "@anduintransaction/rivendell";
import * as k8s from "@utils/k8s";

import ses from "@app/gondor/ses";
import maintenance from "@app/gondor/maintenance";

import full from "@app/gondor/topology/full";
import slim from "@app/gondor/topology/slim";
import simulator from "@app/gondor/topology/simulator";

import * as features from "@app/gondor/features";

export { default as waitStargazer } from "@app/gondor/wait-stargazer";
export { default as initializer } from "@app/gondor/initializer";
export { default as migrate } from "@app/gondor/migration";
export { default as smokeTest } from "@app/gondor/smokeTest";

export function service(ctx: Context): Promise<K8sObject[]> {
  const resolved = features.getGondorFeatures(ctx);

  const generators: SourceGenerator[] = [maintenance];
  if (resolved.ses) {
    generators.push(ses);
  }
  switch (resolved.topo) {
    case "slim":
      generators.push(slim);
      break;
    case "standard":
      generators.push(full);
      break;
    case "simulator":
      generators.push(simulator);
      break;
  }

  return k8s.combineGenerators(...generators)(ctx);
}
