import { Context, K8sObject } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import * as k8s from "@utils/k8s";
import pg from "@app/keycloak/postgresql";
import keycloak from "@app/keycloak/keycloak";
import init from "@app/keycloak/init";

export default async function(ctx: Context): Promise<K8sObject[]> {
  const generator = ConfigUtils.isProduction(ctx.configs)
    ? k8s.combineGenerators(keycloak, init)
    : k8s.combineGenerators(pg, keycloak, init);
  return generator(ctx);
}
