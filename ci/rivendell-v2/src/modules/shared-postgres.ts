import { Context, K8sObject } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import * as k8s from "@utils/k8s";
import pg from "@app/shared-postgres/postgresql";
import job from "@app/shared-postgres/init-job";

export default async function (ctx: Context): Promise<K8sObject[]> {
  const generator = ConfigUtils.isProduction(ctx.configs)
    ? k8s.combineGenerators(job)
    : k8s.combineGenerators(pg, job);
  return generator(ctx);
}
