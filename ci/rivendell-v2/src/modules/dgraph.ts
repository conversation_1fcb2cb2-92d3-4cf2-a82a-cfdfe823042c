import { Context, K8sObject } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import * as k8s from "@utils/k8s";

import alpha from "@app/dgraph/alpha";
import ratel from "@app/dgraph/ratel";
import zero from "@app/dgraph/zero";

export default async function (ctx: Context): Promise<K8sObject[]> {
  if (ConfigUtils.isProduction(ctx.configs))
    return [];
  return await k8s.combineGenerators(zero, alpha, ratel)(ctx);
}
