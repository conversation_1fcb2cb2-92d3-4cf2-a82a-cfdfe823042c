import { Context, Finder, Module } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";

import { emptyGenerator } from "@app/common/misc";
import { getSimpleClusterConfig, job, server } from "@app/vespa";

export function isVespaEnabled(ctx: Context) {
  return Finder.requiredBool(ctx.configs, ["vespa"], false);
}

export default function (ctx: Context): Module[] {
  const spec = ConfigUtils.isProduction(ctx.configs)
    ? getSimpleClusterConfig(ctx, "vespa", 3, 2)
    : getSimpleClusterConfig(ctx, "vespa", 1, 1);
  const isEnabled = isVespaEnabled(ctx);
  const serverGen = isEnabled ? server(spec) : emptyGenerator;
  const jobGen = isEnabled ? job(spec) : emptyGenerator;

  const commonDeps = ["appconfig", "appsecret"];
  return [
    new Module("vespa", {
      deps: commonDeps,
      generator: serverGen,
    }),
    new Module("vespa-post-setup", {
      deps: ["vespa"],
      generator: jobGen,
    }),
  ];
}
