import { Context, Module } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";

import post_setup_job from "@app/temporal/common/post-setup-job";
import setup_job from "@app/temporal/common/setup-job";
import temporal_deals from "@app/temporal/deals/temporal";
import temporal_others from "@app/temporal/others/temporal";

export default function (ctx: Context): Module[] {
  const commonDeps = ["appconfig", "appsecret", "shared-postgres"];

  const DealsModules = [
    new Module("temporal-setup", {
      deps: commonDeps,
      generator: setup_job,
      waits: [{
        kind: "job",
        name: "init-shared-postgres",
        timeout: 300,
      }],
    }),
    new Module("temporal-services", {
      deps: ["temporal-setup"],
      generator: temporal_deals,
      waits: [
        {
          kind: "job",
          name: "temporal-schema-setup",
          timeout: 300,
        },
      ],
    }),
    new Module("temporal-post-setup", {
      deps: ["temporal-services"],
      generator: post_setup_job,
      waits: [
        {
          kind: "deployment",
          name: "temporal-frontend-v2",
          timeout: 300,
        },
        {
          kind: "deployment",
          name: "temporal-history-v2",
          timeout: 300,
        },
        {
          kind: "deployment",
          name: "temporal-matching-v2",
          timeout: 300,
        },
        {
          kind: "deployment",
          name: "temporal-worker-v2",
          timeout: 300,
        },
      ],
    }),
  ];

  const OthersModules = [
    new Module("temporal-setup", {
      deps: commonDeps,
      generator: setup_job,
    }),
    new Module("temporal-services", {
      deps: ["temporal-setup"],
      generator: temporal_others,
      waits: [
        {
          kind: "job",
          name: "temporal-schema-setup",
          timeout: 300,
        },
      ],
    }),
    new Module("temporal-post-setup", {
      deps: ["temporal-services"],
      generator: post_setup_job,
      waits: [
        {
          kind: "deployment",
          name: "temporal-v2",
          timeout: 300,
        },
      ],
    }),
  ];

  const useDistributed = ConfigUtils.isProduction(ctx.configs);
  if (useDistributed) {
    return DealsModules;
  } else {
    return OthersModules;
  }
}
