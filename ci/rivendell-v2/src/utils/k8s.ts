import { Context, SourceGenerator } from "@anduintransaction/rivendell";
import * as path from "path";
import * as fs from "fs/promises";
import {
  ConfigMap,
  IContainerPort,
  IEnvVar,
  IEnvVarSource,
  IServicePort,
  IServiceSpec,
  Secret,
  Service,
} from "kubernetes-models/v1";
import { Deployment, StatefulSet } from "kubernetes-models/apps/v1";

export function combineGenerators(
  ...generators: SourceGenerator[]
): SourceGenerator {
  return async (ctx: Context) => {
    const res = [];
    for (const g of generators) {
      const objs = await g(ctx);
      res.push(...objs);
    }
    return res;
  };
}

export function createSecretFromRawData(
  name: string,
  data: Record<string, string>,
): Secret {
  const b64Data = Object.entries(data)
    .reduce((accum: Record<string, string>, [k, v]) => {
      accum[k] = btoa(v);
      return accum;
    }, {});
  return createSecret<PERSON>romB64(name, b64Data);
}

export function createSecretFromB64(
  name: string,
  data: Record<string, string>,
): Secret {
  return new Secret({
    metadata: {
      name: name,
    },
    data: data,
  });
}

export function createConfigMapFromData(
  name: string,
  data: Record<string, string>,
): ConfigMap {
  return new ConfigMap({
    metadata: {
      name: name,
    },
    data: data,
  });
}

export async function createConfigMapFromPath(
  name: string,
  dataPath: string, // must be absolute path
): Promise<ConfigMap> {
  const fStats = await fs.lstat(dataPath);
  const files: string[] = [];
  if (fStats.isDirectory()) {
    const dirFiles = await fs
      .readdir(dataPath, { recursive: false })
      .then((i) => i.map((i2) => path.join(dataPath, i2)));
    files.push(...dirFiles);
  } else {
    files.push(dataPath);
  }

  const datas = await Promise.all(files.map(async (f) => {
    return {
      fileName: path.basename(f),
      content: await fs.readFile(f).then((data) => data.toString("utf8")),
    };
  }));

  return new ConfigMap({
    metadata: { name },
    data: Object.fromEntries(
      datas.map(({ fileName, content }) => [fileName, content]),
    ),
  });
}

export type IEnvVarValue = string | IEnvVarSource;

export function envFromSource(key: string, value: IEnvVarValue): IEnvVar {
  return typeof value === "string"
    ? { name: key, value: value }
    : { name: key, valueFrom: value };
}

/**
 * @deprecated using `envFromSource` instead
 */
export function envFromValue(env: string, value: string): IEnvVar {
  return envFromSource(env, value);
}

export function envFromFieldPath(env: string, fieldPath: string): IEnvVar {
  return envFromSource(env, {
    fieldRef: {
      fieldPath: fieldPath,
    },
  });
}

export function envValueFromConfigMap(
  cm: string,
  cmRef: string,
  optional?: boolean,
): IEnvVarSource {
  return {
    configMapKeyRef: {
      name: cm,
      key: cmRef,
      optional: optional,
    },
  };
}

export function envFromConfigMap(
  env: string,
  cm: string,
  cmRef: string,
  optional?: boolean,
): IEnvVar {
  return envFromSource(env, envValueFromConfigMap(cm, cmRef, optional));
}

export function envValueFromSecret(
  secret: string,
  secretRef: string,
  optional?: boolean,
): IEnvVarSource {
  return {
    secretKeyRef: {
      name: secret,
      key: secretRef,
      optional: optional,
    },
  };
}

export function envFromSecret(
  env: string,
  secret: string,
  secretRef: string,
  optional?: boolean,
): IEnvVar {
  return envFromSource(env, envValueFromSecret(secret, secretRef, optional));
}

export interface MonitoredServiceSpec {
  ports: IServicePort[];
  probe?: boolean;
  probeTcp?: boolean;
  probeSchema?: string;
  scrape?: boolean;
  metricPort?: number;
}

export interface SimpleServiceSpec extends MonitoredServiceSpec {
  svcName?: string;
  clusterIP?: IServiceSpec["clusterIP"];
  type?: IServiceSpec["type"];
  publishNotReadyAddresses?: IServiceSpec["publishNotReadyAddresses"];
}

export function monitoringAnnotation(spec: MonitoredServiceSpec) {
  const res = {};
  if (spec.probe) {
    Object.assign(res, {
      "anduin.prometheus.io/probe": "true",
    });
  }
  if (spec.probeTcp) {
    Object.assign(res, {
      "anduin.prometheus.io/probetcp": "true",
    });
  }
  if (!!spec.probeSchema) {
    Object.assign(res, {
      "anduin.prometheus.io/probe-scheme": spec.probeSchema!,
    });
  }
  if (spec.scrape) {
    const metricPort = spec.metricPort || spec.ports[0].port;
    Object.assign(res, {
      "prometheus.io/scrape": "true",
      "prometheus.io/port": `${metricPort}`,
    });
  }
  return res;
}

export function serviceFromDeployment(
  deploy: Deployment,
  spec: SimpleServiceSpec,
) {
  const isHeadless = (spec.clusterIP || "").toLowerCase() === "none";
  const defaultSvcName = isHeadless
    ? `${deploy.metadata?.name}-headless`
    : deploy.metadata?.name;
  const svcName = spec.svcName || defaultSvcName;

  return new Service({
    metadata: {
      name: svcName,
      annotations: monitoringAnnotation(spec),
    },
    spec: {
      selector: deploy.spec?.selector.matchLabels,
      type: spec.type,
      clusterIP: spec.clusterIP,
      publishNotReadyAddresses: spec.publishNotReadyAddresses,
      ports: spec.ports,
    },
  });
}

export function serviceFromStatefulSet(
  sts: StatefulSet,
  spec: SimpleServiceSpec,
) {
  const isHeadless = (spec.clusterIP || "").toLowerCase() === "none";
  const defaultSvcName = isHeadless
    ? sts.spec?.serviceName
    : sts.metadata?.name;
  const svcName = spec.svcName || defaultSvcName;

  return new Service({
    metadata: {
      name: svcName,
      annotations: monitoringAnnotation(spec),
    },
    spec: {
      selector: sts.spec?.selector.matchLabels,
      type: spec.type,
      clusterIP: spec.clusterIP,
      publishNotReadyAddresses: spec.publishNotReadyAddresses,
      ports: spec.ports,
    },
  });
}

export function serviceFromExternalName(
  svcName: string,
  externalName: string,
  spec: MonitoredServiceSpec,
) {
  return new Service({
    metadata: {
      name: svcName,
      annotations: monitoringAnnotation(spec),
    },
    spec: {
      type: "ExternalName",
      externalName: externalName,
      ports: spec.ports,
    },
  });
}

export function containerPortsFromMap(
  ports: Record<string, number>,
): IContainerPort[] {
  return Object
    .entries(ports)
    .map(([portName, portNumber]) => ({
      name: portName,
      containerPort: portNumber,
    }));
}

export function servicePortsFromMap(
  ports: Record<string, number>,
): IServicePort[] {
  return Object
    .entries(ports)
    .map(([portName, portNumber]) => ({
      name: portName,
      port: portNumber,
      targetPort: portName,
    }));
}
