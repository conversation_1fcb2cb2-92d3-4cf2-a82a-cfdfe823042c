import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import { loadContextFromCli } from "@contextes";
import { loadModules } from "@modules/index";

function isDryrun() {
  const env = process.env["DRY_RUN"] || "false";
  return env.trim().toLowerCase() == "true";
}

function getRunner(ctx: Context) {
  const kubeCtx = process.env["RIVENDELL_CONTEXT"];
  const namespace = ConfigUtils.getK8sNamespace(ctx.configs);
  if (isDryrun()) {
    return new KubeDryrunRunner(kubeCtx, namespace);
  }
  return new KubeRunner(kubeCtx, namespace);
}

async function main() {
  const ctx = await loadContextFromCli(process.argv.slice(2));
  const modules = await loadModules(ctx);
  const graph = ModuleGraph.resolve(...modules);
  const planner = new Planner(ctx);
  const plan = await planner.planFromGraph(graph);
  const runner = getRunner(ctx);
  runner.run(plan);
}

main();
