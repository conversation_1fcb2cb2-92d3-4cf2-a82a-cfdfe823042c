import { ModuleGraph, Planner } from "@anduintransaction/rivendell";
import { loadContextFromCli } from "@contextes";
import { loadModules } from "@modules/index";
import * as merge from "deepmerge";

async function main() {
  const ctx = await loadContextFromCli(process.argv.slice(2), false);
  ctx.configs = merge(ctx.configs, {
    stargazerSprintTag: "beta-307.0-candidate-9-f3bc6a",
  });

  const modules = await loadModules(ctx);
  const debugModules = modules
    .filter((m) => !["appconfig", "appsecret"].includes(m.name))
    .map((m) => m.cloneGeneratorOnly());

  const graph = ModuleGraph.resolve(...debugModules);
  const planner = new Planner(ctx);
  const plan = await planner.planFromGraph(graph);
  Planner.showManifests(plan);
}

main();
