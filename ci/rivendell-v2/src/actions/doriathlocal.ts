import * as yaml from "yaml";
import { IPodSpec } from "kubernetes-models/v1";
import { Deployment, StatefulSet } from "kubernetes-models/apps/v1";
import { Job } from "kubernetes-models/batch/v1";
import { ModuleGraph, Planner } from "@anduintransaction/rivendell";
import { loadContextFromCli } from "@contextes";
import { loadModules } from "@modules/index";

function extractImageFromSpec(spec: IPodSpec): string[] {
  const initImages = (spec.initContainers || []).map((c) => c.image!);
  const images = (spec.containers || []).map((c) => c.image!);
  return [initImages, images].flat();
}

async function main() {
  process.env["INFRA_ONLY"] = "true";
  process.env["FAKE_TIDB"] = "true";

  const ctx = await loadContextFromCli([
    "--env=local",
    "--var=stargazerSprintTag=1.0-local",
  ], false);
  let modules = await loadModules(ctx);
  const graph = ModuleGraph.resolve(...modules);
  const planner = new Planner(ctx);
  const plan = (await planner.planFromGraph(graph))
    .filter((s) => s.type === "deploy")
    .filter((s) =>
      ["deployment", "statefulset", "job"].includes(
        s.object.kind.toLowerCase(),
      )
    );
  const images = plan.map((s) => {
    const object = s.object;
    switch (object.kind.toLowerCase()) {
      case "deployment":
        const deploy = object as Deployment;
        return extractImageFromSpec(deploy.spec!.template.spec!);
      case "statefulset":
        const sts = object as StatefulSet;
        return extractImageFromSpec(sts.spec!.template.spec!);
      case "job":
        const job = object as Job;
        return extractImageFromSpec(job.spec!.template.spec!);
      default:
        throw new Error("unreachable");
    }
  }).flat();

  const doriath: any = {
    root_dir: ".",
    pull: Array.from(new Set(images)).sort(),
  };
  console.log(yaml.stringify(doriath));
}

main();
