import { Plan, Planner } from "@anduintransaction/rivendell";
import { loadContextFromCli } from "@contextes";
import { appConfig } from "@modules/appconfig";

async function main() {
  const ctx = await loadContextFromCli(process.argv.slice(2), false);
  ctx.configs = Object.assign(ctx.configs, { maintenance: true });

  const objects = await appConfig(ctx);
  const plan: Plan = objects
    .filter((o) => o.kind.toLowerCase() === "ingress")
    .map((o) => ({
      type: "deploy",
      module: "gondor",
      object: o,
    }));
  Planner.showManifests(plan);
}

main();
