import { Mo<PERSON>leGraph, Planner } from "@anduintransaction/rivendell";
import { loadContextFromCli } from "@contextes";
import { loadModules } from "@modules/index";

async function main() {
  const ctx = await loadContextFromCli(process.argv.slice(2), false);
  const modules = await loadModules(ctx);
  const graph = ModuleGraph.resolve(...modules);
  const planner = new Planner(ctx);
  const plan = await planner.planFromGraph(graph);
  Planner.show(plan);
}

main();
