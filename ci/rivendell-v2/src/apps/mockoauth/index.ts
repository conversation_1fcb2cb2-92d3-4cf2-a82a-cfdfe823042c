import { Context, K8sObject } from "@anduintransaction/rivendell";
import { Deployment } from "kubernetes-models/apps/v1";
import * as k8s from "@utils/k8s";
import * as com from "../common";

export default async function (ctx: Context): Promise<K8sObject[]> {
  const name = "mock-oauth";
  const commonLabels = { app: name };

  const deploy = new Deployment({
    metadata: {
      name: name,
    },
    spec: {
      replicas: 1,
      selector: {
        matchLabels: commonLabels,
      },
      template: {
        metadata: {
          labels: commonLabels,
        },
        spec: {
          nodeSelector: com.node.nodeDatabase(ctx),
          imagePullSecrets: com.misc.getPullSecrets(ctx),
          containers: [{
            name: "mock-oauth",
            image: "ghcr.io/navikt/mock-oauth2-server:2.1.9",            
          }],
        },
      },
    },
  });

  const svc = k8s.serviceFromDeployment(deploy, {
    ports: [{
      name: "mock-oauth",
      port: 8080,
      protocol: "TCP",
      targetPort: 8080,
    }],
  });

  return [
    deploy,
    svc,
  ];
}
