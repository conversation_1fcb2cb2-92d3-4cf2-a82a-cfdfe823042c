import { Context, K8sObject } from "@anduintransaction/rivendell";
import * as path from "path";
import { Job } from "kubernetes-models/batch/v1";
import * as k8s from "@utils/k8s";
import { ConfigUtils } from "@configutils";
import * as com from "../common";

export default async function (ctx: Context): Promise<K8sObject[]> {
  const name = "init-shared-postgres";

  const cm = await k8s.createConfigMapFromPath(
    name,
    path.resolve(__dirname, `./resources/init-db.sh`),
  );

  const job = new Job({
    metadata: { name },
    spec: {
      backoffLimit: 0,
      ttlSecondsAfterFinished: 1800,
      template: {
        metadata: { name },
        spec: {
          restartPolicy: "Never",
          imagePullSecrets: com.misc.getPullSecrets(ctx),
          containers: [{
            name: "init-db",
            image: ConfigUtils.getDockerPublicImage(ctx.configs, "postgres15"),
            command: ["sh"],
            args: [`/opt/anduin/scripts/init-db.sh`],
            env: [
              k8s.envFromValue("PGHOST", "shared-postgres"),
              k8s.envFromValue("PGPORT", "5432"),
              com.env.envFromSharedEnvSecret(
                "PGUSER",
                "STARGAZER_SERVICES_SHARED_POSTGRES_USER",
              ),
              com.env.envFromSharedEnvSecret(
                "PGPASSWORD",
                "STARGAZER_SERVICES_SHARED_POSTGRES_PASSWORD",
              ),
            ],
            resources: com.resources.getResources(
              ctx,
              com.resources.FargateResourceSpecs.S,
            ),
          }],
        },
      },
    },
  });

  com.storage.cmAsVolume(ctx, job.spec!.template.spec!, {
    cmName: cm.metadata!.name!,
    volumeName: "scripts",
    mounts: [{
      container: "init-db",
      path: "/opt/anduin/scripts",
    }],
  });

  return [cm, job];
}
