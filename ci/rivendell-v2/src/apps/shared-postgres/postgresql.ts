import { Context, K8sObject } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import * as k8s from "@utils/k8s";
import postgresql from "../common/postgresql";
import * as com from "../common";

export default async function (ctx: Context): Promise<K8sObject[]> {
  return postgresql(ctx, {
    name: "shared-postgres",
    pgConfigs: {
      "max_connections": ConfigUtils.isLocal(ctx.configs) ? "400" : "200",
    },
    nodeSelector: com.node.nodeDatabase(ctx),
    dockerImage: ConfigUtils.getDockerPublicImage(ctx.configs, "postgres15"),
    resources: {
      limits: {
        memory: "1536Mi",
      },
      requests: {
        memory: "1536Mi",
      },
    },
    defaultDb: "postgres",
    creds: {
      user: {
        valueFrom: k8s.envValueFromSecret(
          com.env.SharedEnvSecretName,
          "STARGAZER_SERVICES_SHARED_POSTGRES_USER",
        ),
      },
      password: {
        valueFrom: k8s.envValueFromSecret(
          com.env.SharedEnvSecretName,
          "STARGAZER_SERVICES_SHARED_POSTGRES_PASSWORD",
        ),
      },
    },
    storageSize: "50Gi",
    needPullSecret: true,
  });
}
