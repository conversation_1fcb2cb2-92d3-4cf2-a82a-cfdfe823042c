import { Context, K8sObject } from "@anduintransaction/rivendell";
import { IServicePort, Service } from "kubernetes-models/v1";

export default async function (ctx: Context): Promise<K8sObject[]> {
    const name = "cassandradb";
    const selector = { name: "temporal-cassandra" };
    const ports: IServicePort[] = [
    {
        name: "cassandra",
        port: 9042,
        protocol: "TCP",
        targetPort: 9042,
    },
    ];

    const svc = new Service({
        metadata: {
        name: name,
        annotations: {
        "anduin.prometheus.io/probetcp": "true",
        },
    },
    spec: {
        selector,
        ports
        },
    });

    return [svc];
}
