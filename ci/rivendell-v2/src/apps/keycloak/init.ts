import { Context, K8sObject } from "@anduintransaction/rivendell";
import { Job } from "kubernetes-models/batch/v1";
import * as k8s from "@utils/k8s";
import { ConfigUtils } from "@configutils";
import * as com from "../common";

export default async function (ctx: Context): Promise<K8sObject[]> {
  const name = "init-keycloak";

  const job = new Job({
    metadata: { name },
    spec: {
      ttlSecondsAfterFinished: 1800,
      template: {
        metadata: { name },
        spec: {
          restartPolicy: "Never",
          imagePullSecrets: com.misc.getPullSecrets(ctx),
          containers: [
            {
              name: "init-keycloak",
              image: ConfigUtils.getDockerEcrImage(
                ctx.configs,
                "keycloakSidecar",
              ),
              env: [
                k8s.envFromValue("KEYCLOAK_HOST", "keycloak"),
                com.env.envFromSharedEnvSecret(
                  "KEYCLOAK_USERNAME",
                  "STARGAZER_SERVICES_KEYCLOAK_USER",
                ),
                com.env.envFromSharedEnvSecret(
                  "KEYCLOAK_PASSWORD",
                  "STARGAZER_SERVICES_KEYCLOAK_PASSWORD",
                ),
                com.env.envFromGondorSecret(
                  "KEYCLOAK_REALM_TOKEN",
                  "STARGAZER_SERVICES_KEYCLOAK_REALM_TOKEN",
                ),
              ],
              resources: com.resources.getResources(
                ctx,
                com.resources.FargateResourceSpecs.S,
              ),
            },
          ],
        },
      },
    },
  });

  return [job];
}
