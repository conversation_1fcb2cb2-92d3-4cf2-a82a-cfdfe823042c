import { Context } from "@anduintransaction/rivendell";
import * as com from "@app/common";
import { ConfigUtils } from "@configutils";
import * as k8s from "@utils/k8s";
import { Deployment } from "kubernetes-models/apps/v1";
import { ConfigMap, Service } from "kubernetes-models/v1";

function isWorkerService(serviceName: string) {
  return serviceName == "worker";
}

export default async function (
  ctx: Context,
  serviceName: string,
  port: number,
  replicas: number,
  temporalCm: ConfigMap,
  dynamicCm: ConfigMap,
): Promise<[Deployment, Service]> {
  const deploymentName = `temporal-${serviceName}-v2`;
  const deploymentLabel = { name: deploymentName };

  const deployment = new Deployment({
    metadata: { name: deploymentName },
    spec: {
      replicas: replicas,
      selector: {
        matchLabels: deploymentLabel,
      },
      strategy: {
        type: "RollingUpdate",
        rollingUpdate: {
          maxSurge: "25%",
          maxUnavailable: "25%",
        },
      },
      template: {
        metadata: {
          labels: deploymentLabel,
          annotations: Object.assign({
            "prometheus.io/port": "9090",
            "prometheus.io/scrape": "true",
            "config-hash": com.checksum.hash(
              temporalCm.data!["config_template.yaml"],
            ),
            "dynamic-hash": com.checksum.hash(
              dynamicCm.data!["dynamic_config.yaml"],
            ),
            version: "v2",
          }),
        },
        spec: {
          nodeSelector: com.node.nodeDatabase(ctx),
          imagePullSecrets: com.misc.getPullSecrets(ctx),
          containers: [
            {
              name: deploymentName,
              image: ConfigUtils.getDockerPublicImage(
                ctx.configs,
                "temporalServer",
              ),
              readinessProbe: isWorkerService(serviceName) ? undefined : {
                tcpSocket: { port: port },
                initialDelaySeconds: 5,
                timeoutSeconds: 30,
              },
              livenessProbe: isWorkerService(serviceName) ? undefined : {
                tcpSocket: { port: port },
                initialDelaySeconds: 150,
              },
              env: [
                k8s.envFromSource("TEMPORAL_ALLOW_NO_AUTH", "true"),
                k8s.envFromFieldPath("POD_IP", "status.podIP"),
                com.env.envFromSharedEnvSecret(
                  "TEMPORAL_PG_USER",
                  "STARGAZER_SERVICES_SHARED_POSTGRES_USER",
                ),
                com.env.envFromSharedEnvSecret(
                  "TEMPORAL_PG_PASSWORD",
                  "STARGAZER_SERVICES_SHARED_POSTGRES_PASSWORD",
                ),
                k8s.envFromSource("SERVICES", serviceName),
              ],
              volumeMounts: [
                {
                  mountPath: "/etc/temporal/config/config_template.yaml",
                  name: "config",
                  subPath: "config_template.yaml",
                },
                {
                  mountPath: "/etc/temporal/dynamic_config",
                  name: "dynamic-config",
                },
              ],
            },
          ],
          volumes: [
            {
              name: "config",
              configMap: {
                name: temporalCm.metadata!.name!,
              },
            },
            {
              name: "dynamic-config",
              configMap: {
                name: dynamicCm.metadata!.name!,
                items: [
                  {
                    key: "dynamic_config.yaml",
                    path: "dynamic_config.yaml",
                  },
                ],
              },
            },
          ],
        },
      },
    },
  });

  const headlessSvc = new Service({
    metadata: {
      name: `${deploymentName}-headless`,
      annotations: isWorkerService(serviceName)
        ? {
          "service.alpha.kubernetes.io/tolerate-unready-endpoints": "true",
        }
        : {
          "anduin.prometheus.io/probetcp": "true",
          "service.alpha.kubernetes.io/tolerate-unready-endpoints": "true",
        },
    },
    spec: {
      selector: deployment.spec?.selector.matchLabels,
      type: "ClusterIP",
      clusterIP: "None",
      publishNotReadyAddresses: true,
      ports: [
        {
          port: port,
          targetPort: port,
          protocol: "TCP",
          name: "grpc-rpc",
        },
        {
          port: 9090,
          targetPort: 9090,
          protocol: "TCP",
          name: "metrics",
        },
      ],
    },
  });

  return [deployment, headlessSvc];
}
