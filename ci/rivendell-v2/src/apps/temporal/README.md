### Temporal Deployment
Deployment should have some sort of orders, should look like this:
```
Storage Services ? -> Setup Job ! -> Temporal Services ! -> Post setup Job !
```

Where:
```
Storage Services: Cassandra/Postgres. Prod environment decouples Storage Services deployment in [infrastructure](https://github.com/anduintransaction/infrastructure)
Setup Job: Config Storage Services schema
Temporal Services: Provide temporal different sevices, services are enabled depend on environment.
Post setup Job: Config temporal for app usage
```

Folder structure:

1. common: Services/Jobs used by all environment
2. deals: Services enable on Prod environment
3. others: Services enable on Non-Prod environment