import { Context, K8sObject } from "@anduintransaction/rivendell";
import * as com from "@app/common";
import { ConfigUtils } from "@configutils";
import * as k8s from "@utils/k8s";
import { Deployment } from "kubernetes-models/apps/v1";

export default async function (ctx: Context): Promise<K8sObject[]> {
  const name = "temporal-web-v2";
  const label = { name: name };

  const deployment = new Deployment({
    metadata: { name: name },
    spec: {
      replicas: 1,
      selector: {
        matchLabels: label,
      },
      template: {
        metadata: {
          labels: label,
          annotations: {
            version: "v2",
          },
        },
        spec: {
          nodeSelector: com.node.nodeDatabase(ctx),
          imagePullSecrets: com.misc.getPullSecrets(ctx),
          containers: [
            {
              name: name,
              image: ConfigUtils.getDockerPublicImage(
                ctx.configs,
                "temporalWeb",
              ),
              env: [
                k8s.envFromSource("TEMPORAL_ADDRESS", "temporal-v2:7233"),
                k8s.envFromSource(
                  "TEMPORAL_CORS_UNSAFE_ALLOW_ALL_ORIGINS",
                  "true",
                ),
                k8s.envFromSource("TEMPORAL_CSRF_COOKIE_INSECURE", "true"),
              ],
            },
          ],
        },
      },
    },
  });

  const svc = k8s.serviceFromDeployment(deployment, {
    probe: true,
    probeSchema: "http",
    ports: [
      {
        name: name,
        port: 8088,
        targetPort: 8080,
        protocol: "TCP",
      },
    ],
  });

  return [deployment, svc];
}
