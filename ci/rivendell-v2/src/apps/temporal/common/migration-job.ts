import { Context, K8sObject } from "@anduintransaction/rivendell";
import * as com from "@app/common";
import { ConfigUtils } from "@configutils";
import * as k8s from "@utils/k8s";
import { Job } from "kubernetes-models/batch/v1";

function getFullSvc(svc: string) {
  return `${svc}.$(POD_NAMESPACE).svc.cluster.local:7233`;
}

export default async function (ctx: Context): Promise<K8sObject[]> {
  const name = "temporal-migration";
  const job = new Job({
    metadata: { name },
    spec: {
      backoffLimit: 5,
      ttlSecondsAfterFinished: 1800,
      template: {
        metadata: { name },
        spec: {
          restartPolicy: "Never",
          imagePullSecrets: com.misc.getPullSecrets(ctx),
          containers: [
            {
              name: "migration",
              image: ConfigUtils.getDockerEcrImage(
                ctx.configs,
                "temporalMigration",
              ),
              env: [
                k8s.envFromSource("TEMPORAL_NS", "default"),
                k8s.envFromFieldPath("POD_NAMESPACE", "metadata.namespace"),
                k8s.envFromSource("TEMPORAL_SOURCE", getFullSvc("temporal-v2")),
              ],
              resources: com.resources.getResources(
                ctx,
                com.resources.FargateResourceSpecs.S,
              ),
            },
          ],
        },
      },
    },
  });

  return [job];
}
