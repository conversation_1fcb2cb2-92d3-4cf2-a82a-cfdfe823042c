import { Context, K8sObject } from "@anduintransaction/rivendell";
import * as com from "@app/common";
import { ConfigUtils } from "@configutils";
import * as k8s from "@utils/k8s";
import { Deployment } from "kubernetes-models/apps/v1";

export default async function (ctx: Context): Promise<K8sObject[]> {
  const name = "temporal-admin-tools-v2";
  const label = { name: name };

  const deployment = new Deployment({
    metadata: {
      name: name,
    },
    spec: {
      replicas: 1,
      selector: {
        matchLabels: label,
      },
      template: {
        metadata: {
          labels: label,
          annotations: {
            version: "v2",
          },
        },
        spec: {
          nodeSelector: com.node.nodeDatabase(ctx),
          imagePullSecrets: com.misc.getPullSecrets(ctx),
          containers: [
            {
              name: name,
              image: ConfigUtils.getDockerPublicImage(
                ctx.configs,
                "temporalAdminTools",
              ),
              env: [
                k8s.envFromFieldPath("POD_NAMESPACE", "metadata.namespace"),
                k8s.envFromSource(
                  "TEMPORAL_ADDRESS",
                  "temporal-v2.$(POD_NAMESPACE).svc.cluster.local:7233",
                ),
              ],
            },
          ],
        },
      },
    },
  });
  return [deployment];
}
