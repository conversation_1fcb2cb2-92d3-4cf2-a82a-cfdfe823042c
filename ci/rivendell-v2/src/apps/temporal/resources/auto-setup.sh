#!/usr/bin/env bash
# Ref: https://github.com/temporalio/docker-builds/blob/main/docker/auto-setup.sh

set -ex

# PG config
: "${DBNAME:=temporal_v2}"
: "${VISIBILITY_DBNAME:=temporal_visibility_v2}"
: "${POSTGRES_PORT:=5432}"
: "${POSTGRES_SEEDS:=shared-postgres}"
: "${POSTGRES_USER:=postgres}"
: "${POSTGRES_PWD:=}"

wait_for_postgres() {
  until nc -z "${POSTGRES_SEEDS%%,*}" "${POSTGRES_PORT}"; do
      echo 'Waiting for PostgreSQL to startup.'
      sleep 1
  done
  echo 'PostgreSQL started.'
}

temporal_sql() {
  local db=$1
  shift
  temporal-sql-tool \
    --plugin postgres12 \
    --ep "${POSTGRES_SEEDS}" \
    -u "${POSTGRES_USER}" \
    -p "${POSTGRES_PORT}" \
    --db "${db}" \
    --tls=false \
    $@
}

setup_postgres_schema() {
  # TODO: check for export removal
  export SQL_PASSWORD=${POSTGRES_PWD}
  POSTGRES_VERSION_DIR=v12

  # Setup primary persistent store
  SCHEMA_DIR=/etc/temporal/schema/postgresql/${POSTGRES_VERSION_DIR}/temporal/versioned
  temporal_sql ${DBNAME} create
  temporal_sql ${DBNAME} setup-schema -v 0.0
  temporal_sql ${DBNAME} update-schema -d ${SCHEMA_DIR}

  # Setup visibility persistent store
  VISIBILITY_SCHEMA_DIR=/etc/temporal/schema/postgresql/${POSTGRES_VERSION_DIR}/visibility/versioned
  temporal_sql ${VISIBILITY_DBNAME} create
  temporal_sql ${VISIBILITY_DBNAME} setup-schema -v 0.0
  temporal_sql ${VISIBILITY_DBNAME} update-schema -d ${VISIBILITY_SCHEMA_DIR}
}

# Main scripts
echo "Wait postgres"
wait_for_postgres
setup_postgres_schema
