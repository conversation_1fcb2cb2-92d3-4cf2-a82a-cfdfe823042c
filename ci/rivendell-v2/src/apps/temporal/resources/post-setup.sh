#!/usr/bin/env bash
# Ref: https://github.com/temporalio/docker-builds/blob/main/docker/auto-setup.sh

set -e

function wait_cluster {
  # Waiting for Temporal server up
  until temporal operator cluster health | grep -q SERVING; do
    echo "Waiting for Temporal server to start..."
    sleep 1
  done
  echo "Temporal server started."
}

function create_ns {
  NS=${1}
  DESCRIPTION=${2:-""}
  RETENTION=${3:-"1"}
  if [ -z "$NS" ]; then
    echo "Namespace name not set"
    exit 1
  fi

  # Create namespace if not existed
  if ! temporal operator namespace describe -n "${NS}"; then
    echo "Namespace ${NS} not found. Creating..."
    temporal operator \
      namespace create \
      --description "${DESCRIPTION}" \
      -n "${NS}"
  else
    echo "Namespace ${NS} already registered"
  fi

  # Wait for newly created namespace refresh
  until temporal operator namespace describe -n "${NS}"; do
    echo "Waiting for namespace cache to refresh..."
    sleep 1
  done
  echo "Namespace cache refreshed."

  echo "Update namespace $NS retention to ${RETEN<PERSON>ON}"
  temporal operator \
    namespace update \
    --retention "${RETENTION}" \
    -n "${NS}"
}

function add_search_attribute_if_not_exists {
  local ns=$1
  local attr_name=$2
  local attr_type=$3
  if ! temporal operator search-attribute list --namespace $ns | grep -w $attr_name; then
    temporal operator search-attribute create \
      --namespace $ns \
      --name $attr_name --type $attr_type
  fi
}

function add_search_attributes_for_public_api {
  until temporal operator search-attribute list --namespace public-api; do
    echo "Waiting for namespace cache to refresh..."
    sleep 1
  done
  echo "Namespace search attribute cache refreshed."

  # Setup custom search attribute
  echo "Setup search attribute"
  add_search_attribute_if_not_exists public-api ServiceAccount Text
  add_search_attribute_if_not_exists public-api SyncApi Bool
}

echo "Temporal CLI address: ${TEMPORAL_ADDRESS}"
wait_cluster
create_ns "default" "Default namespace for Temporal Server." "3d"
create_ns "public-api" "For gondor public api" "30d"
add_search_attributes_for_public_api
