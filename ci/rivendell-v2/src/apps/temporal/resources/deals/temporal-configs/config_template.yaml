log:
  stdout: true
  level: "info"

persistence:
  defaultStore: default
  visibilityStore: visibility
  numHistoryShards: 512
  datastores:
    default:
      sql:
        user: "{{ .Env.TEMPORAL_PG_USER }}"
        password: "{{ .Env.TEMPORAL_PG_PASSWORD }}"
        pluginName: postgres12
        databaseName: temporal_v2
        connectAddr: shared-postgres
        connectProtocol: tcp
        maxConns: 30
        maxIdleConns: 30
        maxConnLifetime: "2h"
        tls:
          enabled: false
    visibility:
      sql:
        user: "{{ .Env.TEMPORAL_PG_USER }}"
        password: "{{ .Env.TEMPORAL_PG_PASSWORD }}"
        pluginName: postgres12
        databaseName: temporal_visibility_v2
        connectAddr: shared-postgres
        connectProtocol: tcp
        maxConns: 8
        maxIdleConns: 8
        maxConnLifetime: "2h"
        tls:
          enabled: false

global:
  membership:
    maxJoinDuration: 30s
    broadcastAddress: "{{ default .Env.POD_IP "0.0.0.0" }}"
  metrics:
    # duplicate metrics if use `temporal` prefix
    prefix: tempor
    prometheus:
      framework: "tally"
      timerType: "histogram"
      listenAddress: "0.0.0.0:9090"
  pprof:
    port: 7936

services:
  frontend:
    rpc:
      grpcPort: 7233
      membershipPort: 6933
      bindOnIP: "0.0.0.0"
  history:
    rpc:
      grpcPort: 7234
      membershipPort: 6934
      bindOnIP: "0.0.0.0"
  matching:
    rpc:
      grpcPort: 7235
      membershipPort: 6935
      bindOnIP: "0.0.0.0"
  worker:
    rpc:
      grpcPort: 7239
      membershipPort: 6939
      bindOnIP: "0.0.0.0"

clusterMetadata:
  enableGlobalNamespace: false
  failoverVersionIncrement: 10
  masterClusterName: "active"
  currentClusterName: "active"
  clusterInformation:
    active:
      enabled: true
      initialFailoverVersion: 1
      rpcName: "temporal-frontend-v2"
      rpcAddress: "127.0.0.1:7233"

dcRedirectionPolicy:
  policy: "noop"
  toDC: ""

archival:
  history:
    state: "enabled"
    enableRead: true
    provider:
      filestore:
        fileMode: "0666"
        dirMode: "0766"
  visibility:
    state: "enabled"
    enableRead: true
    provider:
      filestore:
        fileMode: "0666"
        dirMode: "0766"

namespaceDefaults:
  archival:
    history:
      state: "disabled"
      URI: "file:///tmp/temporal_archival/development"
    visibility:
      state: "disabled"
      URI: "file:///tmp/temporal_vis_archival/development"

publicClient:
  hostPort: dns:///temporal-frontend-v2-headless:7233

dynamicConfigClient:
  filepath: "/etc/temporal/dynamic_config/dynamic_config.yaml"
  pollInterval: "60s"
