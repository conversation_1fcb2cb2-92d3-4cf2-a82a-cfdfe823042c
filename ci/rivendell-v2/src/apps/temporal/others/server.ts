import { Context, K8sObject } from "@anduintransaction/rivendell";
import * as com from "@app/common";
import { ConfigUtils } from "@configutils";
import * as k8s from "@utils/k8s";
import { Deployment } from "kubernetes-models/apps/v1";
import { ConfigMap, Service } from "kubernetes-models/v1";

export default async function (
  ctx: Context,
  temporalCm: ConfigMap,
  dynamicCm: ConfigMap,
): Promise<K8sObject[]> {
  const name = "temporal-v2";
  const label = { name: name };
  const port = 7233;

  const deployment = new Deployment({
    metadata: { name: name },
    spec: {
      revisionHistoryLimit: 10,
      replicas: ctx.env === "blackwood" ? 3 : 1,
      selector: {
        matchLabels: label,
      },
      strategy: {
        type: "RollingUpdate",
        rollingUpdate: {
          maxSurge: 0,
          maxUnavailable: 1,
        },
      },
      template: {
        metadata: {
          labels: label,
          annotations: Object.assign({
            "prometheus.io/port": "9090",
            "prometheus.io/scrape": "true",
            "config-hash": com.checksum.hash(
              temporalCm.data!["config_template.yaml"],
            ),
            "dynamic-hash": com.checksum.hash(
              dynamicCm.data!["dynamic_config.yaml"],
            ),
            version: "v2",
          }),
        },
        spec: {
          nodeSelector: com.node.nodeDatabase(ctx),
          imagePullSecrets: com.misc.getPullSecrets(ctx),
          containers: [
            {
              name: "temporal-auto-setup",
              image: ConfigUtils.getDockerPublicImage(
                ctx.configs,
                "temporalServer",
              ),
              readinessProbe: {
                tcpSocket: { port: port },
                initialDelaySeconds: 5,
                timeoutSeconds: 30,
              },
              livenessProbe: {
                tcpSocket: { port: port },
                initialDelaySeconds: 120,
                timeoutSeconds: 30,
              },
              env: [
                k8s.envFromSource("TEMPORAL_ADDRESS", `${name}:7233`),
                k8s.envFromSource("TEMPORAL_ALLOW_NO_AUTH", "true"),
                k8s.envFromFieldPath("POD_IP", "status.podIP"),
                com.env.envFromSharedEnvSecret(
                  "TEMPORAL_PG_USER",
                  "STARGAZER_SERVICES_SHARED_POSTGRES_USER",
                ),
                com.env.envFromSharedEnvSecret(
                  "TEMPORAL_PG_PASSWORD",
                  "STARGAZER_SERVICES_SHARED_POSTGRES_PASSWORD",
                ),
              ],
              volumeMounts: [
                {
                  mountPath: "/etc/temporal/config/config_template.yaml",
                  name: "config",
                  subPath: "config_template.yaml",
                },
                {
                  mountPath: "/etc/temporal/dynamic_config",
                  name: "dynamic-config",
                },
              ],
            },
          ],
          volumes: [
            {
              name: "config",
              configMap: {
                name: temporalCm.metadata!.name!,
              },
            },
            {
              name: "dynamic-config",
              configMap: {
                name: dynamicCm.metadata!.name!,
                items: [
                  {
                    key: "dynamic_config.yaml",
                    path: "dynamic_config.yaml",
                  },
                ],
              },
            },
          ],
        },
      },
    },
  });

  const svc = k8s.serviceFromDeployment(deployment, {
    probeTcp: true,
    publishNotReadyAddresses: false,
    ports: [
      {
        port: port,
        targetPort: port,
        protocol: "TCP",
        name: name,
      },
    ],
  });

  const headlessSvc = new Service({
    metadata: {
      name: "temporal-v2-headless",
      annotations: {
        "anduin.prometheus.io/probetcp": "true",
        "service.alpha.kubernetes.io/tolerate-unready-endpoints": "true",
      },
    },
    spec: {
      selector: deployment.spec?.selector.matchLabels,
      type: "ClusterIP",
      clusterIP: "None",
      publishNotReadyAddresses: true,
      ports: [
        {
          port: port,
          targetPort: port,
          protocol: "TCP",
          name: name,
        },
      ],
    },
  });

  return [deployment, svc, headlessSvc];
}
