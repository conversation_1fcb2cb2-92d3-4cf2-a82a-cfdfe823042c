import { Context, K8sObject } from "@anduintransaction/rivendell";

import AdminToolGenerator from "@app/temporal/common/admin-tool";
import ConfigMapGenerator from "@app/temporal/common/cm";
import WebGenerator from "@app/temporal/common/web";
import ServerGenerator from "@app/temporal/others/server";

export default async function (ctx: Context): Promise<K8sObject[]> {
  const adminTool = await AdminToolGenerator(ctx);
  const web = await WebGenerator(ctx);
  const [temporalCm, dynamicCm] = await ConfigMapGenerator(ctx, "others");
  const server = await ServerGenerator(ctx, temporalCm, dynamicCm);
  return [temporalCm, dynamicCm, ...server, ...adminTool, ...web];
}
