import { Context, Finder, K8sObject } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import { Secret, ServiceAccount } from "kubernetes-models/v1";

const Certs: Record<string, string> = {
  "us-east-1": `-----BEGIN CERTIFICATE-----
MIIC7jCCApSgAwIBAgIRAP0Yw6p22VNVQlPF8V0TlbEwCgYIKoZIzj0EAwIwgbkx
CzAJBgNVBAYTAlVTMQswCQYDVQQIEwJDQTEWMBQGA1UEBxMNU2FuIEZyYW5jaXNj
bzEaMBgGA1UECRMRMTAxIFNlY29uZCBTdHJlZXQxDjAMBgNVBBETBTk0MTA1MRcw
FQYDVQQKEw5IYXNoaUNvcnAgSW5jLjFAMD4GA1UEAxM3Q29uc3VsIEFnZW50IENB
IDMzNjQyMzI2NjYzMzYzMDg1Mjc5OTM2Mzc3NzEzNTYxMDQwMjIyNTAeFw0yMTA5
MjkwODQyMDBaFw0zMTA5MjcwODQyMDBaMIG5MQswCQYDVQQGEwJVUzELMAkGA1UE
CBMCQ0ExFjAUBgNVBAcTDVNhbiBGcmFuY2lzY28xGjAYBgNVBAkTETEwMSBTZWNv
bmQgU3RyZWV0MQ4wDAYDVQQREwU5NDEwNTEXMBUGA1UEChMOSGFzaGlDb3JwIElu
Yy4xQDA+BgNVBAMTN0NvbnN1bCBBZ2VudCBDQSAzMzY0MjMyNjY2MzM2MzA4NTI3
OTkzNjM3NzcxMzU2MTA0MDIyMjUwWTATBgcqhkjOPQIBBggqhkjOPQMBBwNCAASa
O0kqtINsdDW4w3mp9Xie0nJNBqiTPToxK7T0yZohpL1x7vpKWrUvgSlcgEr889Sw
bVMZGKR9NKCnElV4bNnuo3sweTAOBgNVHQ8BAf8EBAMCAYYwDwYDVR0TAQH/BAUw
AwEB/zApBgNVHQ4EIgQgmFc9HFsK6YWhurZn511hDs0PyM9mFpZFjyvx6HDnN9Qw
KwYDVR0jBCQwIoAgmFc9HFsK6YWhurZn511hDs0PyM9mFpZFjyvx6HDnN9QwCgYI
KoZIzj0EAwIDSAAwRQIhANeZiHVSLuVFo56HGrjqrSx5e9TnUPNHsWXt+b9ceNjN
AiAr4YP1Qm40oNaHUqHxTfvxuX4onVSaT8vY39mQh8d8Wg==
-----END CERTIFICATE-----
`,

  "eu-west-1": `-----BEGIN CERTIFICATE-----
MIIBdzCCAR6gAwIBAgIRAIGH8mea2VdshgXgzxGz0C8wCgYIKoZIzj0EAwIwGjEY
MBYGA1UEAxMPVmF1bHQgZXUtd2VzdC0xMB4XDTIzMTEwMjA1MDk0NVoXDTMzMTAz
MDA1MDk0NVowGjEYMBYGA1UEAxMPVmF1bHQgZXUtd2VzdC0xMFkwEwYHKoZIzj0C
AQYIKoZIzj0DAQcDQgAEm6k3ge/LchFK/o6uwSL6EZVX/W13ezezH4Ly+Ftz9iCr
Fmp5nU9owibK08/NEtmryaS40HCYeCNqabVurzGQuKNFMEMwDgYDVR0PAQH/BAQD
AgEGMBIGA1UdEwEB/wQIMAYBAf8CAQEwHQYDVR0OBBYEFMHTeo/9wssxgNTrvFdt
dzEjNHaWMAoGCCqGSM49BAMCA0cAMEQCIADVw7rnDOOJbB1Asq+beolxT/tc0G4j
MKqM2N/Egd1dAiBeEpGKv7ykGBFDAGDPnuD0lW2cu6wzC0RrKg9eYvqBeg==
-----END CERTIFICATE-----
`,
};

export default async function (ctx: Context): Promise<K8sObject[]> {
  const isVaultEnable = Finder.optionalBool(ctx.configs, ["vault"], false);
  if (!isVaultEnable) return [];

  const sa = new ServiceAccount({
    metadata: {
      name: "vault",
    },
  });

  const region = ConfigUtils.getAwsRegion(ctx.configs);
  const certData = Certs[region] || "";
  const secret = new Secret({
    metadata: {
      name: "vault-tls-secret",
    },
    data: {
      "ca-cert.pem": btoa(certData),
    },
  });

  return [sa, secret];
}
