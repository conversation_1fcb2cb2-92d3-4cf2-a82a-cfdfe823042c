import * as path from "path";
import { Context, K8sObject } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import * as k8s from "@utils/k8s";
import vault from "./vault";

export default async function (ctx: Context): Promise<K8sObject[]> {
  const env = ConfigUtils.getEnvName(ctx.configs);
  const cfgPath = path.join(__dirname, `./environments/${env}/index`);
  const { default: generator } = await import(cfgPath);
  const objs = await k8s.combineGenerators(generator, vault)(
    ctx,
  );
  return objs;
}
