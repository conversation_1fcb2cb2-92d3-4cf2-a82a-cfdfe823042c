import { Context, Finder } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";

export default function (ctx: Context): Record<string, string> {
  return {
    STARGAZER_SERVICES_COOKIE_EXPIRE: "1 day",
    STARGAZER_SERVICES_VERIFY_EMAIL_TOKEN_TIMEOUT: "1 day",
    STARGAZER_SERVICES_RESET_PASSWORD_TOKEN_TIMEOUT: "1 day",
    STARGAZER_SERVICES_LINK_ACCOUNT_TOKEN_TIMEOUT: "1 day",

    STARGAZER_SERVICES_KEYCLOAK_ADMIN_SSL: "false",
    STARGAZER_SERVICES_KEYCLOAK_ADMIN_HOST: "keycloak",
    STARGAZER_SERVICES_KEYCLOAK_ADMIN_PORT: "80",

    STARGAZER_KAFKA_HOST: "kafka-v3",
    STARGAZER_KAFKA_PORT: "9092",
    STARGAZER_KAFKA_BOOTSTRAP_SERVER: "kafka-v3:9092",

    STARGAZER_ONLYOFFICE_SCHEME: "http",
    STARGAZER_ONLYOFFICE_HOST: "oo-doc-server",

    STARGAZER_SERVICES_FOUNDATIONDB_HOST: "foundationdb",
    STARGAZER_SERVICES_FOUNDATIONDB_PORT: "4500",
    STARGAZER_SERVICES_FOUNDATIONDB_DESCRIPTION: "fdb:fdb",
    STARGAZER_SERVICES_FOUNDATIONDB_READ_ONLY_HOST: "foundationdb",
    STARGAZER_SERVICES_FOUNDATIONDB_READ_ONLY_PORT: "4500",
    STARGAZER_SERVICES_FOUNDATIONDB_READ_ONLY_DESCRIPTION: "fdb:fdb",

    STARGAZER_SERVICES_TIMESCALEDB_HOST: "timescaledb-v3",
    STARGAZER_SERVICES_TIMESCALEDB_PORT: "5432",
    STARGAZER_SERVICES_TIMESCALEDB_DATABASE_NAME: "timescaledb",
    STARGAZER_SERVICES_TIMESCALEDB_CONCURRENT_SESSIONS: "16",

    STARGAZER_SERVICES_JWT_RSA_KEY_MAP_FILE: "/opt/anduin/jwt/rsa-keymap.json",
    STARGAZER_SERVICES_JWT_RSA_DEFAULT_KID: "anduin-jwt-key-v1",

    STARGAZER_SERVICES_TEMPORAL_HOST: "temporal-v2",
    STARGAZER_SERVICES_TEMPORAL_PORT: "7233",

    STARGAZER_SERVICES_DGRAPH_HTTP_ENDPOINT: "http://dgraph-alpha:8080",
    STARGAZER_SERVICES_DGRAPH_GRPC_ENDPOINT: "dgraph-alpha:9080",
    STARGAZER_SERVICES_DGRAPH_ZERO_GRPC_ENDPOINT: "dgraph-zero:5080",

    STARGAZER_SERVICES_FUSEKI_ENDPOINT: "http://fuseki:3030",

    STARGAZER_EMAIL_SENDING_THROTTLE_ONE_MESSAGE: "2 seconds",

    STARGAZER_SERVICES_TYK_ADMIN_HOST: "tyk-control",
    STARGAZER_SERVICES_TYK_ADMIN_PORT: "80",
    STARGAZER_SERVICES_TYK_ADMIN_SSL: "false",

    STARGAZER_SERVICES_TYK_HTTP_HOST: "tyk-http",
    STARGAZER_SERVICES_TYK_HTTP_PORT: "80",
    STARGAZER_SERVICES_TYK_HTTP_SSL: "false",

    STARGAZER_SERVICES_CASSANDRA_HOST: "cassandradb",
    STARGAZER_SERVICES_CASSANDRA_PORT: "9042",

    STARGAZER_SERVICES_OPENFGA_HOST: "openfga",
    STARGAZER_SERVICES_OPENFGA_PORT: "8080",
    STARGAZER_SERVICES_OPENFGA_FUND_HOST: "openfga-fund",
    STARGAZER_SERVICES_OPENFGA_FUND_PORT: "8080",

    STARGAZER_SERVICES_TIDB_HOST: "tidb-tidb",
    STARGAZER_SERVICES_TIDB_PORT: "4000",
    STARGAZER_SERVICES_TIDB_DB: "gondor",
    STARGAZER_SERVICES_TIDB_USER: "gondor",

    STARGAZER_SERVICES_TIDB_CDC_HOST: "tidb-ticdc",
    STARGAZER_SERVICES_TIDB_CDC_PORT: "8301",

    STARGAZER_SERVICES_PINOT_ZK_HOST: "zookeeper-local",
    STARGAZER_SERVICES_PINOT_ZK_PORT: "2181",

    STARGAZER_SERVICES_PINOT_CONTROLLER_HOST: "pinot-controller",
    STARGAZER_SERVICES_PINOT_CONTROLLER_PORT: "9000",
    STARGAZER_SERVICES_PINOT_CLUSTER_NAME: `pinot-${ctx.env}`,

    STARGAZER_DEFAULT_AWS_REGION: ConfigUtils.getAwsRegion(ctx.configs),

    STARGAZER_PUBLIC_API_ENDPOINT_DEFAULT_TIMEOUT: "60 seconds",
    STARGAZER_PUBLIC_API_ENDPOINT_MAX_PENDING_REQUESTS: "1000",
    STARGAZER_PUBLIC_API_SYNC_WORKFLOW_WORKFLOW_EXECUTION_DEFAULT_TIMEOUT:
      "60 seconds",
    STARGAZER_PUBLIC_API_SYNC_WORKFLOW_WORKFLOW_RUN_DEFAULT_TIMEOUT:
      "30 seconds",
    STARGAZER_PUBLIC_API_SYNC_WORKFLOW_TASK_DEFAULT_TIMEOUT: "30 seconds",
    STARGAZER_PUBLIC_API_SYNC_WORKFLOW_ACTIVITY_START_TO_CLOSE_DEFAULT_TIMEOUT:
      "30 seconds",
    STARGAZER_PUBLIC_API_ASYNC_WORKFLOW_WORKFLOW_EXECUTION_TIMEOUT:
      "60 minutes",
    STARGAZER_PUBLIC_API_ASYNC_WORKFLOW_WORKFLOW_RUN_TIMEOUT: "20 minutes",
    STARGAZER_PUBLIC_API_ASYNC_WORKFLOW_WORKFLOW_TASK_TIMEOUT: "60 seconds",
    STARGAZER_PUBLIC_API_ASYNC_WORKFLOW_ACTIVITY_START_TO_CLOSE_TIMEOUT:
      "120 seconds",

    STARGAZER_TRACING_COLLECTOR_ENDPOINT:
      "eks-opentelemetry-collector.tracing.svc.cluster.local",
    STARGAZER_TRACING_COLLECTOR_SCHEMA: "http",
    STARGAZER_TRACING_COLLECTOR_PORT: "4317",
    get STARGAZER_TRACING_FULL_COLLECTOR_ENDPOINT() {
      const schema = this.STARGAZER_TRACING_COLLECTOR_SCHEMA;
      const endpoint = this.STARGAZER_TRACING_COLLECTOR_ENDPOINT;
      const port = this.STARGAZER_TRACING_COLLECTOR_PORT;
      return `${schema}://${endpoint}:${port}`;
    },

    STARGAZER_SERVICES_YB_HOST: "yugabyte-tserver",
    STARGAZER_SERVICES_YB_PORT: "5433",
    STARGAZER_SERVICES_YB_DB: "gondor",
    STARGAZER_SERVICES_YB_USER: "gondor",

    STARGAZER_GLOBAL_SIGN_KEY_STORE: "/opt/anduin/globalsign/globalsign.jks",
    STARGAZER_GLOBAL_SIGN_BASE_URL:
      "https://emea.api.dss.globalsign.com:8443/v2",

    STARGAZER_NATS_RPC_ENDPOINT: "nats://nats:4222",

    STARGAZER_SERVICES_EDGEDB_HOST: "edgedb-v2",
    STARGAZER_SERVICES_EDGEDB_PORT: "5656",
    STARGAZER_SERVICES_EDGEDB_DB: "edgedb",
    STARGAZER_SERVICES_EDGEDB_USER: "edgedb",

    STARGAZER_DATAPIPELINE_ENABLED: "true",

    STARGAZER_SVIX_HOST: "svix",
    STARGAZER_SVIX_WEBHOOK_ENABLED: "true",

    STARGAZER_SERVICES_DATA_ROOM_SEMANTIC_SEARCH_ENABLED: `${
      Finder.requiredBool(ctx.configs, ["vespa"], false)
    }`,

    STARGAZER_SERVICES_ANALYTICS_USE_EVENT_PROPERTIES_V2: "false",

    STARGAZER_SERVICES_TEMPORAL_WORKER_MAX_WF_SLOT: "100",
  };
}
