import { Context, K8sObject } from "@anduintransaction/rivendell";
import {
  createGondorIngress,
  gondorForwardRule,
  simpleForwardRule,
} from "../../ingress";

export default function (ctx: Context): K8sObject[] {
  const gondorDomains = [
    "demo.anduin.app",
    "*.simulator.annduin.app",
    "simulator.target.annduin.app",
  ]
    .map((d) =>
      gondorForwardRule(ctx, d, {
        serviceName: "gondor",
        servicePort: 8080,
      })
    );

  return [
    ...createGondorIngress(ctx, {
      albGroupName: "gondor-demo",
      rules: [
        simpleForwardRule("nats-demo-simulator.anduin.app", {
          serviceName: "nats",
          servicePort: 5222,
        }),
        simpleForwardRule("doc-server-demo-simulator.anduin.app", {
          serviceName: "oo-doc-server",
          servicePort: 80,
        }),
        ...gondorDomains,
      ],
    }),
  ];
}
