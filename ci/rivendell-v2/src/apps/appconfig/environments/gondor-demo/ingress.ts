import { Context, K8sObject } from "@anduintransaction/rivendell";
import { createRedirectIngress } from "../../ingress";

export default function (ctx: Context): K8sObject[] {
  const baseDomain = "demo.anduin.dev";
  const appDomain = (app: string) => (`${app}-${baseDomain}`);

  const ingress = createRedirectIngress(
    ctx,
    { albGroupName: "gondor-demo" },
    {
      ["deals-demo.anduintransact.com"]: {
        ruleName: "main",
        target: baseDomain,
      },
      ["demo.anduinsign.io"]: {
        ruleName: "sign",
        target: appDomain("sign"),
      },
      ["demo-internal.fundsub.io"]: {
        ruleName: "fundsub",
        target: appDomain("fundsub"),
      },
      ["demo.target.annduin.app"]: {
        ruleName: "target",
        target: appDomain("target"),
      },
      ["doc-server-demo.anduintransact.com"]: {
        ruleName: "doc-server",
        target: appDomain("doc-server"),
      },
      ["portal-demo.anduin.app"]: {
        ruleName: "portal",
        target: appDomain("portal"),
      },
    },
  );

  return [
    ingress,
  ];
}
