import * as merge from "deepmerge";
import { Context, K8sObject } from "@anduintransaction/rivendell";
import * as k8s from "@utils/k8s";
import common from "../../common";
import {
  createGondorIngress,
  gondorForwardRule,
  standardGondorRoutes,
} from "../../ingress";
import config from "./config";
import ingress from "./ingress";

export default async function (ctx: Context): Promise<K8sObject[]> {
  const cfg = merge.all([
    await common(ctx),
    await config(ctx),
  ]) as Record<string, string>;

  const igv2 = createGondorIngress(ctx, {
    ingressName: "gondor-v2",
    albGroupName: "gondor-demo",
    rules: [
      ...standardGondorRoutes(ctx, {
        envSuffix: "demo",
        customPortal: true,
        customFS: true,
        customIA: true,
        customId: true,
        customTarget: true,
        customSign: true,
        customDataroom: true,
        customPlatform: true,
        customIntegration: true,
        customRia: true,
      }),
      gondorForwardRule(ctx, "*.demo.annduin.app", {
        serviceName: "gondor",
        servicePort: 8080,
      }),
      gondorForwardRule(ctx, "*.demo.anduin.io", {
        serviceName: "gondor",
        servicePort: 8080,
      }),
      // maintain contract for external clients
      // TODO: remove later
      gondorForwardRule(ctx, "api-demo.anduin.app", {
        serviceName: "tyk-http",
        servicePort: 80,
      }),
      gondorForwardRule(ctx, "id-demo.anduin.app", {
        serviceName: "gondor",
        servicePort: 8080,
      }),
    ],
  });

  return [
    k8s.createConfigMapFromData("gondor", cfg),
    ...ingress(ctx),
    ...igv2,
  ];
}
