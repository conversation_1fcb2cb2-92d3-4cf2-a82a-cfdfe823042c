import { Context, K8sObject } from "@anduintransaction/rivendell";
import {
  createGondorIngress,
  gondorForwardRule,
  simpleForwardRule,
} from "../../ingress";

export default function (ctx: Context): K8sObject[] {
  const gondorDomains = [
    "simulator-test.anduin.app",
    "*.simulator-test.annduin.app",
    "simulator-test.target.annduin.app",
  ]
    .map((d) =>
      gondorForwardRule(ctx, d, {
        serviceName: "gondor",
        servicePort: 8080,
      })
    );

  return [
    ...createGondorIngress(ctx, {
      albGroupName: "gondor-staging",
      rules: [
        simpleForwardRule("nats-demo-simulator-test.anduin.app", {
          serviceName: "nats",
          servicePort: 5222,
        }),
        simpleForwardRule("doc-server-demo-simulator-test.anduin.app", {
          serviceName: "oo-doc-server",
          servicePort: 80,
        }),
        ...gondorDomains,
      ],
    }),
  ];
}
