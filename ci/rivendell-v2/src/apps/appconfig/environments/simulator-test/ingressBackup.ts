import { Context, K8sObject } from "@anduintransaction/rivendell";
import { createRedirectIngress } from "../../ingress";

export default function (ctx: Context): K8sObject[] {
  return [createRedirectIngress(
    ctx,
    {
      ingressName: "gondor-prod-backup",
      albGroupName: "gondor-demo",
    },
    {
      ["simulator.anduin.app"]: {
        ruleName: "main",
        target: "simulator-test.anduin.app",
      },
    },
  )];
}
