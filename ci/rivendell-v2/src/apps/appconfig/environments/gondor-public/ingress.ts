import { Context, K8sObject } from "@anduintransaction/rivendell";
import {
  createGondorIngress,
  gondorForwardRule,
  simpleForwardRule,
} from "../../ingress";

export default function (ctx: Context): K8sObject[] {
  const gondorDomains = [
    "deals.anduintransact.com",
    "app.fundsub.io",
    "fundsub.anduin.app",
    "dataroom.anduin.app",
    "sign.anduin.app",
    "platform.anduin.app",
    "id.anduin.app",
    "anduin.app",
    "issuetracker.anduin.app",
    "investoraccess.anduin.app",
    "integrations.anduin.app",
    "advisors.anduin.app",
    "advisor.anduin.app",
    "*.anduin.io",
    "*.anduin.partners",
    "target.anduin.app",
  ]
    .map((d) =>
      gondorForwardRule(ctx, d, {
        serviceName: "gondor",
        servicePort: 8080,
      })
    );

  return [
    ...createGondorIngress(ctx, {
      albGroupName: "gondor",
      rules: [
        simpleForwardRule("nats.anduin.app", {
          serviceName: "nats",
          servicePort: 5222,
        }),
        simpleForwardRule("doc-server.anduin.app", {
          serviceName: "oo-doc-server",
          servicePort: 80,
        }),
        gondorForwardRule(ctx, "api.anduin.app", {
          serviceName: "tyk-http",
          servicePort: 80,
        }),
        gondorForwardRule(ctx, "s-api.anduin.app", {
          serviceName: "tyk-http",
          servicePort: 80,
        }),
        gondorForwardRule(ctx, "portal.anduin.app", {
          serviceName: "gondor-admin-portal",
          servicePort: 8080,
        }),
        ...gondorDomains,
      ],
    }),
  ];
}
