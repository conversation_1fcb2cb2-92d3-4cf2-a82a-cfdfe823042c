import { Context } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";

export default async function (ctx: Context): Promise<Record<string, string>> {
  return {
    STARGAZER_ENDPOINT_HOSTNAME: "gondor",
    STAR<PERSON>ZER_SERVICES_BASE_URL: "https://deals.anduintransact.com",
    STARGAZER_SERVICES_PORTAL_URL: "https://portal.anduin.app",
    STARGAZER_SERVICES_DEPLOYMENT: "gondor-public",
    STARGAZER_ENVIRONMENT_FALLBACK_SUBDOMAIN: "anduin.io",
    STARGAZER_ENVIRONMENT_INTERNAL_SUBDOMAINS: "anduin.io anduin.partners",
    STARGAZER_ENVIRONMENT_EXTERNAL_TARGET_DOMAIN: "target.anduin.app",

    STARGAZER_SERVICES_ANDUIN_INTERNAL_ENTITIES: "ent70do7ye98407r",

    STARGAZER_ONLYOFFICE_PUBLIC_SCHEME: "https",
    STARGAZER_ONLYOFFICE_PUBLIC_HOST: "doc-server.anduin.app",
    STARGAZER_ONLYOFFICE_GONDOR_SERVER: "http://gondor:8080",

    STARGAZER_SERVICES_SAML_ENTITY_ID: "https://id.anduin.app",

    STARGAZER_SERVICES_CASSANDRA_REPLICATION: "3",

    STARGAZER_SERVICES_SEED_CUSTOM_DOMAINS: "",

    STARGAZER_SERVICES_MULTI_REGION_ENABLE_SECONDARY_REGION: "false",
    STARGAZER_SERVICES_MULTI_REGION_MAIN_REGION_ENDPOINT:
      "http://localhost:8080",
    STARGAZER_SERVICES_MULTI_REGION_REGION_NAME: "US",

    STARGAZER_S3_DEFAULT_BUCKET: "gondor-assets-document-production",
    STARGAZER_S3_BATCH_DOWNLOAD_BUCKET: "gondor-batch-download-production",
    STARGAZER_S3_PUBLIC_BUCKET: "gondor-public-document-production",
    STARGAZER_S3_FORM_TEMPLATE_BUCKET: "gondor-form-template",
    STARGAZER_S3_EXPORT_BUCKET: "gondor-export-production",
    STARGAZER_S3_FORM_STORAGE_BUCKET: "gondor-form-storage-production",
    STARGAZER_S3_WEB_BUILDER_BUCKET: "gondor-webbuilder-production",
    STARGAZER_S3_EMAIL_STORAGE_BUCKET: "gondor-email-storage",
    STARGAZER_S3_RESOURCES_BUCKET: "gondor-assets-document-production",
    STARGAZER_S3_TEMP_UPLOAD_BUCKET: "gondor-temp-upload-production",
    STARGAZER_S3_CLOUD_FRONT_ENABLED: "true",
    STARGAZER_S3_CLOUD_FRONT_DOMAIN: "docs.anduin.app",

    STARGAZER_EMAIL_SENDING_DYNAMO_DB_STATUS_TABLE: "EmailStatusProduction",

    STARGAZER_SERVICES_SYSTEM_AUDIT_LOG_GROUP: "gondor-system-audit-production",

    STARGAZER_EMAIL_HIGH_ENGAGEMENT_DOMAIN: "anduin.app",
    STARGAZER_EMAIL_STANDARD_DOMAIN: "anduin.email",
    STARGAZER_EMAIL_SENDING_DISABLE: "false",
    STARGAZER_EMAIL_SENDING_MANAGER_USE_SCHEDULER: "true",
    STARGAZER_SES_CONFIGURATION_SET: "ses-production",
    STARGAZER_SES_S3_BUCKET: "gondor-ses-email-production",
    STARGAZER_SES_SQS_RECEIVE_QUEUE: "ses-receive-production",
    STARGAZER_SES_SQS_EVENT_QUEUE: "ses-event-production",
    STARGAZER_SES_REGION: "us-east-1",
    STARGAZER_CUSTOMER_SUPPORT_EMAIL: "<EMAIL>",

    STARGAZER_SERVICES_ANALYTICS_S3_BUCKET: "stargazer-analytics-production",
    STARGAZER_SERVICES_ANALYTICS_S3_PREFIX: "v2",

    STARGAZER_SERVICES_CAPTCHA_ENABLED: "true",

    STARGAZER_SERVICES_OAUTH2_GOOGLE_ENABLED: "true",
    STARGAZER_SERVICES_OAUTH2_ENABLE_SKIP_LINK_ACCOUNT: "false",

    STARGAZER_SERVICES_SAFE_GA_TRACKING_ID: "UA-*********-1",
    STARGAZER_SERVICES_DATA_ROOM_GA_TRACKING_ID: "UA-*********-2",
    STARGAZER_SERVICES_ISSUE_TRACKER_GA_TRACKING_ID: "UA-*********-3",

    STARGAZER_SERVICES_GTM_CONTAINER_ID: "",

    STARGAZER_SERVICES_SURVICATE_FUNDSUB_SURVEY_URL:
      "https://survey.survicate.com/workspaces/********************************/web_surveys.js",

    STARGAZER_SERVICES_GETFEEDBACK_FUNDSUB_BUTTON_ID: "6b28f8c82425",
    STARGAZER_SERVICES_GETFEEDBACK_FUNDSUB_BUTTON_HASH:
      "sha256-tmnz5oMxlWC+F49xDiTqCp3X5WGHAJcMZf/FMVAkhq4=",

    STARGAZER_SERVICES_MASTER_KEY_ARN:
      "aws-kms://arn:aws:kms:us-east-1:************:key/afaccf61-cd8a-432f-9195-08a71676ea89",
    STARGAZER_SERVICES_ENCRYPTED_DATA_KEY_BLOB:
      "AQICAHgYJRihTvejjFmD+eB8OXJG4VBhgll2k3eersiBq9mlHAEv5zg/3Y5a4Mn23UQI43u7AAAA4zCB4AYJKoZIhvcNAQcGoIHSMIHPAgEAMIHJBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDMkJpgTQXnm5gaTaTgIBEICBmytHKPzsgUHIXyXkedEVgKXvmFjJId9YUHAk/9WL8W8lzlWuNj5ZHjxakwXogdRD+QrxXtrHOwB7/9I/fsNaoc6yat5YMC1JY2H/9p2jiObW5X4HDyvAPP9ygbl+/3UuovBeAibHv9E4cH5tBPnO8c+7pzy+TR9Icwvhq/aWxKYvhlidO/5TM3IksspFkqLE7Xw6l2Vs0FTP1Q1B",

    STARGAZER_SERVICES_SHAREPOINT_USERNAME:
      "<EMAIL>",
    STARGAZER_SERVICES_SHAREPOINT_TENANT:
      "da6e6f9c-8c65-4fff-92d5-cc99aa412bd2",
    STARGAZER_SERVICES_SHAREPOINT_CLIENT_ID:
      "b1f98303-d2b4-4bcb-b186-bc3a5fafe508",
    STARGAZER_SERVICES_SHAREPOINT_SCOPE:
      "user.read offline_access files.readwrite.all sites.readwrite.all",

    STARGAZER_SERVICES_SERVERLESS_NAMESPACE: "prod",
    STARGAZER_SERVICES_SERVERLESS_ASYNC_SQS_QUEUE:
      "anduin-serverless-public-response-queue",

    STARGAZER_TRACING_ENABLED: "true",

    STARGAZER_SERVICES_TEXTRACT_ENABLED: "true",
    STARGAZER_SERVICES_TEXTRACT_SNS_ARN:
      "arn:aws:sns:us-east-1:************:AmazonTextract-gondor-production-20230504075852510700000002",
    STARGAZER_SERVICES_TEXTRACT_SNS_ROLE_ARN:
      "arn:aws:iam::************:role/textract-sns-production",
    STARGAZER_SERVICES_TEXTRACT_SQS_QUEUE:
      "AmazonTextract-gondor-production-20230504075859180600000004",
    STARGAZER_SERVICES_TEXTRACT_S3_BUCKET: "gondor-textract-production",

    STARGAZER_DOCUSIGN_DEMO_ACCOUNT_ID: "23b0dcd8-8684-49b2-9b94-fe4e522ac7a6",
    STARGAZER_DOCUSIGN_DEMO_USER_ID: "646ff222-9d43-43bb-bc55-1277556a5f44",
    STARGAZER_DOCUSIGN_DEMO_CERITY_API_USER_ID:
      "770a0369-38ea-4d1d-8b6a-026644fbc8af",

    STARGAZER_DOCUSIGN_PROD_ACCOUNT_ID: "021be170-4852-4969-8d90-22bc69209d10",
    STARGAZER_DOCUSIGN_PROD_USER_ID: "28a54c5f-ad5a-4fbc-9a66-abe62fe7e3d3",
    STARGAZER_DOCUSIGN_PROD_CERITY_API_USER_ID:
      "df8c37ad-a98f-4d81-b4d0-946f8217ccc3",

    STARGAZER_DOCUSIGN_CAN_USE_PROD: "true",
    STARGAZER_DOCUSIGN_PHONE_OTP_CONFIG_ID:
      "3099e1b0-8319-4829-90a5-c7fdf0c20c25",
    STARGAZER_DOCUSIGN_ID_VERIFICATION_WORKFLOW_ID:
      "6b2d3c20-6e49-4c91-b1d1-5bd580d827b6",

    STARGAZER_ACTIONLOGGER_ENABLED: "true",

    STARGAZER_NATS_WS_ENDPOINT: "wss://nats.anduin.app",

    STARGAZER_SERVICES_GLOBAL_DATABASE_WRITE_HOST:
      "global-database-production.anduin.local",
    STARGAZER_SERVICES_GLOBAL_DATABASE_WRITE_DATABASE_NAME: "gondor_public",
    STARGAZER_SERVICES_GLOBAL_DATABASE_READ_HOST:
      "global-database-production.anduin.local",
    STARGAZER_SERVICES_GLOBAL_DATABASE_READ_DATABASE_NAME: "gondor_public",

    STARGAZER_CDN_ENABLED: "true",
    STARGAZER_CDN_ROOT: "https://cdn.anduin.app",
    STARGAZER_ASYNC_API_ENABLED: "true",

    SCHWAB_CARBON_COPY_EMAIL: "<EMAIL>",

    STARGAZER_INTEG_PLATFORM_RECEIVER_HOOK:
      "https://hooks.integration-platform.anduin.app/trigger/SW5zdGFuY2VGbG93Q29uZmlnOjdjY2E2ZjYyLTg5NGItNGMxYi04N2M4LTkxNjE5MDIyNzgzZg==",

    STARGAZER_SERVICES_FOUNDATIONDB_DESCRIPTION: "fdb:TeTqS0hMB60BpSXXpRkSZs34Y37rqsGl",

    STARGAZER_SERVICES_FOUNDATIONDB_READ_ONLY_HOST: "foundationdb-readonly",
    STARGAZER_SERVICES_FOUNDATIONDB_VERBOSE_TRACE_EVENT: "false",

    STARGAZER_SAPI_MTLS_SERVERLESS_REGION: ConfigUtils.getAwsRegion(
      ctx.configs,
    ),

    STARGAZER_SERVICES_ZOT_HOST: "https://zot.us-east-1.anduin.app",
    STARGAZER_SERVICES_ZOT_USERNAME: "gondor",
    STARGAZER_SERVICES_ZOT_PASSWORD: "********************************",
    STARGAZER_SERVICES_ZOT_REPO_PREFIX: "gondor-public",

    STARGAZER_SERVICES_OTP_AUTHENTICATION_ENABLE_REVERT_OTP: "true",
    STARGAZER_SERVICES_OTP_AUTHENTICATION_LOGIN_EMAIL_ADDRESS: "<EMAIL>",
  };
}
