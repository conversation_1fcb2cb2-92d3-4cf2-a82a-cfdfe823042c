import { Context, K8sObject } from "@anduintransaction/rivendell";
import * as k8s from "@utils/k8s";

export default function (ctx: Context): K8sObject[] {
  return [
    k8s.serviceFromExternalName(
      "dgraph-alpha",
      "dgraph-alpha-v3-us-internal.anduin.local",
      {
        probeTcp: true,
        ports: [{
          name: "graphql",
          port: 8080,
          protocol: "TCP",
          targetPort: 8080,
        }, {
          name: "grpc",
          port: 9080,
          protocol: "TCP",
          targetPort: 9080,
        }],
      },
    ),
    k8s.serviceFromExternalName(
      "foundationdb",
      "foundationdb-internal-v3.anduin.local",
      {
        probeTcp: true,
        ports: [{
          name: "foundationdb",
          port: 4500,
          protocol: "TCP",
          targetPort: 4500,
        }],
      },
    ),
    k8s.serviceFromExternalName(
      "foundationdb-readonly",
      "foundationdb-readonlly-internal.anduin.local",
      {
        probeTcp: true,
        ports: [{
          name: "foundationdb",
          port: 4500,
          protocol: "TCP",
          targetPort: 4500,
        }],
      },
    ),
    k8s.serviceFromExternalName("kafka-v3", "kafka-internal-v36.anduin.local", {
      probeTcp: true,
      ports: [{
        name: "kafka",
        port: 9092,
        protocol: "TCP",
        targetPort: 9092,
      }],
    }),
    k8s.serviceFromExternalName(
      "postgres-keycloak-11",
      "postgres-shared.anduin.local",
      {
        probeTcp: true,
        ports: [{
          name: "postgres",
          port: 5432,
          protocol: "TCP",
          targetPort: 5432,
        }],
      },
    ),
    k8s.serviceFromExternalName(
      "postgres-openfga-fund",
      "postgres-shared.anduin.local",
      {
        probeTcp: true,
        ports: [{
          name: "postgres",
          port: 5432,
          protocol: "TCP",
          targetPort: 5432,
        }],
      },
    ),
    k8s.serviceFromExternalName(
      "postgres-openfga",
      "postgres-shared.anduin.local",
      {
        probeTcp: true,
        ports: [{
          name: "postgres",
          port: 5432,
          protocol: "TCP",
          targetPort: 5432,
        }],
      },
    ),
    k8s.serviceFromExternalName("svix-redis", "tyk-redis.anduin.local", {
      probeTcp: true,
      ports: [{
        name: "redis",
        port: 6379,
        protocol: "TCP",
        targetPort: 6379,
      }],
    }),
    k8s.serviceFromExternalName("tyk-redis", "tyk-redis.anduin.local", {
      probeTcp: true,
      ports: [{
        name: "redis",
        port: 6379,
        protocol: "TCP",
        targetPort: 6379,
      }],
    }),
    k8s.serviceFromExternalName("tidb-tidb", "tidb-public-tidb.anduin.local", {
      probeTcp: true,
      ports: [{
        name: "sql",
        port: 4000,
        protocol: "TCP",
        targetPort: 4000,
      }],
    }),
    k8s.serviceFromExternalName(
      "tidb-ticdc",
      "tidb-public-ticdc.anduin.local",
      {
        probeTcp: true,
        ports: [{
          name: "sql",
          port: 8301,
          protocol: "TCP",
          targetPort: 8301,
        }],
      },
    ),
    k8s.serviceFromExternalName(
      "shared-postgres",
      "postgres-shared.anduin.local",
      {
        probeTcp: true,
        ports: [{
          name: "postgres",
          port: 5432,
          protocol: "TCP",
          targetPort: 5432,
        }],
      },
    ),
  ];
}
