import * as path from "path";
import * as fs from "fs/promises";

function readFile(relPath: string) {
  const absPath = path.join(__dirname, relPath);
  return fs.readFile(absPath);
}

export async function loadLocalSecret() {
  return {
    jwt: {
      "rsa-keymap.json": await readFile("./secrets/rsa-keymap.json"),
    },
    nats: {
      "nats-jwt.conf": await readFile("./secrets/nats-jwt.conf"),
    },
    static: {
      STARGAZER_DOCUSIGN_DEMO_RSA_KEY: await readFile("./secrets/docusign.key"),
      STARGAZER_SERVICES_GOOGLE_DRIVE_SA_KEY: await readFile(
        "./secrets/ggdrive-sa.json",
      ),
      STARGAZER_SERVICES_SAML_ASSERTION_ENCRYPTION_PRIVATE_KEY: await readFile(
        "./secrets/saml-assertion.key",
      ),
      STARGAZER_SERVICES_SAML_ASSERTION_ENCRYPTION_PUBLIC_KEY: await readFile(
        "./secrets/saml-assertion.pub",
      ),
      STARGAZER_SERVICES_SFTP_SERVER_PRIVATE_KEY: await readFile(
        "./secrets/sftp.key",
      ),
      STARGAZER_SERVICES_SFTP_SERVER_PUBLIC_KEY: await readFile(
        "./secrets/sftp.pub",
      ),

      STARGAZER_DOCUSIGN_DEMO_HMAC_KEY:
        "5bmPFuJNl0wWV56fYEhcRYoBXVhnSJvzHVX6KUZEzhM=",
      STARGAZER_DOCUSIGN_DEMO_INTEGRATION_KEY:
        "30cc045d-220c-41c6-a9cf-64ea4dd7dd43",
      STARGAZER_GLOBAL_SIGN_API_KEY: "1ea41e631c19584b",
      STARGAZER_GLOBAL_SIGN_API_SECRET:
        "b138a9269acd6a93935682bb8e9c38a9be642ba4",
      STARGAZER_GLOBAL_SIGN_KEY_STORE_PASS: "ubr3GvkXdGMtGN7bbEoN",
      STARGAZER_NATS_ACCOUNT_SEED:
        "SAAD57LNV3B4WFRQKHL6MGTPMVTFWWNI73BPUNVOHJJCCP77IQF7CHP6EQ",
      STARGAZER_S3_CLOUD_FRONT_KEYPAIR_ID: "",
      STARGAZER_S3_CLOUD_FRONT_PRIVATE_KEY: "",
      STARGAZER_SERVICES_BOX_ACCESS_TOKEN: "********************************",
      STARGAZER_SERVICES_DROPBOX_ACCESS_TOKEN:
        "TGWk7eTuJQ4AAAAAAAAAAZEmTybxBq2QztplkXh7r2yMQMYQXGylSYbVJTKksxsg",
      STARGAZER_SERVICES_FORM_SHARED_SECRET:
        "ff69ec29054a70725a07bcd9cf758886a578698e5d6291c6770f0fba1faa7c52",
      STARGAZER_SERVICES_JWT_TOKEN_KEY: "3ouJju5cuuTEr1YNDaPuGbz5jBAke4s",
      STARGAZER_SERVICES_KEYCLOAK_REALM_TOKEN:
        "8e19172c36d3e5db797c43b8bc0ad155",
      STARGAZER_SERVICES_MULTI_REGION_SHARED_KEY:
        "e15c291e2d4498493f4fc5ff72958dbf",
      STARGAZER_SERVICES_OAUTH2_GOOGLE_CLIENT_ID:
        "***********-bu4cpnvtcc8o5i4805sfc944uq6coi4k.apps.googleusercontent.com",
      STARGAZER_SERVICES_OAUTH2_GOOGLE_SECRET: "LKUtyuogPdfd94isw5ZLzHoL",
      STARGAZER_SERVICES_PORTAL_ADMIN_PASSWORD: "anduin1808",
      STARGAZER_SERVICES_TURNSTILE_SECRET_INVISIBLE:
        "1x0000000000000000000000000000000AA",
      STARGAZER_SERVICES_TURNSTILE_SECRET_VISIBLE:
        "1x0000000000000000000000000000000AA",
      STARGAZER_SERVICES_TURNSTILE_SITEKEY_INVISIBLE:
        "1x00000000000000000000BB",
      STARGAZER_SERVICES_TURNSTILE_SITEKEY_VISIBLE: "1x00000000000000000000AA",
      STARGAZER_SERVICES_SHAREPOINT_CLIENT_SECRET:
        "VJu.zKQKsNCIre0-cn-ce1_oGG53384_3T",
      STARGAZER_SERVICES_SHAREPOINT_ENCRYPTED_PASS:
        "yN-Hj2pRoRu5ZVFTeKPsOmEI7s-fqRcPiBvtmwQzUg",
      STARGAZER_SERVICES_SHAREPOINT_PASSWORD: "bVEiQ4U?NM}GpX*",
      STARGAZER_SERVICES_TIMESCALEDB_PASSWORD: "timescaledb",
      STARGAZER_SERVICES_TIMESCALEDB_USERNAME: "timescaledb",
      STARGAZER_SERVICES_TWILIO_ACCOUNT_SID:
        "**********************************",
      STARGAZER_SERVICES_TWILIO_KEY_SECRET: "BCHSRM1ErDIvqhdd9iaxqMI2Wc13XiPI",
      STARGAZER_SERVICES_TWILIO_KEY_SID: "**********************************",
      STARGAZER_SERVICES_TWILIO_MESSAGE_SERVICE_ID:
        "MG5ea70445ef177fa7abc9644bc80c5052",
      STARGAZER_SERVICES_TYK_ADMIN_SECRET: "Mp8x99cpdzRNzyH87P6zm6CQaS8tR5C7",
      STARGAZER_SERVICES_TYK_HTTP_SECRET: "",
      STARGAZER_SERVICES_YB_USER_PASSWORD: "tdtMWIPFbh5XYfnZlRJA",
    },
    shared: {
      STARGAZER_SERVICES_KEYCLOAK_PASSWORD: "password",
      STARGAZER_SERVICES_KEYCLOAK_POSTGRES_PASSWORD:
        "keycloak-postgres-password",
      STARGAZER_SERVICES_KEYCLOAK_POSTGRES_USER: "keycloak-postgres-user",
      STARGAZER_SERVICES_KEYCLOAK_USER: "admin",
      STARGAZER_SERVICES_OPENFGA_FUND_POSTGRES_USER: "openfga",
      STARGAZER_SERVICES_OPENFGA_FUND_POSTGRES_PASSWORD:
        "32bc83830f123fd9575d1b53a7129b03",
      get STARGAZER_SERVICES_OPENFGA_FUND_POSTGRES_URI() {
        const creds =
          `${this.STARGAZER_SERVICES_OPENFGA_FUND_POSTGRES_USER}:${this.STARGAZER_SERVICES_OPENFGA_FUND_POSTGRES_PASSWORD}`;
        return `postgres://${creds}@postgres-openfga-fund:5432/openfga-fund?sslmode=disable`;
      },
      STARGAZER_SERVICES_OPENFGA_POSTGRES_USER: "openfga",
      STARGAZER_SERVICES_OPENFGA_POSTGRES_PASSWORD:
        "32bc83830f123fd9575d1b53a7129b03",
      get STARGAZER_SERVICES_OPENFGA_POSTGRES_URI() {
        const creds =
          `${this.STARGAZER_SERVICES_OPENFGA_POSTGRES_USER}:${this.STARGAZER_SERVICES_OPENFGA_POSTGRES_PASSWORD}`;
        return `postgres://${creds}@postgres-openfga:5432/openfga?sslmode=disable`;
      },
      STARGAZER_SERVICES_YB_ROOT_PASSWORD: "LBKGgqjgKRXLYR62z4ov",
      STARGAZER_SERVICES_EDGEDB_ROOT_PASSWORD: "lDn2EcpbyFdQU72mW4vL",
      STARGAZER_SERVICES_EDGEDB_POSTGRES_USER: "edgedb",
      STARGAZER_SERVICES_EDGEDB_POSTGRES_PASSWORD:
        "188eabaea4f64b1d8757a3d9e3398a42",
      get STARGAZER_SERVICES_EDGEDB_POSTGRES_URI() {
        const creds =
          `${this.STARGAZER_SERVICES_EDGEDB_POSTGRES_USER}:${this.STARGAZER_SERVICES_EDGEDB_POSTGRES_PASSWORD}`;
        return `postgres://${creds}@postgres-edgedb:5432/?sslmode=disable`;
      },
      STARGAZER_SERVICES_SHARED_POSTGRES_USER: "postgres",
      STARGAZER_SERVICES_SHARED_POSTGRES_PASSWORD:
        "41e7b3ecb226768aab5fbb7e8cec94e0",
      get STARGAZER_SERVICES_SVIX_POSTGRES_URI() {
        const creds =
          `${this.STARGAZER_SERVICES_SHARED_POSTGRES_USER}:${this.STARGAZER_SERVICES_SHARED_POSTGRES_PASSWORD}`;
        return `postgres://${creds}@shared-postgres:5432/svix?sslmode=disable`;
      },
      STARGAZER_SERVICES_SVIX_MAIN_SECRET: "733c2847129e95d0bbc7b65b8ca08c80",
      STARGAZER_SERVICES_SVIX_JWT_SECRET: "6b700d06a421e9fe610e4d290a6375c6",
    },
    dynamic: {
      STARGAZER_AWS_ACCESS_KEY_ID: "********************",
      STARGAZER_AWS_SECRET_ACCESS_KEY:
        "JnxKa+dP4MMDpd1LDS73tllL7aZNRjQKVlMggSCe",
    },
    tidb: {
      root: "99R5BqqsgNkdLM4QaLKx",
      gondor: "0NZQ9KGH0jG4K96UClaB",
    },
  };
}
