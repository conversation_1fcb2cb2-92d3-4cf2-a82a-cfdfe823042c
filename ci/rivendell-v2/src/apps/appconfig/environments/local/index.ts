import * as merge from "deepmerge";
import { Context, K8sObject } from "@anduintransaction/rivendell";
import * as k8s from "@utils/k8s";
import common from "../../common";

const debugCfg: Record<string, string> = {
  STARGAZER_SERVICES_DEPLOYMENT: "gondor-local-dev",
  // STARGAZER_TRACING_ENABLED: "true",
  // STARGAZER_TRACING_COLLECTOR_ENDPOINT: "**************", // set this to your local ip
};

export default async function (ctx: Context): Promise<K8sObject[]> {
  const cfg = merge.all([
    await common(ctx),
    debugCfg,
  ]) as Record<string, string>;
  return [
    k8s.createConfigMapFromData("gondor", cfg),
  ];
}
