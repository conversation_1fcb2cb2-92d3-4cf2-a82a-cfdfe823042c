import * as merge from "deepmerge";
import { Context, K8sObject } from "@anduintransaction/rivendell";
import * as k8s from "@utils/k8s";
import common from "../../common";

export default async function (ctx: Context): Promise<K8sObject[]> {
  const cfg = merge.all([
    await common(ctx),
    {
      STARGAZER_SERVICES_DEPLOYMENT: "integ-test",
      STARGAZER_SERVICES_FOUNDATIONDB_READ_ONLY_HOST: "foundationdb-readonly",
    },
  ]) as Record<string, string>;
  return [
    k8s.createConfigMapFromData("gondor", cfg),
  ];
}
