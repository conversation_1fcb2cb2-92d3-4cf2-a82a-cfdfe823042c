
stargazer {

  endpointHostname = "gondor"

  backendConfig {
    server {
      baseUrl = "http://localhost:8080"
      deployment = "local-test-build"
      serviceFeatures = "web0, adm0"
    }

    tracingConfig {
        isEnabled = true
        traceRootSpanOnly = true
        collectorEndpoint = "eks-opentelemetry-collector.tracing.svc.cluster.local"
        collectorSchema = "http"
        collectorPort = "4317"
    }

    ses {
      region = "us-west-2"
      s3CredentialConfig = {
        region = "us-west-2"
      }
    }

    email {
      sending {
        disableSendingEmail = false
      }
    }

    aws {
      S3 {
        region = "us-east-1"
        s3CredentialConfig = {
          region = "us-east-1"
        }

        bucket = "gondor-assets-document-test"
        batchDownloadBucket = "gondor-batch-download-test"
        publicBucket = "gondor-public-document-test"
        formTemplateBucket = "gondor-form-template-test"
        dataExportBucket = "gondor-export-integ-test"
        formStorageBucket = "gondor-form-storage-test"
        emailStorageBucket = "gondor-email-storage-integ-test"
        resourcesBucket = "gondor-resources-test"
        tempUploadBucket = "gondor-temp-upload-test"
        webBuilderBucket = "gondor-webbuilder-test"

        cloudFrontConfig = {
          cloudFrontEnabled = false
          domain = ""
        }
      }
    }

    analyticsConfig {
      s3Bucket = "stargazer-analytics-test"
      s3Prefix = "v2"
    }

    oauth2Config {
      clients = [
        {
          name = "google"
          clientId = "553561968184-477b38och9aaaodrvls12uma2ucq2403.apps.googleusercontent.com"
          secret = "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"
        }
      ]
    }

    dataPipelineConfig {
      enableDataIngest = true
      enableDataTransform = true
      enableDataCorrectionCron = true
    }

    svixWebhookConfig {
      enableWebhook = true
    }

    foundationDbReadOnlyConfig {
      host = foundationdb-readonly${?networkDnsSuffix} # Using real fdb readonly for integ test
    }

    zotRegistryConfig {
      host = "https://zot-cicd.anduin.center"
      username = "gondor"
      password = "ydsRVUWQFq8e3aomoPMF6ZYXrCJNkJMz"
      repoPrefix = "dev"
    }

    temporalConfig {
      namespace = "public-api"
    }

    multiRegionConfig {
      allowMock = true
    }

    kafkaConfig {
      multipleWindowStreams = true
    }

  }


}

# vi: ft=hocon
