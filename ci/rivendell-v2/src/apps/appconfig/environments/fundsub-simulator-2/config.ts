import { Context } from "@anduintransaction/rivendell";

export default async function(ctx: Context): Promise<Record<string, string>> {
  return {
    STARGAZER_ONLYOFFICE_PUBLIC_HOST: "doc-server-demo-simulator2.anduin.app",
    STARGAZER_SERVICES_DEPLOYMENT: "fundsub-simulator-2",
    STARGAZER_SERVICES_BASE_URL: "https://simulator.anduin.app",
    STARGAZER_SERVICES_PORTAL_URL: "https://simulator.anduin.app",
    STARGAZER_S3_DEFAULT_BUCKET: "gondor-assets-document-fundsub-demo-2",
    STARGAZER_S3_FORM_STORAGE_BUCKET: "gondor-form-storage-fundsub-demo-2",
    STARGAZER_S3_WEB_BUILDER_BUCKET: "gondor-webbuilder-fundsub-demo-2",
    STARGAZER_S3_TEMP_UPLOAD_BUCKET: "gondor-temp-upload-fundsub-demo-2",
    STARGAZER_NATS_WS_ENDPOINT: "wss://nats-demo-simulator2.anduin.app",
    STARGAZER_SERVICES_ZOT_HOST: "https://zot.us-east-1.anduin.app",
    STARGAZER_SERVICES_ZOT_USERNAME: "gondor",
    STARGAZER_SERVICES_ZOT_PASSWORD: "tTuva2iuiKHRHYMcxahAreaoVTUqYqXz",
    STARGAZER_SERVICES_ZOT_REPO_PREFIX: "fundsub-simulator-2",
  };
}
