import * as merge from "deepmerge";
import { Context, K8sObject } from "@anduintransaction/rivendell";
import * as k8s from "@utils/k8s";
import common from "../../common";
import fscommon from "../../fscommon";
import config from "./config";
import ingress from "./ingress";

export default async function (ctx: Context): Promise<K8sObject[]> {
  const cfg = merge.all([
    await common(ctx),
    await fscommon(ctx),
    await config(ctx),
  ]) as Record<string, string>;

  return [
    k8s.createConfigMapFromData("gondor", cfg),
    ...ingress(ctx),
  ];
}
