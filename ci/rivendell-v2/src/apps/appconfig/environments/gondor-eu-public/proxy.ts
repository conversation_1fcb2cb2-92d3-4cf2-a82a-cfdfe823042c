import { Context, K8sObject } from "@anduintransaction/rivendell";
import * as k8s from "@utils/k8s";

export default function (ctx: Context): K8sObject[] {
  return [
    k8s.serviceFromExternalName(
      "dgraph-alpha",
      "dgraph-alpha-v2413-eu-internal.anduin.local",
      {
        probeTcp: true,
        ports: [{
          name: "graphql",
          port: 8080,
          protocol: "TCP",
          targetPort: 8080,
        }, {
          name: "grpc",
          port: 9080,
          protocol: "TCP",
          targetPort: 9080,
        }],
      },
    ),
    k8s.serviceFromExternalName(
      "foundationdb",
      "foundationdb-eu-internal-v2.anduin.local",
      {
        probeTcp: true,
        ports: [{
          name: "foundationdb",
          port: 4500,
          protocol: "TCP",
          targetPort: 4500,
        }],
      },
    ),
    k8s.serviceFromExternalName(
      "foundationdb-readonly",
      "foundationdb-readonly-eu-internal.anduin.local",
      {
        probeTcp: true,
        ports: [{
          name: "foundationdb",
          port: 4500,
          protocol: "TCP",
          targetPort: 4500,
        }],
      },
    ),
    k8s.serviceFromExternalName("kafka-v3", "kafka-eu-internal.anduin.local", {
      probeTcp: true,
      ports: [{
        name: "kafka",
        port: 9092,
        protocol: "TCP",
        targetPort: 9092,
      }],
    }),
    k8s.serviceFromExternalName(
      "postgres-keycloak-11",
      "postgres-shared-eu.anduin.local",
      {
        probeTcp: true,
        ports: [{
          name: "postgres",
          port: 5432,
          protocol: "TCP",
          targetPort: 5432,
        }],
      },
    ),
    k8s.serviceFromExternalName(
      "postgres-openfga-fund",
      "postgres-shared-eu.anduin.local",
      {
        probeTcp: true,
        ports: [{
          name: "postgres",
          port: 5432,
          protocol: "TCP",
          targetPort: 5432,
        }],
      },
    ),
    k8s.serviceFromExternalName(
      "postgres-openfga",
      "postgres-shared-eu.anduin.local",
      {
        probeTcp: true,
        ports: [{
          name: "postgres",
          port: 5432,
          protocol: "TCP",
          targetPort: 5432,
        }],
      },
    ),
    k8s.serviceFromExternalName("svix-redis", "tyk-eu-redis.anduin.local", {
      probeTcp: true,
      ports: [{
        name: "redis",
        port: 6379,
        protocol: "TCP",
        targetPort: 6379,
      }],
    }),
    k8s.serviceFromExternalName("tyk-redis", "tyk-eu-redis.anduin.local", {
      probeTcp: true,
      ports: [{
        name: "redis",
        port: 6379,
        protocol: "TCP",
        targetPort: 6379,
      }],
    }),
    k8s.serviceFromExternalName(
      "tidb-tidb",
      "tidb-eu-public-tidb.anduin.local",
      {
        probeTcp: true,
        ports: [{
          name: "sql",
          port: 4000,
          protocol: "TCP",
          targetPort: 4000,
        }],
      },
    ),
    k8s.serviceFromExternalName(
      "tidb-ticdc",
      "tidb-eu-public-ticdc.anduin.local",
      {
        probeTcp: true,
        ports: [{
          name: "sql",
          port: 8301,
          protocol: "TCP",
          targetPort: 8301,
        }],
      },
    ),
    k8s.serviceFromExternalName(
      "shared-postgres",
      "postgres-shared-eu.anduin.local",
      {
        probeTcp: true,
        ports: [{
          name: "postgres",
          port: 5432,
          protocol: "TCP",
          targetPort: 5432,
        }],
      },
    ),
  ];
}
