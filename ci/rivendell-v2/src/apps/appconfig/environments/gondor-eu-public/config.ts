import { Context } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";

export default async function (ctx: Context): Promise<Record<string, string>> {
  return {
    STARGAZER_ENDPOINT_HOSTNAME: "gondor",
    STAR<PERSON><PERSON>ER_SERVICES_BASE_URL: "https://deals.eu.anduin.app",
    STARGAZER_SERVICES_PORTAL_URL: "https://portal.eu.anduin.app",
    STARGAZER_SERVICES_DEPLOYMENT: "gondor-eu-public",
    STARGAZER_ENVIRONMENT_FALLBACK_SUBDOMAIN: "eu.anduin.io",
    STARGAZER_ENVIRONMENT_INTERNAL_SUBDOMAINS:
      "eu.anduin.io eu.anduin.partners",
    STARGAZER_ENVIRONMENT_EXTERNAL_TARGET_DOMAIN: "target.eu.anduin.app",

    STARGAZER_SERVICES_ANDUIN_INTERNAL_ENTITIES: "",

    STAR<PERSON><PERSON><PERSON>_ONLYOFFICE_PUBLIC_SCHEME: "https",
    STARGAZER_ONLYOFFICE_PUBLIC_HOST: "doc-server.eu.anduin.app",
    STARGAZER_ONLYOFFICE_GONDOR_SERVER: "http://gondor:8080",

    STARGAZER_SERVICES_SAML_ENTITY_ID: "https://id.anduin.app",

    STARGAZER_SERVICES_CASSANDRA_REPLICATION: "3",

    STARGAZER_SERVICES_SEED_CUSTOM_DOMAINS: "",

    STARGAZER_SERVICES_MULTI_REGION_ENABLE_SECONDARY_REGION: "true",
    STARGAZER_SERVICES_MULTI_REGION_MAIN_REGION_ENDPOINT:
      "https://id.anduin.app",
    STARGAZER_SERVICES_MULTI_REGION_REGION_CODE: "eu",
    STARGAZER_SERVICES_MULTI_REGION_REGION_NAME: "EU",

    STARGAZER_S3_DEFAULT_BUCKET: "gondor-eu-assets-document-production",
    STARGAZER_S3_BATCH_DOWNLOAD_BUCKET: "gondor-eu-batch-download-production",
    STARGAZER_S3_PUBLIC_BUCKET: "gondor-eu-public-document-production",
    STARGAZER_S3_FORM_TEMPLATE_BUCKET: "gondor-eu-form-template",
    STARGAZER_S3_EXPORT_BUCKET: "gondor-eu-export-production",
    STARGAZER_S3_FORM_STORAGE_BUCKET: "gondor-eu-form-storage-production",
    STARGAZER_S3_WEB_BUILDER_BUCKET: "gondor-eu-webbuilder-production",
    STARGAZER_S3_EMAIL_STORAGE_BUCKET: "gondor-eu-email-storage",
    STARGAZER_S3_RESOURCES_BUCKET: "gondor-eu-assets-document-production",
    STARGAZER_S3_TEMP_UPLOAD_BUCKET: "gondor-eu-temp-upload-production",
    STARGAZER_S3_CLOUD_FRONT_ENABLED: "true",
    // TODO: setup cloud front
    STARGAZER_S3_CLOUD_FRONT_DOMAIN: "docs.eu.anduin.app",

    STARGAZER_EMAIL_SENDING_DYNAMO_DB_STATUS_TABLE: "EmailStatusProduction",

    STARGAZER_SERVICES_SYSTEM_AUDIT_LOG_GROUP: "gondor-system-audit-production",

    STARGAZER_EMAIL_HIGH_ENGAGEMENT_DOMAIN: "eu.anduin.app",
    STARGAZER_EMAIL_STANDARD_DOMAIN: "eu.anduin.email",
    STARGAZER_EMAIL_SENDING_DISABLE: "false",
    STARGAZER_EMAIL_SENDING_MANAGER_USE_SCHEDULER: "true",
    STARGAZER_SES_CONFIGURATION_SET: "ses-eu-production",
    STARGAZER_SES_S3_BUCKET: "gondor-ses-email-eu-production",
    STARGAZER_SES_SQS_RECEIVE_QUEUE: "ses-receive-eu-production",
    STARGAZER_SES_SQS_EVENT_QUEUE: "ses-event-eu-production",
    STARGAZER_SES_REGION: "eu-west-1",
    STARGAZER_CUSTOMER_SUPPORT_EMAIL: "<EMAIL>",

    STARGAZER_SERVICES_ANALYTICS_S3_BUCKET: "stargazer-eu-analytics-production",
    STARGAZER_SERVICES_ANALYTICS_S3_PREFIX: "v2",

    STARGAZER_SERVICES_CAPTCHA_ENABLED: "true",

    STARGAZER_SERVICES_OAUTH2_GOOGLE_ENABLED: "true",
    STARGAZER_SERVICES_OAUTH2_ENABLE_SKIP_LINK_ACCOUNT: "false",

    STARGAZER_SERVICES_SAFE_GA_TRACKING_ID: "UA-*********-1",
    STARGAZER_SERVICES_DATA_ROOM_GA_TRACKING_ID: "UA-*********-2",
    STARGAZER_SERVICES_ISSUE_TRACKER_GA_TRACKING_ID: "UA-*********-3",

    STARGAZER_SERVICES_GTM_CONTAINER_ID: "",

    STARGAZER_SERVICES_SURVICATE_FUNDSUB_SURVEY_URL:
      "https://survey.survicate.com/workspaces/********************************/web_surveys.js",

    STARGAZER_SERVICES_GETFEEDBACK_FUNDSUB_BUTTON_ID: "6b28f8c82425",
    STARGAZER_SERVICES_GETFEEDBACK_FUNDSUB_BUTTON_HASH:
      "sha256-lhsN7OnIHmTEITj+IgqZtr5GyGOE57DcdDBbgJBUSIo=",

    STARGAZER_SERVICES_MASTER_KEY_ARN:
      "aws-kms://arn:aws:kms:eu-west-1:************:key/dc215a96-b62c-47b6-9d1b-b68b89376905",
    STARGAZER_SERVICES_ENCRYPTED_DATA_KEY_BLOB:
      "AQICAHiEGIHAaLExXzuu9hCclYYtw/41G3umyOu9aVuJru5WowE7GJWftNifqHmE4NrxtEqOAAAA5TCB4gYJKoZIhvcNAQcGoIHUMIHRAgEAMIHLBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDGolk0wt4f6fuQhDzgIBEICBnSTI9fHdae8wWCwToljnAtjqw2SvTcmZa94xVx+0L3zYWSQWeYoVcjRmJWbrQSmD55bMXEAG3xuktgZht2DDJm565lHCdhb8tb4ZOWcFN1ZdTwjhebIluqIQ9lOPFktic+TUmZC/cjM83Vlf9y3R8QU1tFqfN+LsNkb6cBczgYPvJJ+1DVqIbNxldQnZoXc2EdgpyLuY+Xq5qeh58NY=",

    STARGAZER_SERVICES_SHAREPOINT_USERNAME:
      "<EMAIL>",
    STARGAZER_SERVICES_SHAREPOINT_TENANT:
      "da6e6f9c-8c65-4fff-92d5-cc99aa412bd2",
    STARGAZER_SERVICES_SHAREPOINT_CLIENT_ID:
      "b1f98303-d2b4-4bcb-b186-bc3a5fafe508",
    STARGAZER_SERVICES_SHAREPOINT_SCOPE:
      "user.read offline_access files.readwrite.all sites.readwrite.all",

    STARGAZER_SERVICES_SERVERLESS_NAMESPACE: "prod",
    STARGAZER_SERVICES_SERVERLESS_ASYNC_SQS_QUEUE:
      "anduin-serverless-public-response-queue",

    STARGAZER_TRACING_ENABLED: "true",

    STARGAZER_SERVICES_TEXTRACT_ENABLED: "true",
    STARGAZER_SERVICES_TEXTRACT_SNS_ARN:
      "arn:aws:sns:eu-west-1:************:AmazonTextract-gondor-eu-production-20240112051315404400000004",
    STARGAZER_SERVICES_TEXTRACT_SNS_ROLE_ARN:
      "arn:aws:iam::************:role/textract-sns-eu-production",
    STARGAZER_SERVICES_TEXTRACT_SQS_QUEUE:
      "AmazonTextract-gondor-eu-production-20240112051315403700000003",
    STARGAZER_SERVICES_TEXTRACT_S3_BUCKET: "gondor-eu-textract-production",

    STARGAZER_DOCUSIGN_DEMO_ACCOUNT_ID: "23b0dcd8-8684-49b2-9b94-fe4e522ac7a6",
    STARGAZER_DOCUSIGN_DEMO_USER_ID: "646ff222-9d43-43bb-bc55-1277556a5f44",
    STARGAZER_DOCUSIGN_DEMO_CERITY_API_USER_ID:
      "770a0369-38ea-4d1d-8b6a-026644fbc8af",

    STARGAZER_DOCUSIGN_PROD_OAUTH_BASE_PATH: "account.docusign.com",
    STARGAZER_DOCUSIGN_PROD_API_BASE_PATH: "https://eu.docusign.net/restapi",
    STARGAZER_DOCUSIGN_PROD_ACCOUNT_ID: "59a38557-67fc-4869-a33e-8fa1cfaa31d4",
    STARGAZER_DOCUSIGN_PROD_USER_ID: "fec1d2f4-d39e-47d2-80a9-9aed198dc03e",
    STARGAZER_DOCUSIGN_PROD_CERITY_API_USER_ID:
      "df8c37ad-a98f-4d81-b4d0-946f8217ccc3",

    STARGAZER_DOCUSIGN_CAN_USE_PROD: "true",
    STARGAZER_DOCUSIGN_PHONE_OTP_CONFIG_ID:
      "ce4c67b2-04f6-41f2-a03c-2ceec81f30f9",
    STARGAZER_DOCUSIGN_ID_VERIFICATION_WORKFLOW_ID:
      "6b2d3c20-6e49-4c91-b1d1-5bd580d827b6",

    STARGAZER_ACTIONLOGGER_ENABLED: "true",

    STARGAZER_NATS_WS_ENDPOINT: "wss://nats.eu.anduin.app",

    STARGAZER_SERVICES_GLOBAL_DATABASE_WRITE_HOST:
      "global-database-production.anduin.local",
    STARGAZER_SERVICES_GLOBAL_DATABASE_WRITE_DATABASE_NAME: "gondor_public",
    STARGAZER_SERVICES_GLOBAL_DATABASE_READ_HOST:
      "global-database-reader-production-eu.anduin.local",
    STARGAZER_SERVICES_GLOBAL_DATABASE_READ_DATABASE_NAME: "gondor_public",

    STARGAZER_CDN_ENABLED: "true",
    STARGAZER_CDN_ROOT: "https://cdn.anduin.app",

    STARGAZER_ASYNC_API_ENABLED: "true",

    STARGAZER_PRISMATIC_ORG_URL: "https://integration-platform.eu.anduin.app",
    STARGAZER_INTEG_PLATFORM_RECEIVER_HOOK:
      "https://hooks.integration-platform.anduin.app/trigger/SW5zdGFuY2VGbG93Q29uZmlnOjdjY2E2ZjYyLTg5NGItNGMxYi04N2M4LTkxNjE5MDIyNzgzZg==",

    STARGAZER_SERVICES_FOUNDATIONDB_READ_ONLY_HOST: "foundationdb-readonly",
    STARGAZER_SERVICES_FOUNDATIONDB_VERBOSE_TRACE_EVENT: "false",

    STARGAZER_SAPI_MTLS_SERVERLESS_REGION: ConfigUtils.getAwsRegion(
      ctx.configs,
    ),

    STARGAZER_SERVICES_ZOT_HOST: "https://zot.eu-west-1.anduin.app",
    STARGAZER_SERVICES_ZOT_USERNAME: "gondor",
    STARGAZER_SERVICES_ZOT_PASSWORD: "********************************",
    STARGAZER_SERVICES_ZOT_REPO_PREFIX: "gondor-eu-public",

    STARGAZER_SERVICES_OTP_AUTHENTICATION_ENABLE_REVERT_OTP: "true",
    STARGAZER_SERVICES_OTP_AUTHENTICATION_LOGIN_EMAIL_ADDRESS: "<EMAIL>",
  };
}
