import { Context, K8sObject } from "@anduintransaction/rivendell";
import {
  createGondorIngress,
  gondorForwardRule,
  simpleForwardRule,
} from "../../ingress";

export default function (ctx: Context): K8sObject[] {
  const gondorDomains = [
    "deals.eu.anduin.app",
    "id.eu.anduin.app",
    "fundsub.eu.anduin.app",
    "dataroom.eu.anduin.app",
    "platform.eu.anduin.app",
    "investoraccess.eu.anduin.app",
    "integrations.eu.anduin.app",
    "advisors.eu.anduin.app",
    "advisor.eu.anduin.app",
    "eu.target.anduin.app",
    "target.eu.anduin.app",
    "*.eu.anduin.io",
    "*.eu.anduin.partners",
  ]
    .map((d) =>
      gondorForwardRule(ctx, d, {
        serviceName: "gondor",
        servicePort: 8080,
      })
    );

  return [
    ...createGondorIngress(ctx, {
      albGroupName: "gondor",
      accessLogBucket: "anduin-elb-log-eu",
      rules: [
        simpleForwardRule("nats.eu.anduin.app", {
          serviceName: "nats",
          servicePort: 5222,
        }),
        simpleForwardRule("doc-server.eu.anduin.app", {
          serviceName: "oo-doc-server",
          servicePort: 80,
        }),
        gondorForwardRule(ctx, "api.eu.anduin.app", {
          serviceName: "tyk-http",
          servicePort: 80,
        }),
        gondorForwardRule(ctx, "s-api.eu.anduin.app", {
          serviceName: "tyk-http",
          servicePort: 80,
        }),
        gondorForwardRule(ctx, "portal.eu.anduin.app", {
          serviceName: "gondor-admin-portal",
          servicePort: 8080,
        }),
        ...gondorDomains,
      ],
    }),
  ];
}
