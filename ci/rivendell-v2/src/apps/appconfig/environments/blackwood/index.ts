import * as merge from "deepmerge";
import { Context, K8sObject } from "@anduintransaction/rivendell";
import * as k8s from "@utils/k8s";
import common from "../../common";

export default async function (ctx: Context): Promise<K8sObject[]> {
  const cfg = merge.all([
    {
      STARGAZER_SERVICES_DEPLOYMENT: "gondor-local-dev",
      STARGAZER_SERVICES_BASE_URL: "http://gondor-local.io:8080",
      STARGAZER_S3_CLOUD_FRONT_ENABLED: "false",
    },
    await common(ctx),
  ]) as Record<string, string>;
  return [
    k8s.createConfigMapFromData("gondor", cfg),
  ];
}
