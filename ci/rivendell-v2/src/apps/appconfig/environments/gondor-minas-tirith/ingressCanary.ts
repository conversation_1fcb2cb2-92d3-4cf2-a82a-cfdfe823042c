import { Context, K8sObject } from "@anduintransaction/rivendell";
import { createRedirectIngress } from "../../ingress";
import { RedirectRule } from "../../ingress/types";

export default function (ctx: Context): K8sObject[] {
  const toCanary = (name: string) => (`${name}-canary.anduin.dev`);
  const toMinas = (name: string) => (`${name}-minas-tirith.anduin.dev`);
  const appRedirect = (appName: string): Record<string, RedirectRule> => ({
    [toCanary(appName)]: {
      ruleName: appName,
      target: toMinas(appName),
    },
  });

  return [createRedirectIngress(
    ctx,
    {
      ingressName: "gondor-canary",
      albGroupName: "gondor-canary",
    },
    {
      ["canary.anduin.dev"]: {
        ruleName: "main",
        target: "minas-tirith.anduin.dev",
      },
      ...appRedirect("portal"),
      ...appRedirect("fundsub"),
      ...appRedirect("sign"),
      ...appRedirect("dataroom"),
      ...appRedirect("platform"),
      ...appRedirect("integrations"),
      ...appRedirect("api"),
      ...appRedirect("s-api"),
    },
  )];
}
