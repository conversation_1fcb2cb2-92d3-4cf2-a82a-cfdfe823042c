import { Context } from "@anduintransaction/rivendell";

export default async function (ctx: Context): Promise<Record<string, string>> {
  return {
    STARGAZER_ENDPOINT_HOSTNAME: "gondor",
    STARGAZER_ENVIRONMENT_FALLBACK_SUBDOMAIN: "simulator.annduin.app",
    STARGAZER_ENVIRONMENT_INTERNAL_SUBDOMAINS: "simulator.annduin.app",
    STARGAZER_ENVIRONMENT_EXTERNAL_TARGET_DOMAIN:
      "simulator.target.annduin.app",

    STARGAZER_SERVICES_ANDUIN_INTERNAL_ENTITIES: "",

    STARGAZER_ONLYOFFICE_PUBLIC_SCHEME: "https",
    STARGAZER_ONLYOFFICE_GONDOR_SERVER: "http://gondor:8080",

    STARGAZER_SERVICES_SAML_ENTITY_ID: "https://demo.anduin.app/account",

    STARGAZER_SERVICES_CASSANDRA_REPLICATION: "1",

    STARGAZER_SERVICES_SEED_CUSTOM_DOMAINS: "",

    STARGAZER_SERVICES_MULTI_REGION_ENABLE_SECONDARY_REGION: "false",
    STARGAZER_SERVICES_MULTI_REGION_MAIN_REGION_ENDPOINT:
      "http://localhost:8080",

    STARGAZER_S3_BATCH_DOWNLOAD_BUCKET: "gondor-batch-download-fundsub-demo",
    STARGAZER_S3_PUBLIC_BUCKET: "gondor-public-document-fundsub-demo",
    STARGAZER_S3_FORM_TEMPLATE_BUCKET: "gondor-form-template",
    STARGAZER_S3_EXPORT_BUCKET: "gondor-export-test",
    STARGAZER_S3_EMAIL_STORAGE_BUCKET: "gondor-email-storage-test",
    STARGAZER_S3_RESOURCES_BUCKET: "gondor-resources-fundsub-demo",
    STARGAZER_S3_CLOUD_FRONT_ENABLED: "false",
    STARGAZER_S3_CLOUD_FRONT_DOMAIN: "",

    STARGAZER_EMAIL_SENDING_DYNAMO_DB_STATUS_TABLE: "EmailStatusTest",

    STARGAZER_SERVICES_SYSTEM_AUDIT_LOG_GROUP: "gondor-system-audit-test",

    STARGAZER_EMAIL_HIGH_ENGAGEMENT_DOMAIN: "simul.anduintransact.email",
    STARGAZER_EMAIL_STANDARD_DOMAIN: "simul.anduintransact.email",
    STARGAZER_EMAIL_SENDING_DISABLE: "true",
    STARGAZER_EMAIL_SENDING_MANAGER_USE_SCHEDULER: "true",
    STARGAZER_SES_CONFIGURATION_SET: "ses-simulator-v3",
    STARGAZER_SES_S3_BUCKET: "",
    STARGAZER_SES_SQS_RECEIVE_QUEUE: "",
    STARGAZER_SES_SQS_EVENT_QUEUE: "",
    STARGAZER_SES_REGION: "us-east-1",
    STARGAZER_CUSTOMER_SUPPORT_EMAIL: "<EMAIL>",

    STARGAZER_SERVICES_ANALYTICS_S3_BUCKET: "stargazer-analytics-simulator",
    STARGAZER_SERVICES_ANALYTICS_S3_PREFIX: "v2",

    STARGAZER_SERVICES_CAPTCHA_ENABLED: "true",

    STARGAZER_SERVICES_OAUTH2_GOOGLE_ENABLED: "true",
    STARGAZER_SERVICES_OAUTH2_ENABLE_SKIP_LINK_ACCOUNT: "true",

    STARGAZER_SERVICES_SAFE_GA_TRACKING_ID: "",
    STARGAZER_SERVICES_DATA_ROOM_GA_TRACKING_ID: "",
    STARGAZER_SERVICES_ISSUE_TRACKER_GA_TRACKING_ID: "",

    STARGAZER_SERVICES_GTM_CONTAINER_ID: "",

    STARGAZER_SERVICES_SURVICATE_FUNDSUB_SURVEY_URL: "",

    STARGAZER_SERVICES_GETFEEDBACK_FUNDSUB_BUTTON_ID: "",
    STARGAZER_SERVICES_GETFEEDBACK_FUNDSUB_BUTTON_HASH: "",

    STARGAZER_SERVICES_MASTER_KEY_ARN:
      "aws-kms://arn:aws:kms:us-east-1:************:key/2c5300f2-515e-422e-8f99-d6a67b3392a4",
    STARGAZER_SERVICES_ENCRYPTED_DATA_KEY_BLOB:
      "AQICAHhTNgXOanuG36K3ThdTRqWnPeaPvDajkOb2FRdjM7z+iQFls8JO7bXp9MOwmlvtLISkAAAA5TCB4gYJKoZIhvcNAQcGoIHUMIHRAgEAMIHLBgkqhkiG9w0BBwEwHgYJYIZIAWUDBAEuMBEEDDAvnTO0LKSVSNwmAgIBEICBnfg/MCxHOKfZ2aHIJx0rpPD5zoKmvoQfa8CqZT52mjb0n51JrflgDxYkbxPhRwVLFn41KF1ayFhGr/CKYx+4rTCJZSBs6DtnQx1XkXR4dDdY8z2nyyzMISmSDfSxp3vwMx7IXkO5pi/J72By+g5PO4HrZ+BgrHGGRmR1hKhKkLOKr0XMT5QWUn6dQkIl3xKTtnGghvPuwYh5YGUVbqY=",

    STARGAZER_SERVICES_SHAREPOINT_USERNAME:
      "<EMAIL>",
    STARGAZER_SERVICES_SHAREPOINT_TENANT:
      "da6e6f9c-8c65-4fff-92d5-cc99aa412bd2",
    STARGAZER_SERVICES_SHAREPOINT_CLIENT_ID:
      "638f4538-f77f-4362-8536-1011744684e7",
    STARGAZER_SERVICES_SHAREPOINT_SCOPE:
      "user.read offline_access files.readwrite.all sites.readwrite.all",

    STARGAZER_SERVICES_SERVERLESS_NAMESPACE: "prod",
    STARGAZER_SERVICES_SERVERLESS_ASYNC_SQS_QUEUE: "",

    STARGAZER_TRACING_ENABLED: "true",

    STARGAZER_DOCUSIGN_DEMO_ACCOUNT_ID: "23b0dcd8-8684-49b2-9b94-fe4e522ac7a6",
    STARGAZER_DOCUSIGN_DEMO_USER_ID: "646ff222-9d43-43bb-bc55-1277556a5f44",
    STARGAZER_DOCUSIGN_DEMO_CERITY_API_USER_ID: "770a0369-38ea-4d1d-8b6a-026644fbc8af",

    STARGAZER_CDN_ENABLED: "true",
    STARGAZER_CDN_ROOT: "https://cdn.anduin.app",
    STARGAZER_ASYNC_API_ENABLED: "true",

    STARGAZER_SERVICES_FOUNDATIONDB_VERBOSE_TRACE_EVENT: "true",

    STARGAZER_SERVICES_OTP_AUTHENTICATION_ENABLE_REVERT_OTP: "false",
    STARGAZER_SERVICES_OTP_AUTHENTICATION_LOGIN_EMAIL_ADDRESS: "<EMAIL>",
  };
}
