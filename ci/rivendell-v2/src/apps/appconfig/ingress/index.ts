import { Context } from "@anduintransaction/rivendell";
import {
  IIngressRule,
  IIngressSpec,
  Ingress,
} from "kubernetes-models/networking.k8s.io/v1";
import { GondorIngressOpts, RedirectRule } from "./types";
import {
  advancedAnnotations,
  cloudflareProxiedAnnotations,
  injectRedirectAnnotation,
  standardAnnotations,
} from "./annotations";

import { annotationRule } from "./rules";
export * from "./rules";

export function createRedirectIngress(
  ctx: Context,
  opts: GondorIngressOpts = {},
  rules: Record<string, RedirectRule> = {},
) {
  const annotations = standardAnnotations(ctx, opts);
  Object.values(rules).forEach((r) => {
    injectRedirectAnnotation(annotations, r);
  });
  const transformedRules = Object
    .entries(rules)
    .map(([source, config]): IIngressRule => {
      const ruleName = `redirect-${config.ruleName}`;
      return annotationRule(source, ruleName);
    });

  return new Ingress({
    metadata: {
      name: opts.ingressName || "gondor",
      annotations: annotations,
    },
    spec: {
      ingressClassName: opts.ingressClass || "alb",
      rules: transformedRules,
    },
  });
}

// function isPrivateRoute(rule: IIngressRule): boolean {
//   const host = rule.host!;
//   return host.startsWith("target") ||
//     host.endsWith("fundsub.io") ||
//     host.endsWith("anduin.partners") ||
//     host.endsWith("anduin.io");
// }

export function createGondorIngress(
  ctx: Context,
  opts: GondorIngressOpts = {},
): Ingress[] {
  const spec: IIngressSpec = {
    ingressClassName: opts.ingressClass || "alb",
    rules: opts.rules || [],
  };

  const publicName = opts.ingressName || "gondor";
  const publicIngress = new Ingress({
    metadata: {
      name: publicName,
      annotations: advancedAnnotations(ctx, opts),
    },
    spec,
  });

  const privateName = `${publicName}-private`;
  const privateIngress = new Ingress({
    metadata: {
      name: privateName,
      annotations: cloudflareProxiedAnnotations(ctx, opts),
    },
    spec,
  });

  return [publicIngress, privateIngress];
}
