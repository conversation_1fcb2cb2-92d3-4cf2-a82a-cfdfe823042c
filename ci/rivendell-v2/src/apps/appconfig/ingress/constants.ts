export const AlbAnnotation = "alb.ingress.kubernetes.io";

export const RegionalDomainMapping: Record<string, string> = {
  "us-east-1": "", // no regional suffix on US
  "eu-west-1": "eu",
};

export const Actions = {
  NotFound: {
    type: "fixed-response",
    fixedResponseConfig: {
      contentType: "text/plain",
      statusCode: "404",
      messageBody: "404 not found",
    },
  },

  Redirect(target: string, secure: boolean = true) {
    return {
      type: "redirect",
      redirectConfig: {
        host: target,
        protocol: secure ? "HTTPS" : "HTTP",
        port: secure ? "443" : "80",
        statusCode: "HTTP_302",
      },
    };
  },
};
