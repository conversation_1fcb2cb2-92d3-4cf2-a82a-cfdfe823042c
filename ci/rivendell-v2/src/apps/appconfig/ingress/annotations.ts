import { Context, Finder } from "@anduintransaction/rivendell";
import { GondorIngressOpts, RedirectRule } from "./types";
import { Actions, AlbAnnotation } from "./constants";

export function standardAnnotations(
  ctx: Context,
  opts: GondorIngressOpts = {},
): Record<string, string> {
  const albPorts = [{ "HTTP": 80 }, { "HTTPS": 443 }];
  return {
    [`${AlbAnnotation}/scheme`]: "internet-facing",
    [`${AlbAnnotation}/target-type`]: "ip",
    [`${AlbAnnotation}/group.name`]: opts.albGroupName || "",
    [`${AlbAnnotation}/listen-ports`]: JSON.stringify(albPorts),
    [`${AlbAnnotation}/ssl-redirect`]: "443",
    [`${AlbAnnotation}/ssl-policy`]: "ELBSecurityPolicy-FS-1-2-Res-2020-10",
  };
}

export function advancedAnnotations(
  ctx: Context,
  opts: GondorIngressOpts = {},
): Record<string, string> {
  const annotations = standardAnnotations(ctx, opts);
  const albAttributes = Object.entries({
    "access_logs.s3.enabled": "true",
    "access_logs.s3.bucket": opts.accessLogBucket || "anduin-elb-log",
    "access_logs.s3.prefix": opts.accessLogBucketPrefix || "eks-main-alb",
    "idle_timeout.timeout_seconds": "300",
    "routing.http.desync_mitigation_mode": "monitor",
  })
    .map(([key, value]) => `${key}=${value}`)
    .join(",");
  const wafAclArn = Finder.requiredString(ctx.configs, ["wafAclArn"], "");

  annotations[`${AlbAnnotation}/load-balancer-attributes`] = albAttributes;
  annotations[`${AlbAnnotation}/wafv2-acl-arn`] = wafAclArn;
  annotations[`${AlbAnnotation}/actions.response-404`] = JSON.stringify(
    Actions.NotFound,
  );

  return annotations;
}

export function cloudflareProxiedAnnotations(
  ctx: Context,
  opts: GondorIngressOpts = {},
): Record<string, string> {
  const annotations = advancedAnnotations(ctx, opts);

  const albPorts = [{ HTTPS: 444 }];
  const cfCertArn = Finder.requiredString(ctx.configs, ["cfCertArn"]);
  const cfTruststoreArn = Finder
    .requiredString(ctx.configs, ["cfTruststoreArn"]);

  annotations[`${AlbAnnotation}/listen-ports`] = JSON.stringify(albPorts);
  annotations[`${AlbAnnotation}/certificate-arn`] = cfCertArn;
  annotations[`${AlbAnnotation}/mutual-authentication`] = JSON.stringify([
    { port: 444, mode: "verify", trustStore: cfTruststoreArn },
  ]);
  delete annotations[`${AlbAnnotation}/ssl-redirect`];

  return annotations;
}

export function injectRedirectAnnotation(
  annotations: Record<string, string>,
  rule: RedirectRule,
) {
  const { ruleName, target } = rule;
  const action = JSON.stringify(Actions.Redirect(target));
  annotations[`${AlbAnnotation}/actions.redirect-${ruleName}`] = action;
}
