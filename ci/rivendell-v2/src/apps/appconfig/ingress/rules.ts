import { Context, Finder } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import {
  IHTTPIngressPath,
  IIngressBackend,
  IIngressRule,
} from "kubernetes-models/networking.k8s.io/v1";
import { RegionalDomainMapping } from "./constants";
import { ForwardSpec, GondorForwardSpec, GondorRouteOpts } from "./types";

import * as features from "@app/gondor/features";

export function simpleForwardRule(
  host: string,
  opts: ForwardSpec = {},
): IIngressRule {
  return {
    host: host,
    http: {
      paths: [{
        path: opts.path || "/",
        pathType: opts.pathType || "Prefix",
        backend: {
          service: {
            name: opts.serviceName || "",
            port: {
              name: opts.servicePortName,
              number: opts.servicePort,
            },
          },
        },
      }],
    },
  };
}

export function annotationRule(host: string, ruleName: string): IIngressRule {
  return {
    host: host,
    http: {
      paths: [{
        path: "/",
        pathType: "Prefix",
        backend: {
          service: {
            name: ruleName,
            port: {
              name: "use-annotation",
            },
          },
        },
      }],
    },
  };
}

export function gondorForwardRule(
  ctx: Context,
  host: string,
  opts: GondorForwardSpec = {},
): IIngressRule {
  const isMaintenance = Finder
    .requiredBool(ctx.configs, ["maintenance"], false);
  const gondorBackend: IIngressBackend = isMaintenance
    ? {
      service: {
        name: "maintenance",
        port: {
          number: 8080,
        },
      },
    }
    : {
      service: {
        name: opts.serviceName || "gondor",
        port: {
          name: opts.servicePortName,
          number: opts.servicePort,
        },
      },
    };

  const gondorPath: IHTTPIngressPath = {
    path: opts.path || "/",
    pathType: opts.pathType || "Prefix",
    backend: gondorBackend,
  };

  return {
    host: host,
    http: {
      paths: [gondorPath],
    },
  };
}

export function standardGondorRoutes(
  ctx: Context,
  opts: GondorRouteOpts,
): IIngressRule[] {
  const resolvedFeatures = features.getGondorFeatures(ctx);

  const rootDomain = ConfigUtils.isProduction(ctx.configs)
    ? "anduin.app"
    : "anduin.dev";

  const regionalSuffix =
    RegionalDomainMapping[ConfigUtils.getAwsRegion(ctx.configs)];
  if (regionalSuffix === undefined) {
    throw new Error("unsupported region domain");
  }

  const regionalBaseDomain = `${regionalSuffix}.${rootDomain}`
    .replace(/^\./, "");
  const envBaseDomain = `${opts.envSuffix}.${regionalBaseDomain}`
    .replace(/^\./, "");
  const appDomain = (app: string) => {
    return opts.envSuffix === ""
      ? `${app}.${envBaseDomain}`
      : `${app}-${envBaseDomain}`;
  };

  const gondorSvc: GondorForwardSpec = {
    serviceName: "gondor",
    servicePort: 8080,
  };
  const gondorPortalSvc: GondorForwardSpec = {
    serviceName: features.portalService(resolvedFeatures),
    servicePort: 8080,
  };

  const routes: IIngressRule[] = [
    gondorForwardRule(ctx, envBaseDomain, gondorSvc), // main entry
    simpleForwardRule(appDomain("nats"), {
      serviceName: "nats",
      servicePort: 5222,
    }),
  ];
  if (opts.customPortal) {
    routes.push(gondorForwardRule(ctx, appDomain("portal"), gondorPortalSvc));
  }
  if (opts.customFS) {
    routes.push(gondorForwardRule(ctx, appDomain("fundsub"), gondorSvc));
  }
  if (opts.customIA) {
    routes.push(gondorForwardRule(ctx, appDomain("ia"), gondorSvc));
  }
  if (opts.customId) {
    routes.push(gondorForwardRule(ctx, appDomain("id"), gondorSvc));
  }
  if (opts.customTarget) {
    routes.push(gondorForwardRule(ctx, appDomain("target"), gondorSvc));
  }
  if (opts.customSign) {
    routes.push(gondorForwardRule(ctx, appDomain("sign"), gondorSvc));
  }
  if (opts.customDataroom) {
    routes.push(gondorForwardRule(ctx, appDomain("dataroom"), gondorSvc));
  }
  if (opts.customPlatform) {
    routes.push(gondorForwardRule(ctx, appDomain("platform"), gondorSvc));
  }
  if (opts.customIntegration) {
    routes.push(gondorForwardRule(ctx, appDomain("integrations"), gondorSvc));
  }
  if (opts.customRia) {
    routes.push(gondorForwardRule(ctx, appDomain("advisors"), gondorSvc));
  }
  if (features.havePublicApi(resolvedFeatures)) {
    routes.push(gondorForwardRule(ctx, appDomain("api"), {
      serviceName: "tyk-http",
      servicePort: 80,
    }));
    routes.push(gondorForwardRule(ctx, appDomain("s-api"), {
      serviceName: "tyk-http",
      servicePort: 80,
    }));
  }
  if (Finder.optionalBool(ctx.configs, ["onlyoffice"], false)) {
    routes.push(simpleForwardRule(appDomain("doc-server"), {
      serviceName: "oo-doc-server",
      servicePort: 80,
    }));
  }
  return routes;
}
