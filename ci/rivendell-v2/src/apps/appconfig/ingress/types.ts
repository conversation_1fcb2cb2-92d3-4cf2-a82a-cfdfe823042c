import {
  IHTTPIngressPath,
  IIngressRule,
} from "kubernetes-models/networking.k8s.io/v1";

export interface GondorIngressOpts {
  ingressName?: string;
  ingressClass?: string;
  rules?: IIngressRule[];

  albGroupName?: string;
  accessLogBucket?: string;
  accessLogBucketPrefix?: string;
}

export interface ForwardSpec {
  path?: string;
  pathType?: IHTTPIngressPath["pathType"];
  serviceName?: string;
  servicePort?: number;
  servicePortName?: string;
}

export interface GondorForwardSpec extends ForwardSpec {}

export interface GondorRouteOpts {
  envSuffix: string;
  customPortal?: boolean;
  customFS?: boolean;
  customIA?: boolean;
  customId?: boolean;
  customTarget?: boolean;
  customSign?: boolean;
  customDataroom?: boolean;
  customPlatform?: boolean;
  customIntegration?: boolean;
  customRia?: boolean;
}

export interface RedirectRule {
  ruleName: string;
  target: string;
}
