import {
  Context,
  K8sObject,
  NoopSecretProvider,
  SecretValue,
} from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import * as k8s from "@utils/k8s";

function secretToRecord(secrets: SecretValue[]) {
  return secrets
    .filter((s) => s.value !== undefined)
    .reduce(
      (accum: Record<string, string>, { name, value }) => {
        accum[name] = value!;
        return accum;
      },
      {},
    );
}

async function safeGuardGetSecret(
  ctx: Context,
  prefix: string,
): Promise<SecretValue[]> {
  const secrets = (await ctx.getSecretPrefix(prefix))
    .filter((s) => !!s.name);
  const isEmptySecret = secrets === undefined ||
    secrets === null ||
    secrets.length === 0;
  const isNoop = ctx.secretProvider instanceof NoopSecretProvider;
  if (!isNoop && isEmptySecret) {
    throw new Error(`env "${ctx.env}" have empty secret prefix "${prefix}"`);
  }
  return secrets;
}

export default async function (ctx: Context): Promise<K8sObject[]> {
  const env = ConfigUtils.getEnvName(ctx.configs);

  const staticSecrets = await safeGuardGetSecret(ctx, "static");
  const sharedSecrets = await safeGuardGetSecret(ctx, "shared");
  const jwtSecrets = await safeGuardGetSecret(ctx, "jwt");
  const tidbSecrets = await safeGuardGetSecret(ctx, "tidb");
  const natsSecrets = await safeGuardGetSecret(ctx, "nats");
  const secrets = [
    k8s.createSecretFromRawData("gondor-static", secretToRecord(staticSecrets)),
    k8s.createSecretFromRawData(
      "shared-environment",
      secretToRecord(sharedSecrets),
    ),
    k8s.createSecretFromRawData("jwt-keymap", secretToRecord(jwtSecrets)),
    k8s.createSecretFromRawData("tidb-secrets", secretToRecord(tidbSecrets)),
    k8s.createSecretFromRawData("nats-jwt-config", secretToRecord(natsSecrets)),
  ];
  if (["blackwood", "local", "integ-test"].includes(env)) {
    const dynamicSecrets = await ctx.getSecretPrefix("dynamic");
    secrets.push(
      k8s.createSecretFromRawData(
        "gondor-dynamic",
        secretToRecord(dynamicSecrets),
      ),
    );
  }

  return secrets;
}
