#!/usr/bin/env bash

function doBackup {
  if [ $# -lt 1 ]; then
    echo "USAGE: $0 backup [s3 prefix]"
    exit 1
  fi
  s3Prefix=$1
  echo "Backing up to $s3Prefix"
  if aws s3 ls $s3Prefix; then
    aws s3 rm --recursive s3://$s3Prefix
  fi
  formIds=`curl -sS $GONDOR_HOST:$GONDOR_PORT/api/v3/form-public/get-all-form-ids -H 'Content-Type: application/json' -d '{"sharedSecret": "'$SHARED_SECRET'"}' | jq -r '.forms[]'`
  if [ $? -ne 0 ]; then
    return 0
  fi
  cd /tmp
  rm -rf form-backup form-backup.tar.gz && mkdir -p form-backup
  if [ ! -z "$formIds" ]; then
    for formId in $formIds; do
      echo "Downloading $formId"
      mkdir -p form-backup/$formId
      AWS_ACCESS_KEY_ID=$STARGAZER_AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY=$STARGAZER_AWS_SECRET_ACCESS_KEY AWS_DEFAULT_REGION=$FORM_BUCKET_AWS_DEFAULT_REGION aws s3 cp --recursive s3://$FORM_BUCKET/forms/$formId form-backup/$formId
    done
  fi
  echo "Uploading"
  tar czf form-backup.tar.gz form-backup &&
    aws s3 cp form-backup.tar.gz s3://$s3Prefix
}

function doRestore {
  if [ $# -lt 1 ]; then
    echo "USAGE: $0 restore [s3 prefix]"
    exit 1
  fi
  s3Prefix=$1
  echo "Restoring from $s3Prefix"
  cd /tmp &&
    rm -rf backup/* &&
    mkdir -p backup &&
    cd backup &&
    aws s3 cp s3://$s3Prefix form-backup.tar.gz &&
    tar xzf form-backup.tar.gz
  if [ $? -ne 0 ]; then
    return 1
  fi
  formIds=`ls form-backup`
  if [ -z "$formIds" ]; then
    echo "No form found"
    return 0
  fi
  for formId in $formIds; do
    target=$FORM_BUCKET/forms/$formId
    echo "Restoring $formId to $target"
    if AWS_ACCESS_KEY_ID=$STARGAZER_AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY=$STARGAZER_AWS_SECRET_ACCESS_KEY AWS_DEFAULT_REGION=$FORM_BUCKET_AWS_DEFAULT_REGION aws s3 ls $target; then
      AWS_ACCESS_KEY_ID=$STARGAZER_AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY=$STARGAZER_AWS_SECRET_ACCESS_KEY AWS_DEFAULT_REGION=$FORM_BUCKET_AWS_DEFAULT_REGION aws s3 rm --recursive s3://$target
    fi
    AWS_ACCESS_KEY_ID=$STARGAZER_AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY=$STARGAZER_AWS_SECRET_ACCESS_KEY AWS_DEFAULT_REGION=$FORM_BUCKET_AWS_DEFAULT_REGION aws s3 cp --recursive form-backup/$formId s3://$target
  done
  echo "Done"
}

if [ $# -lt 1 ]; then
  echo "USAGE: $0 [backup|restore]"
  exit 1
fi

cmd=$1
shift

case $cmd in
  backup)
    doBackup $@
    ;;
  restore)
    doRestore $@
    ;;
  *)
    echo "USAGE: $0 [backup|restore]"
    exit 1
    ;;
esac
