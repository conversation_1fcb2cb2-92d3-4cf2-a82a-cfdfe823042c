import { Context, K8sObject } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import { Deployment } from "kubernetes-models/apps/v1";
import * as k8s from "@utils/k8s";
import * as com from "../common";

export default async function (ctx: Context): Promise<K8sObject[]> {
  const name = "gondor-memcached";
  const commonLabels = { app: name };

  const deploy = new Deployment({
    metadata: {
      name: name,
    },
    spec: {
      replicas: 1,
      selector: {
        matchLabels: commonLabels,
      },
      template: {
        metadata: {
          labels: commonLabels,
        },
        spec: {
          nodeSelector: com.node.nodeDatabase(ctx),
          imagePullSecrets: com.misc.getPullSecrets(ctx),
          containers: [{
            name: "memcached",
            image: ConfigUtils.getDockerPublicImage(ctx.configs, "memcached"),
            args: [
              "--max-item-size=20m",
            ],
            readinessProbe: {
              tcpSocket: {
                port: "memcached",
              },
              initialDelaySeconds: 5,
              timeoutSeconds: 10,
            },
            livenessProbe: {
              tcpSocket: {
                port: "memcached",
              },
              initialDelaySeconds: 5,
              timeoutSeconds: 10,
            },
            ports: [{
              name: "memcached",
              containerPort: 11211,
            }],
          }],
        },
      },
    },
  });

  const svc = k8s.serviceFromDeployment(deploy, {
    probeTcp: true,
    ports: [{
      name: "memcached",
      port: 11211,
      protocol: "TCP",
      targetPort: 11211,
    }],
  });

  return [
    deploy,
    svc,
  ];
}
