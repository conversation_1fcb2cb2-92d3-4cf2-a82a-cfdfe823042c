import { Context, K8sObject } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import * as k8s from "@utils/k8s";
import postgresql from "../common/postgresql";
import * as com from "../common";

export default async function (ctx: Context): Promise<K8sObject[]> {
  return postgresql(ctx, {
    name: "timescaledb-v3",
    pgConfigs: {
      "max_connections": "400",
    },
    nodeSelector: com.node.nodeDatabase(ctx),
    dockerImage: ConfigUtils.getDockerEcrImage(ctx.configs, "timescaledb"),
    resources: {
      limits: {
        memory: "2Gi",
      },
      requests: {
        memory: "300Mi",
      },
    },
    defaultDb: "timescaledb",
    creds: {
      user: {
        valueFrom: k8s.envValueFromSecret(
          com.env.GondorSecretName,
          "STARGAZER_SERVICES_TIMESCALEDB_USERNAME",
        ),
      },
      password: {
        valueFrom: k8s.envValueFromSecret(
          com.env.GondorSecretName,
          "STARGAZER_SERVICES_TIMESCALEDB_PASSWORD",
        ),
      },
    },
    storageSize: "40Gi",
    extraEnvs: [
      k8s.envFromValue("TSDB_DEFAULT_VERSION", "2.14.2"),
    ],
    needPullSecret: true,
  });
}
