import { Context, K8sObject } from "@anduintransaction/rivendell";
import * as path from "path";
import { Job } from "kubernetes-models/batch/v1";
import * as k8s from "@utils/k8s";
import { ConfigUtils } from "@configutils";
import * as com from "../common";

export default async function (ctx: Context): Promise<K8sObject[]> {
  const name = "migrate-timescaledb-v3";

  const cm = await k8s.createConfigMapFromPath(
    name,
    path.resolve(__dirname, `./resources/migrate.sh`),
  );
  const shouldMigration = !ConfigUtils.isEnvNukable(ctx.configs);

  const job = new Job({
    metadata: { name },
    spec: {
      backoffLimit: 0,
      ttlSecondsAfterFinished: 1800,
      template: {
        metadata: { name },
        spec: {
          restartPolicy: "Never",
          imagePullSecrets: com.misc.getPullSecrets(ctx),
          containers: [{
            name: "migrate",
            image: ConfigUtils.getDockerEcrImage(ctx.configs, "timescaledb"),
            command: ["sh"],
            args: [`/opt/anduin/scripts/migrate.sh`],
            env: [
              k8s.envFromValue("ENABLE_MIGRATION", `${shouldMigration}`),
              k8s.envFromValue("PGPORT", "5432"),
              com.env.envFromGondorSecret(
                "PGUSER",
                "STARGAZER_SERVICES_TIMESCALEDB_USERNAME",
              ),
              com.env.envFromGondorSecret(
                "PGPASSWORD",
                "STARGAZER_SERVICES_TIMESCALEDB_PASSWORD",
              ),
            ],
            resources: com.resources.getResources(
              ctx,
              com.resources.FargateResourceSpecs.S,
            ),
          }],
        },
      },
    },
  });

  com.storage.cmAsVolume(ctx, job.spec!.template.spec!, {
    cmName: cm.metadata!.name!,
    volumeName: "scripts",
    mounts: [{
      container: "migrate",
      path: "/opt/anduin/scripts",
    }],
  });

  const pvc = com.storage.storageAsPvc(ctx, job.spec!.template.spec!, {
    name,
    accessModes: ["ReadWriteOnce"],
    size: "4Gi",
    mounts: [{
      container: "migrate",
      path: "/data",
    }],
  });

  return [cm, job, pvc];
}
