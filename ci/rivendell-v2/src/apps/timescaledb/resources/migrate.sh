#!/usr/bin/env bash

set -e

if [ "${ENABLE_MIGRATION}" = "false" ]; then
  echo "Skip timescaledb v3 migration"
  exit 0
fi

mkdir -p /data/timescaledb
if test -f /data/timescaledb/migrated; then
  echo "Already migrated timescaledb v3"
  exit 0
fi

# Migration logic
echo "Doing migration"
SRC="postgres://${PGUSER}:${PGPASSWORD}@timescaledb-v2:5432/timescaledb"
TARGET="postgres://${PGUSER}:${PGPASSWORD}@timescaledb-v3:5432/timescaledb"
DATA_FILE="/data/timescaledb/dump.sql"

count=0
echo "Waiting for src pg at timescaledb-v2:5432"
until psql $SRC -v ON_ERROR_STOP=1 --echo-errors -c "\l" | grep timescaledb; do
    echo "Retrying $count"
    sleep 5
    count=`expr $count + 1`
    if [ $count -gt 20 ]; then
        echo "Something wrong, cannot connect to timescaledb-v2:5432"
        exit 1
    fi
done

count=0
echo "Waiting for src pg at timescaledb-v3:5432"
until psql $TARGET -v ON_ERROR_STOP=1 --echo-errors -c "\l" | grep timescaledb; do
    echo "Retrying $count"
    sleep 5
    count=`expr $count + 1`
    if [ $count -gt 20 ]; then
        echo "Something wrong, cannot connect to timescaledb-v3:5432"
        exit 1
    fi
done

# Backup old db data
pg_dump -d "$SRC" \
  --format=plain \
  --quote-all-identifiers \
  --no-tablespaces \
  --no-owner \
  --no-privileges \
  --file="${DATA_FILE}"

# Restore data into new db
psql $TARGET -v ON_ERROR_STOP=1 --echo-errors \
  -c "SELECT public.timescaledb_pre_restore();" \
  -f "${DATA_FILE}" \
  -c "SELECT public.timescaledb_post_restore();"

touch /data/timescaledb/migrated
