server {
  listen       18080 default_server;
  
  server_tokens off ;

  client_max_body_size 256m;
  ignore_invalid_headers off;

  gzip          on;
  gzip_buffers  16 8k;

  proxy_set_header X-Real-IP $remote_addr;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_set_header X-NginX-Proxy true;
  proxy_ssl_session_reuse off;
  proxy_set_header Host $http_host;
  proxy_set_header X-Forwarded-Proto $http_x_forwarded_proto;
  proxy_set_header X-Forwarded-Port $http_x_forwarded_port;

  add_header Access-Control-Allow-Origin $http_Origin always;
  add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
  add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
  add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';
<% if (!local) { %>
  add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
  add_header Content-Security-Policy "frame-ancestors 'self' https://*.anduintransact.com https://*.anduin.app https://*.anduin.dev https://*.anduin.io https://*.anduin.partners https://*.anduinroom.io https://*.fundsub.io https://*.annduin.app https://*.anduinsign.io ;" always;
<% } %>
  add_header X-Content-Type-Options 'nosniff' always;
  #add_header X-Frame-Options 'SAMEORIGIN' always;
  proxy_hide_header Access-Control-Allow-Origin;

  location ~ /(.*) {
    proxy_pass http://127.0.0.1:80;
  }
}
