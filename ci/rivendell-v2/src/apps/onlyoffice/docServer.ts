import * as path from "path";
import * as ejs from "ejs";
import { Context, K8sObject } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import { Deployment } from "kubernetes-models/apps/v1";
import * as k8s from "@utils/k8s";
import * as com from "../common";

async function getNginxConfTemplate(ctx: Context) {
  const cfgPath = path.resolve(__dirname, "./resources/nginx.conf");
  return await ejs.renderFile(cfgPath, {
    local: ConfigUtils.isLocal(ctx.configs),
  });
}

export default async function (ctx: Context): Promise<K8sObject[]> {
  const name = "oo-doc-server";
  const commonLabels = { name };

  const nginxConf = await getNginxConfTemplate(ctx);
  const nginxCm = k8s.createConfigMapFromData(
    "oo-server-nginx",
    {
      "default.conf": nginxConf,
    },
  );

  const deploy = new Deployment({
    metadata: { name },
    spec: {
      replicas: 1,
      selector: {
        matchLabels: commonLabels,
      },
      strategy: {
        type: "RollingUpdate",
        rollingUpdate: {
          maxSurge: "25%",
          maxUnavailable: "25%",
        },
      },
      template: {
        metadata: {
          labels: commonLabels,
          annotations: {
            "nginx-hash": com.checksum.hash(nginxConf),
          },
        },
        spec: {
          nodeSelector: com.node.nodeDatabase(ctx),
          imagePullSecrets: com.misc.getPullSecrets(ctx),
          containers: [
            {
              name,
              image: ConfigUtils.getDockerEcrImage(
                ctx.configs,
                "onlyOfficeDocumentServer",
              ),
              stdin: true,
              tty: true,
              readinessProbe: {
                httpGet: { path: "/", port: 18080 },
                initialDelaySeconds: 5,
                timeoutSeconds: 30,
              },
              livenessProbe: {
                httpGet: { path: "/", port: 18080 },
                initialDelaySeconds: 120,
                timeoutSeconds: 30,
              },
              resources: com.resources.getResources(ctx, {
                limits: { memory: "2048Mi" },
                requests: { memory: "1024Mi" },
              }),
              env: [
                k8s.envFromValue("JWT_ENABLED", "false"),
                k8s.envFromConfigMap(
                  "POSTGRESQL_SERVER_HOST",
                  "onlyoffice",
                  "postgres.host",
                ),
                k8s.envFromConfigMap(
                  "POSTGRESQL_SERVER_PORT",
                  "onlyoffice",
                  "postgres.port",
                ),
                k8s.envFromConfigMap(
                  "POSTGRESQL_SERVER_DB_NAME",
                  "onlyoffice",
                  "postgres.database",
                ),
                k8s.envFromSecret(
                  "POSTGRESQL_SERVER_USER",
                  "onlyoffice",
                  "postgres.username",
                ),
                k8s.envFromSecret(
                  "POSTGRESQL_SERVER_PASS",
                  "onlyoffice",
                  "postgres.password",
                ),
                k8s.envFromConfigMap(
                  "RABBITMQ_SERVER_URL",
                  "onlyoffice",
                  "rabbitmq.url",
                ),
                k8s.envFromConfigMap(
                  "REDIS_SERVER_HOST",
                  "onlyoffice",
                  "redis.host",
                ),
                k8s.envFromConfigMap(
                  "REDIS_SERVER_PORT",
                  "onlyoffice",
                  "redis.port",
                ),
              ],
            },
            {
              name: "nginx",
              image: ConfigUtils.getDockerPublicImage(ctx.configs, "nginx"),
            },
          ],
        },
      },
    },
  });

  com.storage.cmAsVolume(ctx.configs, deploy.spec!.template.spec!, {
    cmName: nginxCm.metadata!.name!,
    volumeName: "nginx-conf",
    mounts: [{
      container: "nginx",
      path: "/etc/nginx/conf.d",
    }],
  });

  const svc = k8s.serviceFromDeployment(deploy, {
    svcName: name,
    probe: true,
    probeSchema: "http",
    ports: [{
      name: "api",
      port: 80,
      protocol: "TCP",
      targetPort: 18080,
    }],
  });

  return [deploy, svc, nginxCm];
}
