import { Context, K8sObject } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import { Deployment } from "kubernetes-models/apps/v1";
import * as k8s from "@utils/k8s";
import * as com from "../common";

export default async function (ctx: Context): Promise<K8sObject[]> {
  const name = "oo-redis";
  const commonLabels = { name };

  const deploy = new Deployment({
    metadata: { name },
    spec: {
      replicas: 1,
      selector: {
        matchLabels: commonLabels,
      },
      strategy: {
        type: "RollingUpdate",
        rollingUpdate: {
          maxSurge: 0,
          maxUnavailable: 1,
        },
      },
      template: {
        metadata: {
          labels: commonLabels,
        },
        spec: {
          nodeSelector: com.node.nodeDatabase(ctx),
          imagePullSecrets: com.misc.getPullSecrets(ctx),
          containers: [{
            name,
            image: ConfigUtils.getDockerPublicImage(ctx.configs, "redis"),
            args: ["redis-server", "--appendonly", "yes"],
            readinessProbe: {
              tcpSocket: { port: 6379 },
              initialDelaySeconds: 5,
              timeoutSeconds: 30,
            },
            livenessProbe: {
              tcpSocket: { port: 6379 },
              initialDelaySeconds: 120,
              timeoutSeconds: 30,
            },
            resources: com.resources.getResources(ctx, {
              limits: { memory: "256Mi" },
              requests: { memory: "100Mi" },
            }),
          }],
        },
      },
    },
  });

  const pvcs = [];
  if (ConfigUtils.isPersistData(ctx.configs)) {
    const pvc = com.storage.storageAsPvc(ctx, deploy.spec!.template.spec!, {
      name,
      accessModes: ["ReadWriteOnce"],
      size: "2Gi",
      mounts: [{
        container: name,
        path: "/data",
      }],
    });
    com.storage.legacyAnnotateClaim(ctx, pvc);
    pvcs.push(pvc);
  }

  const svc = k8s.serviceFromDeployment(deploy, {
    svcName: name,
    probeTcp: true,
    ports: [{
      name: "redis",
      port: 6379,
      protocol: "TCP",
      targetPort: 6379,
    }],
  });

  return [deploy, svc, ...pvcs];
}
