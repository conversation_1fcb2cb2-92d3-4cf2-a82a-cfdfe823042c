import { Context, K8sObject } from "@anduintransaction/rivendell";
import * as k8s from "@utils/k8s";

export default async function (_ctx: Context): Promise<K8sObject[]> {
  return [
    k8s.createConfigMapFromData("onlyoffice", {
      "postgres.host": "oo-postgres",
      "postgres.port": "5432",
      "postgres.database": "onlyoffice",
      "rabbitmq.url": "amqp://oo-rabbitmq:5672",
      "redis.host": "oo-redis",
      "redis.port": "6379",
    }),
    k8s.createSecretFromRawData("onlyoffice", {
      "postgres.username": "onlyoffice",
      "postgres.password": "2f00219bb001cea210ebdee078bfeab8",
    }),
  ];
}
