import {
  Context,
  K8sObject,
  SourceGenerator,
} from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import * as k8s from "@utils/k8s";
import { Deployment } from "kubernetes-models/apps/v1";
import * as com from "../common";

export default function (name: string): SourceGenerator {
  return async function (ctx: Context): Promise<K8sObject[]> {
    const commonLabels = { name };

    const deploy = new Deployment({
      metadata: { name },
      spec: {
        replicas: 1,
        selector: {
          matchLabels: commonLabels,
        },
        strategy: {
          type: "RollingUpdate",
          rollingUpdate: {
            maxSurge: 0,
            maxUnavailable: 1,
          },
        },
        template: {
          metadata: {
            labels: commonLabels,
          },
          spec: {
            nodeSelector: com.node.nodeDatabase(ctx),
            imagePullSecrets: com.misc.getPullSecrets(ctx),
            containers: [{
              name,
              image: ConfigUtils.getDockerEcrImage(ctx.configs, "foundationDb"),
              readinessProbe: {
                tcpSocket: { port: 4500 },
                initialDelaySeconds: 5,
                timeoutSeconds: 30,
              },
              livenessProbe: {
                tcpSocket: { port: 4500 },
                initialDelaySeconds: 120,
                timeoutSeconds: 30,
              },
              resources: com.resources.getResources(ctx, {
                requests: {
                  memory: "1536Mi",
                },
              }),
              env: [
                k8s.envFromValue("FDB_DATA_DIR", "/data/foundationdb/data"),
                k8s.envFromValue("FDB_LOG_DIR", "/data/foundationdb/logs"),
              ],
            }],
          },
        },
      },
    });

    const pvc = [];
    if (ConfigUtils.isPersistData(ctx.configs)) {
      const dataPvc = com.storage.storageAsPvc(
        ctx,
        deploy.spec!.template.spec!,
        {
          name,
          accessModes: ["ReadWriteOnce"],
          size: ctx.env === "gondor-minas-tirith" ? "250Gi" : "100Gi",
          mounts: [{
            container: name,
            path: "/data/foundationdb",
          }],
        },
      );
      com.storage.legacyAnnotateClaim(ctx, dataPvc);
      pvc.push(dataPvc);
    }

    // to reduce IO load on root disk for integ test
    if (ctx.env === "integ-test") {
      com.storage.ephermeralAsVolume(ctx, deploy.spec!.template.spec!, {
        name: "data",
        accessModes: ["ReadWriteOnce"],
        size: "10Gi",
        mounts: [{
          container: name,
          path: "/data/foundationdb",
        }],
      });
    }

    const svc = k8s.serviceFromDeployment(deploy, {
      probeTcp: true,
      ports: [{
        name,
        port: 4500,
        protocol: "TCP",
        targetPort: 4500,
      }],
    });

    const headlessSvc = k8s.serviceFromDeployment(deploy, {
      clusterIP: "None",
      ports: [{
        name,
        port: 4500,
        protocol: "TCP",
        targetPort: 4500,
      }],
    });

    return [deploy, svc, headlessSvc, ...pvc];
  };
}
