import { Context, K8sObject } from "@anduintransaction/rivendell";
import * as k8s from "@utils/k8s";
import { Deployment } from "kubernetes-models/apps/v1";
import * as path from "path";
import * as com from "../common";
import * as fdbCommon from "./common";
import { FdbReadonlyCondition, isEnableFdbReadonly } from "./flags";

export default async function (ctx: Context): Promise<K8sObject[]> {
  const name = "fdb-stats";
  const commonLabels = { name };

  const cm = await k8s.createConfigMapFromPath(
    name,
    path.resolve(__dirname, `./resources/config.yml`),
  );

  const deploy = new Deployment({
    metadata: { name },
    spec: {
      replicas: 1,
      selector: {
        matchLabels: commonLabels,
      },
      strategy: {
        type: "RollingUpdate",
        rollingUpdate: {
          maxSurge: 0,
          maxUnavailable: 1,
        },
      },
      revisionHistoryLimit: 10,
      template: {
        metadata: {
          labels: commonLabels,
        },
        spec: {
          nodeSelector: com.node.nodeDatabase(ctx),
          imagePullSecrets: com.misc.getPullSecrets(ctx),
          initContainers: [
            ...fdbCommon.initWaitFdbContainers(ctx),
          ],
          containers: [{
            name,
            image: fdbCommon.getFdbStatImage(ctx),
            readinessProbe: {
              httpGet: {
                path: "/health",
                port: 8081,
              },
              initialDelaySeconds: 5,
              timeoutSeconds: 30,
            },
            livenessProbe: {
              httpGet: {
                path: "/health",
                port: 8081,
              },
              initialDelaySeconds: 120,
              timeoutSeconds: 30,
            },
            resources: com.resources.getResources(ctx, {
              requests: {
                memory: "50Mi",
              },
            }),
            env: [
              k8s.envFromValue("APP_CONFIG_PATH", "/etc/fdb-stats"),
              {
                name: "POD_NAMESPACE",
                valueFrom: {
                  fieldRef: {
                    fieldPath: "metadata.namespace",
                  },
                },
              },
              k8s.envFromValue(
                "FDB_BACKUP_TAG",
                "$(POD_NAMESPACE)-v3-tag",
              ),
              k8s.envFromValue(
                "FDB_DR_BACKUP_TAG",
                "fdb-dr-readonly",
              ),
              k8s.envFromValue(
                "IS_ENABLE_FDB_READ_ONLY",
                isEnableFdbReadonly(ctx) !== FdbReadonlyCondition.None ? "true" : "false",
              )
            ],
          }],
        },
      },
    },
  });

  com.storage.cmAsVolume(ctx, deploy.spec!.template.spec!, {
    cmName: name,
    volumeName: name,
    mounts: [{
      container: name,
      path: "/etc/fdb-stats",
    }],
  });

  const svc = k8s.serviceFromDeployment(deploy, {
    scrape: true,
    ports: [{
      name,
      port: 8081,
      protocol: "TCP",
      targetPort: 8081,
    }],
  });

  return [deploy, svc, cm];
}
