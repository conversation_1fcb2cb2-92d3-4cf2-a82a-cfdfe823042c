listenAddress: ":8081"
fdbHost: "foundationdb"
fdbPort: 4500
drFdbHost: "foundationdb-readonly"
drFdbPort: 4500
scrapeInterval: 60
prefixes:
  - \x02acme-challenge\x00
  - \x02acme-flow\x00
  - \x02ActionEventLoggerSubspace\x00
  - \x02adulog\x00
  - \x02adu\x00
  - \x02AmplitudeLoggingStateSubspace\x00
  - \x02app-cdm-index\x00
  - \x02app-cdm-primary\x00
  - \x02apps\x00
  - \x02app-token-indexing\x00
  - \x02app-tokens\x00
  - \x02ccl\x00
  - \x02cdm-index\x00
  - \x02cdm-off\x00
  - \x02cdm-type-index\x00
  - \x02cedm-index\x00
  - \x02cedm-off\x00
  - \x02cedm\x00
  - \x02collab-groups\x00
  - \x02custom-domain\x00
  - \x02documentTypeData\x00
  - \x02emat\x00
  - \x02emct\x00
  - \x02emst\x00
  - \x02emtr\x00
  - \x02entity-app-indexing\x00
  - \x02entity-cdm-index\x00
  - \x02entity-cdm-primary\x00
  - \x02fssi\x00
  - \x02fundsub-admin-general\x00
  - \x02fundsub-global-offering\x00
  - \x02fundsub-lp-form\x00
  - \x02fundsub-tax-form-config\x00
  - \x02itracker-channel-map\x00
  - \x02itracker-collab-channels\x00
  - \x02itracker-issue-lists\x00
  - \x02itracker-issues\x00
  - \x02itracker-matter-map\x00
  - \x02itracker-modules\x00
  - \x02itracker-reversed-channel-map\x00
  - \x02itracker-reversed-trxn-map\x00
  - \x02itracker-trxn-map\x00
  - \x02notification-by-space-type-time-indexing\x00
  - \x02notification-by-type-time-indexing\x00
  - \x02notification-params\x00
  - \x02notification-space-indexing\x00
  - \x02notifications\x00
  - \x02notification-type-indexing\x00
  - \x02off-cdm\x00
  - \x02off-cedm\x00
  - \x02offering-access\x00
  - \x02offering-resources\x00
  - \x02off-pri-cdm\x00
  - \x02off-pri-cedm\x00
  - \x02role_permissions_indexing\x00
  - \x02role_permissions\x00
  - \x02roles_reversed\x00
  - \x02roles\x00
  - \x02rs-tag\x00
  - \x02S3CassandraPublishingStateSubspace\x00
  - \x02S3PublishingStateSubspace\x00
  - \x02sig-mod\x00
  - \x02sig-req-item\x00
  - \x02sig-req\x00
  - \x02src\x00
  - \x02surl\x00
  - \x02svl\x00
  - \x02system-cdm-index\x00
  - \x02tag-rs\x00
  - \x02tags\x00
  - \x02termData\x00
  - \x02tinyurl\x00
  - \x02token-meta-data\x00
  - \x02token-secret-forward-indexing\x00
  - \x02token-secret-revert-indexing\x00
  - \x02ucl\x00
  - \x02urem\x00
  - \x02user-itracker\x00
