#!/bin/env bash

set -e

FDB_CLUSTER_FILE="/tmp/fdb.cluster"
FDB_DESCRIPTION=${FDB_DESCRIPTION:-"fdb:fdb"}

function makeClusterFile {
    # Convert FDB_HOST to IP form if it is not
    if [[ ! $FDB_HOST =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        FDB_IPS=`dig +search +short $FDB_HOST 2>/dev/null | grep '^[0-9\.]*$'`
    fi
    JOINT_HOSTS=$(echo "$FDB_IPS" | tr ' ' '\n' | awk -v port="$FDB_PORT" '{print $1":"port}' | paste -sd,)
    echo "${FDB_DESCRIPTION}@${JOINT_HOSTS}" > $FDB_CLUSTER_FILE
    echo "Using cluster file $FDB_CLUSTER_FILE: `cat $FDB_CLUSTER_FILE`"
}

makeClusterFile
exec backup_agent \
  -C $FDB_CLUSTER_FILE \
  --knob_http_request_aws_v4_header=true \
  --tls_verify_peers Check.Valid=0
