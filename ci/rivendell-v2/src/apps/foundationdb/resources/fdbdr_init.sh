#!/bin/env bash

set -eu -o pipefail

: "${SOURCE_FDB_HOST:=}"
: "${SOURCE_FDB_DESCRIPTOR:=fdb:fdb}"
: "${SOURCE_FDB_PORT:=4500}"

: "${DEST_FDB_HOST:=}"
: "${DEST_FDB_DESCRIPTOR:=fdb:fdb}"
: "${DEST_FDB_PORT:=4500}"

: "${FDB_DR_TAG:=default}"

SOURCE_CLUSTER_FILE="/tmp/source.cluster"
DEST_CLUSTER_FILE="/tmp/dest.cluster"

function makeClusterFile {
    local FDB_HOST=$1
    local FDB_PORT=$2
    local FDB_DESCRIPTION=$3
    local FDB_CLUSTER_FILE=$4

    # Convert FDB_HOST to IP form if it is not
    if [[ ! "${FDB_HOST}" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        FDB_IPS=`dig +search +short $FDB_HOST 2>/dev/null | grep '^[0-9\.]*$'`
    fi
    JOINT_HOSTS=$(echo "$FDB_IPS" | tr ' ' '\n' | awk -v port="$FDB_PORT" '{print $1":"port}' | paste -sd,)
    echo "${FDB_DESCRIPTION}@${JOINT_HOSTS}" > $FDB_CLUSTER_FILE


    echo "Writing to $FDB_CLUSTER_FILE with host: ${FDB_HOST}"
    echo "Writing to $FDB_CLUSTER_FILE with port: ${FDB_PORT}"
    echo "Writing to $FDB_CLUSTER_FILE with descriptor: ${FDB_DESCRIPTION}"
}

function getDrStatus() {
  fdbdr status \
    -t ${FDB_DR_TAG} \
    -s "${SOURCE_CLUSTER_FILE}" \
    -d "${DEST_CLUSTER_FILE}"
}

makeClusterFile "${SOURCE_FDB_HOST}" "${SOURCE_FDB_PORT}" "${SOURCE_FDB_DESCRIPTOR}" "${SOURCE_CLUSTER_FILE}"
makeClusterFile "${DEST_FDB_HOST}" "${DEST_FDB_PORT}" "${DEST_FDB_DESCRIPTOR}" "${DEST_CLUSTER_FILE}"

if getDrStatus | grep "No previous backups found"; then
  fdbdr start \
    -t ${FDB_DR_TAG} \
    -s "${SOURCE_CLUSTER_FILE}" \
    -d "${DEST_CLUSTER_FILE}"
fi
