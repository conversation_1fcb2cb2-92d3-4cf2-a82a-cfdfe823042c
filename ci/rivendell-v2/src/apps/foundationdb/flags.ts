import { Context } from "@anduintransaction/rivendell";

export enum FdbReadonlyCondition {
  None = 0,
  EnabledAgent = 1 << 1,
  EnabledServer = 1 << 2,
  EnabledAgentAndServer = EnabledAgent | EnabledServer,
}

export function isEnableFdbReadonly(ctx: Context): FdbReadonlyCondition {
  switch (ctx.env) {
    case "gondor-public":
    case "gondor-eu-public":
      return FdbReadonlyCondition.EnabledAgent;

    // case "local":
    case "blackwood":
    case "integ-test":
    case "gondor-canary":
    case "gondor-minas-tirith":
      return FdbReadonlyCondition.EnabledAgentAndServer;

    default:
      return FdbReadonlyCondition.None;
  }
}
