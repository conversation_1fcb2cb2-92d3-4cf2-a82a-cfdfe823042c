import { Context, K8sObject } from "@anduintransaction/rivendell";
import * as path from "path";
import * as k8s from "@utils/k8s";
import { Deployment } from "kubernetes-models/apps/v1";
import * as com from "../common";
import * as fdbCommon from "./common";
import { FdbReadonlyCondition, isEnableFdbReadonly } from "./flags";

const replicaMap: Record<string, number> = {
  "gondor-public": 3,
};

export default async function (ctx: Context): Promise<K8sObject[]> {
  const name = "fdb-backup-agent";
  const commonLabels = { app: name };
  const startScript = "fdbbackup_start.sh";

  const cm = await k8s.createConfigMapFromPath(
    "fdb-backup-agent-script",
    path.resolve(__dirname, `./resources/${startScript}`),
  );

  const deploy = new Deployment({
    metadata: { name },
    spec: {
      replicas: replicaMap[ctx.env] || 1,
      selector: {
        matchLabels: commonLabels,
      },
      template: {
        metadata: {
          labels: commonLabels,
          annotations: {
            "config-hash": com.checksum.hash(cm.data![startScript]),
          },
        },
        spec: {
          nodeSelector: com.node.nodeDatabase(ctx),
          imagePullSecrets: com.misc.getPullSecrets(ctx),
          initContainers: [...fdbCommon.initWaitFdbContainers(ctx)],
          containers: [
            {
              name: "backup-agent",
              image: fdbCommon.getFdbImage(ctx),
              command: ["sh"],
              args: [`/opt/anduin/scripts/${startScript}`],
              env: [
                isEnableFdbReadonly(ctx) !== FdbReadonlyCondition.None
                  ? com.env.envFromGondorConfig(
                    "FDB_HOST",
                    "STARGAZER_SERVICES_FOUNDATIONDB_READ_ONLY_HOST",
                  )
                  : com.env.envFromGondorConfig(
                    "FDB_HOST",
                    "STARGAZER_SERVICES_FOUNDATIONDB_HOST",
                  ),
                isEnableFdbReadonly(ctx) !== FdbReadonlyCondition.None
                  ? com.env.envFromGondorConfig(
                    "FDB_DESCRIPTION",
                    "STARGAZER_SERVICES_FOUNDATIONDB_READ_ONLY_DESCRIPTION",
                  )
                  : com.env.envFromGondorConfig(
                    "FDB_DESCRIPTION",
                    "STARGAZER_SERVICES_FOUNDATIONDB_DESCRIPTION",
                  ),
                com.env.envFromGondorConfig(
                  "FDB_PORT",
                  "STARGAZER_SERVICES_FOUNDATIONDB_PORT",
                ),
              ],
            },
          ],
        },
      },
    },
  });

  com.storage.cmAsVolume(ctx, deploy.spec!.template.spec!, {
    cmName: cm.metadata!.name!,
    volumeName: "scripts",
    mounts: [
      {
        container: "backup-agent",
        path: "/opt/anduin/scripts",
        isReadOnly: true,
      },
    ],
  });

  if (ctx.env === "gondor-public") {
    deploy.spec!.template.spec!.affinity = {
      nodeAffinity: {
        preferredDuringSchedulingIgnoredDuringExecution: [
          {
            weight: 100,
            preference: {
              matchExpressions: [
                {
                  key: "topology.kubernetes.io/zone",
                  operator: "In",
                  values: ["us-east-1b"],
                },
              ],
            },
          },
        ],
      },
    };
  }

  return [deploy, cm];
}
