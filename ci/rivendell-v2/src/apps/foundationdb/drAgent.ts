import {
  Context,
  K8sObject,
  SourceGenerator,
} from "@anduintransaction/rivendell";
import * as path from "path";
import * as k8s from "@utils/k8s";
import { IEnvVar } from "kubernetes-models/v1";
import { Deployment } from "kubernetes-models/apps/v1";
import { Job } from "kubernetes-models/batch/v1";
import * as com from "../common";
import * as fdbC<PERSON>mon from "./common";

export interface DrAgentSpec {
  name: string;
  source: fdbCommon.FdbClusterSpec;
  dest: fdbCommon.FdbClusterSpec;
  replicas?: number;
}

export default function (spec: DrAgentSpec): SourceGenerator {
  return async function (ctx: Context): Promise<K8sObject[]> {
    const name = `${spec.name}-agent`;
    const commonLabels = { app: name };
    const startScript = "fdbdr_start.sh";

    const cm = await k8s.createConfigMapFromPath(
      `${name}-script`,
      path.resolve(__dirname, `./resources/${startScript}`),
    );

    const deploy = new Deployment({
      metadata: { name },
      spec: {
        replicas: spec.replicas || 1,
        selector: {
          matchLabels: commonLabels,
        },
        template: {
          metadata: {
            labels: commonLabels,
            annotations: {
              "script-hash": com.checksum.hash(cm.data![startScript]),
            },
          },
          spec: {
            nodeSelector: com.node.nodeDatabase(ctx),
            imagePullSecrets: com.misc.getPullSecrets(ctx),
            initContainers: [
              ...fdbCommon.initWaitFdbContainers(ctx),
            ],
            containers: [{
              name: "dr-agent",
              image: fdbCommon.getFdbImage(ctx),
              command: ["sh"],
              args: [`/opt/anduin/scripts/${startScript}`],
              env: [
                ...convertToEnv("SOURCE", spec.source),
                ...convertToEnv("DEST", spec.dest),
                {
                  name: "POD_NAMESPACE",
                  valueFrom: {
                    fieldRef: {
                      fieldPath: "metadata.namespace",
                    },
                  },
                },
              ],
            }],
          },
        },
      },
    });

    com.storage.cmAsVolume(ctx, deploy.spec!.template.spec!, {
      cmName: cm.metadata!.name!,
      volumeName: "scripts",
      mounts: [{
        container: "dr-agent",
        path: "/opt/anduin/scripts",
        isReadOnly: true,
      }],
    });

    return [deploy, cm];
  };
}

export function initDrJob(spec: DrAgentSpec): SourceGenerator {
  return async function (ctx: Context): Promise<K8sObject[]> {
    const name = `${spec.name}-init`;
    const startScript = "fdbdr_init.sh";

    const cm = await k8s.createConfigMapFromPath(
      `${name}-init-script`,
      path.resolve(__dirname, `./resources/${startScript}`),
    );

    const job = new Job({
      metadata: { name },
      spec: {
        backoffLimit: 0,
        template: {
          metadata: { name },
          spec: {
            restartPolicy: "Never",
            nodeSelector: com.node.nodeDatabase(ctx),
            imagePullSecrets: com.misc.getPullSecrets(ctx),
            initContainers: [
              ...fdbCommon.initWaitFdbContainers(ctx),
            ],
            containers: [{
              name: "dr-init",
              image: fdbCommon.getFdbImage(ctx),
              command: ["sh"],
              args: [`/opt/anduin/scripts/${startScript}`],
              env: [
                ...convertToEnv("SOURCE", spec.source),
                ...convertToEnv("DEST", spec.dest),
                k8s.envFromValue("FDB_DR_TAG", spec.name),
                {
                  name: "POD_NAMESPACE",
                  valueFrom: {
                    fieldRef: {
                      fieldPath: "metadata.namespace",
                    },
                  },
                },
              ],
            }],
          },
        },
      },
    });

    com.storage.cmAsVolume(ctx, job.spec!.template.spec!, {
      cmName: cm.metadata!.name!,
      volumeName: "scripts",
      mounts: [{
        container: "dr-init",
        path: "/opt/anduin/scripts",
        isReadOnly: true,
      }],
    });

    return [job, cm];
  };
}

function convertToEnv(
  envPrefix: string,
  spec: fdbCommon.FdbClusterSpec,
): IEnvVar[] {
  const envName = (name: string) => `${envPrefix}_${name}`;
  const envs: IEnvVar[] = [
    k8s.envFromSource(envName("FDB_HOST"), spec.host),
    k8s.envFromSource(envName("FDB_PORT"), spec.port),
    k8s.envFromSource(envName("FDB_DESCRIPTOR"), spec.descriptor),
  ];
  return envs;
}
