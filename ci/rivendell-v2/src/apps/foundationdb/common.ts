import { Context, Finder } from "@anduintransaction/rivendell";
import { ConfigUtils } from "@configutils";
import * as k8s from "@utils/k8s";
import { IContainer } from "kubernetes-models/v1";
import * as com from "../common";

import { FdbReadonlyCondition, isEnableFdbReadonly } from "./flags";
export * from "./flags";

export interface FdbClusterSpec {
  descriptor: k8s.IEnvVarValue;
  host: k8s.IEnvVarValue;
  port: k8s.IEnvVarValue;
}

function getFdbLegacyKey(key: string): string {
  return `${key}Legacy`;
}

export function isLegacyFdb(ctx: Context): boolean {
  return Finder.requiredBool(ctx.configs, ["useLegacyFoundationDb"], false);
}

export function getFdbImage(ctx: Context): string {
  const key = "foundationDb";
  return isLegacyFdb(ctx)
    ? ConfigUtils.getDockerEcrImage(ctx.configs, getFdbLegacyKey(key))
    : ConfigUtils.getDockerEcrImage(ctx.configs, key);
}

export function getFdbWaitImage(ctx: Context): string {
  const key = "foundationDbWait";
  return isLegacyFdb(ctx)
    ? ConfigUtils.getDockerEcrImage(ctx.configs, getFdbLegacyKey(key))
    : ConfigUtils.getDockerEcrImage(ctx.configs, key);
}

export function getFdbStatImage(ctx: Context): string {
  const key = "foundationDbStats";
  return isLegacyFdb(ctx)
    ? ConfigUtils.getDockerGithubImage(ctx.configs, getFdbLegacyKey(key))
    : ConfigUtils.getDockerGithubImage(ctx.configs, key);
}

export function initWaitFdbContainers(ctx: Context): IContainer[] {
  const containers = [{
    name: "wait-foundationdb",
    image: getFdbWaitImage(ctx),
    env: [
      com.env.envFromGondorConfig(
        "FDB_HOST",
        "STARGAZER_SERVICES_FOUNDATIONDB_HOST",
      ),
      com.env.envFromGondorConfig(
        "FDB_PORT",
        "STARGAZER_SERVICES_FOUNDATIONDB_PORT",
      ),
      com.env.envFromGondorConfig(
        "FDB_DESCRIPTION",
        "STARGAZER_SERVICES_FOUNDATIONDB_DESCRIPTION",
      ),
    ],
  }];

  if (isEnableFdbReadonly(ctx) !== FdbReadonlyCondition.None) {
    containers.push({
      name: "wait-foundationdb-readonly",
      image: getFdbWaitImage(ctx),
      env: [
        com.env.envFromGondorConfig(
          "FDB_HOST",
          "STARGAZER_SERVICES_FOUNDATIONDB_READ_ONLY_HOST",
        ),
        com.env.envFromGondorConfig(
          "FDB_PORT",
          "STARGAZER_SERVICES_FOUNDATIONDB_READ_ONLY_PORT",
        ),
        com.env.envFromGondorConfig(
          "FDB_DESCRIPTION",
          "STARGAZER_SERVICES_FOUNDATIONDB_READ_ONLY_DESCRIPTION",
        ),
      ],
    });
  }

  return containers;
}
