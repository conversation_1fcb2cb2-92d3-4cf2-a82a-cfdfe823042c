import {
  Context,
  InfisicalOpt,
  InfisicalSecretProvider,
  Noop<PERSON><PERSON>ret<PERSON><PERSON>ider,
  SecretProvider,
  StaticSecretProvider,
} from "@anduintransaction/rivendell";
import { ConfigUtils, loadConfigFromCli } from "./configutils";
import { loadLocalSecret } from "@app/appconfig/environments/local/secrets";

const StargazerProjectID = "655c57eb5a031f5c8aa11a95";
const StargazerSharedProjectID = "670deb1b440e59da6c9c59cf";

async function getSecretProvider(
  env: string,
): Promise<SecretProvider> {
  const projectId: string = (env.includes("common") || env.includes("shared"))
    ? StargazerSharedProjectID
    : StargazerProjectID;

  const opts: InfisicalOpt = {
    projectId,
    siteUrl: "https://infisical.anduin.center",
    clientId: process.env["ANDUIN_INFISICAL_CLIENT_ID"] || "",
    clientSecret: process.env["ANDUIN_INFISICAL_CLIENT_SECRET"] || "",
    debug: false,
  };

  switch (env) {
    case "local":
    case "blackwood":
    case "integ-test":
      const secret = await loadLocalSecret();
      return new StaticSecretProvider(secret);

    case "fundsub-simulator":
    case "fundsub-simulator-2":
      return new InfisicalSecretProvider({
        ...opts,
        forceEnv: "fundsub-simulator",
      });

    default:
      return new InfisicalSecretProvider(opts);
  }
}

export async function loadContextFromCli(
  args: string[],
  loadSecret: boolean = true,
) {
  const cfg = await loadConfigFromCli(args);
  const env = ConfigUtils.getEnvName(cfg);
  const sp = loadSecret
    ? await getSecretProvider(env)
    : new NoopSecretProvider();
  await sp.doAuth();
  return new Context(env, cfg, sp);
}
