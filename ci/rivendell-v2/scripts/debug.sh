#!/bin/env bash

set -e

here=`cd $(dirname $BASH_SOURCE); pwd`

function infoLog {
  echo $@ 1>&2
}

function getCurrentNamespace() {
  kubectl config view --minify -o json | \
    jq -r ".contexts[] | select(.name == \"$(kubectl config current-context)\") | .context.namespace"
}

function getGondorImage() {
  kubectl get deployments.apps gondor -o json | jq -r '.spec.template.spec.containers[0].image'
}

CURRENT_NAMESPACE=${1:-`getCurrentNamespace`}
infoLog "Using env: ${CURRENT_NAMESPACE}" 

VERSION_FLAGS=""
if [ "$DEBUG_IMAGE" = "true" ]; then
  GONDOR_IMAGE=`getGondorImage`
  IMAGE_NAME=$(echo $GONDOR_IMAGE | cut -d':' -f1 | cut -d'/' -f2 )
  TAG=$(echo $GONDOR_IMAGE | cut -d':' -f2)

  infoLog "Using gondor name: $IMAGE_NAME" 
  infoLog "Using gondor tag: $TAG" 
  VERSION_FLAGS="--var=stargazerSprintTag=$TAG --var=stargazerImage=$IMAGE_NAME"
fi

# $here/../node_modules/.bin/ts-node $here/../src/actions/adhoc/maintainance.ts --env=${CURRENT_NAMESPACE} $VERSION_FLAGS
$here/../node_modules/.bin/ts-node $here/../src/actions/debug.ts --env=${CURRENT_NAMESPACE} $VERSION_FLAGS
