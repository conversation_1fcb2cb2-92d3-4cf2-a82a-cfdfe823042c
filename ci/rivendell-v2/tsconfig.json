{"ts-node": {"require": ["tsconfig-paths/register"]}, "compilerOptions": {"baseUrl": "./src", "outDir": "./dist", "module": "commonjs", "target": "es2022", "moduleResolution": "node", "moduleDetection": "force", "noUnusedLocals": true, "noEmit": false, "declaration": false, "strict": true, "downlevelIteration": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "allowJs": true, "types": ["@types/node"], "paths": {"@contextes": ["contextes"], "@configutils": ["configutils"], "@app/*": ["apps/*"], "@modules/*": ["modules/*"], "@utils/*": ["utils/*"]}}, "include": ["src"], "exclude": ["node_modules", "dist"]}