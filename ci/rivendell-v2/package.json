{"name": "rivendell-v2", "scripts": {"compile-check": "tsc --noEmit"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/ejs": "^3.1.5", "@types/minimist": "^1.2.5", "@types/node": "^20.9.4", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.0.0"}, "dependencies": {"@anduintransaction/rivendell": "^0.6.0", "@kubernetes-models/tidb-operator": "^0.2.2", "bcrypt": "^5.1.1", "deepmerge": "^4.3.1", "ejs": "^3.1.10", "kubernetes-models": "^4.4.2", "minimist": "^1.2.8", "yaml": "^2.3.4"}}