# Fund Subscription GraphQL Implementation

## Overview

The fund subscription GraphQL implementation spans multiple modules and provides both query and mutation capabilities for fund subscription data. The implementation follows a layered architecture with different data sources and storage systems.

## Architecture

### Schema Locations

1. **Main GraphQL Schema (Rohan)**
   - Location: `modules/rohan/rohan/src/anduin/rohan/graphql/RohanSchema.scala`
   - Purpose: Primary GraphQL API for fund subscription queries
   - Contains: Query definitions, resolvers, type mappings

2. **Fund Subscription Specific Schema**
   - Location: `modules/rohan/rohan/src/anduin/rohan/graphql/fundsub.scala`
   - Purpose: Fund subscription specific GraphQL operations
   - Contains: Fund subscription queries and helper functions

3. **GraphQL Type Definitions**
   - Location: `modules/rohan/rohan/src/anduin/graphql/models/fundsub/`
   - Files: `FundSubSchema.scala`, `FundSubLpSchema.scala`, `FundSubSignatureSchema.scala`
   - Purpose: Type definitions and schema mappings

4. **Data Lake Schema (Evendim)**
   - Location: `modules/evendim/resources/anduin/evendim/datalakeSchema.graphql`
   - Purpose: Data lake operations and mutations
   - Contains: `addFundSubscription`, `updateFundSubscription`, `deleteFundSubscription`

## Data Storage Integration

### FoundationDB Integration
- **Primary Storage**: Main fund subscription data, LP models, form data
- **Operations**: Handled by `FundSubModelStoreOperations`, `FundSubLpModelStoreOperations`
- **Removal**: Implemented in `DataRemovalService.removeTransaction()` and `removeFormData()`

### TiDB/Greylin Integration
- **Secondary Storage**: Additional schemas for GraphQL-specific data
- **Tables**:
  - `pb_subscription_data_schema`: Subscription form data in JSON format
  - `pb_fund_data_schema`: Fund metadata in JSON format
  - `pb_fund_subscription_data_template`: Links to data templates
  - `pb_fund_subscription`: Fund data subscription information

### DMS Integration
- **File Storage**: Document and file management
- **Operations**: Handled by `FileService` and `DsmRemovalService`
- **Removal**: Implemented in `DsmRemovalService.handleRemovalReq()`

## Key GraphQL Operations

### Queries

1. **fundSubPublicModel**
   - Arguments: `FundSubId`
   - Returns: `FundSubPublicModel`
   - Resolver: `fundsub.getFundSubPublicModel()`

2. **fundSubAdminRestrictedModel**
   - Arguments: `FundSubId`
   - Returns: `FundSubAdminRestrictedModel`
   - Resolver: `FundSubAdminResolver.fetchFundSubAdminRestrictedModel()`

3. **fundSubLpModel**
   - Arguments: `FundSubLpId`
   - Returns: `FundSubLpModel`
   - Resolver: `FundSubLpResolver.fetchFundSubLpModel()`

4. **fundSubLpRestrictedModel**
   - Arguments: `FundSubLpId`
   - Returns: `FundSubLpRestrictedModel`
   - Resolver: `FundSubLpResolver.fetchFundSubLpRestrictedModel()`

### Data Lake Mutations (Evendim)

1. **addFundSubscription**
   - Input: `AddFundSubscriptionInput[]`
   - Purpose: Create new fund subscriptions
   - Supports: Upsert operations

2. **updateFundSubscription**
   - Input: `UpdateFundSubscriptionInput`
   - Purpose: Update existing fund subscriptions

3. **deleteFundSubscription**
   - Input: `FundSubscriptionFilter`
   - Purpose: Delete fund subscriptions from data lake

## Resolver Patterns

### Authentication and Authorization
- Uses `GraphqlServerContext` for user context
- Permission validation through `fundSubPermissionService`
- Role-based access control with `RebacStoreOperation`

### Data Fetching Patterns
```scala
// Typical resolver pattern
Field(
  name = "fundSubPublicModel",
  fieldType = fundsub.FundSubPublicModelType,
  arguments = FundSubSchema.FundSubIdArgument :: Nil,
  resolve = ctx => {
    val fundSubId = ctx.arg[FundSubId](FundSubSchema.FundSubIdArgument)
    fundsub
      .getFundSubPublicModel(ctx.ctx, fundSubId)
      .runToFutureWithTracing(ctx.ctx, Map("name" -> "fundSubPublicModel"))
  }
)
```

### Error Handling
- Uses ZIO Task for async operations
- Tracing integration with `runToFutureWithTracing`
- Permission validation before data access

## TiDB Schema Operations

### SubscriptionDataSchemaOperations
- **Table**: `pb_subscription_data_schema`
- **Key**: `FundSubLpId`
- **Operations**: `getOpt`, `insert`, `update`, `delete`, `upsert`
- **Special**: `getByFundSubId` for fund-level queries

### FundDataSchemaOperations
- **Table**: `pb_fund_data_schema`
- **Key**: `FundSubId`
- **Operations**: `getOpt`, `insert`, `update`, `delete`, `upsert`
- **Special**: `updateFundData` for schema updates

### FundSubscriptionDataTemplateOperations
- **Table**: `pb_fund_subscription_data_template`
- **Key**: `(FundSubId, DataTemplateVersionId)`
- **Operations**: `insert`, `update`, `delete`, `batchDelete`
- **Special**: `deleteByFundSubId` for fund-level deletion

### FundDataFundSubscriptionOperations
- **Table**: `pb_fund_subscription`
- **Key**: `FundDataFundSubscriptionId`
- **Operations**: `insert`, `update`, `delete`
- **Special**: `get(fundSubId)` and batch delete operations

## Data Removal Requirements

### Current Implementation Gaps
The current `DataRemovalService` handles:
- ✅ FoundationDB records (via `removeTransaction`, `removeFormData`)
- ✅ DMS files (via `DsmRemovalService`)
- ❌ **Missing**: TiDB/Greylin GraphQL data removal

### Required GraphQL Data Removal
When removing a fund subscription, the following TiDB data must be deleted:

1. **Subscription Data Schemas**: All records with `FundSubLpId` matching the fund
2. **Fund Data Schema**: Record with matching `FundSubId`
3. **Fund Subscription Data Templates**: All templates linked to the fund
4. **Fund Data Subscriptions**: All fund data subscription records

### Integration Pattern
The GraphQL data removal has been integrated into `DataRemovalService.removeFundSub()`:

```scala
private def removeGraphQLData(fundSubId: FundSubId): Task[Int] = {
  for {
    _ <- ZIO.logInfo(s"Removing GraphQL data for fund subscription $fundSubId")

    // Remove subscription data schemas (by fund sub ID pattern)
    subscriptionDataCount <- greylinDataService.run {
      SubscriptionDataSchemaOperations.Default.getByFundSubId(fundSubId).flatMap { schemas =>
        ZIOUtils.foreachParN(10)(schemas) { schema =>
          SubscriptionDataSchemaOperations.Default.delete(schema.id)
        }.as(schemas.size)
      }
    }

    // Remove fund data schema
    fundDataCount <- greylinDataService.run {
      FundDataSchemaOperations.Default.delete(fundSubId).as(1)
    }.catchAll(_ => ZIO.succeed(0)) // Ignore if not found

    // Remove fund subscription data templates
    templateCount <- greylinDataService.run {
      FundSubscriptionDataTemplateOperations.Default.deleteByFundSubId(fundSubId).as(1)
    }.catchAll(_ => ZIO.succeed(0)) // Ignore if not found

    // Remove fund data subscriptions
    fundDataSubCount <- greylinDataService.run {
      FundDataFundSubscriptionOperations.Default.get(fundSubId).flatMap { subscriptions =>
        val subscriptionIds = subscriptions.map(_.id)
        ZIOUtils.when(subscriptionIds.nonEmpty) {
          FundDataFundSubscriptionOperations.Default.delete(subscriptionIds)
        }.as(subscriptions.size)
      }
    }

    totalCount = subscriptionDataCount + fundDataCount + templateCount + fundDataSubCount
    _ <- ZIO.logInfo(s"Removed $totalCount GraphQL records for fund subscription $fundSubId")
  } yield totalCount
}

private def removeFundSub(fundSubId: FundSubId): Task[Int] = {
  val transactionId = fundSubId.parent
  for {
    graphqlDataCount <- removeGraphQLData(fundSubId)  // IMPLEMENTED
    formDataCount <- removeFormData(fundSubId)
    trxnModelCount <- removeTransaction(transactionId)
  } yield trxnModelCount + formDataCount + graphqlDataCount
}
```

## Security Considerations

### Authorization Rules
- Data lake schema enforces admin-only access for mutations
- Query access controlled by fund membership
- Permission validation in resolvers

### Data Protection
- Archived fund verification before deletion
- Audit logging for data removal operations
- Transaction-based operations for consistency

## Performance Considerations

### Batch Operations
- Use `parTraverseN` for parallel processing
- Batch delete operations for large datasets
- Transaction size limits for FoundationDB

### Caching and Optimization
- GraphQL field-level caching
- Efficient data fetching patterns
- Minimal database round trips

## Implementation Status

### ✅ Completed
- **GraphQL Data Removal**: Implemented `removeGraphQLData()` method in `DataRemovalService`
- **TiDB Operations Integration**: Added support for all four GraphQL-related TiDB tables
- **Service Dependencies**: Updated `DataRequestActivitiesImpl` to include `GreylinDataService`
- **Error Handling**: Graceful handling of missing records with logging
- **Parallel Processing**: Efficient batch deletion with `foreachParN(10)`

### 🔧 Implementation Details
- **Location**: `itools/olympian/jvm/src/anduin/dataprotection/service/DataRemovalService.scala`
- **Dependencies**: Added `GreylinDataService` to constructor and workflow activities
- **Operations Used**: `.Default` context for all TiDB model operations
- **Logging**: Comprehensive logging for tracking removal progress
- **Integration**: Seamlessly integrated into existing `removeFundSub()` flow

### 📋 Data Removal Coverage
1. **SubscriptionDataSchema**: ✅ Removed by fund subscription pattern matching
2. **FundDataSchema**: ✅ Removed by exact fund subscription ID
3. **FundSubscriptionDataTemplate**: ✅ Removed by fund subscription ID
4. **FundDataFundSubscription**: ✅ Batch removal of linked subscriptions

## Testing Patterns

### Integration Tests
- Located in `modules/fundsub/fundsub/jvm/it/`
- Test GraphQL authorization and data access
- Verify data removal completeness
- **Recommended**: Add specific tests for GraphQL data removal

### Test Data Setup
- Use existing entities for testing
- Random selection to avoid complex setup
- Non-destructive testing patterns
- **Suggested**: Test with archived fund subscriptions for removal validation
