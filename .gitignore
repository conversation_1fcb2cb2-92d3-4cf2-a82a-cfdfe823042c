# gradle
*~
target
target-intellij
/*.ipr
/*.iws
*.classpath
*.project
*.settings

*.sublime-*
.nb-gradle
.vscode/


# Eclipse
.cache
.classpath
.project
.scala_dependencies
.settings
.target/
.worksheet/

# IntelliJ
# User-specific stuff:
.idea/
*.iml

/local.sbt

# mill
/mill.*

# ENSIME
.ensime
.ensime_cache/
.ensime_lucene/

# Mac
.DS_Store

# node_modules
/.history
/tools/
/node_modules/

# go.cd
cruise-output/

# sass
.sass-cache/

/local/
/mima/jars/

# all logs
logs/
/.bundle/
/webTarget/

# vs code
ensime-langserver.log
pc.stdout.log

# MS Word swap file
~$*.docx

# Bloop dir
.bloop

# Serverless
.serverless
serverless/*/local

# metals
/.metals
metals.sbt

/.bsp
/out

.yarn/*
!.yarn/patches
!.yarn/releases
!.yarn/plugins
!.yarn/sdks
!.yarn/versions
.pnp.*

/ci/apps/app-configs/**/secrets/*.env
!/ci/apps/app-configs/**/local-dev/secrets/*.env
!/ci/apps/app-configs/**/test-build/secrets/*.env
/ci/apps/app-configs/**/rsa-keymap.json
!/ci/apps/app-configs/local-dev/rsa-keymap.json
!/ci/apps/app-configs/test-build/rsa-keymap.json
/ci/apps/app-configs/**/globalsign.jks
!/ci/apps/app-configs/local-dev/globalsign.jks
!/ci/apps/app-configs/test-build/globalsign.jks
/ci/apps/app-configs/**/docusign.key
/ci/apps/app-configs/**/docusign.prod.key
!/ci/apps/app-configs/local-dev/docusign.key
!/ci/apps/app-configs/test-build/docusign.key
/ci/apps/app-configs/**/nats-jwt.conf
!/ci/apps/app-configs/local-dev/nats-jwt.conf
!/ci/apps/app-configs/test-build/nats-jwt.conf

.env
.local.env
*.semanticdb

# Cursor IDE
# The .cursorignore file is used to limit file indexing (max 10,000 files)
# This file should be personalized per developer and not committed to the repo
.cursorignore
.cursor

# Cursor rules
.cursorrules