# Stargazer Mill Build Guide

## Mill Configuration & Setup

### Mill Version & Wrapper Script
- **Always use `./mill`** instead of `mill` - this uses the wrapper script that ensures correct version
- **Build file**: `build.mill`
- **JVM Options**: Configured in `.mill-jvm-opts`

### Key Configuration Files
- `.mill-version` - Mill version specification
- `.mill-jvm-opts` - JVM memory and GC settings for Mill
- `build.mill` - Main build configuration
- `mill-build/build.mill` - Mill build module configuration

## Module Structure & Organization

### Top-Level Module Organization
```
build/          - Build configuration and utilities
platform/       - Core platform modules (stargazer, stargazerCore, stargazerModel, etc.)
modules/        - Business logic modules (bifrost, dataextract, fundsub, gaia, etc.)
gondor/         - Gondor application modules (gondor, gondorCore, gondorModel, etc.)
itools/         - Internal tools (olympian, pantheon)
apps/           - Application modules (maya, gondor)
js/             - JavaScript/frontend modules
```

### Cross-Platform Module Pattern
Most modules follow this structure:
```
moduleName/
├── jvm/           - JVM/backend implementation
│   ├── src/       - Source code
│   ├── test/      - Unit tests
│   └── it/        - Integration tests
├── js/            - JavaScript/frontend implementation
│   ├── src/       - Source code
│   └── test/      - Unit tests
└── shared/        - Shared code between platforms
    ├── src/
    └── test/
```

### Module Path Mapping
Directory structure maps to Mill module paths using dot notation:
- `modules/fundsub/` → `modules.fundsub`
- `gondor/gondorCore/` → `gondor.gondorCore`
- `itools/olympian/` → `itools.olympian`
- `platform/stargazer/` → `platform.stargazer`

## Core Mill Commands

### Compilation Commands
```bash
# Compile all modules
./mill __.compile

# Compile specific module
./mill modules.fundsub.fundsub.jvm.compile
./mill gondor.gondorCore.jvm.compile
./mill itools.olympian.jvm.compile

# Check code style (scalafmt + scalafix)
./mill __.checkStyle
./mill __.checkStyleCached  # Use cached results when possible

# Reformat code
./mill __.reformat

# Clean all build artifacts
./mill clean
```

### Test Commands

#### Unit Tests
```bash
# Run all unit tests
./mill __.test.test

# Run unit tests for specific module
./mill modules.fundsub.fundsub.jvm.test.test
./mill gondor.gondorCore.jvm.test.test

# Run specific unit test class
./mill modules.fundsub.fundsub.jvm.test.testOnly "com.anduin.fundsub.FundsubServiceTest"
./mill gondor.gondor.jvm.test.testOnly "*FileServiceTest"
```

#### Integration Tests
```bash
# Run all integration tests (cached)
./mill __.it.testCached

# Run integration tests for specific module
./mill modules.fundsub.fundsub.jvm.it.testCached
./mill itools.olympian.jvm.it.testCached

# Run specific integration test class
./mill __.fundsub.fundsub.jvm.it.testOnly "*FundsubServiceInteg"
./mill __.gondorCore.jvm.it.testOnly "*FileServiceInteg"
./mill __.olympian.jvm.it.testOnly "*ExportJobServiceInteg"
```

#### Multi-Region Tests
```bash
# Run multi-region integration tests
./mill __.multiregionit.testCached
./mill gondor.gondor.jvm.multiregionit.testCached
```

### Utility Commands
```bash
# Show all available commands (warning: very long output)
./mill resolve

# Show module dependencies
./mill visualizeDeps

# Install JavaScript dependencies
./mill workspaceJsPackageInstall
```

## Client Building & Development

### Development Client Building
```bash
# Build all clients in development mode
./mill devBuildAllClients

# Build all clients except Fundsub
./mill devBuildAllClientsExceptFundsub

# Build web resources first (required before client builds)
./mill devBuildWebResources

# Build specific clients
./mill devBuildFundsubClient
./mill devBuildDataExtractClient
./mill devBuildSignatureClient
./mill devBuildInvestorProfileClient
./mill devBuildDataroomClient
./mill devBuildFundDataClient
./mill devBuildPantheonClient
./mill devBuildItoolsClient
./mill devBuildNaryaClient
./mill devBuildMayaClient
./mill devBuildGondorClient
./mill devBuildRiaClient
./mill devBuildHeimdallClient
./mill devBuildIntegPlatformClient
```

### AI Agent Build
Use the following to bypass linter checks during agent execution:
```bash
ANDUIN_BUILD_ENV=agent ./mill ...
```
This allows the AI agent to prioritize core logic implementation without incurring overhead from linter-related tool calls.

### Production Client Building
```bash
# Build all clients in production mode
./mill prodBuildAllClients

# Build specific clients for production
./mill prodBuildFundsubClient
./mill prodBuildDataExtractClient
# ... (same pattern as dev builds)
```

## Server & Application Management

### Development Server
```bash
# Start development server
./mill startDevServer

# Start server in background
./mill reStart

# Stop background server
./mill reStop
```

### Database & Migration Commands
```bash
# Run database migrations
./mill runMigration

# Run hotfix migrations
./mill runHotfixMigration

# Initialize test data for integration tests
./mill initTestData
```

### Application Utilities
```bash
# Generate endpoint report
./mill runEndpointReport

# Package application for deployment
./mill apps.gondor.gondorAppServer.universalPackageZip
```

## Performance & Optimization

### Parallel Execution
```bash
# Use -j flag for parallel execution
./mill -j 4 __.compile          # Compile with 4 parallel jobs
./mill -j 2 __.test.test         # Run tests with 2 parallel jobs
./mill -j 4 __.checkStyleCached  # Style check with 4 parallel jobs
```

### Memory Configuration
The codebase uses different JVM configurations for different scenarios:
- **Development**: 16GB heap (`.mill-jvm-opts`)
- **CI Build**: 20-22GB heap (`ci/scripts/build/jvmopts/`)
- **Tests**: 16GB heap with specific GC tuning

### Selective Execution
```bash
# Use cached results when possible
./mill __.testCached
./mill __.checkStyleCached

# Selective test execution (used in CI)
./mill selective.prepare __.testCached
./mill selective.run __.testCached
```

## Common Development Workflows

### Full Development Setup
```bash
# 1. Install JS dependencies
./mill workspaceJsPackageInstall

# 2. Build web resources
./mill devBuildWebResources

# 3. Build all clients
./mill devBuildAllClients

# 4. Initialize test data
./mill initTestData

# 5. Start development server
./mill startDevServer
```

### Testing Workflow
```bash
# 1. Run style checks
./mill __.checkStyle

# 2. Run unit tests
./mill __.test.test

# 3. Run integration tests
./mill __.it.testCached

# 4. Run specific test if needed
./mill __.fundsub.fundsub.jvm.it.testOnly "*SpecificTestInteg"
```

### Build Verification
```bash
# 1. Clean build
./mill clean

# 2. Compile everything
./mill __.compile

# 3. Run style checks
./mill __.checkStyleCached

# 4. Build clients
./mill devBuildAllClients
```

## Module-Specific Patterns

### Business Logic Modules (modules/*)
- **Pattern**: `modules.{moduleName}.{subModule}.{platform}`
- **Examples**: 
  - `modules.fundsub.fundsub.jvm`
  - `modules.dataextract.dataextractCore.js`
  - `modules.gaia.gaia.jvm.it`

### Platform Modules (platform/*)
- **Pattern**: `platform.{moduleName}.{platform}`
- **Examples**:
  - `platform.stargazer.jvm`
  - `platform.stargazerCore.js`
  - `platform.stargazerModel.jvm`

### Application Modules (apps/*, gondor/*, itools/*)
- **Pattern**: `{topLevel}.{moduleName}.{platform}`
- **Examples**:
  - `apps.maya.mayaApp.js`
  - `gondor.gondorCore.jvm`
  - `itools.olympian.jvm.it`

## Troubleshooting

### Common Issues

#### Out of Memory Errors
```bash
# Check current JVM settings
cat .mill-jvm-opts

# For large builds, increase heap size temporarily
export MILL_JVM_OPTS="-Xmx24G -Xms16G"
./mill __.compile
```

#### Build Cache Issues
```bash
# Clean and rebuild
./mill clean
./mill __.compile

# Clean specific module
./mill modules.fundsub.fundsub.jvm.clean
```

#### JavaScript Build Issues
```bash
# Reinstall JS dependencies
rm -rf node_modules yarn.lock
./mill workspaceJsPackageInstall

# Rebuild web resources
./mill devBuildWebResources
```

#### Test Failures
```bash
# Run tests with more verbose output
./mill modules.fundsub.fundsub.jvm.test.test -v

# Run single test for debugging
./mill __.fundsub.fundsub.jvm.it.testOnly "*SpecificTestInteg" -v
```

### Performance Issues
- Use `-j` flag for parallel execution
- Use `*Cached` variants when available
- Check `.mill-jvm-opts` for memory settings
- Consider selective execution for large test suites

### Module Resolution Issues
```bash
# Verify module structure
./mill resolve modules.fundsub._

# Check dependencies
./mill modules.fundsub.fundsub.jvm.ivyDeps
```

## Integration with Development Tools

### IDE Integration
- Mill has IntelliJ IDEA support via BSP (Build Server Protocol)
- Use `./mill mill.bsp.BSP/install` to set up BSP
- VS Code support available through Metals

### CI/CD Integration
- Use `./mill` wrapper script in CI
- Set appropriate `MILL_JVM_OPTS_PATH` for different environments
- Use selective execution for faster CI builds
- Cache `out/` directory for build acceleration

### Local Development
- Use `./mill reStart` for background server development
- Use `devBuild*` commands for faster iteration
- Use `testCached` variants to avoid re-running unchanged tests

## Output Directory Structure

All build artifacts are stored in the `out/` directory:
- `out/modules/` - Module-specific build outputs
- `out/platform/` - Platform module outputs  
- `out/gondor/` - Gondor module outputs
- `out/mill-profile.json` - Build performance profile
- `out/mill-dependency-tree.json` - Dependency information

For detailed output structure, see: https://mill-build.com/mill/Out_Dir.html
