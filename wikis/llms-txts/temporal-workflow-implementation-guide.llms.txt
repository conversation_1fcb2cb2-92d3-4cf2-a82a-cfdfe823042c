# Temporal Workflow Implementation Guide

Guide for implementing Temporal workflows in this Scala codebase using ZIO Temporal.

## 1. Core Components & Libraries

**Key Components:**
- **Effect Type**: `WorkflowTask[A]` for workflow composition
- **Workflow Interfaces**: `@workflowInterface` traits extending `TemporalWorkflow[Input, Output]`
- **Activity Interfaces**: `@activityInterface` traits extending `TemporalActivity`
- **Data Models**: Protobuf messages with `TemporalData` typeclass
- **Registration**: `WorkflowImpl` and `ActivityImpl` for worker registration

**Essential Imports:**
```scala
import zio.temporal.{workflowInterface, workflowMethod, activityInterface, activityMethod}
import zio.temporal.workflow.{ZWorkflow, ZActivityStub, ZActivityOptions}
import zio.temporal.{ZRetryOptions, JavaTypeTag}
import anduin.workflow.{WorkflowTask, TemporalWorkflowService, TemporalQueue}
import anduin.workflow.common.{TemporalWorkflow, TemporalActivity, TemporalWorkflowCompanion, TemporalActivityCompanion}
```

**Best Practices:**
- Split complex operations into granular activities for better error handling
- Use sagas for compensation logic
- Process collections individually (separate activities per item)
- Use `WorkflowTask.traverse` for sequential processing
- Set appropriate timeouts and retries (see Section 4)

## 2. Protobuf Setup

**File Locations:**
- Business domain: `{module}Model/jvm/protobuf/`
- Module-specific: `{module}/jvm/it/protobuf/`

**Template:**
```protobuf
syntax = "proto3";
import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "anduin.workflow.your.package"
  single_file: true
};

message YourWorkflowInput {
  string entity_id = 1 [(scalapb.field).type = "anduin.id.YourEntityId"];
  string actor = 2 [(scalapb.field).type = "anduin.model.common.user.UserId"];
}

message YourWorkflowOutput {
  string result_status = 1;  // Avoid "status" - generates enums
  string message = 2;
}
```

**Critical Notes:**
- Use `[(scalapb.field).type = "..."]` for ID type conversion
- Avoid field names like `status` that generate unexpected enums
- Wrong file placement causes compilation issues

## 3. Implementation Patterns

### Workflow Interface
```scala
@workflowInterface
trait YourWorkflow extends TemporalWorkflow[YourWorkflowInput, YourWorkflowOutput] {
  @workflowMethod
  override def run(input: YourWorkflowInput): YourWorkflowOutput
}

object YourWorkflow extends TemporalWorkflowCompanion[YourWorkflow] {
  override val queue: TemporalQueue = TemporalQueue.Default
  override val maximumRetryAttempts: Int = 3
}
```

### Workflow Implementation
```scala
class YourWorkflowImpl extends YourWorkflow {
  private val activities = newActivityStub[YourActivity]

  override def runAsync(input: YourWorkflowInput): WorkflowTask[YourWorkflowOutput] = {
    for {
      _ <- WorkflowTask.succeed(scribe.info(s"Starting: ${input.data}"))
      result <- WorkflowTask.executeActivity(activities.processData(input.data))
      _ <- WorkflowTask.succeed(scribe.info(s"Completed: $result"))
    } yield YourWorkflowOutput(result)
  }

  override def run(input: YourWorkflowInput): YourWorkflowOutput = runAsync(input).getOrThrow
}

object YourWorkflowImpl extends TemporalWorkflowImplCompanion[YourWorkflow, YourWorkflowImpl]
```

### Activity Interface
```scala
@activityInterface(namePrefix = "YourActivity")
trait YourActivity extends TemporalActivity {
  @activityMethod
  def processData(data: String): String

  @activityMethod
  def validateInput(input: String): Boolean
}

object YourActivity extends TemporalActivityCompanion[YourActivity]() {
  override val startToCloseTimeout: Duration = Duration.ofMinutes(10)
  override val maximumRetryAttempts: Int = 3
  override val heartbeatTimeout: Option[Duration] = Some(Duration.ofMinutes(1))
}
```

### Activity Implementation
```scala
final case class YourActivityImpl(yourService: YourService)(
  using val temporalWorkflowService: TemporalWorkflowService
) extends YourActivity {

  override def processData(data: String): String = {
    temporalWorkflowService.executeTask(yourService.processData(data), "processData")
  }

  override def validateInput(input: String): Boolean = {
    temporalWorkflowService.executeTask(yourService.validateInput(input), "validateInput")
  }
}
```

## 4. Error Handling & Retry Strategies

### Activity Error Handling
```scala
class YourWorkflowImpl extends YourWorkflow {
  private val activities = ZWorkflow.newActivityStub[YourActivity](
    ZActivityOptions
      .withStartToCloseTimeout(Duration.ofMinutes(5))
      .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(3))
  )

  override def run(input: YourWorkflowInput): YourWorkflowOutput = {
    try {
      ZActivityStub.execute(activities.processData(input.data))
      YourWorkflowOutput("success")
    } catch {
      case e: ActivityFailure =>
        e.getCause match {
          case _: ValidationException => YourWorkflowOutput("validation_failed")
          case _ => throw e
        }
    }
  }
}
```

### Saga Pattern for Compensation
```scala
import zio.temporal.workflow.ZSaga

class YourWorkflowImpl extends YourWorkflow {
  private inline def attemptSagaActivity[R](inline f: R)(using JavaTypeTag[R]) =
    ZSaga.attempt(ZActivityStub.execute(f))

  private inline def compensateSagaActivity[R](inline f: R)(using JavaTypeTag[R]) =
    ZSaga.compensation { ZActivityStub.execute(f); () }

  override def run(input: YourWorkflowInput): YourWorkflowOutput = {
    val saga = for {
      _ <- compensateSagaActivity(activities.rollbackOperation(input.data))
      result <- attemptSagaActivity(activities.performOperation(input.data))
    } yield result

    val result = saga.runOrThrow(ZSaga.Options(parallelCompensation = true))
    YourWorkflowOutput(result)
  }
}
```

## 5. Registration & Module Organization

### Workflow Module Pattern
```scala
trait YourWorkflowModule extends YourServiceModule {
  lazy val yourActivityImpl = ActivityImpl.derived[YourActivity, YourActivityImpl]
}

object YourWorkflowModule {
  lazy val yourWorkflowImpl = WorkflowImpl.derived[YourWorkflow, YourWorkflowImpl]

  lazy val allWorkflows: List[WorkflowImpl[?, ?]] = List(yourWorkflowImpl)
  lazy val allActivities: List[ActivityImpl[?, ?]] = List(/* defined in trait */)
}
```

### Worker Registration
```scala
private lazy val commonWorkflows: List[WorkflowImpl[?, ?]] =
  List(YourWorkflowModule.yourWorkflowImpl) ++ YourWorkflowModule.allWorkflows

private lazy val commonActivities: List[ActivityImpl[?, ?]] =
  List(yourActivityImpl) ++ YourWorkflowModule.allActivities
```

## 6. Testing Patterns

### Integration Test Setup
```scala
object YourWorkflowInteg extends YourBaseInteg with TemporalFixture {
  override def testWorkflows: List[WorkflowImpl[?, ?]] = List(
    WorkflowImpl.derived[YourWorkflow, YourWorkflowImpl]
  )

  override def testActivities: List[ActivityImpl[?, ?]] = List(
    ActivityImpl.derived[YourActivity, YourActivityImpl]
  )

  override def spec = suite("YourWorkflowInteg") {
    test("Should process data successfully") {
      for {
        output <- temporalWorkflowService.runSync[
          YourWorkflowInput, YourWorkflowOutput, YourWorkflow
        ](YourWorkflowInput("test data"))
      } yield assertTrue(output.result == "processed: test data")
    }
  }
}
```

### Test Execution
```bash
./mill __.yourModule.jvm.it.testOnly "*YourWorkflowInteg"
```

## 7. Best Practices & Conventions

### Naming Conventions
- **Workflows**: `{Domain}{Purpose}Workflow` → `{Domain}{Purpose}WorkflowImpl`
- **Activities**: `{Domain}{Purpose}Activity` → `{Domain}{Purpose}ActivityImpl`
- **Data Models**: `{Purpose}Input`, `{Purpose}Output`, `{Purpose}Params`

### File Organization
```
your-module/jvm/
├── src/your/package/
│   ├── workflow/YourWorkflow.scala + YourWorkflowImpl.scala
│   └── activity/YourActivity.scala + YourActivityImpl.scala
└── it/
    ├── src/your/package/YourWorkflowInteg.scala
    └── protobuf/your/package/your_workflow.proto
```

### Queue Selection
- **TemporalQueue.Default**: Standard operations
- **TemporalQueue.Priority**: High-priority/time-sensitive
- **Custom Queues**: Resource isolation

### Retry Guidelines
- **Transient Errors**: `maximumAttempts = 3-5`
- **External APIs**: `maximumAttempts = 3` with exponential backoff
- **Database Ops**: `maximumAttempts = 2-3`
- **Non-Retryable**: `maximumAttempts = 1`

## 8. Timeouts & Configuration

### Timeout Types

**Workflow Timeouts:**
- **Execution Timeout**: Total workflow lifetime (default: 60min)
- **Run Timeout**: Single workflow run (default: 60min)
- **Task Timeout**: Worker task processing (default: 60sec)

**Activity Timeouts:**
- **Start-To-Close**: Execution time (default: 60min) - **Most Important**
- **Schedule-To-Close**: Total time including queue (optional)
- **Schedule-To-Start**: Queue time (optional)
- **Heartbeat**: Progress monitoring interval (optional)

**Note**: Activities must have either Start-To-Close OR Schedule-To-Close timeout set.

### Configuration Patterns

**Companion Object (Recommended):**
```scala
object YourActivity extends TemporalActivityCompanion[YourActivity]() {
  override val startToCloseTimeout: Duration = Duration.ofMinutes(10)
  override val maximumRetryAttempts: Int = 3
  override val heartbeatTimeout: Option[Duration] = Some(Duration.ofMinutes(1))
}
```

**Inline Configuration:**
```scala
private val activities = ZWorkflow.newActivityStub[YourActivity](
  ZActivityOptions
    .withStartToCloseTimeout(Duration.ofMinutes(5))
    .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(1))
)
```

### Timeout Guidelines by Duration

**Quick (< 30s):** `Duration.ofSeconds(30)`, retries: 3-5, no heartbeat
**Standard (30s-5min):** `Duration.ofMinutes(5)`, retries: 2-3, optional heartbeat
**Long (5-30min):** `Duration.ofMinutes(30)`, retries: 1-2, heartbeat required
**Very Long (> 30min):** `Duration.ofHours(1-2)`, retries: 1, heartbeat required

### Heartbeats

**Use for:** Activities > 5min, need cancellation/progress monitoring
**Don't use for:** Quick activities < 1min, simple DB operations

**Implementation:**
```scala
// In activity - send heartbeat during long processing
_ <- ZIO.attempt(Activity.getExecutionContext().heartbeat(s"Progress: ${index}/${total}"))

// Configuration
object LongActivity extends TemporalActivityCompanion[LongActivity]() {
  override val startToCloseTimeout: Duration = Duration.ofMinutes(30)
  override val heartbeatTimeout: Option[Duration] = Some(Duration.ofMinutes(2))
}
```

### Error Handling

**Timeout Errors:**
```scala
try {
  ZActivityStub.execute(activities.processData(input))
} catch {
  case e: ActivityTimeoutException =>
    e.getTimeoutType match {
      case TimeoutType.TIMEOUT_TYPE_START_TO_CLOSE => // Handle execution timeout
      case TimeoutType.TIMEOUT_TYPE_HEARTBEAT => // Handle heartbeat timeout
    }
}
```

**Don't Retry Timeouts:**
```scala
operation.retry(Schedule.recurWhile {
  case _: TimeoutFailure => false // Don't retry timeouts
  case _: TemporalFailure => true
})
```

## 9. Parent-Child Workflow Patterns

**Use Child Workflows When:**
- Complex multi-step business logic per item
- Independent lifecycle management needed
- Isolation and error boundaries required
- Processing large batches where each item may take significant time

**Use Activities When:**
- Simple single-step operations
- High-frequency, lightweight operations
- Simple transformations or validations

### Child Workflow Configuration
```scala
// Basic child workflow creation
val childWorkflowStub = temporalWorkflowService.newChildWorkflowStub[YourChildWorkflow]

// With custom options
val childWorkflowStub = ZWorkflow.newChildWorkflowStub[YourChildWorkflow](
  ZChildWorkflowOptions
    .withWorkflowId(ZWorkflow.randomUUID.toString)
    .withRetryOptions(ZRetryOptions.default.withMaximumAttempts(1))
    .withWorkflowRunTimeout(Duration.ofMinutes(10))
    .withParentClosePolicy(ParentClosePolicy.PARENT_CLOSE_POLICY_TERMINATE)
)
```

### Parent Close Policies
- **TERMINATE** (Most Common): Child workflows terminate when parent completes/fails
- **ABANDON** (Rare): Child workflows continue running independently
- **REQUEST_CANCEL**: Child workflows receive cancellation request but can choose to ignore

### Batch Processing Pattern
```scala
// Parent workflow - orchestrates batch
class BatchProcessingWorkflowImpl extends BatchProcessingWorkflow {
  override def processBatch(param: BatchProcessingParam): BatchProcessingResponse = {
    val results = param.itemIds.map { itemId =>
      itemId -> ZChildWorkflowStub.execute(
        childWorkflow.processItem(ItemProcessingParam(itemId, param.processingType))
      )
    }
    ZActivityStub.execute(activities.completeBatch(param.batchId, results))
    BatchProcessingResponse(s"Processed ${results.size} items")
  }
}

// Child workflow - processes individual item
class ItemProcessingWorkflowImpl extends ItemProcessingWorkflow {
  override def processItem(param: ItemProcessingParam): ItemProcessingResponse = {
    try {
      ZActivityStub.execute(activities.validateItem(param.itemId))
      val result = ZActivityStub.execute(activities.processItemData(param.itemId, param.processingType))
      ZActivityStub.execute(activities.saveItemResult(param.itemId, result))
      ItemProcessingResponse("success", Some(result))
    } catch {
      case e: ActivityFailure =>
        ZActivityStub.execute(activities.markItemFailed(param.itemId, e.getCause.getMessage))
        ItemProcessingResponse("failed", None) // Return error response instead of throwing
    }
  }
}
```

### Error Handling Patterns
```scala
// Continue on individual failures
val results = param.itemIds.map { itemId =>
  try {
    Some(ZChildWorkflowStub.execute(childWorkflow.processItem(ItemProcessingParam(itemId))))
  } catch {
    case _: ChildWorkflowFailure =>
      scribe.warn(s"Skipping failed item: $itemId")
      None
  }
}.flatten

// Fail fast on critical errors
val maxFailures = param.itemIds.size / 4 // Allow 25% failure rate
var failureCount = 0
val results = param.itemIds.takeWhile(_ => failureCount < maxFailures).map { itemId =>
  try {
    ZChildWorkflowStub.execute(childWorkflow.processItem(ItemProcessingParam(itemId)))
  } catch {
    case e: ChildWorkflowFailure =>
      failureCount += 1
      if (failureCount >= maxFailures) throw new RuntimeException(s"Too many failures: $failureCount")
      ItemProcessingResponse("failed")
  }
}
```


## 10. Integration & Deployment

### Database Integration
```scala
override def saveData(data: YourData): String = {
  temporalWorkflowService.executeTask(
    for {
      id <- yourDatabaseService.save(data)
      _ <- ZIO.logInfo(s"Saved data with ID: $id")
    } yield id,
    "saveData"
  )
}
```

### External Service Integration
```scala
override def callExternalApi(request: ApiRequest): ApiResponse = {
  temporalWorkflowService.executeTask(
    externalApiService
      .call(request)
      .retry(Schedule.exponentialBackoff(1.second) && Schedule.recurs(3))
      .catchAll(error => ZIO.fail(new RuntimeException(s"API call failed: ${error.getMessage}"))),
    "callExternalApi"
  )
}
```

### Worker Configuration
```scala
temporalConfig {
  worker {
    maxConcurrentActivityExecutions = 100
    maxConcurrentWorkflowTaskExecutions = 50
    overrideQueueSuffix = "production" // or "staging", "dev"
  }
}
```

### Environment Variables
- `TEMPORAL_HOST`: Temporal server host
- `TEMPORAL_PORT`: Temporal server port
- `TEMPORAL_NAMESPACE`: Temporal namespace
- `TEMPORAL_TASK_QUEUE_SUFFIX`: Queue suffix for environment isolation

## 11. Workflow Versioning

### Interface Versioning
```scala
// Version 1
@workflowInterface
trait YourWorkflowV1 extends TemporalWorkflow[YourInputV1, YourOutputV1]

// Version 2 - Add new methods, don't modify existing ones
@workflowInterface
trait YourWorkflowV2 extends TemporalWorkflow[YourInputV2, YourOutputV2] {
  @workflowMethod
  def runV2(input: YourInputV2): YourOutputV2

  @workflowMethod(name = "run") // Keep original method for compatibility
  def runV1(input: YourInputV1): YourOutputV1
}
```

### Data Model Evolution
```protobuf
message YourWorkflowInput {
  reserved 2; // removed field
  reserved "old_field_name";

  string data = 1;
  string new_field = 3; // New fields get new numbers
}
```

## 12. Refactoring Existing Logic into Workflows

### Analyze & Decompose Complex Operations
```scala
// Original monolithic function
def archiveFundSub(fundSubId: FundSubId, actor: UserId): Task[Unit] = {
  for {
    fundSubModel <- getFundSubModel(fundSubId)
    _ <- validateNotArchived(fundSubModel)
    lpIds <- getLpIds(fundSubId)
    amounts <- ZIO.foreach(lpIds)(removeLpComplex(_, actor)) // Complex logic
    admins <- getAdmins(fundSubId)
    _ <- ZIO.foreach(admins)(removeAdminComplex(fundSubId, _, actor))
    _ <- updateFundStatus(fundSubId, amounts.sum)
    _ <- archiveInExternalSystems(fundSubId)
  } yield ()
}
```

### Split into Granular Activities
```scala
@activityInterface(namePrefix = "ArchiveFundSub")
trait ArchiveFundSubActivity extends TemporalActivity {
  @activityMethod
  def validateFundSubNotArchived(fundSubId: FundSubId): Unit

  @activityMethod
  def getLpIdsToRemove(fundSubId: FundSubId): Seq[FundSubLpId]

  @activityMethod // Individual processing - key insight!
  def removeLp(lpId: FundSubLpId, actor: UserId): Option[Double]

  @activityMethod
  def removeAdmin(fundSubId: FundSubId, admin: UserId, actor: UserId): Unit

  @activityMethod
  def updateFundSubStatus(fundSubId: FundSubId, totalCommitmentAmount: Double): Unit
}
```

### Orchestrate with Sequential Processing
```scala
class ArchiveFundSubWorkflowImpl extends ArchiveFundSubWorkflow {
  private val activities = newActivityStub[ArchiveFundSubActivity]

  override def runAsync(input: ArchiveFundSubInput): WorkflowTask[ArchiveFundSubOutput] = {
    for {
      _ <- WorkflowTask.executeActivity(activities.validateFundSubNotArchived(input.fundSubId))

      lpIds <- WorkflowTask.executeActivity(activities.getLpIdsToRemove(input.fundSubId))

      // Process each LP individually - key pattern!
      commitmentAmounts <- WorkflowTask.traverse(lpIds) { lpId =>
        WorkflowTask.executeActivity(activities.removeLp(lpId, input.actor))
      }

      admins <- WorkflowTask.executeActivity(activities.getAdminsToRemove(input.fundSubId))
      _ <- WorkflowTask.traverse(admins) { admin =>
        WorkflowTask.executeActivity(activities.removeAdmin(input.fundSubId, admin, input.actor))
      }

      totalCommitmentAmount = commitmentAmounts.flatten.sum
      _ <- WorkflowTask.executeActivity(activities.updateFundSubStatus(input.fundSubId, totalCommitmentAmount))

    } yield ArchiveFundSubOutput("success", s"Fund archived with ${lpIds.size} LPs, ${admins.size} admins")
  }
}
```

### Key Refactoring Insights
1. **Individual Item Processing**: Use `WorkflowTask.traverse(items)(processItem)` instead of `ZIO.foreach`
2. **Logical Step Boundaries**: Separate validation → data retrieval → processing → final updates
3. **State Aggregation**: Collect results from individual operations in workflow logic
4. **Error Boundaries**: Each activity represents a failure/retry boundary
5. **Progressive Enhancement**: Start with existing service calls wrapped in activities

## 13. Quick Reference

### Essential Commands
```bash
# Run integration tests
./mill __.yourModule.jvm.it.testOnly "*YourWorkflowInteg"

# Build and test
./mill __.compile
./mill __.test.test
```

### Common Patterns Summary
1. **Workflow Interface**: `@workflowInterface` trait extending `TemporalWorkflow[Input, Output]`
2. **Activity Interface**: `@activityInterface` trait extending `TemporalActivity`
3. **Implementation**: Use `WorkflowTask` for composition, `temporalWorkflowService.executeTask` for activities
4. **Error Handling**: Try-catch in workflows, return error responses instead of throwing
5. **Timeouts**: Configure in companion objects, use heartbeats for long operations
6. **Child Workflows**: Use for complex batch processing with independent error boundaries
7. **Refactoring**: Split monolithic functions into granular activities, use `WorkflowTask.traverse` for collections