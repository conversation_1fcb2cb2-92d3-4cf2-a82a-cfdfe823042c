Instructions for conducting thorough code reviews in this Scala 3 codebase with Mill, ZIO, Tapir, FoundationDB, Temporal, and frontend components.

## 1. Architecture & Structure

### Module Organization
- [ ] **Module placement**: Business logic in `modules/`, platform in `platform/`, apps in `apps/`
- [ ] **Cross-platform structure**: Proper `jvm/`, `js/`, `shared/` organization
- [ ] **Dependency flow**: Apps → modules → platform (never reverse)
- [ ] **Naming convention**: `{category}.{module}.{platform}` (e.g., `modules.fundsub.fundsub.jvm`)

### File Structure
```
{module}/
├── jvm/src/              # Backend Scala code
├── js/src/               # Frontend Scala.js code
├── shared/src/           # Cross-platform shared code
├── jvm/test/src/         # Unit tests
├── jvm/it/src/           # Integration tests
├── jvm/protobuf/         # Protocol buffer definitions
├── package.mill          # Build configuration
└── dependency.mill       # Dependencies
```

- [ ] **Package structure**: Domain-driven design with clear boundaries
- [ ] **Test separation**: Unit and integration tests properly organized
- [ ] **Protobuf location**: `.proto` files in correct module

## 2. Scala Code Quality

### Scala 3 Best Practices
- [ ] **Modern syntax**: Use Scala 3 features (enums, union types, given/using)
- [ ] **No legacy syntax**: Avoid Scala 2 patterns (`_` wildcards, old implicits)
- [ ] **Immutability**: Prefer `val` over `var`, immutable collections
- [ ] **Null safety**: Use `Option` instead of `null`
- [ ] **Type annotations**: Explicit types for public APIs and return values

```scala
// ✅ Good - Real enum from codebase
enum SystemTag(val value: Int) extends IntEnum {
  case Imported extends SystemTag(0)
  case Unrecognized extends SystemTag(100)
}

enum TextAlignment(val value: String) extends StringEnum {
  case Left extends TextAlignment("left")
  case Center extends TextAlignment("center")
  case Right extends TextAlignment("right")
}

// ✅ Good - Context parameters
def timed[A](thunk: => A)(using c: Clock) = (thunk, c.now)

// ❌ Bad - Legacy patterns
sealed trait PaymentStatus
def processPayment(amount: BigDecimal)(implicit ec: ExecutionContext): Task[PaymentResult] = ???
```

### Error Handling & Effects
- [ ] **ZIO effects**: Proper `ZIO[R, E, A]` type parameters
- [ ] **Specific errors**: Custom error types instead of `Throwable`
- [ ] **No exceptions**: Use `ZIO.fail()` in business logic
- [ ] **Resource management**: Use `ZIO.acquireReleaseWith` or timeout patterns

```scala
// ✅ Good - Real error handling from codebase
def defaultEndpointErrorHandlerCallback(throwable: Throwable)(cb: Callback = Callback.empty): Callback = {
  throwable match {
    case e: EndpointException =>
      for {
        _ <- Toast.errorCallback(e.responseText)
        _ <- cb
      } yield ()
    case _ =>
      throw throwable
  }
}

// ✅ Good - ZIO validation patterns
def validate[E <: Throwable](condition: => Boolean)(exception: => E): IO[E, Unit] = {
  if (condition) {
    ZIO.unit
  } else {
    ZIO.fail(exception)
  }
}

// ✅ Good - Timeout handling
def timeout[R, E, A](timeout: FiniteDuration)(effect: ZIO[R, E, A]): ZIO[R, E, A] = {
  effect.timeoutFail(
    throw new TimeoutException(s"ZIO effect timed-out after ${timeout.toSeconds} seconds")
  )(zio.Duration.fromSeconds(timeout.toSeconds))
}
```

## 3. Domain-Specific Patterns

### Tapir Endpoints
- [ ] **Authentication**: Use `AuthenticatedEndpoints` or `PublicApiEndpoints`
- [ ] **Input validation**: Use `AuthenticatedEndpointValidator` for business rules
- [ ] **Error handling**: Standardized error responses with proper HTTP status codes
- [ ] **Timeouts**: Explicit timeouts for long ops; avoid unbounded requests
- [ ] **Security**: Bearer token auth; never trust userId from payload (use `ctx.actor.userId`)
- [ ] **API correctness**: Pagination and filtering return consistent results; keep outputs backward‑compatible where possible

### FoundationDB Integration
- [ ] **Transactions**: Reads use read paths; writes use transact; keep transactions short
- [ ] **Store providers**: Correct `FDBOperationProvider` and keyspace usage; no cross‑keyspace mistakes
- [ ] **Queries**: Ensure index coverage for new filters; avoid N+1 patterns inside transactions
- [ ] **Retries**: Keep DB retry attempts conservative (often 1); handle splitting/retry at application level (`splitTransaction`, `foreachGrouped`)
- [ ] **Idempotency**: Write paths must be idempotent under retry
- [ ] **Resource cleanup**: No blocking or heavy compute inside transactions

```scala
// ✅ Good - Real FDB operations from codebase
trait FDBOperations[O] {
  def getProvider(keySpace: FDBRecordKeySpace): FDBOperationProvider[O]

  val Production: FDBOperationProvider[O] = getProvider(FDBRecordProductionKeySpace)
  val Test: FDBOperationProvider[O] = getProvider(FDBRecordTestKeySpace)

  def getProviderCached(using keySpaceEnum: FDBKeySpaceEnum): FDBOperationProvider[O] = {
    keySpaceEnum match {
      case FDBKeySpaceEnum.Production => Production
      case FDBKeySpaceEnum.Test => Test
    }
  }
}

// ✅ Good - Transaction with proper error handling
def createRecord(record: SampleTrackingEvent): Task[Unit] = {
  FDBRecordDatabase.transact(storeProvider) { store =>
    for {
      _ <- store.create(record)
    } yield ()
  } // Automatically retries FDBStoreTransactionConflictException
}

// ✅ Good - Read operation
def getRecord(id: SampleTrackingEventPrimaryKey): Task[Option[SampleTrackingEvent]] = {
  FDBRecordDatabase.transact(storeProvider) { store =>
    store.getOpt(id)
  }
}
```

### Temporal Workflows
- [ ] **Interfaces**: Proper `@workflowInterface` and `@workflowMethod` annotations
- [ ] **Activities**: Correct `@activityInterface` and `@activityMethod` setup
- [ ] **Error handling**: Use saga pattern for compensation
- [ ] **Timeouts & retries**: Explicit timeouts; bounded retries (avoid infinite/backoff loops)
- [ ] **Execution mode**: Use sync activity execution when ordering/consistency requires
- [ ] **Health**: Heartbeats for schedulers/long runners; configure poller concurrency appropriately
- [ ] **Determinism**: No non-deterministic operations (e.g., `System.currentTimeMillis()`)

```scala
// ✅ Good
final case class TemporalEnvironment(
  workflowService: ZEnvironment[TemporalWorkflowService],
  workflowClient: ZEnvironment[ZWorkflowClient],
  scheduleClient: ZEnvironment[ZScheduleClient],
  serviceStubs: ZEnvironment[ZWorkflowServiceStubs],
  metricRegistry: ZEnvironment[MeterRegistry],
  tracing: ZEnvironment[AggregatedTracing]
)

// ✅ Good - Workflow service integration
import zio.temporal.workflow.{ZWorkflowClient, ZWorkflowServiceStubs}
import anduin.workflow.TemporalWorkflowService
```

### Frontend Components
- [ ] **Component choice**: Appropriate React vs Laminar usage
- [ ] **Endpoint UX**: Wrap async with `toReactCallbackWithErrorHandler`; use Toast on failures
- [ ] **React-Laminar**: Mount with `L.render`; clean up on unmount
- [ ] **Design system**: Use design system components; convert Tailwind vars to HtmlMod when dynamic
- [ ] **Performance**: Avoid unnecessary re-renders; debounce inputs where needed

```scala
// ✅ Good - Real Laminar component from codebase
final case class GenericTagL(
  tagInfo: TagTrait,
  isDeleting: Signal[Boolean] = Signal.fromValue(false),
  showRemoveIcon: Boolean = false,
  onRemove: Observer[Unit] = Observer.empty,
  maxTagWidth: Int = 100
) {
  def apply(): HtmlElement = {
    val color = TagJsUtils.TagColorToTagColor(tagInfo.color)
    div(
      maxWidth.px := maxTagWidth,
      TagL(
        label = Val(tagInfo.name),
        color = Val(color),
        isDisabled = isDeleting,
        onClose = if (showRemoveIcon) Option(onRemove) else None
      )()
    )
  }
}

// ✅ Good - React wrapper mounting Laminar
private case class Backend(scope: BackendScope[Props, Unit]) {
  private val ref = react.Ref[dom.html.Div]
  private var dataVar: Option[Var[Dashboard.Data]] = None
  private var rootNode: L.RootNode = scala.compiletime.uninitialized

  def mount: Callback = {
    for {
      props <- scope.props
      _ <- ref.foreach { ele =>
        val ourDataVar = Var(props.data)
        dataVar = Option(ourDataVar)
        rootNode = L.render(
          ele,
          Dashboard(
            dataVar = ourDataVar,
            router = props.router,
            updateHeaderDisplayConfigCallback = props.updateHeaderDisplayConfigCallback
          )()
        )
      }
    } yield ()
  }
}
```
- [ ] **Performance**: Avoid unnecessary re-renders, efficient signals

## 4. Testing

### Test Quality
- [ ] **Assertions**: Use ScalaTest matchers for descriptive error messages
- [ ] **Organization**: Clear structure with `describe`/`it` or `suite`/`test`
- [ ] **Integration tests**: Use `*Integ` classes with proper fixtures
- [ ] **Test data**: Clean setup and teardown

### Test Categories
- [ ] **Unit tests**: Individual functions/classes in isolation
- [ ] **Integration tests**: Component interactions with real dependencies
- [ ] **Multi-region tests**: Distributed system behavior across regions

## 5. Performance & Scalability

### Performance
- [ ] **Batching**: Batch DB writes/reads; avoid per‑row transactions
- [ ] **Parallelism**: Use `ZIO.foreachPar` for independent operations; cap concurrency where needed
- [ ] **Queries**: Verify indexes; avoid expensive scans
- [ ] **Memory**: Avoid loading huge datasets; stream when possible
- [ ] **UI**: Cancel in-flight requests on unmount/close to prevent leaks (see PdfViewer pattern)

```scala
// ✅ Good - Real ZIO patterns from codebase
def toReactCallbackWithErrorHandler(
  task: Task[Callback],
  onError: Throwable => Callback = defaultEndpointErrorHandlerCallback(_)()
): Callback = {
  toReactCallback(task.catchAll(error => ZIO.succeed(onError(error))))
}

// ✅ Good - Parallel processing
ZIO.foreachPar(paymentIds)(paymentService.processPayment)

// ❌ Bad - Sequential independent operations
ZIO.foreach(paymentIds)(paymentService.processPayment)
```

### Scalability
- [ ] **Chunking**: Process large datasets in chunks with backpressure
- [ ] **Timeouts**: Explicit timeouts for I/O; avoid unbounded waits
- [ ] **Resource limits**: Configure pools; avoid blocking on default dispatcher
- [ ] **Caching**: Use caching where hotspots identified by metrics

## 6. Security

### Authentication & Authorization
- [ ] **Endpoint security**: All sensitive endpoints authenticated
- [ ] **Permission checks**: Business logic validates user permissions
- [ ] **Input sanitization**: User inputs validated and sanitized
- [ ] **SQL injection**: No string concatenation in queries

### Data Protection
- [ ] **PII handling**: Personal data protected and encrypted
- [ ] **Logging**: No sensitive data in logs (passwords, tokens, PII)
- [ ] **Error messages**: No sensitive information leaked
- [ ] **Secrets management**: No hardcoded secrets or credentials

```scala
// ✅ Good - Real authentication from codebase
final case class InvestmentEntityOwnerValidator(
  override val id: String
)(
  using investmentEntityAuthorizationService: InvestmentEntityAuthorizationService
) extends AuthenticatedEndpointValidator[InvestmentEntityOwnerValidationParams](id) {

  override def validate(params: InvestmentEntityOwnerValidationParams, userId: UserId): Task[Unit] = {
    FDBClient.transact(
      investmentEntityAuthorizationService.validateAdminPermission(
        userId,
        params.investmentEntityId
      )
    )
  }
}

// ✅ Good - Error handling with proper logging
protected def catchError(error: Throwable): UIO[GeneralServiceException] = {
  error match {
    case ex: GeneralServiceException =>
      ZIO.logErrorCause("GeneralServiceException: ", ex.toCause).as(ex)
    case ex: TimeoutException =>
      ZIO.logErrorCause(ex.toCause).as(GeneralServiceException("Request timeout"))
    case ex: Throwable =>
      ZIO.logErrorCause(s"Exception: ", ex.toCause).as(GeneralServiceException("Unknown server error"))
  }
}
```

## 9. Documentation & Maintainability

### Documentation
- [ ] **Public API**: Clear documentation for public methods
- [ ] **Complex logic**: Explanatory comments for non-obvious code
- [ ] **Migration notes**: Breaking changes documented

### Maintainability
- [ ] **Naming**: Clear, descriptive names for classes, methods, variables
- [ ] **Function size**: Functions focused and reasonably sized
- [ ] **Code duplication**: Common logic properly abstracted
- [ ] **Dependencies**: Minimal coupling between modules

## 10. AI Agent Review Protocol

### Review Process
1. **Pattern Compliance**: Verify Scala 3 syntax, domain patterns, codebase conventions
2. **Security & Performance**: Check vulnerabilities, anti-patterns, resource management
3. **Integration Impact**: Consider effects on other modules and systems

### Feedback Standards
- **Specificity**: Reference exact file paths and line numbers
- **Code Examples**: Provide concrete before/after examples
- **Reasoning**: Explain why changes improve code quality
- **Priority**: Classify issues by severity

## 11. Common Anti-Patterns

### Scala Anti-Patterns
- **Mutable state in shared code**: Use immutable data structures
- **Nested futures/callbacks**: Use ZIO composition
- **Uncaught exceptions**: Proper error handling in effect types
- **Bloated classes**: Large classes with too many responsibilities

### Architecture Anti-Patterns
- **Circular dependencies**: Modules depending on each other
- **God objects**: Classes with excessive responsibilities
- **Tight coupling**: Modules knowing too much about each other
- **Magic constants**: Hardcoded values instead of configuration

### Domain Anti-Patterns
- **Primitive obsession**: Using basic types instead of domain types
- **Anemic domain models**: Business logic scattered across services
- **Missing validation**: Input validation only at UI layer
- **Inconsistent state**: Data integrity not maintained

## 12. AI Review Mistakes to Avoid

### False Positives
- **Over-flagging style**: Focus on logic and security over formatting
- **Missing context**: Read full module context before flagging
- **Outdated patterns**: Use current codebase patterns as reference

### Incomplete Analysis
- **Surface review**: Check for deeper architectural and security issues
- **Missing integration**: Consider impact on other modules
- **Ignoring tests**: Review test adequacy and maintainability