# Protobuf & Serialization Implementation Guide for AI Coding Agents

This guide provides comprehensive instructions for implementing protobuf messages and serialization patterns in this Scala-based codebase using ScalaPB, Circe, and ZIO.

## 1. Architecture Overview

This codebase uses a comprehensive serialization stack built around:
- **ScalaPB**: Protocol buffer compiler for Scala with type-safe message generation
- **Circe**: JSON library with automatic codec derivation and type-safe JSON handling
- **BinaryModel**: Type-safe binary serialization abstraction over protobuf and JSON
- **ModelCodecs**: Utilities for protobuf/JSON conversions with proper error handling
- **ZIO Integration**: Effect-based error handling and streaming for large datasets

### Core Components
- **Protobuf Messages**: Generated from .proto files with ScalaPB annotations
- **Type Mappers**: Convert between protobuf types and custom Scala types
- **Circe Codecs**: JSON serialization with automatic derivation and defaults
- **Binary Models**: Type-safe binary serialization for storage and transmission
- **Streaming Support**: FDBChunkModel for handling large messages efficiently

### Key Libraries
```scala
import scalapb.{GeneratedMessage, GeneratedMessageCompanion, TypeMapper}
import io.circe.{Code<PERSON>, Encoder, Decoder, <PERSON>son}
import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.model.{BinaryModel, codec.ModelCodecs}
import anduin.scalapbcirce.{ScalapbCirce, CirceTypeMapper}
import anduin.fdb.subspace.FDBChunkModel
import zio.{Task, ZIO, ZStream}
```

## 2. Protobuf Message Design Patterns

### 2.1 Basic Message Structure
```protobuf
syntax = "proto3";

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.{module}"
  single_file: true
  import: "anduin.model.common.user.UserId"
  import: "java.time.Instant"
  import: "anduin.id.{module}.*"
};

message UserProfileMessage {
  string user_id = 1 [(scalapb.field).type = "UserId"];
  string email = 2;
  string first_name = 3;
  string last_name = 4;
  InstantMessage created_at = 5 [(scalapb.field).type = "Instant"];
  UserStatus status = 6;
  repeated string tags = 7;
  map<string, string> metadata = 8;
}

enum UserStatus {
  USER_STATUS_UNSPECIFIED = 0;
  USER_STATUS_ACTIVE = 1;
  USER_STATUS_INACTIVE = 2;
  USER_STATUS_SUSPENDED = 3;
}
```

### 2.2 Advanced Message Patterns
```protobuf
// Oneof for mutually exclusive fields
message PaymentMethod {
  oneof method {
    CreditCardMessage credit_card = 1;
    BankAccountMessage bank_account = 2;
    DigitalWalletMessage digital_wallet = 3;
  }
}

// Optional fields with proper type mapping
message UserPreferences {
  string user_id = 1 [(scalapb.field).type = "UserId"];
  optional string theme_opt = 2 [(scalapb.field).type = "Option[ThemeId]"];
  optional InstantMessage last_login_opt = 3 [(scalapb.field).type = "Option[Instant]"];
}

// Collections with type mapping
message EntityPermissions {
  string entity_id = 1 [(scalapb.field).type = "EntityId"];
  repeated string user_ids = 2 [(scalapb.field).type = "UserId"];
  map<string, string> role_assignments = 3 [(scalapb.field).key_type = "UserId", value_type = "RoleId"];
}
```

### 2.3 Backward Compatibility Patterns
```protobuf
message UserModel {
  // Reserved fields for removed/deprecated fields
  reserved 10, 15, 9 to 11;
  reserved "old_field_name", "deprecated_field";
  
  string user_id = 1;
  string email = 2;
  string first_name = 3;
  string last_name = 4;
  // New fields use new field numbers
  string phone_number = 5; // Added in v2
  repeated string tags = 6; // Added in v3
  UserPreferences preferences = 7; // Added in v4
}
```

## 3. Type Mappers and Custom Types

### 3.1 Basic Type Mapper Implementation
```scala
import scalapb.TypeMapper

// For custom ID types
given userIdTypeMapper: TypeMapper[String, UserId] =
  TypeMapper[String, UserId](UserId.apply)(_.idString)

// For time types
given instantTypeMapper: TypeMapper[InstantMessage, Instant] =
  TypeMapper[InstantMessage, Instant](_.toInstant)(InstantMessage.fromInstant)

// For optional types
given optionalUserIdTypeMapper: TypeMapper[String, Option[UserId]] =
  TypeMapper[String, Option[UserId]] { str =>
    if (str.isEmpty) None else Some(UserId(str))
  } { opt =>
    opt.map(_.idString).getOrElse("")
  }
```

### 3.2 JSON Type Mappers
```scala
import anduin.scalapbcirce.CirceTypeMapper.given

// For JSON fields in protobuf
message ConfigurationMessage {
  string json_config = 1 [(scalapb.field).type = "Option[UserConfig]"];
  string json_metadata = 2 [(scalapb.field).type = "Option[Map[String, String]]"];
}
```

## 4. Circe JSON Codec Patterns

### 4.1 Basic Codec Derivation
```scala
import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.model.codec.ProtoCodecs.given

final case class UserResponse(
  id: UserId,
  email: String,
  firstName: String,
  lastName: String,
  createdAt: Instant,
  status: UserStatus
)

object UserResponse {
  given Codec.AsObject[UserResponse] = deriveCodecWithDefaults
}

// For protobuf messages - automatic codec derivation
final case class CreateUserRequest(
  userModel: UserProfileMessage,
  metadata: Map[String, String]
)

object CreateUserRequest {
  given Codec.AsObject[CreateUserRequest] = deriveCodecWithDefaults
}
```

### 4.2 Advanced Codec Patterns
```scala
// For sealed traits with type discrimination
final case class ApiResponse[T](
  data: T,
  status: String,
  timestamp: Instant
)

object ApiResponse {
  given [T: Encoder: Decoder]: Codec.AsObject[ApiResponse[T]] = deriveCodecWithDefaults
}

// For enums
import anduin.circe.generic.semiauto.deriveEnumCodec

enum ProcessingStatus extends Enum {
  case Pending, Processing, Completed, Failed
}

object ProcessingStatus {
  given Codec[ProcessingStatus] = deriveEnumCodec
}
```

## 5. Binary Model Implementation

### 5.1 Protobuf Binary Models
```scala
import anduin.model.BinaryModel

// Automatic derivation for protobuf messages
given userProfileBinaryModel: BinaryModel[UserProfileMessage] = 
  BinaryModel.protoMessageBinaryModel

// Usage with proper error handling
def serializeUser(user: UserProfileMessage): Task[Array[Byte]] = {
  ZIO.fromEither(
    BinaryModel[UserProfileMessage].toByteArray(user)
      .left.map(ex => new RuntimeException(s"Serialization failed: ${ex.message}", ex.cause))
  )
}

def deserializeUser(bytes: Array[Byte]): Task[UserProfileMessage] = {
  ZIO.fromEither(
    BinaryModel[UserProfileMessage].fromByteArray(bytes)
      .left.map(ex => new RuntimeException(s"Deserialization failed: ${ex.message}", ex.cause))
  )
}
```

### 5.2 Circe Binary Models
```scala
// For non-protobuf case classes
final case class UserMetadata(
  preferences: Map[String, String],
  settings: UserSettings
)

object UserMetadata {
  given Codec.AsObject[UserMetadata] = deriveCodecWithDefaults
  given BinaryModel[UserMetadata] = BinaryModel.circeBinaryModel
}
```

## 6. Message Conversion Patterns

### 6.1 Protobuf/JSON Conversions
```scala
import anduin.model.codec.ModelCodecs
import anduin.scalapbcirce.ScalapbCirce

// Convert protobuf to JSON
def messageToJson[M <: GeneratedMessage](
  message: M,
  includeDefaults: Boolean = false
): Json = {
  ModelCodecs.messageToJson(message, includeDefaults)
}

// Convert JSON to protobuf with error handling
def jsonToMessage[M <: GeneratedMessage](
  json: Json
)(using companion: GeneratedMessageCompanion[M]): Task[M] = {
  ZIO.fromEither(
    ModelCodecs.jsonToMessage(json.hcursor)
      .left.map(failure => new RuntimeException(s"JSON conversion failed: ${failure.message}"))
  )
}

// Using ScalapbCirce directly
def convertWithScalapbCirce[M <: GeneratedMessage](
  message: M
)(using companion: GeneratedMessageCompanion[M]): Task[M] = {
  for {
    json <- ZIO.attempt(ScalapbCirce.defaultPrinter.toJson(message))
    result <- ZIO.attempt(ScalapbCirce.parser.fromJson[M](json))
  } yield result
}
```

### 6.2 Type-Safe Conversions
```scala
// Using ModelCodecs for sealed value types
def convertSealedValue[M <: GeneratedMessage, S <: GeneratedSealedOneof](
  sealedValue: S
)(using 
  typeMapper: TypeMapper[M, S],
  encoder: Encoder[S],
  decoder: Decoder[S]
): Json = {
  ModelCodecs.sealedValueMessageEncoder[M, S].apply(sealedValue)
}
```

## 7. Error Handling Patterns

### 7.1 ZIO-Based Error Handling
```scala
import anduin.model.BinaryModel.BinaryModelParserException

// Custom error types
sealed trait SerializationError extends Throwable
case class ProtobufSerializationError(message: String, cause: Throwable) extends SerializationError
case class JsonConversionError(message: String, cause: Throwable) extends SerializationError

// Safe serialization with proper error handling
def safeSerialize[M <: GeneratedMessage](message: M): Task[Array[Byte]] = {
  ZIO.fromEither(
    BinaryModel[M].toByteArray(message)
      .left.map(ex => ProtobufSerializationError(s"Failed to serialize ${message.getClass.getSimpleName}", ex.cause))
  )
}

def safeDeserialize[M <: GeneratedMessage](
  bytes: Array[Byte]
)(using companion: GeneratedMessageCompanion[M]): Task[M] = {
  ZIO.fromEither(
    BinaryModel[M].fromByteArray(bytes)
      .left.map(ex => ProtobufSerializationError(s"Failed to deserialize ${companion.scalaDescriptor.name}", ex.cause))
  )
}

// JSON conversion with error handling
def safeJsonConversion[M <: GeneratedMessage](
  json: Json
)(using companion: GeneratedMessageCompanion[M]): Task[M] = {
  ZIO.fromEither(
    ModelCodecs.jsonToMessage(json.hcursor)
      .left.map(failure => JsonConversionError(s"JSON conversion failed for ${companion.scalaDescriptor.name}", 
        new RuntimeException(failure.message)))
  )
}
```

### 7.2 Retry and Recovery Patterns
```scala
import zio.{Schedule, Duration}

// Retry serialization with exponential backoff
def serializeWithRetry[M <: GeneratedMessage](message: M): Task[Array[Byte]] = {
  safeSerialize(message)
    .retry(Schedule.exponentialBackoff(100.millis) && Schedule.recurs(3))
    .catchAll { error =>
      ZIO.logError(s"Serialization failed after retries: ${error.getMessage}") *>
      ZIO.fail(error)
    }
}
```

## 8. Performance Optimization Patterns

### 8.1 Streaming Large Messages
```scala
import anduin.fdb.subspace.FDBChunkModel

// For large protobuf messages
given chunkModel: FDBChunkModel[LargeDataMessage] = FDBChunkModel.modelInstance

// Stream processing for large datasets
def processLargeDataset(stream: ZStream[Any, Throwable, DataMessage]): Task[Unit] = {
  stream
    .grouped(100) // Process in batches
    .mapZIOPar(4) { batch =>
      processBatch(batch)
    }
    .runDrain
}

// Chunked storage for very large messages
def storeLargeMessage(message: LargeDataMessage): Task[Unit] = {
  val chunkStream = FDBChunkModel[LargeDataMessage].toByteStream(message)
  chunkStream
    .grouped(10240) // 10KB chunks
    .zipWithIndex
    .mapZIO { case (chunk, index) =>
      storeChunk(index, chunk.toArray)
    }
    .runDrain
}
```

### 8.2 Performance-Critical Serialization
```scala
// Direct protobuf serialization for performance-critical paths
def fastSerialization[M <: GeneratedMessage](message: M): Array[Byte] = {
  message.toByteArray // Direct protobuf serialization
}

// Batch processing with parallel execution
def batchSerialize[M <: GeneratedMessage](messages: List[M]): Task[List[Array[Byte]]] = {
  ZIO.foreachPar(messages) { message =>
    ZIO.attempt(message.toByteArray)
  }
}

// Memory-efficient streaming serialization
def streamSerialize[M <: GeneratedMessage](
  messages: ZStream[Any, Throwable, M]
): ZStream[Any, Throwable, Array[Byte]] = {
  messages.map(_.toByteArray)
}
```

## 9. Testing Patterns

### 9.1 Serialization Round-Trip Tests
```scala
import zio.test.*

object SerializationSpecs extends ZIOSpecDefault {
  
  def spec = suite("Serialization Tests") {
    test("protobuf round-trip serialization") {
      val original = UserProfileMessage(
        userId = "user123",
        email = "<EMAIL>",
        firstName = "John",
        lastName = "Doe"
      )
      
      for {
        serialized <- safeSerialize(original)
        deserialized <- safeDeserialize[UserProfileMessage](serialized)
      } yield assertTrue(deserialized == original)
    } +
    
    test("JSON conversion round-trip") {
      val original = UserResponse(
        id = UserId("user123"),
        email = "<EMAIL>",
        firstName = "John",
        lastName = "Doe",
        createdAt = Instant.now(),
        status = UserStatus.Active
      )
      
      for {
        json <- ZIO.attempt(original.asJson)
        converted <- ZIO.fromEither(json.as[UserResponse])
      } yield assertTrue(converted == original)
    }
  }
}
```

### 9.2 Performance Testing
```scala
test("large message streaming performance") {
  val largeMessage = generateLargeMessage(1000000) // 1M records
  
  for {
    start <- Clock.nanoTime
    _ <- processLargeDataset(ZStream.succeed(largeMessage))
    end <- Clock.nanoTime
    duration = (end - start) / 1000000 // Convert to milliseconds
  } yield assertTrue(duration < 5000) // Should complete within 5 seconds
}
```

## 10. Best Practices and Conventions

### 10.1 Naming Conventions
- **Protobuf packages**: `anduin.protobuf.{module}`
- **Message names**: `{Entity}{Purpose}Message` (e.g., `UserProfileMessage`)
- **Enum names**: `{Entity}{Property}` with `UPPER_CASE` values
- **Field names**: `snake_case` in protobuf, `camelCase` in generated Scala
- **File names**: `{entity}_{purpose}.proto`

### 10.2 File Organization
```
your-module/
├── shared/
│   └── protobuf/
│       ├── {module}/
│       │   ├── core.proto
│       │   ├── events.proto
│       │   └── api.proto
│       └── external/
│           └── dependencies.proto
└── src/
    └── {package}/
        ├── model/
        │   ├── codecs/
        │   └── mappers/
        └── serialization/
```

### 10.3 Field Numbering Guidelines
- **Core fields**: 1-100
- **Optional fields**: 101-200
- **Repeated fields**: 201-300
- **Map fields**: 301-400
- **Reserved ranges**: Document in comments

### 10.4 Import Organization
```protobuf
// Standard imports first
import "scalapb/scalapb.proto";
import "google/protobuf/wrappers.proto";

// Common types
import "date_time.proto";
import "external/squants.proto";

// Module-specific imports
import "{module}/common.proto";
```

## 11. Integration Points

### 11.1 Database Integration
```scala
// FDB integration with chunked storage
def saveLargeEntity[M <: GeneratedMessage: FDBChunkModel](
  id: EntityId,
  entity: M
): Task[Unit] = {
  for {
    chunks <- FDBChunkModel[M].toByteStream(entity).runCollect
    _ <- storeEntityChunks(id, chunks.toArray)
  } yield ()
}

// Regular entity storage
def saveEntity[M <: GeneratedMessage](
  id: EntityId,
  entity: M
): Task[Unit] = {
  for {
    bytes <- safeSerialize(entity)
    _ <- storeEntityBytes(id, bytes)
  } yield ()
}
```

### 11.2 API Integration
```scala
// Tapir endpoint with protobuf serialization
val createUserEndpoint = endpoint
  .post
  .in("users")
  .in(jsonBody[CreateUserRequest])
  .out(jsonBody[UserResponse])
  .serverLogic { request =>
    for {
      user <- userService.createUser(request.userModel)
      response = UserResponse.fromProtobuf(user)
    } yield response.asRight
  }
```

### 11.3 Event Streaming
```scala
// Event serialization for streaming
def publishEvent[E <: GeneratedMessage](event: E): Task[Unit] = {
  for {
    bytes <- safeSerialize(event)
    _ <- eventBus.publish(bytes)
  } yield ()
}

def consumeEvents[E <: GeneratedMessage](
  topic: String
)(using companion: GeneratedMessageCompanion[E]): ZStream[Any, Throwable, E] = {
  eventBus.subscribe(topic)
    .mapZIO(bytes => safeDeserialize[E](bytes))
}
```

## 12. Complete Implementation Example

### 12.1 Define Protobuf Schema
```protobuf
// user_profile.proto
syntax = "proto3";

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.user"
  single_file: true
  import: "anduin.model.common.user.UserId"
  import: "anduin.id.entity.EntityId"
  import: "java.time.Instant"
};

message UserProfileMessage {
  string user_id = 1 [(scalapb.field).type = "UserId"];
  string entity_id = 2 [(scalapb.field).type = "EntityId"];
  string email = 3;
  string first_name = 4;
  string last_name = 5;
  InstantMessage created_at = 6 [(scalapb.field).type = "Instant"];
  UserStatus status = 7;
  repeated string tags = 8;
  map<string, string> metadata = 9;
  optional UserPreferences preferences = 10;
}

message UserPreferences {
  string theme = 1;
  bool notifications_enabled = 2;
  string timezone = 3;
}

enum UserStatus {
  USER_STATUS_UNSPECIFIED = 0;
  USER_STATUS_ACTIVE = 1;
  USER_STATUS_INACTIVE = 2;
  USER_STATUS_SUSPENDED = 3;
}
```

### 12.2 Implement Scala Models
```scala
// UserModels.scala
package anduin.user.model

import anduin.circe.generic.semiauto.deriveCodecWithDefaults
import anduin.model.codec.ProtoCodecs.given
import anduin.protobuf.user.{UserProfileMessage, UserStatus}
import io.circe.Codec

final case class UserProfile(
  id: UserId,
  entityId: EntityId,
  email: String,
  firstName: String,
  lastName: String,
  createdAt: Instant,
  status: UserStatus,
  tags: List[String] = List.empty,
  metadata: Map[String, String] = Map.empty,
  preferences: Option[UserPreferences] = None
)

object UserProfile {
  given Codec.AsObject[UserProfile] = deriveCodecWithDefaults

  def fromProtobuf(proto: UserProfileMessage): UserProfile = {
    UserProfile(
      id = UserId(proto.userId),
      entityId = EntityId(proto.entityId),
      email = proto.email,
      firstName = proto.firstName,
      lastName = proto.lastName,
      createdAt = proto.createdAt,
      status = proto.status,
      tags = proto.tags.toList,
      metadata = proto.metadata.toMap,
      preferences = proto.preferences.map(UserPreferences.fromProtobuf)
    )
  }

  def toProtobuf(profile: UserProfile): UserProfileMessage = {
    UserProfileMessage(
      userId = profile.id.idString,
      entityId = profile.entityId.idString,
      email = profile.email,
      firstName = profile.firstName,
      lastName = profile.lastName,
      createdAt = InstantMessage.fromInstant(profile.createdAt),
      status = profile.status,
      tags = profile.tags,
      metadata = profile.metadata,
      preferences = profile.preferences.map(UserPreferences.toProtobuf)
    )
  }
}

final case class UserPreferences(
  theme: String,
  notificationsEnabled: Boolean,
  timezone: String
)

object UserPreferences {
  given Codec.AsObject[UserPreferences] = deriveCodecWithDefaults

  def fromProtobuf(proto: anduin.protobuf.user.UserPreferences): UserPreferences = {
    UserPreferences(
      theme = proto.theme,
      notificationsEnabled = proto.notificationsEnabled,
      timezone = proto.timezone
    )
  }

  def toProtobuf(prefs: UserPreferences): anduin.protobuf.user.UserPreferences = {
    anduin.protobuf.user.UserPreferences(
      theme = prefs.theme,
      notificationsEnabled = prefs.notificationsEnabled,
      timezone = prefs.timezone
    )
  }
}
```

### 12.3 Implement Service Layer
```scala
// UserService.scala
package anduin.user.service

import anduin.model.BinaryModel
import anduin.protobuf.user.UserProfileMessage
import zio.{Task, ZIO}

final case class UserService(
  userRepository: UserRepository
) {

  def createUser(profile: UserProfile): Task[UserProfile] = {
    for {
      proto <- ZIO.succeed(UserProfile.toProtobuf(profile))
      bytes <- serializeUser(proto)
      _ <- userRepository.save(profile.id, bytes)
      saved <- getUserById(profile.id)
    } yield saved
  }

  def getUserById(id: UserId): Task[UserProfile] = {
    for {
      bytes <- userRepository.findById(id)
      proto <- deserializeUser(bytes)
    } yield UserProfile.fromProtobuf(proto)
  }

  private def serializeUser(proto: UserProfileMessage): Task[Array[Byte]] = {
    ZIO.fromEither(
      BinaryModel[UserProfileMessage].toByteArray(proto)
        .left.map(ex => new RuntimeException(s"Failed to serialize user: ${ex.message}", ex.cause))
    )
  }

  private def deserializeUser(bytes: Array[Byte]): Task[UserProfileMessage] = {
    ZIO.fromEither(
      BinaryModel[UserProfileMessage].fromByteArray(bytes)
        .left.map(ex => new RuntimeException(s"Failed to deserialize user: ${ex.message}", ex.cause))
    )
  }
}
```

## 13. Common Anti-Patterns to Avoid

### 13.1 Protobuf Anti-Patterns
```protobuf
// ❌ DON'T: Reuse field numbers
message BadUserModel {
  string user_id = 1;
  string old_field = 2; // Later removed
  string new_field = 2; // DON'T reuse field number 2
}

// ❌ DON'T: Change field types
message BadUserModel {
  string user_id = 1;
  int32 age = 2; // Later changed to int64 - breaks compatibility
}

// ❌ DON'T: Use poor field naming
message BadUserModel {
  string userId = 1; // Should be user_id
  string FirstName = 2; // Should be first_name
}

// ❌ DON'T: Skip scalapb options
// Missing package_name and imports

// ✅ DO: Use proper patterns
message GoodUserModel {
  reserved 10, 15; // Reserve removed field numbers
  reserved "old_field_name";

  string user_id = 1 [(scalapb.field).type = "UserId"];
  string email = 2;
  string first_name = 3;
  string last_name = 4;
  // New fields use new numbers
  string phone_number = 5;
}
```

### 13.2 Scala Implementation Anti-Patterns
```scala
// ❌ DON'T: Manual codec derivation
given badCodec: Codec[UserResponse] = Codec.from(
  Decoder.instance { cursor =>
    // Manual decoding - error prone
    for {
      id <- cursor.downField("id").as[String]
      email <- cursor.downField("email").as[String]
    } yield UserResponse(UserId(id), email)
  },
  Encoder.instance { user =>
    // Manual encoding - error prone
    Json.obj("id" -> Json.fromString(user.id.idString))
  }
)

// ✅ DO: Use automatic derivation
given goodCodec: Codec.AsObject[UserResponse] = deriveCodecWithDefaults

// ❌ DON'T: Ignore serialization errors
def badSerialization(message: UserProfileMessage): Array[Byte] = {
  message.toByteArray // Can throw exceptions
}

// ✅ DO: Handle errors properly
def goodSerialization(message: UserProfileMessage): Task[Array[Byte]] = {
  ZIO.fromEither(
    BinaryModel[UserProfileMessage].toByteArray(message)
      .left.map(ex => new RuntimeException(s"Serialization failed: ${ex.message}", ex.cause))
  )
}

// ❌ DON'T: Load large datasets into memory
def badProcessing(): Task[List[UserProfileMessage]] = {
  getAllUsers().map(_.toList) // Could cause OOM
}

// ✅ DO: Use streaming for large datasets
def goodProcessing(): ZStream[Any, Throwable, UserProfileMessage] = {
  getAllUsersStream()
    .grouped(100)
    .flatMap(ZStream.fromIterable)
}
```

### 13.3 Performance Anti-Patterns
```scala
// ❌ DON'T: Use JSON for performance-critical serialization
def slowSerialization(message: UserProfileMessage): String = {
  message.asJson.noSpaces // JSON is slower than protobuf
}

// ✅ DO: Use protobuf for performance-critical paths
def fastSerialization(message: UserProfileMessage): Array[Byte] = {
  message.toByteArray
}

// ❌ DON'T: Serialize in loops without batching
def inefficientBatch(messages: List[UserProfileMessage]): Task[List[Array[Byte]]] = {
  ZIO.foreach(messages) { message =>
    // Individual serialization calls
    ZIO.attempt(message.toByteArray)
  }
}

// ✅ DO: Use parallel processing for batches
def efficientBatch(messages: List[UserProfileMessage]): Task[List[Array[Byte]]] = {
  ZIO.foreachPar(messages) { message =>
    ZIO.attempt(message.toByteArray)
  }
}
```

## 14. Migration and Versioning Strategies

### 14.1 Schema Evolution
```protobuf
// Version 1
message UserModelV1 {
  string user_id = 1;
  string email = 2;
  string name = 3;
}

// Version 2 - Add fields, don't modify existing
message UserModelV2 {
  string user_id = 1;
  string email = 2;
  string name = 3;
  // New fields
  string first_name = 4;
  string last_name = 5;
  UserStatus status = 6;
}

// Version 3 - Deprecate old fields
message UserModelV3 {
  reserved 3; // Deprecated 'name' field
  reserved "name";

  string user_id = 1;
  string email = 2;
  string first_name = 4;
  string last_name = 5;
  UserStatus status = 6;
  // New fields
  repeated string tags = 7;
}
```

### 14.2 Migration Utilities
```scala
object UserModelMigration {

  def migrateV1ToV2(v1: UserModelV1): UserModelV2 = {
    val nameParts = v1.name.split(" ", 2)
    UserModelV2(
      userId = v1.userId,
      email = v1.email,
      name = v1.name, // Keep for backward compatibility
      firstName = nameParts.headOption.getOrElse(""),
      lastName = nameParts.lift(1).getOrElse(""),
      status = UserStatus.USER_STATUS_ACTIVE
    )
  }

  def migrateV2ToV3(v2: UserModelV2): UserModelV3 = {
    UserModelV3(
      userId = v2.userId,
      email = v2.email,
      firstName = v2.firstName,
      lastName = v2.lastName,
      status = v2.status,
      tags = List.empty
    )
  }
}
```

This guide ensures type-safe, performant, and backward-compatible serialization across the codebase while following established patterns and best practices.
