# Configuration Management Implementation Guide for AI Coding Agents

This guide provides comprehensive instructions for implementing configuration management in this Scala-based codebase using ZIO Config and TypeSafe Config.

## 1. Architecture Overview

### Core Components
- **ZIO Config Integration**: Uses `zio-config` library with TypeSafe Config backend
- **Configuration Case Classes**: Strongly-typed configuration with nested structures
- **Environment-Specific Loading**: TypeSafe Config with environment-based configuration files
- **Validation System**: ZIO Config built-in validation and custom `ZIOUtils.validate`
- **Secret Management**: Environment variables and external secret providers
- **Feature Flags**: Service-based feature management with runtime updates

### Key Libraries
```scala
import zio.{Config, ConfigProvider, IO, Task}
import zio.config.*
import zio.config.typesafe.*
import zio.config.magnolia.*
import com.typesafe.config.{Config as TypesafeConfig, ConfigFactory}
import com.anduin.stargazer.service.utils.ZIOUtils
import io.circe.{Codec, Decoder, Encoder}
import anduin.model.common.ModelCodecs.deriveCodecWithDefaults
```

## 2. Configuration Definition Patterns

### 2.1 Basic Configuration Case Classes
```scala
// Define configuration with proper defaults and types
final case class DatabaseConfig(
  host: String,
  port: Int = 5432,
  database: String,
  username: String,
  password: String,
  maxConnections: Int = 10,
  connectionTimeout: Duration = 30.seconds,
  sslMode: String = "require"
)

// Nested configuration structures
final case class ServerConfig(
  hostname: String = "0.0.0.0",
  port: Int = 8080,
  internalPort: Int = 8081,
  timeout: FiniteDuration = 30.seconds,
  enableMetric: Boolean = true
)

final case class AppConfig(
  database: DatabaseConfig,
  server: ServerConfig,
  features: FeatureConfig = FeatureConfig()
)
```

### 2.2 Configuration Derivation
```scala
object AppConfig {
  // Use ZIO Config derivation
  private lazy val deriveAppConfig = deriveConfig[AppConfig]
  private lazy val deriveDatabaseConfig = deriveConfig[DatabaseConfig]
  private lazy val deriveServerConfig = deriveConfig[ServerConfig]
  
  // JSON codec derivation for API responses
  given Codec.AsObject[AppConfig] = deriveCodecWithDefaults
  given Codec.AsObject[DatabaseConfig] = deriveCodecWithDefaults
}
```

## 3. Configuration Loading Patterns

### 3.1 TypeSafe Config Integration
```scala
object ConfigLoader {
  
  def loadAppConfig(): IO[Config.Error, AppConfig] = {
    val typesafeConfig = ConfigFactory.load()
    read {
      deriveConfig[AppConfig] `from` ConfigProvider.fromTypesafeConfig(
        typesafeConfig.getConfig("app")
      )
    }
  }
  
  def loadEnvironmentSpecificConfig(environment: String): IO[Config.Error, AppConfig] = {
    val configFile = s"application-$environment.conf"
    val typesafeConfig = ConfigFactory.load(configFile)
    read {
      deriveConfig[AppConfig] `from` ConfigProvider.fromTypesafeConfig(typesafeConfig)
    }
  }
  
  // Load nested configurations
  def loadGondorConfig(allConfig: TypesafeConfig): IO[Config.Error, GondorConfig] = {
    val gondorConfig = allConfig.getConfig("stargazer")
    for {
      commonConfig <- read {
        deriveConfig[CommonConfig] `from` ConfigProvider.fromTypesafeConfig(
          gondorConfig.getConfig("commonConfig")
        )
      }
      backendConfig <- read {
        deriveConfig[GondorBackendConfig] `from` ConfigProvider.fromTypesafeConfig(
          gondorConfig.getConfig("backendConfig")
        )
      }
    } yield GondorConfig(
      gondorConfig.getString("hostname"),
      gondorConfig.getInt("port"),
      gondorConfig.getInt("internalPort"),
      gondorConfig.getString("endpointHostname"),
      FiniteDuration(gondorConfig.getDuration("timeout").toNanos, TimeUnit.NANOSECONDS),
      commonConfig,
      backendConfig
    )
  }
}
```

### 3.2 Environment Variable Integration
```scala
// Use environment variables for sensitive configuration
final case class DatabaseConfig(
  host: String,
  port: Int = 5432,
  database: String,
  username: String,
  password: String = sys.env.getOrElse("DB_PASSWORD", ""),
  sslMode: String = sys.env.getOrElse("DB_SSL_MODE", "require")
)

// Load secrets from environment
object SecretLoader {
  def loadDatabasePassword(): Task[String] = {
    ZIO.fromOption(sys.env.get("DATABASE_PASSWORD"))
      .orElseFail(new RuntimeException("DATABASE_PASSWORD environment variable not found"))
  }
  
  def loadJwtSecret(): Task[String] = {
    ZIO.fromOption(sys.env.get("JWT_SECRET"))
      .orElseFail(new RuntimeException("JWT_SECRET environment variable not found"))
  }
}
```

## 4. Configuration Validation Patterns

### 4.1 Built-in ZIO Config Validation
```scala
// ZIO Config provides automatic validation for basic types
final case class ServerConfig(
  port: Int,        // Automatically validates as integer
  timeout: Duration // Automatically validates as duration
)

// Custom validation using ZIO Config descriptors
import zio.config.ConfigDescriptor.*

val portDescriptor: ConfigDescriptor[Int] = 
  int("port").validate("Port must be between 1 and 65535")(port => 
    port > 0 && port <= 65535
  )
```

### 4.2 Custom Validation with ZIOUtils
```scala
object ConfigValidator {
  
  def validateDatabaseConfig(config: DatabaseConfig): Task[Unit] = {
    for {
      _ <- ZIOUtils.validate(config.host.nonEmpty) {
        new IllegalArgumentException("Database host cannot be empty")
      }
      _ <- ZIOUtils.validate(config.port > 0 && config.port <= 65535) {
        new IllegalArgumentException(s"Database port ${config.port} must be between 1 and 65535")
      }
      _ <- ZIOUtils.validate(config.username.nonEmpty) {
        new IllegalArgumentException("Database username cannot be empty")
      }
      _ <- ZIOUtils.validate(config.password.length >= 8) {
        new IllegalArgumentException("Database password must be at least 8 characters")
      }
    } yield ()
  }
  
  def validateServerConfig(config: ServerConfig): Task[Unit] = {
    for {
      _ <- ZIOUtils.validate(config.port != config.internalPort) {
        new IllegalArgumentException("Server port and internal port must be different")
      }
      _ <- ZIOUtils.validate(config.timeout.toSeconds > 0) {
        new IllegalArgumentException("Server timeout must be positive")
      }
    } yield ()
  }
}

// Validate during configuration loading
def loadValidatedConfig(): Task[AppConfig] = {
  for {
    config <- ConfigLoader.loadAppConfig().mapError(error => 
      new RuntimeException(s"Configuration loading failed: $error")
    )
    _ <- ConfigValidator.validateDatabaseConfig(config.database)
    _ <- ConfigValidator.validateServerConfig(config.server)
  } yield config
}
```

## 5. Feature Flag Management

### 5.1 Service-Based Feature Flags
```scala
final case class ServiceFeature(gondorConfig: GondorConfig) {
  
  lazy val features: Set[ServiceFeatureId] = gondorConfig.backendConfig.server.serviceFeatures
    .split(",")
    .flatMap(idString => ServiceFeatureId.values.find(_.value == idString.trim))
    .toSet
  
  def hasFeature(feature: ServiceFeatureId): Boolean = {
    features.contains(feature)
  }
  
  def hasServerRunning: Boolean = {
    features.exists(_ != ServiceFeatureId.Initializer)
  }
}

// Usage in business logic
def processRequest(request: Request): Task[Response] = {
  if (serviceFeature.hasFeature(ServiceFeatureId.NewProcessing)) {
    newProcessingLogic(request)
  } else {
    legacyProcessingLogic(request)
  }
}
```

### 5.2 Runtime Feature Flag Configuration
```scala
final case class FeatureConfig(
  enableNewUserFlow: Boolean = false,
  enableAdvancedSearch: Boolean = false,
  maxUploadSize: Long = 10.megabytes.toBytes,
  experimentalFeatures: Set[String] = Set.empty
)

object FeatureFlags {
  private val configRef = Ref.make(FeatureConfig()).runSync
  
  def isEnabled(feature: String): Task[Boolean] = {
    configRef.get.map(_.experimentalFeatures.contains(feature))
  }
  
  def updateConfig(newConfig: FeatureConfig): Task[Unit] = {
    for {
      _ <- configRef.set(newConfig)
      _ <- ZIO.logInfo(s"Feature configuration updated: $newConfig")
    } yield ()
  }
  
  def enableFeature(feature: String): Task[Unit] = {
    configRef.update(config => 
      config.copy(experimentalFeatures = config.experimentalFeatures + feature)
    )
  }
}
```

## 6. Environment-Specific Configuration

### 6.1 Configuration File Structure
```
src/main/resources/
├── application.conf              # Base configuration
├── application-development.conf  # Development overrides
├── application-staging.conf      # Staging overrides
├── application-production.conf   # Production overrides
└── reference.conf               # Default values
```

### 6.2 Environment Detection and Loading
```scala
object EnvironmentConfigLoader {

  def detectEnvironment(): String = {
    sys.env.getOrElse("ENVIRONMENT", "development")
  }

  def loadEnvironmentConfig(): Task[AppConfig] = {
    val environment = detectEnvironment()
    for {
      _ <- ZIO.logInfo(s"Loading configuration for environment: $environment")
      config <- loadEnvironmentSpecificConfig(environment)
      _ <- validateEnvironmentConfig(config, environment)
    } yield config
  }

  private def validateEnvironmentConfig(config: AppConfig, environment: String): Task[Unit] = {
    environment match {
      case "production" =>
        for {
          _ <- ZIOUtils.validate(config.server.enableMetric) {
            new IllegalStateException("Metrics must be enabled in production")
          }
          _ <- ZIOUtils.validate(config.database.sslMode == "require") {
            new IllegalStateException("SSL must be required for database in production")
          }
        } yield ()
      case "development" =>
        ZIO.unit // More lenient validation for development
      case _ =>
        ZIO.unit
    }
  }
}
```

## 7. Secret Management Patterns

### 7.1 Environment Variable Secrets
```scala
final case class SecretConfig(
  databasePassword: String,
  jwtSecret: String,
  apiKeys: Map[String, String]
)

object SecretLoader {
  def loadSecrets(): Task[SecretConfig] = {
    for {
      dbPassword <- loadSecret("DATABASE_PASSWORD")
      jwtSecret <- loadSecret("JWT_SECRET")
      apiKeys <- loadApiKeys()
    } yield SecretConfig(dbPassword, jwtSecret, apiKeys)
  }

  private def loadSecret(key: String): Task[String] = {
    ZIO.fromOption(sys.env.get(key))
      .orElseFail(new RuntimeException(s"Required secret $key not found"))
  }

  private def loadApiKeys(): Task[Map[String, String]] = {
    ZIO.attempt {
      sys.env.toMap
        .filter(_._1.startsWith("API_KEY_"))
        .map { case (key, value) =>
          key.stripPrefix("API_KEY_") -> value
        }
    }
  }
}
```

### 7.2 External Secret Providers
```scala
// Integration with external secret management systems
trait SecretProvider {
  def getSecret(key: String): Task[String]
  def getSecrets(keys: List[String]): Task[Map[String, String]]
}

final case class VaultSecretProvider(
  vaultUrl: String,
  vaultToken: String
) extends SecretProvider {

  def getSecret(key: String): Task[String] = {
    // Implementation for HashiCorp Vault integration
    for {
      response <- httpClient.get(s"$vaultUrl/v1/secret/data/$key")
        .header("X-Vault-Token", vaultToken)
      secret <- ZIO.fromEither(response.body.as[VaultResponse])
        .mapError(error => new RuntimeException(s"Failed to parse vault response: $error"))
    } yield secret.data.data(key)
  }

  def getSecrets(keys: List[String]): Task[Map[String, String]] = {
    ZIO.collectAllPar(keys.map(key => getSecret(key).map(key -> _))).map(_.toMap)
  }
}
```

## 8. Configuration Testing Patterns

### 8.1 Test Configuration Setup
```scala
object TestConfig {

  val testAppConfig: AppConfig = AppConfig(
    database = DatabaseConfig(
      host = "localhost",
      port = 5432,
      database = "test_db",
      username = "test_user",
      password = "test_password"
    ),
    server = ServerConfig(
      hostname = "localhost",
      port = 0, // Random port for tests
      internalPort = 0
    )
  )

  def withTestConfig[R, E, A](effect: ZIO[R & AppConfig, E, A]): ZIO[R, E, A] = {
    effect.provideLayer(ZLayer.succeed(testAppConfig))
  }
}

class ConfigurationSpec extends ZIOSpecDefault {
  def spec = suite("Configuration")(
    test("should load valid configuration") {
      for {
        config <- ConfigLoader.loadAppConfig()
        _ <- assertTrue(config.database.host.nonEmpty)
        _ <- assertTrue(config.server.port > 0)
      } yield ()
    },

    test("should validate configuration constraints") {
      val invalidConfig = AppConfig(
        database = DatabaseConfig(
          host = "",
          port = -1,
          database = "test",
          username = "",
          password = "weak"
        ),
        server = ServerConfig()
      )

      for {
        result <- ConfigValidator.validateDatabaseConfig(invalidConfig.database).exit
        _ <- assertTrue(result.isFailure)
      } yield ()
    },

    test("should handle missing environment variables gracefully") {
      for {
        result <- SecretLoader.loadSecret("NONEXISTENT_SECRET").exit
        _ <- assertTrue(result.isFailure)
      } yield ()
    }
  )
}
```

## 9. ZIO Layer Integration

### 9.1 Configuration as ZIO Service
```scala
// Define configuration as a ZIO service
trait ConfigService {
  def getAppConfig: Task[AppConfig]
  def getDatabaseConfig: Task[DatabaseConfig]
  def getServerConfig: Task[ServerConfig]
}

final case class ConfigServiceLive(appConfig: AppConfig) extends ConfigService {
  def getAppConfig: Task[AppConfig] = ZIO.succeed(appConfig)
  def getDatabaseConfig: Task[DatabaseConfig] = ZIO.succeed(appConfig.database)
  def getServerConfig: Task[ServerConfig] = ZIO.succeed(appConfig.server)
}

object ConfigService {
  val layer: ZLayer[Any, Throwable, ConfigService] = ZLayer {
    for {
      config <- ConfigLoader.loadValidatedConfig()
    } yield ConfigServiceLive(config)
  }

  def getAppConfig: ZIO[ConfigService, Throwable, AppConfig] =
    ZIO.serviceWithZIO[ConfigService](_.getAppConfig)
}
```

### 9.2 Dependent Service Configuration
```scala
// Services that depend on configuration
final case class DatabaseService(config: DatabaseConfig) {
  def connect(): Task[Connection] = ???
}

object DatabaseService {
  val layer: ZLayer[ConfigService, Throwable, DatabaseService] = ZLayer {
    for {
      config <- ZIO.serviceWithZIO[ConfigService](_.getDatabaseConfig)
    } yield DatabaseService(config)
  }
}

// Application assembly
object Application {
  val appLayer: ZLayer[Any, Throwable, DatabaseService & ServerService] =
    ConfigService.layer >>> (DatabaseService.layer ++ ServerService.layer)
}
```

## 10. Best Practices and Conventions

### 10.1 Naming Conventions
- **Configuration Case Classes**: `{Domain}Config` (e.g., `DatabaseConfig`, `ServerConfig`)
- **Configuration Loaders**: `{Domain}ConfigLoader` or `ConfigLoader`
- **Configuration Validators**: `{Domain}ConfigValidator` or `ConfigValidator`
- **Environment Variables**: `UPPER_SNAKE_CASE` (e.g., `DATABASE_PASSWORD`)
- **Configuration Keys**: `camelCase` in Scala, `kebab-case` in HOCON files

### 10.2 File Organization
```
platform/stargazerConfig/
├── shared/src/com/anduin/stargazer/service/
│   ├── GondorConfig.scala           # Main configuration
│   ├── GondorBackendConfig.scala    # Backend-specific config
│   ├── CommonConfig.scala           # Shared configuration
│   └── FeatureConfig.scala          # Feature flags
└── jvm/src/com/anduin/stargazer/apps/stargazer/
    └── StargazerSettings.scala      # Configuration loading
```

### 10.3 Configuration Validation Guidelines
- **Always validate** configuration on application startup
- **Use ZIOUtils.validate** for custom business logic validation
- **Fail fast** on invalid configuration rather than using defaults
- **Log configuration** loading and validation events
- **Separate validation** logic from configuration definition

### 10.4 Environment-Specific Guidelines
- **Use environment variables** for secrets and environment-specific values
- **Provide sensible defaults** for development environments
- **Require explicit configuration** for production environments
- **Validate environment constraints** (e.g., SSL required in production)
- **Use different validation rules** per environment when appropriate

### 10.5 Security Best Practices
- **Never commit secrets** to version control
- **Use environment variables** or external secret providers for sensitive data
- **Validate secret strength** (e.g., minimum password length)
- **Enable security features** by default (SSL, authentication, etc.)
- **Audit configuration access** in production environments

## 11. Integration with Existing Codebase Patterns

### 11.1 TypeSafe Config Integration
```scala
// Follow existing patterns in StargazerSettings.scala
private lazy val deriveCommonConfig = deriveConfig[CommonConfig]
private lazy val deriveBackendConfig = deriveConfig[GondorBackendConfig]

def loadGondorConfig(allConfig: TypesafeConfig): IO[Config.Error, GondorConfig] = {
  val gondorConfig = allConfig.getConfig("stargazer")
  for {
    commonConfig <- read {
      deriveCommonConfig `from` ConfigProvider.fromTypesafeConfig(
        gondorConfig.getConfig("commonConfig")
      )
    }
    backendConfig <- read {
      deriveBackendConfig `from` ConfigProvider.fromTypesafeConfig(
        gondorConfig.getConfig("backendConfig")
      )
    }
  } yield GondorConfig(/* ... */)
}
```

### 11.2 JSON Codec Integration
```scala
// Use existing codec patterns
import anduin.model.common.ModelCodecs.deriveCodecWithDefaults

final case class YourConfig(
  field1: String,
  field2: Int = 42
)

object YourConfig {
  given Codec.AsObject[YourConfig] = deriveCodecWithDefaults
}
```

### 11.3 Validation Integration
```scala
// Use existing ZIOUtils.validate patterns
import com.anduin.stargazer.service.utils.ZIOUtils

def validateYourConfig(config: YourConfig): Task[Unit] = {
  for {
    _ <- ZIOUtils.validate(config.field1.nonEmpty) {
      new IllegalArgumentException("field1 cannot be empty")
    }
    _ <- ZIOUtils.validate(config.field2 > 0) {
      new IllegalArgumentException("field2 must be positive")
    }
  } yield ()
}
```

This guide ensures consistent, secure, and maintainable configuration management that integrates seamlessly with the existing codebase patterns and ZIO ecosystem.
```
