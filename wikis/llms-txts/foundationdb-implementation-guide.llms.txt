# FoundationDB Implementation Guide for AI Coding Agents

This guide provides comprehensive instructions for implementing FoundationDB operations in this Scala-based codebase using the FDB Record Layer with ZIO integration.

## 1. Architecture Overview

FoundationDB is a distributed database designed to handle large volumes of structured data across clusters of commodity servers. This codebase uses the FDB Record Layer, which provides a record-oriented store on top of FoundationDB's key-value interface.

### Core Components
- **FDB Record Layer**: Provides structured record storage with schema management
- **ZIO Integration**: Uses `RecordIO` and `RecordReadIO` effect types for async operations
- **Store Providers**: `FDBRecordStoreProvider` implementations for different record types
- **Operations Layer**: `FDBOperations` trait for consistent operation patterns
- **Transaction Management**: `FDBRecordDatabase.transact()` for transaction boundaries
- **Keyspace Management**: Environment-specific keyspaces (Production/Test)
- **Error Handling**: Automatic retry for transaction conflicts

### Key Libraries
```scala
import anduin.fdb.record.{FDBRecordDatabase, FDBRecordStoreProvider, FDBOperations}
import anduin.fdb.record.model.{RecordIO, RecordReadIO, RecordTask, RecordReadTask}
import com.apple.foundationdb.record.provider.foundationdb.FDBExceptions.FDBStoreTransactionConflictException
import com.apple.foundationdb.record.query.RecordQuery
import com.apple.foundationdb.record.query.expressions.Query
import com.anduin.stargazer.service.utils.ZIOUtils
import zio.{Task, ZIO, Schedule}
```

## 2. Store Provider Implementation Patterns

### 2.1 Basic Store Provider Pattern
```scala
final case class MyRecordStoreProvider(
  override protected val keySpace: FDBRecordKeySpace
) extends FDBRecordStoreProvider[FDBRecordEnum.MyRecord.type](
  FDBRecordEnum.MyRecord,
  MyProtoFileObject
) {

  override protected def recordBuilderFn(builder: RecordMetaDataBuilder): Unit = {
    builder
      .getRecordType(MyRecordModel.scalaDescriptor.name)
      .setPrimaryKey(MyRecordStoreProvider.primaryKeyExpression)
  }

  override protected def indexes: Seq[IndexMappingWithVersion] = Seq(
    MyRecordStoreProvider.statusIndexMapping -> 1,
    MyRecordStoreProvider.timestampIndexMapping -> 2
  )
}

object MyRecordStoreProvider extends FDBStoreProviderCompanion[FDBRecordEnum.MyRecord.type] {
  private val primaryKeyExpression = Key.Expressions.field("id")
  
  private val statusIndexMapping = FDBIndexMapping[String, MyRecordModel, FDBRecordEnum.MyRecord.type](
    index = new Index("status_index", Key.Expressions.field("status")),
    recordModel = MyRecordModel
  )
}
```

### 2.2 Operations Layer Pattern
```scala
trait MyRecordOperations {
  def createRecord(record: MyRecordModel): RecordTask[Unit]
  def getRecord(id: MyRecordId): RecordTask[Option[MyRecordModel]]
  def updateRecord(record: MyRecordModel): RecordTask[Unit]
  def deleteRecord(id: MyRecordId): RecordTask[Unit]
  def queryByStatus(status: String): RecordTask[List[MyRecordModel]]
}

final case class MyRecordOperationsImpl(store: FDBRecordStore[FDBRecordEnum.MyRecord.type])
  extends MyRecordOperations {

  override def createRecord(record: MyRecordModel): RecordTask[Unit] = {
    store.create(record).unit
  }

  override def getRecord(id: MyRecordId): RecordTask[Option[MyRecordModel]] = {
    store.getOpt(id)
  }

  override def updateRecord(record: MyRecordModel): RecordTask[Unit] = {
    store.update(record)
  }

  override def deleteRecord(id: MyRecordId): RecordTask[Unit] = {
    store.delete(id)
  }

  override def queryByStatus(status: String): RecordTask[List[MyRecordModel]] = {
    val query = RecordQuery.newBuilder()
      .setRecordType(MyRecordModel.scalaDescriptor.name)
      .setFilter(Query.field("status").equalsValue(status))
      .setLimit(1000)
      .build()
    
    store.query(query).flatMap(_.runCollect)
  }
}

object MyRecordStoreOperations extends FDBOperations.Single[FDBRecordEnum.MyRecord.type, MyRecordOperations](
  MyRecordStoreProvider
) {
  override def apply(store: FDBRecordStore[FDBRecordEnum.MyRecord.type]): MyRecordOperations = {
    MyRecordOperationsImpl(store)
  }
}
```

## 3. Transaction Management Patterns

### 3.1 Basic Transaction Usage
```scala
// Simple transaction
def createRecord(record: MyRecordModel): Task[Unit] = {
  FDBRecordDatabase.transact(MyRecordStoreOperations.getProviderCached) { ops =>
    ops.createRecord(record)
  }
}

// Transaction with context access
def createRecordWithContext(record: MyRecordModel): Task[Unit] = {
  FDBRecordDatabase.transactC(MyRecordStoreOperations.getProviderCached) { (ctx, ops) =>
    for {
      _ <- RecordIO.succeed(scribe.info(s"Transaction ID: ${ctx.getTransactionId}"))
      _ <- ops.createRecord(record)
    } yield ()
  }
}

// Read-only transaction
def getRecord(id: MyRecordId): Task[Option[MyRecordModel]] = {
  FDBRecordDatabase.transactRead(MyRecordStoreOperations.getProviderCached) { ops =>
    ops.getRecord(id)
  }
}
```

### 3.2 Error Handling and Retry Patterns
```scala
// Automatic retry for transaction conflicts (built into FDBRecordDatabase)
def robustOperation(record: MyRecordModel): Task[Unit] = {
  FDBRecordDatabase.transact(MyRecordStoreOperations.getProviderCached) { ops =>
    ops.createRecord(record)
  } // Automatically retries FDBStoreTransactionConflictException

// Custom error handling
def operationWithErrorHandling(id: MyRecordId): Task[MyRecordModel] = {
  FDBRecordDatabase.transact(MyRecordStoreOperations.getProviderCached) { ops =>
    ops.getRecord(id).flatMap {
      case Some(record) => RecordIO.succeed(record)
      case None => RecordIO.fail(new RuntimeException(s"Record not found: $id"))
    }
  }.catchAll {
    case _: FDBStoreTransactionConflictException => 
      ZIO.logWarning("Transaction conflict, will be retried automatically") *>
      ZIO.fail(new RuntimeException("Transaction conflict"))
    case other => ZIO.fail(other)
  }
}
```

## 4. Batch Processing and Performance Patterns

### 4.1 Controlled Parallelism
```scala
// Process multiple records with controlled parallelism
def processRecordsInParallel(records: List[MyRecordModel]): Task[List[Unit]] = {
  ZIOUtils.foreachParN(parallelism = 8)(records) { record =>
    FDBRecordDatabase.transact(MyRecordStoreOperations.getProviderCached) { ops =>
      ops.createRecord(record)
    }
  }
}

// Batch processing with transaction boundaries
def createRecordsInBatches(records: List[MyRecordModel], batchSize: Int = 100): Task[Unit] = {
  ZIO.foreachDiscard(records.grouped(batchSize)) { batch =>
    FDBRecordDatabase.transact(MyRecordStoreOperations.getProviderCached) { ops =>
      RecordIO.collectAllPar(batch.map(ops.createRecord))
    }
  }
}
```

### 4.2 Large Data Scanning
```scala
// Large scan with continuation
def scanAllRecords(): Task[List[MyRecordModel]] = {
  FDBRecordDatabase.largeScan(
    store = MyRecordStoreProvider.Production,
    mapping = MyRecordMapping,
    tupleRange = TupleRange.ALL,
    fn = (loopCount: Int, records: List[(MyRecordId, MyRecordModel)]) => {
      ZIO.logInfo(s"Processing batch $loopCount with ${records.size} records") *>
      ZIO.succeed(records.map(_._2))
    },
    limit = 1000
  ).map(_.flatten)
}

// Streaming large datasets
def streamRecords(): Task[ZStream[Any, Throwable, MyRecordModel]] = {
  FDBRecordDatabase.largeScanStream(
    store = MyRecordStoreProvider.Production,
    mapping = MyRecordMapping,
    tupleRange = TupleRange.ALL,
    limit = 1000
  ).map(_.map(_._2))
}
```

## 5. Query and Indexing Patterns

### 5.1 Index-Based Queries
```scala
// Query using index
def queryByStatusIndex(status: String): Task[List[MyRecordModel]] = {
  val tupleRange = TupleRange.allOf(Tuple.from(status))
  
  FDBRecordDatabase.largeScanIndex(
    store = MyRecordStoreProvider.Production,
    mapping = MyRecordStoreProvider.statusIndexMapping,
    tupleRange = tupleRange,
    indexScanType = IndexScanType.BY_VALUE,
    fn = (records: List[MyRecordModel]) => ZIO.succeed(records),
    transactionLimit = 1000
  ).map(_.flatten)
}

// Complex query with filters
def complexQuery(status: String, minTimestamp: Instant): Task[List[MyRecordModel]] = {
  FDBRecordDatabase.transact(MyRecordStoreOperations.getProviderCached) { ops =>
    val query = RecordQuery.newBuilder()
      .setRecordType(MyRecordModel.scalaDescriptor.name)
      .setFilter(
        Query.and(
          Query.field("status").equalsValue(status),
          Query.field("timestamp").greaterThan(minTimestamp.toEpochMilli)
        )
      )
      .setLimit(1000)
      .build()
    
    ops.store.query(query).flatMap(_.runCollect)
  }
}
```

## 6. Environment and Keyspace Management

### 6.1 Keyspace Configuration
```scala
// Use environment-specific operations
def getOperations(using keySpace: FDBKeySpaceEnum): FDBOperationProvider[MyRecordOperations] = {
  MyRecordStoreOperations.getProviderCached
}

// Service with keyspace dependency
class MyRecordService(using keySpace: FDBKeySpaceEnum) {
  
  private val operations = MyRecordStoreOperations.getProviderCached
  
  def createRecord(record: MyRecordModel): Task[Unit] = {
    FDBRecordDatabase.transact(operations)(_.createRecord(record))
  }
  
  def getRecord(id: MyRecordId): Task[Option[MyRecordModel]] = {
    FDBRecordDatabase.transactRead(operations)(_.getRecord(id))
  }
}

// Usage in different environments
given productionKeySpace: FDBKeySpaceEnum = FDBKeySpaceEnum.Production
given testKeySpace: FDBKeySpaceEnum = FDBKeySpaceEnum.Test

val productionService = new MyRecordService(using productionKeySpace)
val testService = new MyRecordService(using testKeySpace)
```

## 7. Testing Patterns

### 7.1 Integration Test Setup
```scala
object MyRecordServiceInteg extends YourBaseInteg {

  private given testKeySpace: FDBKeySpaceEnum = FDBKeySpaceEnum.Test
  private val service = new MyRecordService()

  override def spec = suite("MyRecordServiceInteg")(
    test("Should create and retrieve record") {
      for {
        record = MyRecordModel(id = MyRecordId.random(), status = "active")
        _ <- service.createRecord(record)
        retrieved <- service.getRecord(record.id)
      } yield assertTrue(retrieved.contains(record))
    },
    
    test("Should handle batch operations") {
      val records = (1 to 10).map(_ => MyRecordModel(id = MyRecordId.random(), status = "batch")).toList
      for {
        _ <- ZIOUtils.foreachParN(4)(records)(service.createRecord)
        count <- service.countByStatus("batch")
      } yield assertTrue(count >= 10)
    }
  )
}
```

## 8. Best Practices and Anti-Patterns

### 8.1 Best Practices
- **Use FDBOperations.getProviderCached**: Ensures proper keyspace management
- **Leverage automatic retry**: FDBRecordDatabase handles transaction conflicts automatically
- **Control parallelism**: Use ZIOUtils.foreachParN instead of unlimited parallelism
- **Use proper effect types**: RecordIO for writes, RecordReadIO for reads
- **Implement proper indexing**: Create indexes for frequently queried fields
- **Batch operations**: Group multiple operations in single transactions when possible
- **Use large scan operations**: For processing large datasets with continuation

### 8.2 Anti-Patterns to Avoid
- **Don't use unlimited parallelism**: `ZIO.foreachPar` can overwhelm FDB
- **Don't ignore transaction size limits**: Keep transactions under 10MB
- **Don't perform long-running operations in transactions**: Keep transactions short
- **Don't mix keyspaces**: Always use consistent keyspace within operations
- **Don't ignore error handling**: Handle FDB-specific exceptions appropriately
- **Don't scan without limits**: Always use appropriate limits and continuation

## 9. Performance Optimization

### 9.1 Efficient Data Access
```scala
// Use read snapshots for consistent reads
def getConsistentData(id: MyRecordId): Task[Option[MyRecordModel]] = {
  FDBRecordDatabase.transactRead(MyRecordStoreOperations.getProviderCached) { ops =>
    ops.store.get(id, snapshot = true)
  }
}

// Optimize batch reads
def getMultipleRecords(ids: List[MyRecordId]): Task[Map[MyRecordId, MyRecordModel]] = {
  FDBRecordDatabase.transactRead(MyRecordStoreOperations.getProviderCached) { ops =>
    RecordIO.collectAllPar(ids.map { id =>
      ops.getRecord(id).map(id -> _)
    }).map(_.collect { case (id, Some(record)) => id -> record }.toMap)
  }
}
```

## 10. Large Data Handling Patterns

### 10.1 Chunk-Based Storage
```scala
// For large messages that exceed FDB value size limits
import anduin.fdb.subspace.{FDBChunkSubspace, FDBChunkModel}

given chunkModel: FDBChunkModel[MyLargeMessage] = FDBChunkModel.modelInstance

val chunkSubspace = FDBChunkSubspace[MyRecordId, MyLargeMessage](
  subspaceEnum = FDBSubspaceEnum.MyLargeData,
  chunkLimitInBytes = 10240 // 10KB chunks
)

// Store large data
def storeLargeData(id: MyRecordId, data: MyLargeMessage): Task[Unit] = {
  FDBClient.transact(chunkSubspace.set(id, data))
}

// Retrieve large data
def getLargeData(id: MyRecordId): Task[Option[MyLargeMessage]] = {
  FDBClient.read(chunkSubspace.get(id)).map(_.map(_._2))
}
```

### 10.2 Streaming Large Datasets
```scala
// Process large datasets with streaming
def processLargeDataset(processor: MyRecordModel => Task[Unit]): Task[Unit] = {
  for {
    stream <- streamRecords()
    _ <- stream
      .grouped(100) // Process in batches
      .mapZIOPar(4) { batch =>
        ZIO.foreachDiscard(batch)(processor)
      }
      .runDrain
  } yield ()
}

// Memory-efficient aggregation
def aggregateRecords[A](
  initialValue: A,
  aggregator: (A, MyRecordModel) => A
): Task[A] = {
  for {
    stream <- streamRecords()
    result <- stream.runFold(initialValue)(aggregator)
  } yield result
}
```

## 11. Complete Example: User Profile Management

### Step 1: Define Protobuf Model
```protobuf
// user_profile.proto
syntax = "proto3";
import "scalapb/scalapb.proto";

option (scalapb.options) = {
  package_name: "com.example.userprofile"
  single_file: true
};

message UserProfileModel {
  string user_id = 1;
  string email = 2;
  string full_name = 3;
  string status = 4;
  int64 created_at = 5;
  int64 updated_at = 6;
}
```

### Step 2: Store Provider Implementation
```scala
final case class UserProfileStoreProvider(
  override protected val keySpace: FDBRecordKeySpace
) extends FDBRecordStoreProvider[FDBRecordEnum.UserProfile.type](
  FDBRecordEnum.UserProfile,
  UserProfileProto
) {

  override protected def recordBuilderFn(builder: RecordMetaDataBuilder): Unit = {
    builder
      .getRecordType(UserProfileModel.scalaDescriptor.name)
      .setPrimaryKey(UserProfileStoreProvider.primaryKeyExpression)
  }

  override protected def indexes: Seq[IndexMappingWithVersion] = Seq(
    UserProfileStoreProvider.emailIndexMapping -> 1,
    UserProfileStoreProvider.statusIndexMapping -> 2
  )
}

object UserProfileStoreProvider extends FDBStoreProviderCompanion[FDBRecordEnum.UserProfile.type] {
  private val primaryKeyExpression = Key.Expressions.field("user_id")

  private val emailIndexMapping = FDBIndexMapping[String, UserProfileModel, FDBRecordEnum.UserProfile.type](
    index = new Index("email_index", Key.Expressions.field("email")),
    recordModel = UserProfileModel
  )

  private val statusIndexMapping = FDBIndexMapping[String, UserProfileModel, FDBRecordEnum.UserProfile.type](
    index = new Index("status_index", Key.Expressions.field("status")),
    recordModel = UserProfileModel
  )
}
```

### Step 3: Operations Implementation
```scala
trait UserProfileOperations {
  def createProfile(profile: UserProfileModel): RecordTask[Unit]
  def getProfile(userId: String): RecordTask[Option[UserProfileModel]]
  def updateProfile(profile: UserProfileModel): RecordTask[Unit]
  def deleteProfile(userId: String): RecordTask[Unit]
  def findByEmail(email: String): RecordTask[Option[UserProfileModel]]
  def listByStatus(status: String): RecordTask[List[UserProfileModel]]
}

final case class UserProfileOperationsImpl(
  store: FDBRecordStore[FDBRecordEnum.UserProfile.type]
) extends UserProfileOperations {

  override def createProfile(profile: UserProfileModel): RecordTask[Unit] = {
    store.create(profile).unit
  }

  override def getProfile(userId: String): RecordTask[Option[UserProfileModel]] = {
    store.getOpt(userId)
  }

  override def updateProfile(profile: UserProfileModel): RecordTask[Unit] = {
    store.update(profile.copy(updatedAt = Instant.now().toEpochMilli))
  }

  override def deleteProfile(userId: String): RecordTask[Unit] = {
    store.delete(userId)
  }

  override def findByEmail(email: String): RecordTask[Option[UserProfileModel]] = {
    val query = RecordQuery.newBuilder()
      .setRecordType(UserProfileModel.scalaDescriptor.name)
      .setFilter(Query.field("email").equalsValue(email))
      .setLimit(1)
      .build()

    store.query(query).flatMap(_.runCollect.map(_.headOption))
  }

  override def listByStatus(status: String): RecordTask[List[UserProfileModel]] = {
    val tupleRange = TupleRange.allOf(Tuple.from(status))
    store.scanIndexRecords(
      UserProfileStoreProvider.statusIndexMapping,
      IndexScanType.BY_VALUE,
      tupleRange
    ).runCollect
  }
}

object UserProfileStoreOperations extends FDBOperations.Single[
  FDBRecordEnum.UserProfile.type,
  UserProfileOperations
](UserProfileStoreProvider) {

  override def apply(store: FDBRecordStore[FDBRecordEnum.UserProfile.type]): UserProfileOperations = {
    UserProfileOperationsImpl(store)
  }
}
```

### Step 4: Service Layer
```scala
class UserProfileService(using keySpace: FDBKeySpaceEnum) {

  private val operations = UserProfileStoreOperations.getProviderCached

  def createUser(userId: String, email: String, fullName: String): Task[UserProfileModel] = {
    val profile = UserProfileModel(
      userId = userId,
      email = email,
      fullName = fullName,
      status = "active",
      createdAt = Instant.now().toEpochMilli,
      updatedAt = Instant.now().toEpochMilli
    )

    FDBRecordDatabase.transact(operations) { ops =>
      for {
        existing <- ops.findByEmail(email)
        _ <- existing match {
          case Some(_) => RecordIO.fail(new RuntimeException(s"User with email $email already exists"))
          case None => ops.createProfile(profile)
        }
      } yield profile
    }
  }

  def getUser(userId: String): Task[Option[UserProfileModel]] = {
    FDBRecordDatabase.transactRead(operations)(_.getProfile(userId))
  }

  def updateUserStatus(userId: String, newStatus: String): Task[Unit] = {
    FDBRecordDatabase.transact(operations) { ops =>
      for {
        profileOpt <- ops.getProfile(userId)
        profile <- profileOpt match {
          case Some(p) => RecordIO.succeed(p)
          case None => RecordIO.fail(new RuntimeException(s"User $userId not found"))
        }
        updatedProfile = profile.copy(status = newStatus, updatedAt = Instant.now().toEpochMilli)
        _ <- ops.updateProfile(updatedProfile)
      } yield ()
    }
  }

  def bulkUpdateStatus(userIds: List[String], newStatus: String): Task[Unit] = {
    ZIOUtils.foreachParN(8)(userIds.grouped(50).toList) { batch =>
      FDBRecordDatabase.transact(operations) { ops =>
        RecordIO.collectAllPar(batch.map { userId =>
          for {
            profileOpt <- ops.getProfile(userId)
            _ <- profileOpt match {
              case Some(profile) =>
                ops.updateProfile(profile.copy(status = newStatus, updatedAt = Instant.now().toEpochMilli))
              case None =>
                RecordIO.succeed(()) // Skip missing users
            }
          } yield ()
        })
      }
    }.unit
  }

  def getUsersByStatus(status: String): Task[List[UserProfileModel]] = {
    FDBRecordDatabase.transactRead(operations)(_.listByStatus(status))
  }
}
```

### Step 5: Integration Test
```scala
object UserProfileServiceInteg extends YourBaseInteg {

  private given testKeySpace: FDBKeySpaceEnum = FDBKeySpaceEnum.Test
  private val service = new UserProfileService()

  override def spec = suite("UserProfileServiceInteg")(
    test("Should create and retrieve user") {
      for {
        profile <- service.createUser("user123", "<EMAIL>", "Test User")
        retrieved <- service.getUser("user123")
      } yield assertTrue(
        retrieved.isDefined,
        retrieved.get.email == "<EMAIL>",
        retrieved.get.status == "active"
      )
    },

    test("Should prevent duplicate email") {
      for {
        _ <- service.createUser("user1", "<EMAIL>", "User 1")
        result <- service.createUser("user2", "<EMAIL>", "User 2").either
      } yield assertTrue(result.isLeft)
    },

    test("Should update user status") {
      for {
        _ <- service.createUser("user456", "<EMAIL>", "Update User")
        _ <- service.updateUserStatus("user456", "inactive")
        updated <- service.getUser("user456")
      } yield assertTrue(
        updated.isDefined,
        updated.get.status == "inactive"
      )
    },

    test("Should handle bulk operations") {
      val userIds = (1 to 100).map(i => s"bulk_user_$i").toList
      for {
        _ <- ZIO.foreachDiscard(userIds) { userId =>
          service.createUser(userId, s"$<EMAIL>", s"Bulk User $userId")
        }
        _ <- service.bulkUpdateStatus(userIds, "bulk_updated")
        updatedUsers <- service.getUsersByStatus("bulk_updated")
      } yield assertTrue(updatedUsers.size >= 100)
    }
  )
}
```

This guide ensures proper FoundationDB usage, prevents transaction conflicts, and maintains data consistency across the codebase while leveraging ZIO's powerful effect system.
