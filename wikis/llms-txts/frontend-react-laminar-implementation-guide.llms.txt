# Frontend React/Laminar Implementation Guide for AI Coding Agents

This guide provides comprehensive instructions for implementing React and Laminar components in this Scala.js-based codebase with design system integration.

## 1. Architecture Overview

This codebase uses a hybrid approach combining React and Laminar for frontend development:

- **React Components**: Used for complex stateful components and integration with existing React ecosystem
- **Laminar Components**: Used for reactive, functional UI components with excellent performance
- **Design System**: Unified component library with both React (`Component`) and Laminar (`ComponentL`) variants
- **State Management**: Reactive state with Laminar Vars/Signals and React hooks
- **Routing**: Integrated LaminarRouter and ReactRouter for navigation
- **GraphQL Integration**: QueryComponentL for data fetching with caching and polling

### Core Libraries and Imports
```scala
// Laminar core
import com.raquo.laminar.api.L.*
import com.raquo.airstream.core.{Signal, EventStream}

// Design system components
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.icon.{Icon, laminar.IconL}
import design.anduin.components.modal.laminar.ModalL
import design.anduin.components.tooltip.laminar.TooltipL
import design.anduin.style.tw.*

// React integration
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*

// GraphQL and routing
import anduin.graphql.component.context.GraphqlContextL
import anduin.routing.LaminarRouter
import stargazer.component.routing.laminar.LaminarRouterProviderL

// Error handling and utilities
import design.anduin.components.error.ErrorBoundaryProvider
import design.anduin.components.util.ComponentUtils
```

### Application Structure Pattern
```scala
// Main application setup
render(
  container = container,
  rootNode = ErrorBoundaryProvider(
    ToastProvider(
      GraphqlContextL(queryScheduler)(
        LaminarRouterProviderL(router)(
          div(
            child <-- views(router)
          )
        )
      )
    )
  )
)
```

## 2. React Component Implementation Patterns

### 2.1 Functional Components with Hooks
```scala
@react.component
def UserProfile(userId: UserId): VdomElement = {
  val (user, setUser) = React.useState[Option[User]](None)
  val (loading, setLoading) = React.useState(true)
  val (error, setError) = React.useState[Option[String]](None)

  React.useEffect(userId) {
    userService.getUser(userId)
      .onComplete {
        case Success(userData) =>
          setUser(Some(userData))
          setLoading(false)
        case Failure(exception) =>
          setError(Some(exception.getMessage))
          setLoading(false)
      }
  }

  <.div(
    tw.p16.bgWhite.rounded8,
    if (loading) LoadingSpinner()()
    else if (error.isDefined) ErrorMessage(error.get)()
    else user.map(UserDetails(_)).getOrElse(EmptyState()())
  )
}
```

### 2.2 Component Props Pattern
```scala
final case class UserDetailsProps(
  user: User,
  onEdit: User => Callback,
  readonly: Boolean = false,
  className: String = ""
)

@react.component
def UserDetails(props: UserDetailsProps): VdomElement = {
  import props.*
  
  <.div(
    ^.className := s"user-details ${className}",
    tw.p16.borderAll.borderGray3.rounded4,
    <.h2(tw.text20.fontSemibold.mb8, user.name),
    <.p(tw.textGray6.mb12, user.email),
    if (!readonly) 
      Button(
        style = Button.Style.Ghost(),
        onClick = onEdit(user)
      )("Edit")
    else EmptyVdom
  )
}
```

### 2.3 React/Laminar Integration Pattern

**CRITICAL: Common Integration Error Patterns and Solutions**

When integrating React components with Laminar components, follow these patterns exactly to avoid compilation errors:

#### Error Pattern 1: L.render not found
```scala
// ❌ WRONG - L is not available in React component scope
L.render(element, laminarComponent())

// ✅ CORRECT - Import with alias to avoid conflicts
import com.raquo.laminar.api.L.{render as laminarRender, *}

// Then use:
laminarRender(element, laminarComponent())
```

#### Error Pattern 2: child <-- returns DynamicInserter, not Node
```scala
// ❌ WRONG - DynamicInserter can't be returned as Node
def myComponent(): Node = {
  child <-- signal.map { value =>
    div("content")
  }
}

// ✅ CORRECT - Wrap in container element
def myComponent(): Node = {
  div(
    child <-- signal.map { value =>
      div("content") 
    }
  )
}
```

#### Error Pattern 3: Tailwind variables need explicit typing
```scala
// ❌ WRONG - Variable class types are ambiguous
val bgClass = if (isSuccess) tw.bgSuccess1 else tw.bgGray1
div(bgClass, "content")

// ✅ CORRECT - Convert to explicit HtmlMod
val bgClass = if (isSuccess) tw.bgSuccess1.toHtmlMod else tw.bgGray1.toHtmlMod
div(bgClass, "content")
```

#### Complete Integration Example
```scala
// CORRECT way to integrate React wrapper with Laminar component
private[mypackage] final case class MyReactWrapper(
  data: MyData,
  onUpdate: MyData => Callback
) {

  def apply(): VdomElement = MyReactWrapper.component(this)
}

private[mypackage] object MyReactWrapper {

  private type Props = MyReactWrapper

  private class Backend(scope: BackendScope[Props, Unit]) {

    def render(props: Props): VdomElement = {
      MountWrapperR(
        onMount = { element =>
          Callback {
            val laminarComponent = MyLaminarComponent(
              data = props.data,
              onUpdate = Observer(data => props.onUpdate(data).runNow())
            )
            // CRITICAL: Use aliased render method
            laminarRender(element, laminarComponent())
          }
        },
        onUnmount = { _ => Callback.empty },
        renderChildren = renderProps => <.div.withRef(renderProps.ref)()
      )()
    }
  }

  private val component = ScalaComponent
    .builder[MyReactWrapper](getClass.getSimpleName)
    .stateless
    .renderBackend[Backend]
    .build

  // Laminar component definition within the same file
  private final case class MyLaminarComponent(
    data: MyData,
    onUpdate: Observer[MyData]
  ) {

    def apply(): HtmlElement = {
      div(
        // CRITICAL: Always wrap child <-- in container
        child <-- dataVar.signal.map { currentData =>
          div(
            className := "my-component",
            renderDataDisplay(currentData)
          )
        }
      )
    }

    private def renderDataDisplay(data: MyData): Node = {
      // CRITICAL: Use .toHtmlMod for dynamic Tailwind classes
      val statusClass = data.status match {
        case Status.Success => tw.bgSuccess1.toHtmlMod
        case Status.Error => tw.bgDanger1.toHtmlMod
        case Status.Warning => tw.bgWarning1.toHtmlMod
      }
      
      div(
        statusClass,
        tw.p16.rounded8,
        data.message
      )
    }
  }
}
```

### 2.4 React/Laminar Integration Pattern
final case class DashboardReactWrapper(
  data: Dashboard.Data,
  router: Router,
  updateCallback: Dashboard.Data => Callback
) {

  private case class Backend(scope: BackendScope[Props, Unit]) {
    private val ref = react.Ref[dom.html.Div]
    private var dataVar: Option[Var[Dashboard.Data]] = None
    private var rootNode: L.RootNode = scala.compiletime.uninitialized

    def mount: Callback = {
      for {
        props <- scope.props
        _ <- ref.foreach { ele =>
          val ourDataVar = Var(props.data)
          dataVar = Option(ourDataVar)
          rootNode = L.render(
            ele,
            Dashboard(
              dataVar = ourDataVar,
              router = props.router,
              updateCallback = props.updateCallback
            )()
          )
        }
      } yield ()
    }

    def unmount: Callback = Callback(rootNode.unmount())

    def updateNodeData(): Callback = {
      for {
        props <- scope.props
        _ <- Callback {
          dataVar.foreach { curVar =>
            if (curVar.now() != props.data) {
              curVar.set(props.data)
            }
          }
        }
      } yield ()
    }
  }

  private val component = ScalaComponent
    .builder[Props](getClass.getSimpleName)
    .stateless
    .renderBackend[Backend]
    .componentDidMount(_.backend.mount)
    .componentWillUnmount(_.backend.unmount)
    .componentDidUpdate { scope =>
      Callback.when(scope.currentProps.data != scope.prevProps.data) {
        scope.backend.updateNodeData()
      }
    }
    .build
}
```

## 3. Laminar Component Implementation Patterns

### 3.1 Basic Component Structure
```scala
def userProfileComponent(userId: UserId): Div = {
  val userVar = Var[Option[User]](None)
  val loadingVar = Var(true)
  val errorVar = Var[Option[String]](None)

  // Load user data
  val loadUserEffect = userService.getUser(userId)
    .onComplete {
      case Success(user) =>
        userVar.set(Some(user))
        loadingVar.set(false)
      case Failure(error) =>
        errorVar.set(Some(error.getMessage))
        loadingVar.set(false)
    }

  div(
    ComponentUtils.testIdL("UserProfile"),
    tw.p16.bgWhite.rounded8.shadow,
    onMountCallback(_ => loadUserEffect),
    child <-- loadingVar.signal.map { loading =>
      if (loading) loadingSpinner()
      else userVar.signal.map {
        case Some(user) => userDetailsComponent(user)
        case None => emptyStateComponent()
      }
    }.flatten
  )
}
```

### 3.2 Signal Composition and List Rendering
```scala
def userListComponent(users: Signal[List[User]]): Div = {
  div(
    ComponentUtils.testIdL("UserList"),
    tw.spaceY8,
    children <-- users.split(_.id) { (userId, userSignal) =>
      userItemComponent(userId, userSignal)
    }
  )
}

def userItemComponent(userId: UserId, userSignal: Signal[User]): Div = {
  div(
    ComponentUtils.testIdL("UserItem"),
    tw.p12.borderAll.borderGray3.rounded4.cursorPointer.hoverBgGray1,
    child.text <-- userSignal.map(_.name),
    onClick --> { _ =>
      navigateToUser(userId)
    }
  )
}
```

### 3.3 Advanced State Management
```scala
object UserStore {
  private val usersVar = Var(Map.empty[UserId, User])
  private val loadingVar = Var(Set.empty[UserId])
  private val errorVar = Var(Map.empty[UserId, String])
  
  val users: Signal[Map[UserId, User]] = usersVar.signal
  val loading: Signal[Set[UserId]] = loadingVar.signal
  val errors: Signal[Map[UserId, String]] = errorVar.signal
  
  def getUser(userId: UserId): Signal[Option[User]] = {
    users.map(_.get(userId))
  }
  
  def getUserWithStatus(userId: UserId): Signal[UserStatus] = {
    Signal.combine(
      getUser(userId),
      loading.map(_.contains(userId)),
      errors.map(_.get(userId))
    ).map { case (userOpt, isLoading, errorOpt) =>
      if (isLoading) UserStatus.Loading
      else if (errorOpt.isDefined) UserStatus.Error(errorOpt.get)
      else userOpt.map(UserStatus.Loaded).getOrElse(UserStatus.NotFound)
    }
  }
  
  def loadUser(userId: UserId): Future[Unit] = {
    if (!loadingVar.now().contains(userId)) {
      loadingVar.update(_ + userId)
      errorVar.update(_ - userId)
      
      userService.getUser(userId)
        .onComplete {
          case Success(user) =>
            usersVar.update(_ + (userId -> user))
            loadingVar.update(_ - userId)
          case Failure(error) =>
            errorVar.update(_ + (userId -> error.getMessage))
            loadingVar.update(_ - userId)
        }
    } else {
      Future.successful(())
    }
  }
}

enum UserStatus {
  case Loading
  case Loaded(user: User)
  case Error(message: String)
  case NotFound
}

## 4. Event Handling and Form Patterns

### 4.1 Form Handling with Validation
```scala
def userFormComponent(onSubmit: User => Unit): Form = {
  val nameVar = Var("")
  val emailVar = Var("")
  val validationVar = Var[Option[String]](None)

  def validateForm(name: String, email: String): Either[String, User] = {
    if (name.trim.isEmpty) Left("Name is required")
    else if (email.trim.isEmpty) Left("Email is required")
    else if (!email.contains("@")) Left("Invalid email format")
    else Right(User(name.trim, email.trim))
  }

  form(
    ComponentUtils.testIdL("UserForm"),
    tw.spaceY16.p16,
    onSubmit.preventDefault --> { _ =>
      validateForm(nameVar.now(), emailVar.now()) match {
        case Right(user) =>
          validationVar.set(None)
          onSubmit(user)
        case Left(error) =>
          validationVar.set(Some(error))
      }
    },
    div(
      label(tw.block.textSm.fontMedium.mb4, "Name"),
      input(
        ComponentUtils.testIdL("NameInput"),
        tw.wPc100.px12.py8.borderAll.borderGray3.rounded4,
        placeholder := "Enter your name",
        value <-- nameVar.signal,
        onInput.mapToValue --> nameVar.writer
      )
    ),
    div(
      label(tw.block.textSm.fontMedium.mb4, "Email"),
      input(
        ComponentUtils.testIdL("EmailInput"),
        tw.wPc100.px12.py8.borderAll.borderGray3.rounded4,
        placeholder := "Enter your email",
        tpe := "email",
        value <-- emailVar.signal,
        onInput.mapToValue --> emailVar.writer
      )
    ),
    child <-- validationVar.signal.map {
      case Some(error) =>
        div(
          tw.p12.bgRed1.borderAll.borderRed3.rounded4.textRed7,
          error
        )
      case None => emptyNode
    },
    ButtonL(
      style = ButtonL.Style.Full(),
      tpe = ButtonL.Type.Submit
    )("Submit")
  )
}
```

### 4.2 Event Bus Communication
```scala
def parentComponent(): Div = {
  val messageEventBus = new EventBus[String]
  val messagesVar = Var[List[String]](Nil)

  div(
    ComponentUtils.testIdL("ParentComponent"),
    tw.spaceY16,
    messageEventBus.events --> { message =>
      messagesVar.update(_ :+ message)
    },
    childComponent(messageEventBus.writer),
    div(
      tw.p16.bgGray1.rounded4,
      h3(tw.textLg.fontSemibold.mb8, "Messages:"),
      children <-- messagesVar.signal.map { messages =>
        messages.map(msg => div(tw.py4.borderBottom.borderGray3, msg))
      }
    )
  )
}

def childComponent(onMessage: Observer[String]): Div = {
  val inputVar = Var("")

  div(
    ComponentUtils.testIdL("ChildComponent"),
    tw.flex.spaceX8,
    input(
      tw.flexGrow.px12.py8.borderAll.borderGray3.rounded4,
      placeholder := "Type a message...",
      value <-- inputVar.signal,
      onInput.mapToValue --> inputVar.writer,
      onKeyDown.filter(_.keyCode == KeyCode.Enter) --> { _ =>
        val message = inputVar.now().trim
        if (message.nonEmpty) {
          onMessage.onNext(message)
          inputVar.set("")
        }
      }
    ),
    ButtonL(
      style = ButtonL.Style.Full(),
      onClick = { _ =>
        val message = inputVar.now().trim
        if (message.nonEmpty) {
          onMessage.onNext(message)
          inputVar.set("")
        }
      }
    )("Send")
  )
}
```

## 5. Resource Management and Cleanup

### 5.1 WebSocket Management
```scala
def webSocketComponent(url: String): Div = {
  val messageVar = Var[List[String]](Nil)
  val connectionVar = Var[Option[WebSocket]](None)
  val statusVar = Var[ConnectionStatus](ConnectionStatus.Disconnected)

  def connectWebSocket(): Unit = {
    statusVar.set(ConnectionStatus.Connecting)
    val ws = new WebSocket(url)

    ws.onopen = { _ =>
      statusVar.set(ConnectionStatus.Connected)
      connectionVar.set(Some(ws))
    }

    ws.onmessage = { event =>
      messageVar.update(_ :+ event.data.toString)
    }

    ws.onerror = { _ =>
      statusVar.set(ConnectionStatus.Error)
    }

    ws.onclose = { _ =>
      statusVar.set(ConnectionStatus.Disconnected)
      connectionVar.set(None)
    }
  }

  def disconnectWebSocket(): Unit = {
    connectionVar.now().foreach(_.close())
    connectionVar.set(None)
    statusVar.set(ConnectionStatus.Disconnected)
  }

  div(
    ComponentUtils.testIdL("WebSocketComponent"),
    tw.p16.spaceY16,
    onMountCallback(_ => connectWebSocket()),
    onUnmountCallback(_ => disconnectWebSocket()),

    // Status indicator
    div(
      tw.flex.itemsCenter.spaceX8,
      div(
        tw.wPx12.hPx12.rounded50,
        statusVar.signal.map {
          case ConnectionStatus.Connected => tw.bgGreen5
          case ConnectionStatus.Connecting => tw.bgYellow5
          case ConnectionStatus.Error => tw.bgRed5
          case ConnectionStatus.Disconnected => tw.bgGray5
        }
      ),
      child.text <-- statusVar.signal.map(_.toString)
    ),

    // Messages
    div(
      tw.hPx200.overflowYAuto.borderAll.borderGray3.rounded4.p12,
      children <-- messageVar.signal.map { messages =>
        messages.map(msg => div(tw.py4.borderBottom.borderGray3, msg))
      }
    )
  )
}

enum ConnectionStatus {
  case Connected, Connecting, Disconnected, Error
}
```

### 5.2 Timer and Interval Management
```scala
def timerComponent(): Div = {
  val timeVar = Var(0)
  val intervalVar = Var[Option[SetIntervalHandle]](None)
  val isRunningVar = Var(false)

  def startTimer(): Unit = {
    if (!isRunningVar.now()) {
      val handle = js.timers.setInterval(1000) {
        timeVar.update(_ + 1)
      }
      intervalVar.set(Some(handle))
      isRunningVar.set(true)
    }
  }

  def stopTimer(): Unit = {
    intervalVar.now().foreach(js.timers.clearInterval)
    intervalVar.set(None)
    isRunningVar.set(false)
  }

  def resetTimer(): Unit = {
    stopTimer()
    timeVar.set(0)
  }

  div(
    ComponentUtils.testIdL("TimerComponent"),
    tw.p16.textCenter.spaceY16,
    onUnmountCallback(_ => stopTimer()),

    div(
      tw.text4xl.fontMono.fontBold,
      child.text <-- timeVar.signal.map { seconds =>
        val minutes = seconds / 60
        val secs = seconds % 60
        f"$minutes%02d:$secs%02d"
      }
    ),

    div(
      tw.flex.justifyCenter.spaceX8,
      ButtonL(
        style = ButtonL.Style.Full(),
        disabled <-- isRunningVar.signal,
        onClick = { _ => startTimer() }
      )("Start"),
      ButtonL(
        style = ButtonL.Style.Ghost(),
        disabled <-- isRunningVar.signal.map(!_),
        onClick = { _ => stopTimer() }
      )("Stop"),
      ButtonL(
        style = ButtonL.Style.Ghost(),
        onClick = { _ => resetTimer() }
      )("Reset")
    )
  )
}

## 6. Performance Optimization Patterns

### 6.1 Efficient List Rendering with Split
```scala
def optimizedListComponent(items: Signal[List[Item]]): Div = {
  // Use distinct to avoid unnecessary recomputations
  val processedItems = items.map { itemList =>
    itemList.map(expensiveProcessing).sortBy(_.priority)
  }.distinct

  div(
    ComponentUtils.testIdL("OptimizedList"),
    tw.spaceY8,
    // Use split for efficient list rendering - only re-renders changed items
    children <-- processedItems.split(_.id) { (itemId, itemSignal) =>
      itemComponent(itemId, itemSignal)
    }
  )
}

def itemComponent(itemId: ItemId, itemSignal: Signal[Item]): Div = {
  div(
    ComponentUtils.testIdL("ListItem"),
    tw.p12.borderAll.borderGray3.rounded4,
    // Use derived signals to minimize re-renders
    child.text <-- itemSignal.map(_.name).distinct,
    child <-- itemSignal.map(_.status).distinct.map { status =>
      div(
        tw.mt8,
        statusBadge(status)
      )
    }
  )
}
```

### 6.2 Conditional Rendering Optimization
```scala
def conditionalComponent(showDetails: Signal[Boolean], user: Signal[User]): Div = {
  div(
    ComponentUtils.testIdL("ConditionalComponent"),
    tw.spaceY16,

    // Efficient conditional rendering
    child <-- showDetails.splitBoolean(
      whenTrue = _ => user.map(userDetailsComponent),
      whenFalse = _ => userSummaryComponent(user)
    ),

    // Alternative pattern for complex conditions
    child <-- Signal.combine(showDetails, user).map { case (show, userData) =>
      if (show) userDetailsComponent(userData)
      else userSummaryComponent(Signal.fromValue(userData))
    }
  )
}
```

## 7. GraphQL Integration Patterns

### 7.1 QueryComponentL Usage
```scala
def userDataComponent(userId: UserId): Div = {
  val userIdVar = Var(userId)

  QueryComponentL(
    query = UserQueries.GetUser,
    variableSignal = userIdVar.signal.map(GetUserVariables(_)),
    options = GraphqlOptions(
      pollInterval = 30.seconds,
      requestTimeout = 10.seconds
    ),
    fetchStrategy = FetchStrategy.CacheFirst,
    initialFetchStrategy = FetchStrategy.NetworkFirst
  ) { queryData =>
    div(
      ComponentUtils.testIdL("UserDataComponent"),
      tw.p16,
      child <-- queryData.data.map {
        case Some(userData) => userDisplayComponent(userData)
        case None => loadingComponent()
      },

      // Refresh button
      ButtonL(
        style = ButtonL.Style.Ghost(),
        onClick = { _ => queryData.forceFetch.onNext(()) }
      )("Refresh")
    )
  }
}
```

### 7.2 Error Handling with GraphQL
```scala
def dataComponentWithErrorHandling[T](
  query: BaseQuery,
  variables: Signal[T],
  renderData: T => Node
): Div = {
  val errorVar = Var[Option[String]](None)

  div(
    ComponentUtils.testIdL("DataComponent"),
    QueryComponentL(
      query = query,
      variableSignal = variables,
      options = GraphqlOptions(requestTimeout = 15.seconds)
    ) { queryData =>
      div(
        child <-- queryData.data.map {
          case Some(data) =>
            errorVar.set(None)
            renderData(data)
          case None =>
            div(
              tw.flex.itemsCenter.justifyCenter.p16,
              LoadingSpinner()()
            )
        }
      )
    },

    // Error display
    child <-- errorVar.signal.map {
      case Some(error) =>
        div(
          tw.p12.bgRed1.borderAll.borderRed3.rounded4.textRed7.mt16,
          IconL(Icon.Glyph.Warning, size = Icon.Size.Px16)(),
          span(tw.ml8, error)
        )
      case None => emptyNode
    }
  )
}
```

## 8. Design System Integration

The Anduin Design System provides dual implementations for all components:
- **React components**: Located in `react/` subdirectories, suffixed with `R` (e.g., `ButtonR`, `TextBoxR`)
- **Laminar components**: Located in `laminar/` subdirectories, suffixed with `L` (e.g., `ButtonL`, `TextBoxL`)

Both implementations share the same design principles and APIs but have framework-specific patterns for state management, event handling, and rendering.

### 8.1 Component Overview and Import Patterns

#### React Components
```scala
// Main component import
import design.anduin.components.button.Button
import design.anduin.components.textbox.TextBox
import design.anduin.components.modal.Modal

// Common utilities and types
import design.anduin.components.icon.Icon
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*

// React-specific imports
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
```

#### Laminar Components
```scala
// Laminar component import
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.components.modal.laminar.ModalL

// Laminar-specific imports
import com.raquo.laminar.api.L.*
import com.raquo.laminar.nodes.ReactiveHtmlElement.Base

// Common utilities and types
import design.anduin.components.icon.Icon
import design.anduin.components.util.ComponentUtils
import design.anduin.style.tw.*
```

### 8.2 Button Component

#### React Usage
```scala
// Basic button
Button(
  style = Button.Style.Full(color = Button.Color.Primary),
  onClick = Callback { /* handle click */ }
)("Click me")

// Button with icon
Button(
  style = Button.Style.Full(
    icon = Some(Icon.Glyph.Plus),
    height = Button.Height.Fix32
  ),
  isDisabled = false,
  testId = "my-button"
)("Add Item")

// Link button
Button(
  tpe = Button.Tpe.Link(href = "/path", target = Button.Target.Blank),
  style = Button.Style.Text(color = Button.Color.Primary)
)("Visit Link")
```

#### Laminar Usage
```scala
// Basic button
ButtonL(
  style = ButtonL.Style.Full(color = ButtonL.Color.Primary),
  onClick = Observer[dom.MouseEvent] { _ => /* handle click */ }
)("Click me")

// Button with reactive state
val isLoading = Var(false)
ButtonL(
  style = ButtonL.Style.Full(
    icon = Some(Icon.Glyph.Plus),
    isBusy = isLoading.signal
  ),
  isDisabled = isLoading.signal
)("Add Item")

// Button with modifiers
ButtonL(
  style = ButtonL.Style.Ghost(),
  testId = "my-button"
)(
  span("Custom content"),
  className := "extra-class"
)
```

### 8.3 TextBox Component

#### React Usage
```scala
// Basic text input
val (value, setValue) = React.useState("")
TextBox(
  value = value,
  onChange = setValue,
  placeholder = "Enter text",
  size = TextBox.Size.Px32
)()

// TextBox with validation status
TextBox(
  value = email,
  onChange = setEmail,
  tpe = TextBox.Tpe.EmailNative,
  status = if (isValid) TextBox.Status.Valid else TextBox.Status.Invalid,
  icon = Some(Icon.Glyph.Mail),
  onClear = Some(Callback { setEmail("") })
)()

// Currency input with masking
TextBox(
  value = amount,
  onChange = setAmount,
  tpe = TextBox.Tpe.Currency(
    currencySymbol = "$",
    allowNegative = false,
    decimalLimit = 2
  )
)()
```

#### Laminar Usage
```scala
// Basic text input with reactive state
val textVar = Var("")
TextBoxL(
  value = textVar.signal,
  onChange = textVar.writer,
  placeholder = "Enter text",
  size = TextBoxL.Size.Px32
)()

// TextBox with validation
val statusVar = Var(TextBoxL.Status.None)
val isDisabled = Var(false)

TextBoxL(
  value = emailVar.signal,
  onChange = emailVar.writer,
  tpe = TextBoxL.Tpe.EmailNative,
  status = statusVar.signal,
  icon = Some(Icon.Glyph.Mail),
  isDisabled = isDisabled.signal,
  onClear = Some(Observer[Unit] { _ => emailVar.set("") })
)()

// Currency input with masking
TextBoxL(
  value = amountVar.signal,
  onChange = amountVar.writer,
  tpe = TextBoxL.Tpe.Currency(
    prefix = "$",
    allowNegative = false,
    decimalLimit = 2
  )
)()

// Number input with constraints
TextBoxL(
  value = numberVar.signal,
  onChange = numberVar.writer,
  tpe = TextBoxL.Tpe.NumberFloat(
    decimalLimit = 2,
    min = 0,
    max = 1000000
  )
)()
```

### 8.4 Modal Component

#### React Usage
```scala
// Basic modal with title and content
Modal(
  title = "Hello World",
  renderTarget = open => Button(onClick = open)("Open Modal"),
  renderContent = close => React.Fragment(
    ModalBody()("This is the modal content"),
    ModalFooterWCancel(cancel = close)(
      Button(
        style = Button.Style.Full(color = Button.Color.Primary),
        onClick = close
      )("Save")
    )
  )
)()

// Controlled modal
val (isOpen, setIsOpen) = React.useState(false)
Modal(
  title = "Controlled Modal",
  isOpened = Some(isOpen),
  afterUserClose = setIsOpen(false),
  renderTarget = _ => Button(onClick = setIsOpen(true))("Open"),
  renderContent = _ => ModalBody()("Content")
)()

// Modal without close button (use with caution)
Modal(
  // No title means no default header/close button
  renderTarget = open => Button(onClick = open)("Open"),
  renderContent = close => React.Fragment(
    ModalBody()(
      p(tw.fontBold.text17.mb12, "Are you sure?"),
      p("This action cannot be undone.")
    ),
    ModalFooterWCancel(cancel = close)(
      Button(
        style = Button.Style.Full(color = Button.Color.Danger),
        onClick = close
      )("Delete")
    )
  )
)()
```

#### Laminar Usage
```scala
val isModalOpen = Var(false)

ModalL(
  renderTarget = toggle => ButtonL(
    onClick = toggle.contramap(_ => ())
  )("Open Modal"),
  renderContent = close => div(
    "Modal content goes here",
    ButtonL(
      onClick = close.contramap(_ => ())
    )("Close")
  ),
  renderTitle = props => "Modal Title",
  size = ModalL.Size(
    width = ModalL.Width.Px600,
    height = ModalL.Height.Content
  ),
  isOpened = Some(isModalOpen.signal),
  afterUserClose = Observer[Unit] { _ => isModalOpen.set(false) }
)()
```

### 8.5 Dropdown Component

#### React Usage
```scala
// Initialize generic dropdown first
case class Character(name: String, race: String)
val CharacterDropdown = (new Dropdown[Character])()

// Basic usage
val (selectedCharacter, setSelectedCharacter) = React.useState(
  Option.empty[Character]
)

CharacterDropdown(
  value = selectedCharacter,
  valueToString = _.name,
  onChange = char => setSelectedCharacter(Some(char)),
  items = List(
    Dropdown.Item(Character("Gandalf", "Wizard")),
    Dropdown.Item(Character("Aragorn", "Ranger")),
    Dropdown.Item(Character("Legolas", "Elf"))
  ),
  button = Dropdown.Button(
    placeholder = span(tw.textGray6, "Choose a character")
  )
)()

// With clear functionality
CharacterDropdown(
  value = selectedCharacter,
  valueToString = _.name,
  onChange = char => setSelectedCharacter(Some(char)),
  onClear = setSelectedCharacter(None),
  items = characterList.map(Dropdown.Item(_))
)()

// With custom rendering
CharacterDropdown(
  value = selectedCharacter,
  valueToString = _.name,
  onChange = setSelectedCharacter,
  items = characterList.map(Dropdown.Item(_)),
  button = Dropdown.Button(
    renderValue = Some(char =>
      React.Fragment(
        IconR(name = getCharacterIcon(char))(),
        span(tw.ml6, char.name)
      )
    )
  ),
  menu = Dropdown.Menu(
    renderItemBody = Some(item =>
      React.Fragment(
        IconR(name = getCharacterIcon(item.value))(),
        span(tw.ml6, item.value.name),
        span(tw.ml2.textGray6, s"(${item.value.race})")
      )
    )
  )
)()
```

#### Laminar Usage
```scala
case class Option(id: String, label: String)
val options = Seq(
  Option("1", "Option 1"),
  Option("2", "Option 2")
)

val selectedOption = Var(Option[Option](None))

DropdownL(
  value = selectedOption.signal,
  valueToString = _.label,
  onChange = selectedOption.writer,
  items = options.map(DropdownL.Item(_)),
  target = DropdownL.Target(
    placeholder = span("Select option..."),
    appearance = DropdownL.Appearance.Full(isFullWidth = true)
  ),
  onClear = Observer[Unit] { _ => selectedOption.set(None) }
)()
```

### 8.6 Accordion Component

#### React Usage
```scala
AccordionR(
  items = List(
    AccordionR.Item(
      renderHeader = <.div(tw.heading3, "First item"),
      renderContent = <.div("Body of the first item")
    ),
    AccordionR.Item(
      renderHeader = <.div(tw.heading3, "Second item"),
      renderContent = <.div("Body of the second item")
    )
  )
)()

// With initial active item
AccordionR(
  initialActiveItem = Option(1), // Zero-indexed
  items = accordionItems
)()

// Fit container
AccordionR(
  fitContainer = true,
  items = accordionItems
)()
```

#### Laminar Usage
```scala
AccordionL(
  items = List(
    AccordionL.Item(
      renderHeader = div(tw.heading3, "First item"),
      renderContent = div("Body of the first item")
    ),
    AccordionL.Item(
      renderHeader = div(tw.heading3, "Second item"),
      renderContent = div("Body of the second item")
    )
  )
)()

// With initial active item and container fitting
AccordionL(
  initialActiveItem = Option(2),
  fitContainer = true,
  items = accordionItems
)().amend(
  height := "800px"
)
```

### 8.7 Checkbox Component

#### React Usage
```scala
// Basic checkbox
val (isChecked, setIsChecked) = React.useState(false)
Checkbox(
  isChecked = isChecked,
  onChange = setIsChecked
)("Remember me")

// Disabled states
Checkbox(isChecked = true, isDisabled = true)("Checked & disabled")
Checkbox(isChecked = false, isDisabled = true)("Unchecked & disabled")

// Read-only states
Checkbox(isChecked = true, isReadOnly = true)("Checked & read-only")
Checkbox(isChecked = false, isReadOnly = true)("Unchecked & read-only")

// Indeterminate state (for parent checkboxes)
Checkbox(
  isChecked = someChildrenSelected,
  isIndeterminate = hasPartialSelection,
  onChange = toggleAllChildren
)("Select All")
```

#### Laminar Usage
```scala
// Basic checkbox with reactive state
val isCheckedVar = Var(false)
CheckboxL(
  isChecked = isCheckedVar.signal,
  onChange = isCheckedVar.writer
)("Remember me")

// Disabled and read-only states
val isDisabled = Var(true)
CheckboxL(
  isChecked = checkedVar.signal,
  onChange = checkedVar.writer,
  isDisabled = isDisabled.signal
)("Disabled checkbox")

CheckboxL(
  isChecked = checkedVar.signal,
  onChange = checkedVar.writer,
  isReadOnly = true
)("Read-only checkbox")

// Indeterminate with complex state
val itemsVar = Var(List.empty[SelectableItem])
val allSelected = itemsVar.signal.map(_.forall(_.isSelected))
val someSelected = itemsVar.signal.map(_.exists(_.isSelected))
val isIndeterminate = Signal.combine(allSelected, someSelected).map {
  case (false, true) => true
  case _ => false
}

CheckboxL(
  isChecked = allSelected,
  isIndeterminate = isIndeterminate,
  onChange = Observer[Boolean] { selectAll =>
    itemsVar.update(_.map(_.copy(isSelected = selectAll)))
  }
)("Select All")
```

### 8.8 Radio Component

#### React Usage
```scala
// Radio group
val (selectedValue, setSelectedValue) = React.useState("Apple")

val fruits = List("Apple", "Orange", "Banana")
fruits.toVdomArray { fruit =>
  <.div(^.key := fruit, tw.mr24)(
    Radio(
      isChecked = selectedValue == fruit,
      onChange = setSelectedValue(fruit)
    )(fruit)
  )
}

// Disabled radio buttons
Radio(isChecked = true, onChange = Callback.empty, isDisabled = true)("Checked")
Radio(isChecked = false, onChange = Callback.empty, isDisabled = true)("Unchecked")

// Read-only radio buttons
Radio(isChecked = true, onChange = Callback.empty, isReadOnly = true)("Checked")
Radio(isChecked = false, onChange = Callback.empty, isReadOnly = true)("Unchecked")
```

#### Laminar Usage
```scala
// Radio group with reactive state
val selectedFruit = Var("Apple")
val fruits = List("Apple", "Orange", "Banana")

div(tw.flex)(
  fruits.map { fruit =>
    div(^.key := fruit, tw.mr24)(
      RadioL(
        isChecked = selectedFruit.signal.map(_ == fruit),
        onChange = Observer[Unit] { _ => selectedFruit.set(fruit) }
      )(fruit)
    )
  }
)

// Disabled and read-only states
RadioL(
  isChecked = Signal.fromValue(true),
  onChange = Observer.empty,
  isDisabled = Signal.fromValue(true)
)("Disabled")

RadioL(
  isChecked = Signal.fromValue(true),
  onChange = Observer.empty,
  isReadOnly = true
)("Read-only")
```

### 8.9 Avatar Component

#### React Usage
```scala
// Basic InitialAvatar
InitialAvatarR(
  id = "<EMAIL>",
  initials = "JD",
  size = InitialAvatar.Size.Px40
)()

// Avatar with kind variants
InitialAvatarR(
  id = userId,
  initials = "AB",
  kind = InitialAvatar.Kind.Organization,
  size = InitialAvatar.Size.Px32
)()

// Special Anduin avatars (overrides id and initials)
InitialAvatarR(
  id = "",
  initials = "",
  kind = InitialAvatar.Kind.AnduinUser,
  size = InitialAvatar.Size.Px40
)()

// Inactive user (empty initials)
InitialAvatarR(
  id = userId,
  initials = "", // Shows default inactive icon
  kind = InitialAvatar.Kind.User
)()

// Avatar with label and email
AvatarLabelR(
  emailAddress = "<EMAIL>",
  fullName = "John Doe",
  kind = InitialAvatar.Kind.User // Optional
)()

// Custom full name rendering
AvatarLabelR(
  emailAddress = "<EMAIL>",
  fullName = "Current User",
  renderFullName = fullName => React.Fragment(
    fullName,
    <.span(tw.textGray7.fontNormal.ml4, "(you)")
  )
)()

// Avatar list
AvatarListR(
  size = InitialAvatar.Size.Px24,
  items = List(
    AvatarList.Item(id = "<EMAIL>", initials = "U1"),
    AvatarList.Item(id = "<EMAIL>", initials = "U2")
  )
)()
```

#### Laminar Usage
```scala
// Basic InitialAvatar
InitialAvatarL(
  id = "<EMAIL>",
  initials = Val("JD"),
  size = InitialAvatar.Size.Px40
)()

// Avatar with reactive initials
val initialsVar = Var("JD")
InitialAvatarL(
  id = userIdVar.signal.map(_.toString),
  initials = initialsVar.signal,
  kind = InitialAvatar.Kind.User,
  size = InitialAvatar.Size.Px32
)()

// Avatar label with reactive full name
val fullNameVar = Var("John Doe")
AvatarLabelL(
  emailAddress = "<EMAIL>",
  fullName = fullNameVar.signal
)()

// Avatar list with reactive items
val avatarItemsVar = Var(List.empty[AvatarList.Item])
AvatarListL(
  size = InitialAvatar.Size.Px24,
  items = avatarItemsVar.signal
)()

// Custom avatar rendering with tooltip
AvatarListL(
  items = Val(avatarItems),
  renderItem = renderProps => {
    TooltipL(
      renderTarget = renderProps.renderAvatar,
      renderContent = _.amend(renderProps.item.id)
    )()
  }
)()
```

### 8.10 Badge Component

#### React Usage
```scala
// Basic badge
BadgeR(
  color = Badge.Color.Primary,
  theme = Badge.Theme.Light,
  count = Option(5)
)()

// Badge with transform function
BadgeR(
  color = Badge.Color.Danger,
  theme = Badge.Theme.Bold,
  count = Option(1500),
  transform = Option(v => if (v > 999) s"${v/1000}k" else v.toString)
)()

// Badge wrapping content
BadgeR(
  color = Badge.Color.Danger,
  theme = Badge.Theme.Bold,
  count = Option(10)
)(
  Button(
    style = Button.Style.Minimal(icon = Option(Icon.Glyph.Comment))
  )()
)

// Dot indicator (no count)
DotR(color = Badge.Color.Success)()

// Dot wrapping content
DotR(color = Badge.Color.Warning)(
  Button(
    style = Button.Style.Full(icon = Option(Icon.Glyph.Bell))
  )()
)
```

#### Laminar Usage
```scala
// Badge with reactive count
val countVar = Var(0)
BadgeL(
  color = Badge.Color.Primary,
  theme = Badge.Theme.Light,
  count = countVar.signal.map(Option(_))
)()

// Badge with conditional display
val notificationCount = Var(0)
BadgeL(
  color = Badge.Color.Danger,
  theme = Badge.Theme.Bold,
  count = notificationCount.signal.map(count =>
    if (count > 0) Some(count) else None
  )
)(
  ButtonL(
    style = ButtonL.Style.Minimal(icon = Option(Icon.Glyph.Mail))
  )()
)

// Dot with reactive state
val hasNotification = Var(false)
child.maybe <-- hasNotification.signal.map { hasNotif =>
  if (hasNotif) {
    Some(DotL(color = Badge.Color.Danger)(
      ButtonL(style = ButtonL.Style.Minimal())()
    ))
  } else None
}
```

### 8.11 Tag Component

#### React Usage
```scala
// Basic tag
TagR(
  label = "In Progress",
  color = Tag.Light.Primary
)()

// Tag with icon
TagR(
  icon = Option(Icon.Glyph.Lock),
  label = "Locked",
  color = Tag.Light.Warning
)()

// Interactive tag with button target
TagR(
  label = "Clickable",
  target = Tag.Target.Button.withCallback(
    Callback { /* handle click */ }
  ),
  color = Tag.Light.Success
)()

// Tag with link target
TagR(
  label = "External Link ↗",
  target = Tag.Target.Link("https://example.com"),
  color = Tag.Light.Primary
)()

// Closable tag
TagR(
  label = "Removable",
  onClose = Option(Callback { /* handle removal */ }),
  color = Tag.Light.Danger
)()

// Disabled tag with tooltip
TooltipR(
  renderContent = _("This option is not available"),
  renderTarget = TagR(
    label = "Disabled",
    isDisabled = true,
    color = Tag.Light.Gray
  )()
)()

// Tag colors - Light theme
TagR(color = Tag.Light.Gray)("Gray")
TagR(color = Tag.Light.Primary)("Primary")
TagR(color = Tag.Light.Success)("Success")
TagR(color = Tag.Light.Warning)("Warning")
TagR(color = Tag.Light.Danger)("Danger")

// Tag colors - Bold theme
TagR(color = Tag.Bold.Primary)("Bold Primary")
TagR(color = Tag.Bold.Success)("Bold Success")

// Tag colors - Custom theme
TagR(color = Tag.Custom.Blue)("Blue")
TagR(color = Tag.Custom.Magenta)("Magenta")
TagR(color = Tag.Custom.YellowGreen)("Yellow Green")

// Tags list
TagsListR(
  items = List(
    TagsListR.Item(label = "Tag 1", color = Tag.Light.Primary),
    TagsListR.Item(label = "Tag 2", color = Tag.Light.Success),
    TagsListR.Item(label = "Very Long Tag Name", color = Tag.Light.Warning)
  ),
  maxLines = 2 // Default
)()
```

#### Laminar Usage
```scala
// Basic tag with reactive label
val labelVar = Var("Status")
TagL(
  label = labelVar.signal,
  color = Val(Tag.Light.Primary)
)()

// Interactive tag with observer
TagL(
  label = Val("Clickable"),
  target = Tag.Target.Button(
    onClick = Observer[Unit] { _ => /* handle click */ }
  ),
  color = Val(Tag.Light.Success)
)()

// Closable tag with reactive state
val isVisible = Var(true)
child.maybe <-- isVisible.signal.map { visible =>
  if (visible) {
    Some(TagL(
      label = Val("Removable"),
      onClose = Option(Observer[Unit] { _ => isVisible.set(false) }),
      color = Val(Tag.Light.Danger)
    )())
  } else None
}

// Tag with conditional styling
val isSelected = Var(false)
TagL(
  label = Val("Selectable"),
  color = isSelected.signal.map {
    if (_) Tag.Bold.Primary else Tag.Light.Gray
  },
  target = Tag.Target.Button(
    onClick = Observer[Unit] { _ => isSelected.update(!_) }
  )
)()

// Tags list with reactive items
val tagsVar = Var(List.empty[TagsListL.Item])
TagsListL(
  items = tagsVar.signal,
  maxLines = 3
)()

// Custom tag rendering with copy functionality
CopyL(
  content = Val("Copyable Tag"),
  renderChildren = renderProps => {
    SingleTooltipL(
      renderTarget = span(
        tw.cursorPointer,
        onClick --> Observer[dom.MouseEvent] { _ =>
          renderProps.copy.onNext(())
        },
        TagL(
          label = Val(renderProps.content),
          color = Val(Tag.Light.Primary)
        )()
      ),
      renderContent = _.amend(
        child <-- renderProps.isCopied.map {
          if (_) "✓ Copied!" else "Click to copy"
        }
      )
    )()
  }
)()
```

### 8.12 Icon Usage

```scala
// Icon sizes
Icon.Size.Px16, Icon.Size.Px24, Icon.Size.Px32
Icon.Size.Custom(20) // Custom pixel size

// Icon types
Icon.Glyph.Plus        // UI glyphs
Icon.File.Pdf          // File type icons
Icon.Folder.Brown      // Folder icons
Icon.Illustration.User // Larger illustrations
```

### 8.13 Testing Support

All components support `testId` parameter for testing:

```scala
// React
Button(testId = "submit-button")("Submit")

// Laminar
ButtonL(testId = "submit-button")("Submit")
```

This generates `data-test-id="Button-submit-button"` attributes.

### 8.14 Tooltip and Popover Integration
```scala
def tooltipExampleComponent(): Div = {
  div(
    ComponentUtils.testIdL("TooltipExample"),
    tw.p16.spaceY16,

    // Simple tooltip
    TooltipL(
      content = "This is a helpful tooltip",
      position = PortalPosition.Top
    )(
      ButtonL(
        style = ButtonL.Style.Ghost(),
        icon = Some(Icon.Glyph.Info)
      )("Hover me")
    ),

    // Complex tooltip with custom content
    TooltipL(
      content = div(
        tw.p12.maxWPx200,
        h4(tw.fontSemibold.mb8, "User Information"),
        p(tw.textSm.textGray6, "This section contains detailed user profile information including contact details and preferences.")
      ),
      position = PortalPosition.Right
    )(
      div(
        tw.p12.borderAll.borderGray3.rounded4.cursorHelp,
        IconL(Icon.Glyph.User)(),
        span(tw.ml8, "User Profile")
      )
    )
  )
}

## 9. Form Validation System

### 9.1 Basic Validation Setup

```scala
import design.anduin.validators.*
import design.anduin.validators.rules.*

// Define field names
val usernameField = FieldName("username")
val passwordField = FieldName("password")

// Create field validators
val usernameValidator = FieldValidator(
  List(
    RequiredRule(ErrorMessage("Username is required")),
    MinLengthRule(3, ErrorMessage("Username must be at least 3 characters")),
    PatternRule("^[a-zA-Z0-9]+$", ErrorMessage("Username can only contain letters and numbers"))
  )
)

val passwordValidator = FieldValidator(
  List(
    RequiredRule(ErrorMessage("Password is required")),
    MinLengthRule(8, ErrorMessage("Password must be at least 8 characters"))
  )
)

// Create form validator
val formValidator = FormValidator(
  Map(
    usernameField -> usernameValidator,
    passwordField -> passwordValidator
  )
)
```

### 9.2 Using Validation with Components

#### React Usage
```scala
// Single field validation
WithFieldValidator(
  validator = usernameValidator,
  render = renderer => {
    Field(
      label = Some("Username"),
      requirement = Field.Requirement.Required,
      validation = renderer.getError()
    )(
      TextBox(
        value = state.username,
        onChange = value => {
          scope.modState(
            _.copy(username = value),
            renderer.validateField(value)(_ => Callback.empty)
          )
        },
        status = renderer.getStatus()
      )()
    )
  }
)()

// Form validation
WithFormValidator(
  validator = formValidator,
  render = renderer => {
    <.div(
      Field(
        label = Some("Username"),
        validation = renderer.errorOf(usernameField)
      )(
        TextBox(
          value = state.username,
          onChange = value => {
            scope.modState(
              _.copy(username = value),
              renderer.validateField(usernameField, value)(_ => Callback.empty)
            )
          },
          status = renderer.statusOf(usernameField)
        )()
      ),
      Button(
        onClick = renderer.validateForm { result =>
          if (result.valid) {
            // Submit form
            Callback { /* submit logic */ }
          } else {
            Callback.alert("Please fix validation errors")
          }
        }
      )("Submit")
    )
  }
)()
```

#### Laminar Usage
```scala
// Field validation with reactive state
val usernameVar = Var("")
val validator = FieldValidator(
  List(
    RequiredRule(ErrorMessage("Username is required")),
    MinLengthRule(3, ErrorMessage("Must be at least 3 characters"))
  )
)

WithFieldValidatorL(
  validator = validator,
  render = renderer => {
    FieldL(
      label = Some("Username"),
      validation = renderer.getError()
    )(
      TextBoxL(
        value = usernameVar.signal,
        onChange = Observer[String] { value =>
          usernameVar.set(value)
          renderer.validateField(value)(_ => ()).onNext(())
        },
        status = renderer.getStatus()
      )()
    )
  }
)()
```

### 9.3 Validation Rules

```scala
// Built-in rules
RequiredRule(ErrorMessage("This field is required"))
MinLengthRule(8, ErrorMessage("Must be at least 8 characters"))
MaxLengthRule(100, ErrorMessage("Must be less than 100 characters"))
EmailAddressRule(ErrorMessage("Must be a valid email"))
UrlRule(ErrorMessage("Must be a valid URL"))
PatternRule("^\\d{3}-\\d{2}-\\d{4}$", ErrorMessage("Must be in XXX-XX-XXXX format"))
BetweenRule(18, 100, canEqual = true, ErrorMessage("Age must be between 18 and 100"))
GreaterThanRule(0, canEqual = false, ErrorMessage("Must be greater than 0"))
LessThanRule(1000, canEqual = true, ErrorMessage("Must be 1000 or less"))
IdenticalRule("expectedValue", ErrorMessage("Must match expected value"))
DifferentRule("forbiddenValue", ErrorMessage("Cannot be this value"))
IntegerRule(ErrorMessage("Must be a whole number"))
ColorRule(ErrorMessage("Must be a valid color (#000 or #000000)"))
DomainRule(ErrorMessage("Must be a valid domain"))
StringCaseRule(StringCaseRule.UpperCase, ErrorMessage("Must be uppercase"))

// Custom callback rule
CallbackRule(
  checker = value => value.contains("@"),
  error = ErrorMessage("Must contain @ symbol")
)

// Dynamic rules with callbacks
BetweenRule(
  minCb = CallbackTo.pure(getMinValue()),
  maxCb = CallbackTo.pure(getMaxValue()),
  canEqualCb = CallbackTo.pure(true),
  errorCb = CallbackTo.pure(ErrorMessage("Value out of range"))
)
```

### 9.4 Custom Validation Rule

```scala
case class PasswordStrengthRule(errorCb: CallbackTo[ErrorMessage]) extends Rule {
  def validate(valueCb: CallbackTo[String]): Future[CallbackTo[Result]] = {
    Future.successful {
      for {
        value <- valueCb
        error <- errorCb
      } yield {
        val hasUpper = value.exists(_.isUpper)
        val hasLower = value.exists(_.isLower)
        val hasDigit = value.exists(_.isDigit)
        val hasSpecial = value.exists("!@#$%^&*".contains)
        val isStrong = value.length >= 8 && hasUpper && hasLower && hasDigit && hasSpecial

        Result(isStrong, error)
      }
    }
  }
}

object PasswordStrengthRule {
  def apply(error: ErrorMessage): PasswordStrengthRule = {
    PasswordStrengthRule(errorCb = CallbackTo.pure(error))
  }
}

// Usage
val passwordValidator = FieldValidator(
  List(
    RequiredRule(ErrorMessage("Password is required")),
    PasswordStrengthRule(ErrorMessage(
      "Password must contain uppercase, lowercase, digit, and special character"
    ))
  )
)
```

## 10. Style and Layout Guidelines

### 10.1 Color System

The Anduin Design System uses a systematic color palette:

```scala
// Using TailwindCSS-style utilities
import design.anduin.style.tw.*

// Gray colors (10 shades: gray0 to gray9)
div(tw.textGray7.bgGray2.borderGray4)

// Purposeful colors (5 shades each)
div(tw.textPrimary4.bgSuccess2.borderDanger3)

// Color with opacity using Colors helper
import design.anduin.style.Colors
div(^.backgroundColor := Colors.primary4("50%"))
div(^.borderColor := Colors.success3("30%"))

// Hover and active states
div(tw.hover(tw.bgPrimary4.textGray0))
button(tw.active(tw.bgPrimary5))
```

### 10.2 Layout with Flexbox

Use flexbox for all layout needs:

```scala
// Flex container
div(tw.flex,
  div(tw.flexNone.mr8, "Fixed width"),
  div(tw.flexFill, "Takes remaining space")
)

// Column layout
div(tw.flex.flexCol,
  div(tw.flexNone, "First"),
  div(tw.flexNone, "Second")
)

// Alignment
div(tw.flex.itemsCenter.justifyBetween,
  div("Left"),
  div("Right")
)

// Wrapping
div(tw.flex.flexWrap,
  // Items will wrap to new lines
)
```

### 10.3 Typography

```scala
// Font sizes (px12, px13, px16, px20, px26, px32, px48)
div(tw.text16) // 16px font size

// Line heights (px16, px20, px24, px40, px64, ratio1p5)
div(tw.leading24) // 24px line height
div(tw.copy) // 1.5 ratio for paragraphs

// Font family
code(tw.fontMono, "Code content")

// Recommended combinations:
// Font 12/13 → Line height 20
// Font 16/20 → Line height 24
// Font 26/32 → Line height 40
// Font 48 → Line height 64
```

### 10.4 Component Configuration

#### Style Options
Most components support these style variants:
- `Full`: Solid background button/input style
- `Ghost`: Outline style with transparent background
- `Minimal`: Minimal styling with no background/border
- `Text`: Text-only style for links/buttons

#### Size Options
Common size options across components:
- `Px20`: 20px height (extra compact, buttons only)
- `Px24`: 24px height (compact)
- `Px32`: 32px height (default)
- `Px40`: 40px height (large)

#### Color Options
Standard color palette:
- `Gray0`, `Gray9`: Neutral colors
- `Primary`: Brand primary color
- `Success`: Green for positive actions
- `Danger`: Red for destructive actions
- `Warning`: Orange for warnings

## 11. Router Integration Patterns

### 9.1 LaminarRouter Usage
```scala
def navigationComponent(): Div = {
  WithLaminarRouterL { router =>
    div(
      ComponentUtils.testIdL("Navigation"),
      tw.flex.spaceX16.p16.borderBottom.borderGray3,

      ButtonL(
        style = ButtonL.Style.Ghost(),
        onClick = { _ => router.pushState(HomePage) }
      )("Home"),

      ButtonL(
        style = ButtonL.Style.Ghost(),
        onClick = { _ => router.pushState(UsersPage) }
      )("Users"),

      ButtonL(
        style = ButtonL.Style.Ghost(),
        onClick = { _ => router.pushState(SettingsPage) }
      )("Settings")
    )
  }
}

def routedContentComponent(): Div = {
  WithLaminarRouterL { router =>
    div(
      ComponentUtils.testIdL("RoutedContent"),
      tw.p16,
      child <-- router.currentPageSignal.map {
        case HomePage => homePageComponent()
        case UsersPage => usersPageComponent()
        case SettingsPage => settingsPageComponent()
        case _ => notFoundComponent()
      }
    )
  }
}
```

### 9.2 React/Laminar Router Integration
```scala
def hybridRouterComponent(): VdomElement = {
  WithLaminarRouterR { router =>
    <.div(
      tw.hPc100.flex.flexCol,

      // React navigation header
      <.nav(
        tw.flex.spaceX16.p16.borderBottom.borderGray3.bgWhite,
        Button(
          style = Button.Style.Ghost(),
          onClick = Callback(router.pushState(HomePage))
        )("Home"),
        Button(
          style = Button.Style.Ghost(),
          onClick = Callback(router.pushState(DashboardPage))
        )("Dashboard")
      ),

      // Laminar content area
      <.div.withRef(ref => {
        val container = ref.getDOMNode()
        L.render(
          container,
          routedLaminarContent(router)
        )
      })(tw.flexGrow)
    )
  }
}
```

## 12. Testing Patterns

### 12.1 Component Testing Setup
```scala
// Test utilities for Laminar components
object ComponentTestUtils {

  def renderComponent[T <: Element](component: T): T = {
    val container = dom.document.createElement("div")
    dom.document.body.appendChild(container)
    L.render(container, component)
    component
  }

  def findByTestId(testId: String): Option[Element] = {
    Option(dom.document.querySelector(s"[data-test-id='$testId']"))
  }

  def clickElement(element: Element): Unit = {
    val event = new dom.MouseEvent("click", new dom.MouseEventInit {
      bubbles = true
      cancelable = true
    })
    element.dispatchEvent(event)
  }

  def typeInInput(input: dom.html.Input, text: String): Unit = {
    input.value = text
    val event = new dom.Event("input", new dom.EventInit {
      bubbles = true
      cancelable = true
    })
    input.dispatchEvent(event)
  }
}

// Example test
class UserComponentTest extends AnyFunSuite {

  test("user component displays user information") {
    val user = User("John Doe", "<EMAIL>")
    val component = userDisplayComponent(Signal.fromValue(user))

    val rendered = ComponentTestUtils.renderComponent(component)

    val nameElement = ComponentTestUtils.findByTestId("UserName")
    assert(nameElement.isDefined)
    assert(nameElement.get.textContent == "John Doe")

    val emailElement = ComponentTestUtils.findByTestId("UserEmail")
    assert(emailElement.isDefined)
    assert(emailElement.get.textContent == "<EMAIL>")
  }

  test("user form handles input and submission") {
    var submittedUser: Option[User] = None
    val component = userFormComponent { user =>
      submittedUser = Some(user)
    }

    val rendered = ComponentTestUtils.renderComponent(component)

    val nameInput = ComponentTestUtils.findByTestId("NameInput")
      .get.asInstanceOf[dom.html.Input]
    val emailInput = ComponentTestUtils.findByTestId("EmailInput")
      .get.asInstanceOf[dom.html.Input]
    val submitButton = ComponentTestUtils.findByTestId("SubmitButton").get

    ComponentTestUtils.typeInInput(nameInput, "Jane Doe")
    ComponentTestUtils.typeInInput(emailInput, "<EMAIL>")
    ComponentTestUtils.clickElement(submitButton)

    assert(submittedUser.isDefined)
    assert(submittedUser.get.name == "Jane Doe")
    assert(submittedUser.get.email == "<EMAIL>")
  }
}
```

## 13. Best Practices and Anti-Patterns

### 13.1 Common Anti-Patterns to Avoid

**❌ Memory Leaks**
```scala
// DON'T: Forget to cleanup resources
def badWebSocketComponent(): Div = {
  val messageVar = Var[List[String]](Nil)

  div(
    onMountCallback { _ =>
      val ws = new WebSocket("ws://example.com")
      ws.onmessage = { event =>
        messageVar.update(_ :+ event.data.toString)
      }
      // Missing cleanup - WebSocket will remain open
    }
  )
}

// DON'T: Create permanent observers
val userSignal = Var(initialUser).signal
userSignal.foreach { user =>
  // This creates a permanent observer that won't be cleaned up
  updateUI(user)
}
```

**❌ Unnecessary Re-renders**
```scala
// DON'T: Recreate components unnecessarily
def badListComponent(users: Signal[List[User]]): Div = {
  div(
    children <-- users.map { userList =>
      userList.map(user => userItemComponent(user)) // Recreates all components
    }
  )
}

// DON'T: Perform expensive operations in signals
def badExpensiveComponent(items: Signal[List[Item]]): Div = {
  div(
    children <-- items.map { itemList =>
      itemList.map { item =>
        // Expensive operation runs on every signal update
        val processed = expensiveProcessing(item)
        itemComponent(processed)
      }
    }
  )
}
```

**❌ Direct DOM Manipulation**
```scala
// DON'T: Manipulate DOM directly
def badEditableComponent(user: User): Div = {
  div(
    span(user.name),
    button(
      "Edit",
      onClick --> { _ =>
        // Don't manipulate DOM directly
        val input = dom.document.createElement("input")
        // Use Laminar's reactive system instead
      }
    )
  )
}
```

### 13.2 Best Practices Summary

**✅ Resource Management**
- Always cleanup WebSocket connections, timers, and event listeners
- Use `onUnmountCallback` for cleanup operations
- Manage subscriptions properly with EventBus

**✅ Performance Optimization**
- Use `split` for efficient list rendering
- Use `distinct` to avoid unnecessary recomputations
- Leverage `splitBoolean` for conditional rendering
- Cache expensive computations outside of signals

**✅ State Management**
- Use Vars for mutable reactive state
- Derive signals for computed values
- Keep state as local as possible
- Use proper typing for all state

**✅ Component Design**
- Keep components focused and composable
- Use proper prop types with case classes
- Implement proper error handling
- Add test IDs for testing

**✅ Integration Patterns**
- Use design system components consistently
- Follow established routing patterns
- Integrate GraphQL properly with error handling
- Use error boundaries for fault tolerance

## 14. Quick Reference

### 14.1 Component Naming Convention
- React components: `ComponentR` (e.g., `ButtonR`, `TextBoxR`, `ModalR`)
- Laminar components: `ComponentL` (e.g., `ButtonL`, `TextBoxL`, `ModalL`)
- Shared types: No suffix (e.g., `Button.Style`, `TextBox.Size`, `Icon.Glyph`)

### 14.2 Common Import Blocks

#### React
```scala
import design.anduin.components.button.Button
import design.anduin.components.textbox.TextBox
import design.anduin.components.modal.Modal
import design.anduin.components.icon.Icon
import design.anduin.style.tw.*
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
```

#### Laminar
```scala
import design.anduin.components.button.laminar.ButtonL
import design.anduin.components.textbox.laminar.TextBoxL
import design.anduin.components.modal.laminar.ModalL
import design.anduin.components.icon.Icon
import design.anduin.style.tw.*
import com.raquo.laminar.api.L.*
```

### 14.3 Essential Patterns

1. **State Management**
   - React: `val (value, setValue) = React.useState("")`
   - Laminar: `val valueVar = Var("")`

2. **Event Handling**
   - React: `onClick = Callback { /* action */ }`
   - Laminar: `onClick = Observer[dom.MouseEvent] { _ => /* action */ }`

3. **Conditional Rendering**
   - React: `TagMod.when(condition) { /* content */ }`
   - Laminar: `child.maybe <-- signal.map { if (_) Some(content) else None }`

4. **Testing Support**
   - Both: `testId = "component-id"` generates `data-test-id="Component-component-id"`

5. **Loading States**
   - React: `Button(style = Button.Style.Full(isBusy = isLoading))("Save")`
   - Laminar: `ButtonL(style = ButtonL.Style.Full(isBusy = isLoadingVar.signal))("Save")`

6. **Focus Management**
   - Both: `Button(isAutoFocus = true)` and `TextBox(isAutoFocus = true)`

### 14.4 Performance Tips
- Use `split` for efficient list rendering in Laminar
- Use `distinct` to avoid unnecessary recomputations
- Leverage `splitBoolean` for conditional rendering
- Cache expensive computations outside of signals
- Always cleanup resources with `onUnmountCallback`

### 14.5 React/Laminar Integration Error Prevention Checklist

When integrating React with Laminar components, always verify these patterns:

#### Required Imports for Integration
```scala
// ALWAYS import these for React/Laminar integration
import com.raquo.laminar.api.L.{render as laminarRender, *}
import design.anduin.components.wrapper.react.MountWrapperR
import japgolly.scalajs.react.*
import japgolly.scalajs.react.vdom.html_<^.*
```

#### Component Return Type Patterns
```scala
// ✅ CORRECT - child <-- wrapped in container
def componentWithSignal(): Node = {
  div(
    child <-- signal.map(renderContent)
  )
}

// ❌ WRONG - can't return DynamicInserter as Node
def componentWithSignal(): Node = {
  child <-- signal.map(renderContent) // Type error!
}
```

#### Dynamic CSS Class Patterns
```scala
// ✅ CORRECT - convert to HtmlMod
val statusClasses = status match {
  case Active => tw.bgSuccess1.toHtmlMod
  case Inactive => tw.bgGray1.toHtmlMod
}

// ❌ WRONG - ambiguous types
val statusClasses = status match {
  case Active => tw.bgSuccess1    // Type error!
  case Inactive => tw.bgGray1     // Type error!
}
```

#### Render Method Pattern
```scala
// ✅ CORRECT - use aliased render
laminarRender(element, component())

// ❌ WRONG - L.render not available in React scope
L.render(element, component()) // Not found error!
```

### 14.6 Common Component Configurations

#### Button Styles
```scala
Button.Style.Full(color = Button.Color.Primary)    // Solid primary button
Button.Style.Ghost(color = Button.Color.Success)   // Outline success button
Button.Style.Minimal(icon = Some(Icon.Glyph.Plus)) // Icon-only button
Button.Style.Text(color = Button.Color.Danger)     // Text-only link button
```

#### TextBox Types
```scala
TextBox.Tpe.Text                                    // Plain text input
TextBox.Tpe.EmailNative                            // Email input with validation
TextBox.Tpe.Password                               // Password input
TextBox.Tpe.Currency(prefix = "$", decimalLimit = 2) // Currency input
TextBox.Tpe.NumberFloat(min = 0, max = 100)       // Number input with constraints
```

#### Modal Sizes
```scala
ModalL.Size.Small    // 400px width
ModalL.Size.Medium   // 600px width
ModalL.Size.Large    // 800px width
ModalL.Size.XLarge   // 1000px width
```

## 15. Complete Example: User Management Dashboard

```scala
// Complete example combining all patterns
def userManagementDashboard(): Div = {
  val selectedUserVar = Var[Option[UserId]](None)
  val searchQueryVar = Var("")
  val isModalOpenVar = Var(false)

  div(
    ComponentUtils.testIdL("UserManagementDashboard"),
    tw.hPc100.flex.flexCol,

    // Header with search and actions
    div(
      tw.flex.itemsCenter.justifyBetween.p16.borderBottom.borderGray3.bgWhite,
      h1(tw.text2xl.fontBold, "User Management"),
      div(
        tw.flex.itemsCenter.spaceX12,
        input(
          ComponentUtils.testIdL("SearchInput"),
          tw.wPx300.px12.py8.borderAll.borderGray3.rounded4,
          placeholder := "Search users...",
          value <-- searchQueryVar.signal,
          onInput.mapToValue --> searchQueryVar.writer
        ),
        ButtonL(
          style = ButtonL.Style.Full(),
          icon = Some(Icon.Glyph.Plus),
          onClick = { _ => isModalOpenVar.set(true) }
        )("Add User")
      )
    ),

    // Main content area
    div(
      tw.flexGrow.flex,

      // User list
      div(
        tw.wPx400.borderRight.borderGray3.overflowYAuto,
        QueryComponentL(
          query = UserQueries.GetUsers,
          variableSignal = searchQueryVar.signal.map(GetUsersVariables(_)),
          options = GraphqlOptions(pollInterval = 30.seconds)
        ) { queryData =>
          child <-- queryData.data.map {
            case Some(users) =>
              userListComponent(
                users = Signal.fromValue(users),
                selectedUser = selectedUserVar.signal,
                onSelectUser = selectedUserVar.writer
              )
            case None =>
              div(
                tw.flex.itemsCenter.justifyCenter.p16,
                LoadingSpinner()()
              )
          }
        }
      ),

      // User details
      div(
        tw.flexGrow.p16,
        child <-- selectedUserVar.signal.map {
          case Some(userId) =>
            userDetailsPanel(userId)
          case None =>
            div(
              tw.flex.itemsCenter.justifyCenter.hPc100.textGray5,
              "Select a user to view details"
            )
        }
      )
    ),

    // Add user modal
    child <-- isModalOpenVar.signal.map { isOpen =>
      if (isOpen) {
        ModalL(
          isOpen = isModalOpenVar.signal,
          onClose = isModalOpenVar.writer.contramap(_ => false),
          size = ModalL.Size.Medium
        )(
          ModalBodyL(
            h2(tw.textXl.fontSemibold.mb16, "Add New User"),
            userFormComponent(
              onSubmit = { user =>
                // Handle user creation
                createUser(user).onComplete { _ =>
                  isModalOpenVar.set(false)
                }
              },
              onCancel = { _ =>
                isModalOpenVar.set(false)
              }
            )
          )
        )
      } else emptyNode
    }
  )
}
```

This comprehensive guide provides all the patterns and best practices needed to build consistent, performant, and maintainable React and Laminar components in this codebase. It combines architectural patterns with detailed component documentation from the Anduin Design System, covering everything from basic component usage to advanced state management, validation, and performance optimization techniques.
