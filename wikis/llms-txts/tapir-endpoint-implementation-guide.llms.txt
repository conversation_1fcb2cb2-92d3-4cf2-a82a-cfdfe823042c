# Tapir Endpoint Implementation Guide for AI Coding Agents

This guide provides comprehensive instructions for implementing Tapir endpoints in this Scala-based codebase using ZIO and type-safe API development.

## 1. Architecture Overview

Tapir is a declarative, type-safe library for describing HTTP API endpoints in Scala. In this codebase, <PERSON><PERSON><PERSON> is integrated with ZIO for effect management, providing a robust foundation for building APIs with proper authentication, validation, error handling, and observability.

At its core, this codebase uses <PERSON><PERSON><PERSON> for:

- **Type-safe API definitions**: Endpoints are defined with compile-time type safety for inputs, outputs, and errors
- **Authentication integration**: Bearer token authentication with session validation
- **Input validation**: Declarative validation with proper error handling
- **Error handling**: Standardized error responses with proper HTTP status codes
- **Observability**: Integrated tracing, metrics, and logging
- **Async operations**: Long-running operations with proper timeout handling

### Core Components
- **AuthenticatedEndpoints**: For internal authenticated APIs with bearer token security
- **PublicApiEndpoints**: For external public APIs with different authentication schemes
- **AsyncEndpoint**: For long-running operations with async execution patterns
- **AuthenticatedEndpointValidator**: For input validation with business logic
- **Server Implementations**: Trait-based server logic with proper error handling
- **Interceptors**: Security headers, CORS, caching, and telemetry integration

### Key Libraries
```scala
import anduin.tapir.AuthenticatedEndpoints
import anduin.tapir.AsyncEndpoint
import anduin.tapir.PublicEndpoints
import anduin.tapir.server.{AuthenticatedEndpointValidator, AuthenticatedValidationEndpointServer}
import com.anduin.stargazer.service.utils.{ZIOTelemetryUtils, ZIOUtils}
import sttp.tapir.*
import sttp.tapir.json.circe.*
import io.circe.{Encoder, Decoder}
```

## 2. Required Dependencies

### Core Tapir Dependencies
```scala
import sttp.tapir.*
import sttp.tapir.json.circe.jsonBody
import sttp.model.{StatusCode, Method}
import io.circe.{Encoder, Decoder}
```

### Codebase-Specific Dependencies
```scala
import anduin.tapir.AuthenticatedEndpoints
import anduin.tapir.AsyncEndpoint
import anduin.tapir.endpoint.{AuthenticatedEndpointValidationParams, TapirEndpointInfo}
import anduin.tapir.server.{AuthenticatedEndpointValidator, AuthenticatedValidationEndpointServer}
import anduin.service.{GeneralServiceException, AuthenticatedRequestContext}
import com.anduin.stargazer.service.utils.{ZIOTelemetryUtils, ZIOUtils}
```

## 3. Implementation Patterns

### 3.1 Authenticated Endpoint Pattern
```scala
package your.package.endpoints

import anduin.tapir.AuthenticatedEndpoints
import anduin.service.GeneralServiceException

object YourEndpoints extends AuthenticatedEndpoints {

  val processData = authEndpoint[
    ProcessDataParams,
    GeneralServiceException,
    ProcessDataResponse
  ](
    "your-service" / "process-data"
  )

}

final case class ProcessDataParams(
  data: String,
  options: ProcessingOptions
) extends AuthenticatedEndpointValidationParams

final case class ProcessDataResponse(
  result: String,
  processedAt: Instant
)
```

### 3.2 Public API Endpoint Pattern
```scala
package your.package.endpoints

import anduin.brienne.endpoint.{PublicApiEndpoints, TapirEndpointInfo}
import anduin.brienne.fundsub.api.external.GeneralRequestError

object YourPublicEndpoints extends PublicApiEndpoints {

  protected def basePath: EndpointInput[Unit] = "api" / "v1" / "your-service"

  val getData = basePathEmptyBodyPublicApiEndpoint[GetDataResponse](
    Method.GET,
    TapirEndpointInfo(
      name = "Get data",
      summary = "Retrieve data by ID",
      description = "Returns data for the specified identifier",
      tags = List("data", "public")
    )
  )

}
```

### 3.3 Async Endpoint Pattern
```scala
package your.package.endpoints

import anduin.tapir.AsyncEndpoint
import anduin.asyncapiv2.execution.AsyncApiTemporalQueue

object YourAsyncEndpoints extends AsyncEndpoint {

  val processLargeDataset = asyncEndpoint[
    ProcessLargeDatasetParams,
    GeneralServiceException,
    ProcessLargeDatasetResponse
  ](
    "your-service" / "process-large-dataset",
    AsyncApiTemporalQueue.Default
  )

}
```

### 3.4 Validation Pattern
```scala
package your.package.validator

import anduin.tapir.server.AuthenticatedEndpointValidator
import anduin.model.common.user.UserId
import com.anduin.stargazer.service.utils.ZIOUtils
import zio.Task

final case class ProcessDataValidator(
  yourService: YourService
) extends AuthenticatedEndpointValidator[ProcessDataParams]("processData") {

  override def validate(params: ProcessDataParams, userId: UserId): Task[Unit] = {
    for {
      _ <- ZIOUtils.validate(params.data.trim.nonEmpty) {
        GeneralServiceException("Data cannot be empty")
      }
      _ <- ZIOUtils.validate(params.options.isValid) {
        GeneralServiceException("Invalid processing options")
      }
      _ <- yourService.validateUserAccess(userId, params.resourceId)
    } yield ()
  }

}
```

## 4. Error Handling Strategies

### 4.1 Standardized Error Responses
```scala
// Authenticated endpoints use oneOf with proper error variants
endpoint.errorOut(
  oneOf[AuthenticatedEndpointError[GeneralServiceException]](
    oneOfVariantFromMatchType(
      StatusCode.Forbidden, 
      stringBody.mapTo[AuthenticatedEndpoints.Unauthorized]
    ),
    oneOfVariantValueMatcher(
      StatusCode.InternalServerError,
      customCodecJsonBody[GeneralServiceException].map(
        AuthenticatedEndpoints.ServiceError(_)
      )(_.error)
    ) {
      _.asMatchable match {
        case AuthenticatedEndpoints.ServiceError(_: GeneralServiceException) => true
      }
    }
  )
)
```

### 4.2 Server Error Handling Patterns
```scala
// Use catchError for automatic error handling
authRouteCatchError(endpoint) { (params, ctx) =>
  yourService.processData(params, ctx.actor.userId)
}

// Use forwardError to preserve original error messages
authRouteForwardError(endpoint) { (params, ctx) =>
  yourService.processData(params, ctx.actor.userId)
}
```

## 5. Security and Authentication

### 5.1 Bearer Token Authentication
```scala
// Endpoints automatically include bearer token security
endpoint.securityIn(auth.bearer[String]().map(BearerToken.apply)(_.token))
```

### 5.2 Security Interceptors
```scala
// Server configuration with security interceptors
val armeriaZioServerOptions = ArmeriaZioServerOptions.customiseInterceptors
  .corsInterceptor(
    CORSInterceptor.customOrThrow(
      CORSConfig.default.copy(exposedHeaders = ExposedHeaders.All)
    )
  )
  .addInterceptor(SecurityHeaderInterceptor.responseInterceptor)
  .addInterceptor(NoCacheInterceptor.responseInterceptor)
  .options
```

## 6. Performance and Monitoring

### 6.1 Timeout Configuration
```scala
// Standard timeout patterns
protected val defaultTimeout: FiniteDuration = FiniteDuration(30, TimeUnit.SECONDS)
protected val longTimeout: FiniteDuration = FiniteDuration(90, TimeUnit.SECONDS)

// Apply timeout in server logic
authRouteCatchError(endpoint, timeout = longTimeout) { (params, ctx) =>
  ZIOUtils.timeout(longTimeout)(
    yourService.longRunningOperation(params)
  )
}
```

### 6.2 Telemetry Integration
```scala
// Automatic telemetry injection in server implementations
ZIOLoggingUtils.annotateRequest(uri, headers, Some(ctx.actor.userId)) {
  injectTelemetry(uri, ctx)(
    ZIOUtils.timeout(timeout)(impl(params, ctx))
      .tapDefect(cause => ZIO.logErrorCause(cause))
  )
    .provideEnvironment(tracingEnvironment.environment)
}

// Custom metrics injection
ZIOTelemetryUtils.injectMetrics("endpoint_name", Map("operation" -> "process"))(
  yourService.processData(params)
)
```

## 7. Server Implementation Patterns

### 7.1 Authenticated Server Implementation
```scala
package your.package.server

import anduin.tapir.server.AuthenticatedValidationEndpointServer
import anduin.service.GeneralServiceException

final case class YourEndpointServer(
  yourService: YourService,
  protected val authorizationService: AuthorizationService,
  override protected val interpreter: ArmeriaZioServerInterpreter[Any]
) extends AuthenticatedValidationEndpointServer {

  private val processDataValidator = ProcessDataValidator(yourService)

  val services: List[TapirServerService] = List(
    validateRouteCatchError(YourEndpoints.processData, processDataValidator) { (params, ctx) =>
      yourService.processData(params, ctx.actor.userId)
    }
  )

}
```

### 7.2 Public API Server Implementation
```scala
final case class YourPublicApiServer(
  yourService: YourService,
  protected val authorizationService: AuthorizationService,
  override protected val interpreter: ArmeriaZioServerInterpreter[Any]
) extends PublicApiTapirServerAuthentication {

  val services: List[TapirServerService] = List(
    publicApiEndpoint(YourPublicEndpoints.getData) { (params, ctx) =>
      yourService.getData(params).map(Right(_)).catchAll { error =>
        ZIO.succeed(Left(ServerErrorResponse("Internal error")))
      }
    }
  )

}
```

## 8. Testing Patterns

### 8.1 Integration Test Setup
```scala
package your.package

import zio.test.*
import anduin.testing.YourBaseInteg

object YourEndpointInteg extends YourBaseInteg {

  override def spec = suite("YourEndpointInteg") {
    test("Should process data successfully") {
      for {
        // Setup test data
        params = ProcessDataParams("test data", ProcessingOptions.default)
        
        // Make request through endpoint
        response <- yourEndpointServer.processData(params)
        
        // Verify response
        _ <- assertTrue(response.result.contains("processed"))
      } yield ()
    }
  }

}
```

## 9. Best Practices and Conventions

### 9.1 Naming Conventions
- **Endpoint Objects**: `{Domain}Endpoints` (e.g., `UserEndpoints`, `DataRoomEndpoints`)
- **Server Implementations**: `{Domain}EndpointServer` (e.g., `UserEndpointServer`)
- **Validators**: `{Domain}Validator` (e.g., `UserAccessValidator`)
- **Request/Response Models**: `{Action}Params`, `{Action}Response`

### 9.2 File Organization
```
your-module/
├── shared/src/your/package/
│   ├── endpoints/
│   │   ├── YourEndpoints.scala
│   │   └── YourPublicEndpoints.scala
│   └── model/
│       ├── YourParams.scala
│       └── YourResponse.scala
├── jvm/src/your/package/
│   ├── server/
│   │   └── YourEndpointServer.scala
│   └── validator/
│       └── YourValidator.scala
└── jvm/it/src/your/package/
    └── YourEndpointInteg.scala
```

### 9.3 Anti-Patterns to Avoid
- **Missing validation**: Always validate inputs at the endpoint level
- **Generic error handling**: Use specific error types and proper HTTP status codes
- **Exposing internal models**: Use dedicated request/response DTOs
- **Ignoring timeouts**: Always configure appropriate timeouts for operations
- **Missing documentation**: Include endpoint names, descriptions, and tags
- **Inconsistent authentication**: Use appropriate endpoint types for internal vs public APIs

## 10. Complete Example: User Management Endpoint

### Step 1: Define Request/Response Models
```scala
// UserModels.scala
package your.package.model

import anduin.tapir.endpoint.AuthenticatedEndpointValidationParams
import anduin.model.common.user.UserId
import io.circe.{Encoder, Decoder}
import io.circe.generic.semiauto.*

final case class CreateUserParams(
  email: String,
  name: String,
  role: UserRole
) extends AuthenticatedEndpointValidationParams

object CreateUserParams {
  given Encoder[CreateUserParams] = deriveEncoder
  given Decoder[CreateUserParams] = deriveDecoder
}

final case class CreateUserResponse(
  userId: UserId,
  email: String,
  name: String,
  createdAt: Instant
)

object CreateUserResponse {
  given Encoder[CreateUserResponse] = deriveEncoder
  given Decoder[CreateUserResponse] = deriveDecoder
}
```

### Step 2: Define Endpoint
```scala
// UserEndpoints.scala
package your.package.endpoints

import anduin.tapir.AuthenticatedEndpoints
import anduin.service.GeneralServiceException

object UserEndpoints extends AuthenticatedEndpoints {

  val createUser = authEndpoint[
    CreateUserParams,
    GeneralServiceException,
    CreateUserResponse
  ](
    "users" / "create"
  )

  val getUser = authEndpoint[
    GetUserParams,
    GeneralServiceException,
    GetUserResponse
  ](
    "users" / "get"
  )

}
```

### Step 3: Create Validator
```scala
// UserValidator.scala
package your.package.validator

import anduin.tapir.server.AuthenticatedEndpointValidator
import anduin.model.common.user.UserId
import com.anduin.stargazer.service.utils.ZIOUtils
import zio.Task

final case class CreateUserValidator(
  userService: UserService
) extends AuthenticatedEndpointValidator[CreateUserParams]("createUser") {

  override def validate(params: CreateUserParams, userId: UserId): Task[Unit] = {
    for {
      _ <- ZIOUtils.validate(params.email.contains("@")) {
        GeneralServiceException("Invalid email format")
      }
      _ <- ZIOUtils.validate(params.name.trim.nonEmpty) {
        GeneralServiceException("Name cannot be empty")
      }
      _ <- userService.validateCreatePermission(userId, params.role)
      _ <- userService.validateEmailNotExists(params.email)
    } yield ()
  }

}
```

### Step 4: Implement Server
```scala
// UserEndpointServer.scala
package your.package.server

import anduin.tapir.server.AuthenticatedValidationEndpointServer
import anduin.service.GeneralServiceException
import scala.concurrent.duration.*

final case class UserEndpointServer(
  userService: UserService,
  protected val authorizationService: AuthorizationService,
  override protected val interpreter: ArmeriaZioServerInterpreter[Any]
) extends AuthenticatedValidationEndpointServer {

  private val createUserValidator = CreateUserValidator(userService)
  private val getUserValidator = GetUserValidator(userService)

  val services: List[TapirServerService] = List(
    validateRouteCatchError(
      UserEndpoints.createUser,
      createUserValidator,
      timeout = 30.seconds
    ) { (params, ctx) =>
      userService.createUser(params, ctx.actor.userId)
    },

    validateRouteCatchError(
      UserEndpoints.getUser,
      getUserValidator,
      timeout = 10.seconds
    ) { (params, ctx) =>
      userService.getUser(params.userId, ctx.actor.userId)
    }
  )

}
```

### Step 5: Integration Test
```scala
// UserEndpointInteg.scala
package your.package

import zio.test.*
import anduin.testing.YourBaseInteg

object UserEndpointInteg extends YourBaseInteg {

  override def spec = suite("UserEndpointInteg") {
    test("Should create user successfully") {
      for {
        params = CreateUserParams(
          email = "<EMAIL>",
          name = "Test User",
          role = UserRole.Standard
        )

        response <- userEndpointServer.createUser(params)

        _ <- assertTrue(response.email == params.email)
        _ <- assertTrue(response.name == params.name)
        _ <- assertTrue(response.userId.nonEmpty)
      } yield ()
    } +

    test("Should validate email format") {
      for {
        params = CreateUserParams(
          email = "invalid-email",
          name = "Test User",
          role = UserRole.Standard
        )

        result <- userEndpointServer.createUser(params).exit

        _ <- assertTrue(result.isFailure)
      } yield ()
    }
  }

}
```

## 11. Integration Points

### 11.1 Service Integration
```scala
// Integrate with business services
override def validate(params: CreateUserParams, userId: UserId): Task[Unit] = {
  for {
    _ <- userService.validateCreatePermission(userId, params.role)
    _ <- teamService.validateTeamAccess(userId, params.teamId)
    _ <- auditService.logValidationAttempt(userId, "createUser")
  } yield ()
}
```

### 11.2 Database Integration
```scala
// Use FDB transactions in validators
override def validate(params: DataRoomParams, userId: UserId): Task[Unit] = {
  FDBRecordDatabase.transact(DataRoomValidateOperations.Production) { validateOps =>
    validateOps.validateAndGetCurrentState(
      params.dataRoomWorkflowId,
      userId,
      roleChecks,
      checkArchivedStatus = params.checkArchivedStatus
    )
  }.unit
}
```

### 11.3 External Service Integration
```scala
// Handle external service calls with proper error handling
authRouteCatchError(endpoint) { (params, ctx) =>
  for {
    externalData <- externalApiService.fetchData(params.id)
      .retry(Schedule.exponentialBackoff(1.second) && Schedule.recurs(3))
      .catchAll { error =>
        ZIO.logError(s"External API failed: $error") *>
        ZIO.fail(GeneralServiceException("External service unavailable"))
      }
    result <- yourService.processExternalData(externalData, ctx.actor.userId)
  } yield result
}
```

## 12. Async Endpoint Implementation

### 12.1 Complete Async Example
```scala
// Define async endpoint
val processLargeDataset = asyncEndpoint[
  ProcessLargeDatasetParams,
  GeneralServiceException,
  ProcessLargeDatasetResponse
](
  "data" / "process-large-dataset",
  AsyncApiTemporalQueue.Default
)

// Implement async server
validateAsyncRouteCatchError(
  DataEndpoints.processLargeDataset,
  processLargeDatasetValidator
) { (params, ctx) =>
  dataProcessingService.processLargeDataset(params, ctx.actor.userId)
}
```

### 12.2 Async Handler Integration
```scala
// Async endpoints automatically create multiple endpoints:
// - Main endpoint for immediate execution
// - Create async endpoint for starting async execution
// - Run async endpoint for running with specific execution ID
// - Fetch endpoint for checking status and retrieving results

// The handler map is built automatically:
buildHandlerMap(endpoint) { (param, ctx) =>
  impl(param, ctx).map(Right(_)).catchAll(error =>
    catchError(error).map(Left(_))
  )
}
```

## 15. Frontend Integration Patterns

### 15.1 React-Laminar Component Integration

When building frontend components that consume Tapir endpoints, this codebase uses a hybrid approach with React and Laminar components. Here are the key patterns for integrating Laminar components into React applications:

#### MountWrapperR Pattern
```scala
import design.anduin.components.wrapper.react.MountWrapperR
import com.raquo.laminar.api.L
import design.anduin.components.modal.Modal

// In React component render method:
MountWrapperR(
  onMount = element => {
    Callback {
      L.render(
        element,
        LaminarComponent(
          // Component props
          onClose = L.Observer(_ => closeModal.runNow()),
          onSuccess = L.Observer(data => handleSuccess(data).runNow())
        )()
      )
    }
  },
  onUnmount = _ => Callback.empty,
  renderChildren = renderProps => <.div.withRef(renderProps.ref)()
)()
```

#### Key Import Patterns
```scala
// For React-Laminar bridging
import design.anduin.components.wrapper.react.MountWrapperR

// For Laminar rendering
import com.raquo.laminar.api.L

// For React modals (not ModalR)
import design.anduin.components.modal.Modal

// For router integration
import stargazer.component.routing.react.WithReactRouterR
import stargazer.model.routing.DynamicAuthPage
```

#### Common Integration Patterns
1. **Modal Integration**: Use `MountWrapperR` to render Laminar modals within React modal containers
2. **DOM Element Access**: The wrapper provides a DOM element that Laminar can render into
3. **Lifecycle Management**: Proper mount/unmount handling prevents memory leaks
4. **Callback Bridging**: Use `L.Observer` for Laminar to React callback communication
5. **Router Integration**: Use `WithReactRouterR` for navigation within Laminar components

#### Error Handling in Frontend Components
```scala
// Tapir endpoint calls in Laminar components
ZIOUtils.toReactCallback(
  endpointCall(params).fold(
    error => Toast.errorCallback(s"Operation failed: ${error.message}"),
    success => Toast.successCallback("Operation completed successfully")
  )
)
```

### 15.2 Tapir Endpoint Usage in Frontend

When calling Tapir endpoints from frontend components, follow these patterns:

#### Standard Endpoint Call Pattern
```scala
import com.anduin.stargazer.client.utils.ZIOUtils

// In component logic
ZIOUtils.toReactCallback(
  client.endpoint(params).fold(
    error => handleError(error),
    response => handleSuccess(response)
  ).catchAll(unexpected =>
    ZIO.succeed(handleUnexpectedError(unexpected))
  )
)
```

#### Loading State Management
```scala
// Set loading state before operation
setLoading(true)

// Reset loading state in both success and error branches
ZIOUtils.toReactCallback(
  client.endpoint(params).fold(
    error => {
      setLoading(false)
      handleError(error)
    },
    response => {
      setLoading(false)
      handleSuccess(response)
    }
  )
)
```

## 16. Common Issues and Solutions

### 16.1 Import Resolution Issues

#### Modal Component Imports
```scala
// ✅ Correct - Use Modal for React components
import design.anduin.components.modal.Modal

// ✅ For Laminar modals, use laminar package
import design.anduin.components.modal.laminar.{ModalL, ModalBodyL, ModalFooterL}
```

#### Router Imports
```scala
// ✅ Correct - Use stargazer routing components
import stargazer.component.routing.react.WithReactRouterR
import stargazer.model.routing.DynamicAuthPage
```

### 16.2 Type Annotation Issues

#### Ref Type Annotations
```scala
// ✅ Correct - Let Scala infer the type or use proper RefArg type
<.div.withRef { ref =>
  // Scala will infer the correct type
}

// ✅ Alternative - Use the correct RefArg type if needed
<.div.withRef { (ref: japgolly.scalajs.react.vdom.TagOf.RefArg[org.scalajs.dom.HTMLDivElement]) =>
```

#### DOM Element Access from Refs
```scala
// ❌ Incorrect - Direct foreach on RefArg
ref.foreach { element => ... }

// ✅ Correct - Access through value and use proper DOM element
ref.value.foreach { refSet =>
  // Use refSet as the DOM element or cast if needed
  L.render(refSet.asInstanceOf[org.scalajs.dom.HTMLDivElement], component)
}
```

### 16.3 Component Integration Issues

#### VdomNode vs Callback Return Types
```scala
// ❌ Incorrect - withRef expects VdomNode, not Callback
<.div.withRef { ref =>
  Callback { /* some action */ }
}

// ✅ Correct - Return VdomNode and handle callbacks separately
<.div.withRef { ref =>
  <.div(
    ^.onMount --> Callback { /* mount action */ }
  )
}

// ✅ Better - Use MountWrapperR for complex integrations
MountWrapperR(
  onMount = element => Callback { /* mount action */ },
  onUnmount = _ => Callback.empty,
  renderChildren = renderProps => <.div.withRef(renderProps.ref)()
)()
```

### 16.4 Compilation Best Practices

#### Unused Import Cleanup
Always clean up unused imports to avoid warnings:
```scala
// Remove unused wildcard imports
import com.raquo.laminar.api.{L, *}  // ❌ If * is unused
import com.raquo.laminar.api.L      // ✅ Only import what's needed
```

#### Mill Compilation Commands
Use the correct environment variable for agent builds:
```bash
# ✅ Correct command for this codebase
ANDUIN_BUILD_ENV=agent ./mill itools.olympian.js.compile --no-server

# ❌ Don't forget the ANDUIN_BUILD_ENV=agent prefix
./mill itools.olympian.js.compile --no-server
```

This comprehensive guide provides AI coding assistants with the exact patterns and practices used in this codebase for implementing Tapir endpoints with proper authentication, validation, error handling, observability integration, frontend component integration, and common issue resolution.
