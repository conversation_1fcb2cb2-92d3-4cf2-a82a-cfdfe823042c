# Development Documentation
Comprehensive resources for building and maintaining the Anduin codebase.

## Core Documentation
- **[Build with Mill Guide](build-with-mill-guide.llms.txt)**: Comprehensive guide for building, testing, and deploying the codebase using Mill build tool.
- **[Protobuf Serialization Guide](protobuf-serialization-implementation-guide.llms.txt)**: Best practices for designing and implementing protobuf messages and serialization patterns.
- **[Tapir Endpoint Guide](tapir-endpoint-implementation-guide.llms.txt)**: Patterns for defining and implementing Tapir endpoints with proper authentication, validation, and error handling.
- **[Temporal Workflow Guide](temporal-workflow-implementation-guide.llms.txt)**: Best practices for designing and implementing Temporal workflows and activities.
- **[Frontend React/Laminar Guide](frontend-react-laminar-implementation-guide.llms.txt)**: Patterns for building frontend components with React and Laminar.
- **[Configuration Management Guide](configuration-management-implementation-guide.llms.txt)**: Patterns for loading, validating, and managing application configuration.
- **[FoundationDB Guide](foundationdb-implementation-guide.llms.txt)**: Best practices for using FoundationDB with the codebase's ZIO integration.