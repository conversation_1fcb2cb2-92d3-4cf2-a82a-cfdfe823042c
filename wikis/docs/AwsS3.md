# Document storage in AWS S3

## AWS Configuration

### Buckets

All documents are stored in a bucket in AWS S3.  We've created two buckets
```anduin-assets-documents```, and ```anduin-assets-documents-dev``` for production
system and for local dev, respectively.

We use CORS (Cross-Origin-Resource-Sharing) setting to limit access to documents in these
buckets. For example, only client web applications loaded from ```*.anduintransact.com``` domain
can upload a document to the production bucket (of course a proper pre-signed URL is required
for uploading). For flexibility, the dev bucket allows web loaded from any domain to upload
a document (again, with a pre-signed URL).

Together with each bucket, we create an AccessKey that can access the assigned
bucket only.  Particularly, the production AccessKey has the following
permissions to the ```anduin-assets-documents``` bucket:

```
Put Object
Get Object
Delete Ojbect
GetObjectVersion
s3:GetBucketVersioning
s3:ListBucketVersions
```

and nothing else. Similarly for the dev access key.

### Testing DocumentService

The testing page is at ```localhost:8080/#/docStorage```. Before you can use this
page, you need to change the current ```aws.S3``` in your ```local/local.conf``` to the
following configuration:

```
      // This accessKey only has permissions to access the specified bucket on S3.
      // This key is used for LOCAL DEV as the CORS configuration for the assigned
      // bucket allows ALL origin resource to access (of course given the links are
      // pre-signed properly.
      S3 {
        bucket = "anduin-assets-document-dev"
        accessKeyId = "********************"
        secretAccessKey = "XaM0SwlBbAt8tU+DQbjEZl9YPferWMbDIRo4qSgp"
        region = "us-east-1"
      }
```
