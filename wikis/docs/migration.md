# Steps to run gondor migration

Following the steps bellow to migrate from *previous release A* to *new release B*.

## Step 1. Preparation

- Clone two releases A and B to two separate folders stargazerA and stargazerB, respectively.

- Go to stargazerA.

- Nuke `gondor` database.
```bash
  $ ./project/tools/local-deps/nuke-db.sh
```

## Step 2. Create test data from the previous release

- Run release A server to create test users and transactions.

- Stop release A server and create a backup of the database (this step is optional as it's supposed to help
testing easier and incremental).
```bash
  $ ./project/tools/local-deps/backup-tools.sh backup releaseA-db
```

## Step 3. Run migration

- Run migration step script. After executing the script, `gondor` contains migrated data compatible to release B.

```bash
  $ ./sbt gondorAppServer/runMain com.anduin.stargazer.apps.stargazer.GondorMigrationApp
```

## Step 4. Testing migration

- Run release B server against migrated db in `gondor`.

- Testing a different scenario by repeating from `Step 2` after restoring db on `gondor`.
```bash
  $ ./project/tools/local-deps/backup-tools.sh restore releaseA-db
```

## Step 5. Ship it!

- Run migration script against the production database

- Upload the migrated database.

# Notes

- Run protocheck to show incompatible proto messages
```bash
  $ gondorProtobufCheck/run
```
