# General notes #
- We use Ammonite, which has some good [features](http://ammonite.io/#Features). Check them out!.
- Why should we use REPL? → TODO (or search for **REPL-driven development**).
- Scala REPLs are jvm-only. At the moment scalajs doesn't have a usable REPL. In theory, you can launch a REPL from a scalajs codebase. However, it will run in a jvm, so only pure-scala functionalities are available, not functionalities/libs specific to js runtimes.

# How to use REPL in this codebase #

There are 2 modes: attached (default) and standalone.

## Attached REPL ##

In `sbt` shell, start the server (e.g. with `reStartJVM`, or `reStartAll`):

Then run this script from repo's root directory to connect to the REPL embedded in the local app instance:
``` shell
./project/tools/repl.sh
```

The first time it connects, it will say something like this. Just accept.
``` shell
The authenticity of host '[localhost]:7777 ([::1]:7777)' can't be established.
RSA key fingerprint is SHA256:z1g6Q5pOFHAcdhU4XqJcbW6yvy8aMiNQBdxyQ6gCX7k.
Are you sure you want to continue connecting (yes/no)? yes
Warning: Permanently added '[localhost]:7777' (RSA) to the list of known hosts.
```

To exit from the REPL, hit <kbd>^D</kbd>.

This mode:
- Runs in the same jvm as `gondorAppServer`.
- Has access to `gondorAppServer` code and its dependencies.
- Can use actor systems and task executors.

## Standalone REPL ##

Run this script from repo's root directory (`gondorAppServer` doesn't have to be running).
``` shell
./project/tools/repl.sh standalone
```

This mode:
- Has access to `gondorJVM` code and its dependencies.
- Doesn't have actor systems and task executors instantiated.
- Works across `gondorAppServer` restarts.
- Is slow to start/stop due to sbt's overhead.

## REPL from sbt shell ##

From `sbt` shell

``` scala
gondorREPL/run
```

This mode:
- Has access to `gondorJVM` code and its dependencies.
- Doesn't have actor systems and task executors instantiated.
- Is slow to stop due to sbt's overhead.

# Examples of REPL interaction #

```scala
@ import anduin.model.common.user.{UserId, UserInfo, UserChannelIdCoreUtils}
import anduin.model.common.user.{UserId, UserInfo, UserChannelIdCoreUtils}
@ import anduin.id.user.UserRestrictedId
import anduin.id.user.UserRestrictedId
@ import com.anduin.stargazer.service.database.{UserRestrictedDocUtils, UserDbUtils, UserReadChannelUtils}
import com.anduin.stargazer.service.database.{UserRestrictedDocUtils, UserDbUtils, UserReadChannelUtils}
@ import com.anduin.stargazer.service.user.UserUtils
import com.anduin.stargazer.service.user.UserUtils
@ val userId = UserId.unapply("<EMAIL>").get
userId: UserId = UserId(RefinedPart("<EMAIL>"))
@ UserUtils.getUserType(userId).unsafePerformAsync(println) // may print in sbt shell

@ UserDbUtils.get(userId).unsafePerformSync
res6: com.anduin.stargazer.service.database.syncgateway.DbUserModel = DbUserModel(
  UserInfo("Stargazer", "Portal", "Admin", "", "DigitalWorld", "", ""),
  SyncGatewayUserModelRead(
    "<EMAIL>",
    "<EMAIL>",
    Array("!", "********************************************************************************************************"),
    None,
    None,
    None,
    None
  )
)
@ UserRestrictedDocUtils.get(UserRestrictedId(userId)).unsafePerformSync
res7: anduin.protobuf.UserRestrictedMessage = UserRestrictedMessage(None, false, AdminUser, Map(), "", Vector(), Map(), None, None, Map())
@ UserReadChannelUtils.getChannels(userId).unsafePerformSync
res8: Set[anduin.model.ReadChannel] = Set(UserReadChannel(UserId(RefinedPart("<EMAIL>"))))
@
```

## Interacting with actor systems

```scala
import com.anduin.stargazer.apps.stargazer.GondorServiceApp.stargazerSystem
import com.anduin.stargazer.apps.stargazer.StargazerSettings.gondorConfig
import anduin.id.TransactionId
import anduin.refined.Refined
import com.anduin.stargazer.service.cron.DigestEmailActor._

stargazerSystem.actorSelection("user/cronActor/digestActor") ! Schedule // Schedule for sending digest emails today

val trxnId = TransactionId(Refined.unsafeApply("txn000000000000"))
val weeklyConfig = gondorConfig.backendConfig.cron.weeklyDigest
stargazerSystem.actorSelection("user/cronActor/digestActor") ! SendGroup(EmailContact(trxnId), Right(weeklyConfig)) // Send weekly digest to EmailContactOnly of a transaction now
```