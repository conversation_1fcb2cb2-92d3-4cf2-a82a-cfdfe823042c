### Integrate keycloak with account service

References:

 - Keycloak documentation: http://www.keycloak.org/documentation.html
 - Keycloak OIDC management resource: https://keycloak.gitbooks.io/documentation/server_admin/topics/clients/client-oidc.html
 - Keycloak SSO explanation: https://keycloak.gitbooks.io/documentation/server_admin/topics/sso-protocols/oidc.html
 - Keycloak REST API: http://www.keycloak.org/docs-api/3.1/rest-api/index.html
 - Keycloak Admin CLI: https://keycloak.gitbooks.io/documentation/server_admin/topics/admin-cli.html
 - Keycloak identity providers: https://keycloak.gitbooks.io/documentation/server_admin/topics/identity-broker/social-login.html
 - Sync gateway OIDC: https://developer.couchbase.com/documentation/mobile/1.4/guides/authentication/openid/google/index.html

Others:

 - Keycloak login: http://keycloak.gondor.svc.kube:8080. Username: admin. Password: password.
