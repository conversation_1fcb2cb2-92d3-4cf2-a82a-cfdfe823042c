### How to disable sign up in keycloak

1. <PERSON>gin to keycloak admin. On local machine: http://keycloak.gondor.svc.kube-local.io/auth/admin/master/console/
Username: admin, Password: password

2. Go to Realm Settings - Login and turn off `User Registration`

3. Go to Authentication and select `Stargazer-first-broker`

4. Disable `Stargazer Create User If Unique`

5. Click `Action` on `Review Profile` row and select `Config`

6. Set `Update Profile on First Login` to off

### How to enable sign up

1. Login to keycloak admin. On local machine: http://keycloak.gondor.svc.kube-local.io/auth/admin/master/console/
Username: admin, Password: password

2. Go to Realm Settings - Login and turn on `User Registration`

3. Go to Authentication and select `Stargazer-first-broker`

4. Set `Stargazer Create User If Unique` to ALTERNATIVE

5. Click `Action` on `Review Profile` row and select `Config`

6. Set `Update Profile on First Login` to on
