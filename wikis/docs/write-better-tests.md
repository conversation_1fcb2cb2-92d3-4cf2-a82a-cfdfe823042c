# How to write better tests

_Disclaimer: This is for people who write tests._

- [Introduction](#introduction)
- [Actual meaningful content](#actual-meaningful-content)
- [More handy examples](#more-handy-examples)
    - [get option value](#get-option-value)
    - [check size](#check-size)
    - [forall](#forall)
    - [exists](#exists)
    - [contains](#contains)
    - [contain allOf](#contain-allof)
    - [count](#count)

### Introduction 

_(This part doesn't include any technical information. You can skip it at your own will.)_

Let's all be honest. Most of us don't like writing tests ¯\\\_(ツ)_/¯. If we can pick, we would rather spend our time building a brand new feature or doing some magic to speed our code up 5 times. 

It's all fun and games until your new fancy feature happens to break another piece of code that you're completely unaware of. You don't want yourself to be caught on the receiving end of that, do you? **Your tests are the last line of defense** you have against other threats (your future codes included 👀 ).

In a perfect life, tests would never fail and people would live happily ever after. Unfortunately, such life only exists in novels and nothing is certain but death, taxes and failing tests. It could be, after all, a blessing in disguise because you now have a chance to fix your bug (or someone else's) before it could potentially cause more serious damage on the production server.

Before we head into the main part, let me apologize about this post title. It should be called **How to write ~~better~~ maintainable tests** instead since we will mainly focus on writing more readable checks and assertions. A little marketting would do no harm, right?

### Actual meaningful content 

Cool, time to roll up your sleeves and start fixing the failed test.

```
false was not equal to true
```

Well, that's not really a helpful log at all. You look at that specific line:

```scala
signedRequest.recipients.forall(_.status == SignatureRecipientStatus.Signed) shouldBe true
```

Now you know that there is some recipient whose status is not Signed, but you have no other useful information without adding a few print commands and running the whole test suite again. This is really tedious considering that some huge test suites might take a few minutes to finish. 


If we rewrite the check as:

```scala
signedRequest.recipients.foreach(_.status shouldBe SignatureRecipientStatus.Signed)
```

You now get the actual status of that recipient:

```
Released was not equal to Signed
```

But we can even do better with:

```scala
all(signedRequest.recipients) should have(Symbol("status")(SignatureRecipientStatus.Signed))
```

Run the test again, this time you get a very detailed log on the error:


```
'all' inspection failed, because:
     at index 0, The status property had value Released, instead of its expected value Signed, on object SignatureRecipientModel(UserId(user-0000000000000000000000000000000000cc),,Map(),Released,Some(2021-03-21T08:05:03.651322+07:00[Asia/Ho_Chi_Minh]),Vector(SignatureRecipientItem(txnwo1mzmnz78zme.smdnekwl7.fdr000001.fdr57gqzn.fdrdgl2yw.fil510dz712m,txnwo1mzmnz78zme.smdnekwl7.srqjodzlz.sri92d4w5,Released,Some(2021-03-21T08:05:03.651322+07:00[Asia/Ho_Chi_Minh]),UnknownFieldSet(Map()))),Vector(UserId(user-0000000000000000000000000000000000ic)),UnknownFieldSet(Map())) 
```

This log gives you pretty much any information you need on the failed object. The only drawback is that using `Symbol("status")` is not type-safe, which would require you to do some extra work when renaming or deleting that field. I reckon it doesn't tip the scales in this case.

### More handy examples

Please check `SampleSpec.scala` for the full implementation.

```scala
private sealed trait Position
private case object Winger extends Position
private case object Forward extends Position

private case class Player(
  name: String,
  position: Position,
  goals: Int
)

private val Players = Seq(
  Player("Robert Lewandowski", Forward, 32),
  Player("Cristiano Ronaldo", Winger, 23),
  Player("Lionel Messi", Winger, 21),
  Player("Kylian Mbappe", Forward, 18),
  Player("Luis Suarez", Forward, 18),
  Player("Mohamed Salah", Winger, 18)
)
```

#### get option value

###### Bad ❌

```scala
Players.find(_.goals == 20).get shouldBe None
```

```
[info]     java.util.NoSuchElementException: None.get
[info]     at scala.None$.get(Option.scala:627)
[info]     at scala.None$.get(Option.scala:626)
[info]     at anduin.sample.SampleSpec.$anonfun$new$3(SampleSpec.scala:64)
[info]     at org.scalatest.OutcomeOf.outcomeOf(OutcomeOf.scala:85)
[info]     at org.scalatest.OutcomeOf.outcomeOf$(OutcomeOf.scala:83)
[info]     at org.scalatest.OutcomeOf$.outcomeOf(OutcomeOf.scala:104)
[info]     at org.scalatest.Transformer.apply(Transformer.scala:22)
[info]     at org.scalatest.Transformer.apply(Transformer.scala:20)
[info]     at org.scalatest.freespec.AnyFreeSpecLike$$anon$1.apply(AnyFreeSpecLike.scala:407)
[info]     at org.scalatest.TestSuite.withFixture(TestSuite.scala:196)
[info]     ...
```

###### Good ✅

```scala
Players.find(_.goals == 20).value shouldBe None
```

```
The Option on which value was invoked was not defined. (SampleSpec.scala:68)
```

#### check size

###### Bad ❌

```scala
Players.size shouldBe 5
```

```
6 was not equal to 5 (SampleSpec.scala:75)
```

###### Good ✅

```scala
Players should have size 5
```

```
List(Player(Robert Lewandowski,Forward,32), Player(Cristiano Ronaldo,Winger,23), Player(Lionel Messi,Winger,21), Player(Kylian Mbappe,Forward,18), Player(Luis Suarez,Forward,18), Player(Mohamed Salah,Winger,18)) had size 6 instead of expected size 5 (SampleSpec.scala:79)
```

#### forall

###### Bad ❌

```scala
Players.forall(_.position == Forward) shouldBe true
```

```
false was not equal to true (SampleSpec.scala:86)
```

###### Good ✅

```scala
all(Players) should have (Symbol("position")(Forward))
```

```
[info]     'all' inspection failed, because:
[info]       at index 1, The position property had value Winger, instead of its expected value Forward, on object Player(Cristiano Ronaldo,Winger,23) (SampleSpec.scala:90)
[info]     in List(Player(Robert Lewandowski,Forward,32), Player(Cristiano Ronaldo,Winger,23), Player(Lionel Messi,Winger,21), Player(Kylian Mbappe,Forward,18), Player(Luis Suarez,Forward,18), Player(Mohamed Salah,Winger,18)) (SampleSpec.scala:90)
```

#### exists

###### Bad 1 ❌

```scala
Players.exists(_.goals == 30) shouldBe true
```

```
false was not equal to true (SampleSpec.scala:97)
```

###### Good 1 ✅

```scala
atLeast(1, Players) should have (Symbol("goals")(30))
```

```
[info]     'atLeast(1)' inspection failed, because no element satisfied the assertion block:
[info]       at index 0, The goals property had value 32, instead of its expected value 30, on object Player(Robert Lewandowski,Forward,32) (SampleSpec.scala:101),
[info]       at index 1, The goals property had value 23, instead of its expected value 30, on object Player(Cristiano Ronaldo,Winger,23) (SampleSpec.scala:101),
[info]       at index 2, The goals property had value 21, instead of its expected value 30, on object Player(Lionel Messi,Winger,21) (SampleSpec.scala:101),
[info]       at index 3, The goals property had value 18, instead of its expected value 30, on object Player(Kylian Mbappe,Forward,18) (SampleSpec.scala:101),
[info]       at index 4, The goals property had value 18, instead of its expected value 30, on object Player(Luis Suarez,Forward,18) (SampleSpec.scala:101),
[info]       at index 5, The goals property had value 18, instead of its expected value 30, on object Player(Mohamed Salah,Winger,18) (SampleSpec.scala:101)
[info]     in List(Player(Robert Lewandowski,Forward,32), Player(Cristiano Ronaldo,Winger,23), Player(Lionel Messi,Winger,21), Player(Kylian Mbappe,Forward,18), Player(Luis Suarez,Forward,18), Player(Mohamed Salah,Winger,18)) (SampleSpec.scala:101)
```

###### Bad 2 ❌

```scala
Players.exists { player =>
  player.position == Winger && player.goals == 32
} shouldBe true
```

```
false was not equal to true (SampleSpec.scala:105)
```

###### Good 2 ✅


```scala
atLeast(1, Players) should have (Symbol("position")(Winger), Symbol("goals")(32))
```

```
[info]     'atLeast(1)' inspection failed, because no element satisfied the assertion block:
[info]       at index 0, The position property had value Forward, instead of its expected value Winger, on object Player(Robert Lewandowski,Forward,32) (SampleSpec.scala:111),
[info]       at index 1, The goals property had value 23, instead of its expected value 32, on object Player(Cristiano Ronaldo,Winger,23) (SampleSpec.scala:111),
[info]       at index 2, The goals property had value 21, instead of its expected value 32, on object Player(Lionel Messi,Winger,21) (SampleSpec.scala:111),
[info]       at index 3, The position property had value Forward, instead of its expected value Winger, on object Player(Kylian Mbappe,Forward,18) (SampleSpec.scala:111),
[info]       at index 4, The position property had value Forward, instead of its expected value Winger, on object Player(Luis Suarez,Forward,18) (SampleSpec.scala:111),
[info]       at index 5, The goals property had value 18, instead of its expected value 32, on object Player(Mohamed Salah,Winger,18) (SampleSpec.scala:111)
[info]     in List(Player(Robert Lewandowski,Forward,32), Player(Cristiano Ronaldo,Winger,23), Player(Lionel Messi,Winger,21), Player(Kylian Mbappe,Forward,18), Player(Luis Suarez,Forward,18), Player(Mohamed Salah,Winger,18)) (SampleSpec.scala:111)
```

#### contains

```scala
private val PlayerToQuery = Player("Romelu Lukaku", Forward, 19)
```

###### Bad ❌

```scala
Players.contains(PlayerToQuery) shouldBe true
```

```
false was not equal to true (SampleSpec.scala:118)
```

###### Good ✅

```scala
Players should contain(PlayerToQuery)
```

```
List(Player(Robert Lewandowski,Forward,32), Player(Cristiano Ronaldo,Winger,23), Player(Lionel Messi,Winger,21), Player(Kylian Mbappe,Forward,18), Player(Luis Suarez,Forward,18), Player(Mohamed Salah,Winger,18)) did not contain element Player(Romelu Lukaku,Forward,19) (SampleSpec.scala:122)
```

#### contain allOf

###### Bad ❌

```scala
Seq(21, 22, 23).forall { goals =>
  Players.exists(_.goals == goals)
} shouldBe true
```

```
false was not equal to true (SampleSpec.scala:129)
```

###### Good ✅

```scala
Players.map(_.goals) should contain allOf(21, 22, 23)
```

```
List(32, 23, 21, 18, 18, 18) did not contain all of (21, 22, 23) (SampleSpec.scala:135)
```

#### count

###### Bad ❌ 

```scala
Players.count(_.goals == 18) shouldBe 2
```

```
3 was not equal to 2 (SampleSpec.scala:143)
```

###### Good ✅ 

```scala
exactly(2, Players) should have (Symbol("goals")(18))
```

```
'exactly(2)' inspection failed, because 3 elements satisfied the assertion block at index 3, 4 and 5 in List(Player(Robert Lewandowski,Forward,32), Player(Cristiano Ronaldo,Winger,23), Player(Lionel Messi,Winger,21), Player(Kylian Mbappe,Forward,18), Player(Luis Suarez,Forward,18), Player(Mohamed Salah,Winger,18)) (SampleSpec.scala:147)
```