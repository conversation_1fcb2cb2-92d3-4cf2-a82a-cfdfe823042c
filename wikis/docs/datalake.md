## Anduin data lake

### Underlying database

The main database for Anduin data lake is [Dgraph](https://dgraph.io/docs/graphql/overview/)

We strictly only use `GraphQL` capability of `Dgraph`.

### Data lake schema

1. `Dgraph` accepts `GraphQL` schema. But it will generate more types to the core schema to allow simpler `GraphQL`
   query.
2. We also use `caliban-client` to automatically generate Scala types from `GraphQL` schema (must be the generated one)

So, the step to update data lake schema is:

1. Make sure that `Dgraph` is on at `http://dgraph-alpha.[YOUR NETWORK DNF SUFFIX]:8080/` (
   example http://dgraph-alpha.strider2.svc.kube:8080/admin).
2. Update the core schema at `modules/evendim/resources/anduin/evendim/datalakeSchema.graphql`
3. Run
   `./mill -j 0 __.gondorAppServer.runMain com.anduin.stargazer.apps.stargazer.EvendimAdminApp "http://dgraph-alpha.[YOUR NETWORK DNF SUFFIX]:8080" "[PATH]/modules/evendim/graphql/datalake.graphql"`.
   Note that you'll have to use absolute path.
4. Double check `modules/evendim/evendimModel/jvm/src/main/graphql/datalake.graphql` file content to make sure there are
   no redundant characters.
5. `compile` to verify everything works!
6. Coordinate with infra team to upgrade the production databases accordingly.
