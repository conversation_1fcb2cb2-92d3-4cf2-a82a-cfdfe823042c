# ZIO Usage Guidelines for Code Review

## **Core ZIO Patterns & Best Practices**

### **1. Effect Types & Type Aliases**
- **Use appropriate effect types**:
  - `Task[A]` for effects that can fail with `Throwable`
  - `UIO[A]` for effects that cannot fail
  - `URIO[R, A]` for effects requiring environment `R` that cannot fail
  - `RIO[R, A]` for effects requiring environment `R` that can fail with `Throwable`
  - `ZIO[R, E, A]` for fully typed effects

- **Prefer type aliases**: Use the codebase's custom type aliases like `Task`, `UIO`, etc. over raw `ZIO` types when appropriate.

### **2. Error Handling Patterns**

**✅ GOOD:**
```scala
// Use ZIO's error handling combinators
effect
  .catchAll(error => ZIO.succeed(defaultValue))
  .catchAllDefect(defect => ZIO.logError(s"Unexpected error: $defect") *> ZIO.succeed(defaultValue))

// Use ZIOUtils helpers for common patterns
ZIOUtils.failUnless(condition)(GeneralServiceException("Error message"))
ZIOUtils.fromOption(maybeValue, new NoSuchElementException())
```

**❌ BAD:**
```scala
// Don't use try-catch blocks in ZIO code
try {
  // some operation
} catch {
  case e: Exception => // handle
}

// Don't ignore errors without proper handling
effect.orElse(ZIO.succeed(defaultValue)) // Missing logging/handling
```

### **3. Resource Management**

**✅ GOOD:**
```scala
// Use ZIO.scoped for automatic resource cleanup
ZIO.scoped {
  for {
    resource <- ZIO.acquireRelease(acquire)(release)
    result <- useResource(resource)
  } yield result
}

// Use ZIOUtils.createTempFile for file resources
ZIOUtils.createTempFile("prefix", ".tmp", deleteOnExit = true)
```

**❌ BAD:**
```scala
// Don't manually manage resources without proper cleanup
val file = File.createTempFile("prefix", ".tmp")
// ... use file without ensuring cleanup
```

### **4. Service Pattern & Dependency Injection**

**✅ GOOD:**
```scala
// Define services as case classes with dependencies
final case class MyService(
  dependency1: Dependency1,
  dependency2: Dependency2
) {
  def doSomething: Task[Result] = ???
}

// Provide ZLayer in companion object
object MyService {
  val layer: ZLayer[Dependency1 & Dependency2, Nothing, MyService] =
    ZLayer.fromFunction(MyService.apply)
}

// Use ZIO.serviceWithZIO for accessing services
ZIO.serviceWithZIO[MyService](_.doSomething)
```

**❌ BAD:**
```scala
// Don't use global state or singletons
object MyService {
  var globalState: SomeState = _
  def doSomething: Task[Result] = ???
}

// Don't access services directly without ZIO environment
myService.doSomething // Should be in ZIO context
```

### **5. Parallelism & Concurrency**

**✅ GOOD:**
```scala
// Use ZIOUtils helpers for controlled parallelism
ZIOUtils.foreachParN(parallelism = 10)(items)(processItem)
ZIOUtils.collectAllParN(parallelism = 32)(effects)

// Use withParallelism for streams
ZStream.fromIterable(items)
  .mapZIOPar(processItem)
  .withParallelism(16)
```

**❌ BAD:**
```scala
// Don't use unlimited parallelism
ZIO.foreachPar(largeCollection)(expensiveOperation) // Can overwhelm system

// Don't block threads
Thread.sleep(1000) // Use ZIO.sleep instead
```

### **6. Testing Patterns**

**✅ GOOD:**
```scala
// Extend ZIOBaseInteg for integration tests
class MyServiceInteg extends ZIOBaseInteg {
  def spec = suite("MyService")(
    test("should do something") {
      for {
        result <- myService.doSomething
      } yield assertTrue(result.isValid)
    }
  ) @@ TestAspect.timeout(testTimeout)
}

// Use ZIO test assertions
assertTrue(condition)
assertCompletes
```

**❌ BAD:**
```scala
// Don't use blocking assertions in ZIO tests
assert(result == expected) // Use assertTrue instead

// Don't forget timeout aspects
test("long running test") { ... } // Missing @@ TestAspect.timeout
```

### **7. Logging & Telemetry**

**✅ GOOD:**
```scala
// Use ZIO logging
ZIO.logInfo("Operation completed")
ZIO.logErrorCause("Operation failed", cause)

// Use telemetry utils for tracing
ZIOTelemetryUtils.traceWithRootSpan("operation-name")(effect)
ZIOTelemetryUtils.injectMetrics("metric-name")(effect)
```

**❌ BAD:**
```scala
// Don't use println or external logging directly
println("Debug message") // Use ZIO.logInfo instead
logger.info("Message") // Use ZIO logging
```

### **8. Common Anti-Patterns to Avoid**

- **Unsafe operations**: Avoid `Unsafe.unsafely` except in ZIOUtils or app entry points
- **Blocking operations**: Don't use blocking I/O without `ZIO.attemptBlocking`
- **Exception throwing**: Use `ZIO.fail` instead of throwing exceptions
- **Future mixing**: Avoid mixing `Future` with ZIO; use `ZIO.fromFuture` for conversion
- **Resource leaks**: Always use proper resource management with `ZIO.scoped` or `acquireRelease`
- **Ignoring errors**: Don't use `.orDie` without good reason; handle errors explicitly

### **9. Performance Considerations**

- **Use appropriate executors**: Leverage custom executors from `ZIOExecutor` for specific workloads
- **Control parallelism**: Always specify parallelism limits for parallel operations
- **Stream processing**: Use `ZStream` for large data processing with backpressure
- **Timeout operations**: Use `ZIOUtils.timeout` or `.timeoutFail` for long-running operations

### **10. Module & Layer Composition**

**✅ GOOD:**
```scala
// Compose layers using >>> and ++
val appLayer = configLayer >>> serviceLayer >>> serverLayer

// Use ZLayer.scoped for resource management in layers
val layer = ZLayer.scoped {
  for {
    config <- ZIO.service[Config]
    resource <- buildResource(config)
  } yield MyService(resource)
}
```

**❌ BAD:**
```scala
// Don't create layers without proper dependency management
val badLayer = ZLayer.succeed(MyService()) // Missing dependencies

// Don't mix imperative and functional styles
val mixedLayer = ZLayer.fromZIO {
  val service = new MyService() // Imperative construction
  ZIO.succeed(service)
}
```

### **11. Codebase-Specific Utilities**

- **Use `ZIOUtils` helpers**: The codebase provides many utility functions like `foreachParN`, `gatherUnorderedIgnoreFailed`, `timeout`, etc.
- **Follow service module patterns**: Services should be defined in modules extending appropriate base modules (e.g., `GondorCoreServiceModule`)
- **Use proper test base classes**: Extend `ZIOBaseInteg` for integration tests with proper environment setup
- **Leverage telemetry**: Use `ZIOTelemetryUtils` for tracing and metrics in production code

This guide should help identify ZIO best practices, catch common mistakes, and ensure consistent patterns across the codebase.