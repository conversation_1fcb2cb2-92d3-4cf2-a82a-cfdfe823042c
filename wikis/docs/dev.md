## Stargazer local dev

1. (MANDATORY) Install homebrew https://brew.sh/
2. (MANDATORY) Install additional command-line tools: `./project/tools/local-deps/install-clis.sh`
3. Install [VirtualBox 6.1](https://www.virtualbox.org/wiki/Downloads). Add `* 0.0.0.0/0 ::/0` to `/etc/vbox/networks.conf`, create file if not exists. [Reference](https://www.virtualbox.org/manual/ch06.html#network_hostonly)

4. Get `anduin-kube`: `curl https://raw.githubusercontent.com/anduintransaction/anduin-kube/master/install.sh | sh`
5. Open VirtualBox, open `minikube` settings, go to System tab and set base memory to 7168 MB.
6. Run `anduin-kube start`. This should take about 20 - 50 minutes.
7. Copy `project/sample.local.conf` to `local/local.conf` if you haven't done so
8. Modify `local/local.conf` to customize your local configuration if needed. If `local.conf` is not at the right place, `<PERSON>kka` will complain when the server starts.
9. Start all external dependencies with `./project/tools/local-deps/start.sh`
10. Run`./sbt` to start sbt shell. See [build](build.md).
11. To interactively try out code, see [repl](repl.md).

## Setup local commit hook https://github.com/brigade/overcommit
1. Make sure you are in `stargazer` git root directory
2. Install [bundler](http://bundler.io/) `sudo gem install bundler` (on Mac OS, run the command without sudo)
3. Install gems `bundle install --gemfile=.overcommit_gems.rb --binstubs=tools/ruby/bin --path=tools/ruby/gems`
3a. If you are on `Ubuntu` then make sure you install `apt-get install ruby-dev` before
4. Install overcommit `tools/ruby/bin/overcommit -i`
5. Make sure overcommit is install properly by running `tools/ruby/bin/overcommit -r`. If you see this error: `Unable to load configuration from ...: No previously recorded signature for configuration file.`, then execute this command: `tools/ruby/bin/overcommit --sign`.
6. From now on, overcommit will check on everytime you make a local commit. The options for overcommit can be found in `.overcommit.yml`

## IntelliJ setup
1. Install [Scalafmt IntelliJ plugin](https://plugins.jetbrains.com/plugin/8236-scalafmt). We are using [Scalafmt](http://scalameta.org/scalafmt/) to automatically format the code base. So it is essential to install the plugin otherwise, your commit will be rejected by overcommit and CI.

## (Optional) Setup JCE
1. See https://github.com/anduintransaction/stargazer/blob/master/wikis/docs/jce.md

## Allow warnings in local build
By default, our local build will fail even on warnings (unused imports,...) but if you are in the middle of a big refactor and want to temporarily ignore these warnings then you can:
1. In `./sbt` console, type in `set allowWarning in Global := true`
2. After that, the build will not fail on warnings anymore

If you want to have this setting permanently then you can
1. Create if not already exist `local.sbt` file in your main repo directory
2. Add this line `allowWarning in Global := true`

Note that Jenkins builds will *ALWAYS* fail on warning so please do not abuse them.
