# Update ACL

In order to use the latest version of [ACL](https://anduin.design), set it in `AnduinDesign.scala`: 

```scala
package anduin.build

object AnduinDesign {
  lazy val version = "VERSION_GOES_HERE"
}
```

If that ACL version uses a new external library, you might need to run the following commands at the root directory:

```shell
$ ./project/tools/update-scalajs-bundle.sh
$ yarn install
```

These commands will update the `package.json` in projects, and `yarn.lock` files in the root folder. It ensures that all forked repositories will use the same versions of the front-end dependencies.

## Update CSS

1. Override the content of ACL CSS file to `src/main/assets/stylesheets/vendors/_acl.scss`
2. Clear the target of the `gondorWebResources` project:

```shell
$ rm -rf gondor/webResources/target
```

3. Rebuild the CSS inside SBT:

```shell
$ ./sbt
> devBuildWebResources
```
