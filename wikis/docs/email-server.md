# Setup mailgun and ngrok locally to receive emails.

## One-time setup
1. Register a personal free account at https://mailgun.com/
2. Run ```$ brew install ngrok```
3. Open `local.conf` and search for `mailgun` and replace the config with:
```
    mailgun {
      scheme = "https"
      hostname = "api.mailgun.net"
      port     = 443
      timeout  = 1 minute
      apiKey = [YOUR_API_KEY_HERE]
      domain = [YOUR_DOMAIN_HERE]
      retryTimes = 10
      retryInterval = 10 seconds
    }
```
4. Go to https://app.mailgun.com/app/domains. Go to the only domain automatically created there.
The domain will looks like: `sandbox6a3328e867024220aefe84e5fbf515fd.mailgun.org`
- In `local.conf`, replace [YOUR_DOMAIN_HERE] by the domain you see.
- Look for "API Key" in the domain site. Replace [YOUR_API_KEY_HERE] by that value.

## At the beginning of each session
1. Run ```$ project/tools/ngrok.sh``` in a separate console. This will open an ngrok console UI in the terminal.
2. Run ```$ project/tools/mailgun.sh```. This setup the mailgun routes and webhooks.
