## How to set up Zapier integration

### 1. How <PERSON><PERSON><PERSON> works

 - A user (for example a fund manager) wants to set up an integration between our app and their own
   services, for example he needs to update a row in his Google Spreadsheet whenever an investor changes
   status.
 - To do this, he needs to register an account with <PERSON>apier, connect Zapier account with <PERSON><PERSON> account
   (via Oauth 2), then create a **Zap**. 
 - A **Zap** is a workflow that connects a **trigger** with multiple **actions**.
 - A **trigger** receives data from web hook or polling data. Data can be any JSON.
 - **Actions** receives data from trigger or previous action step, and do something likes
sending email or update a row in Google Spreadsheet.
   
### 2. Setup to work with Zapier

#### 2.1. Required tools

 - A Zapier account
 - Ngrok

#### 2.2. Setup authentication

Zapier needs Oauth 2 to connect Zapier account with Anduin account.

 - First, create a new Zapier account, then go to https://developer.zapier.com/ and click on
**Start a Zapier Integration**
 - Input the integration's name, description, ... to the form.
 - Click **Authentication**, then choose **Oauth v2**.
 - Expand **Step 2**, then copy the Oauth Redirect URL.
 - Go to **Anduin Admin Portal**, **Oauth 2 Integrations**, then click **Create new integration**
 - Input a name, and paste the Redirect URL in the previous step.
 - After creating a new Oauth 2 Integration, click on **Credential** to copy **Client ID** and **Client Secret**
 - Go back to Zapier, paste **Client ID** and **Client Secret** to **Step 3**
 - Run **Gondor Server**, and **ngrok** (`ngrok http 8080`), copy **ngrok domain**
 - Expand **Step 4**, input following data:
   - **Authorization URL**: Choose **GET**, and `https://[your-domain].ngrok.io/oauth2/authorize`.
   - **Access Token Request**: Choose **POST**, and `https://[your-domain].ngrok.io/oauth2/token`.
   - **Refresh Token Request**: Choose **POST**, and `https://[your-domain].ngrok.io/oauth2/token`.
   Also expand **Show options** and add:
     - `client_id`: `{{process.env.CLIENT_ID}}`
     - `client_secret`: `{{process.env.CLIENT_SECRET}}`
   - **Automatically Refresh Token**: Do not check in local dev.
   - **Test**: Click **Switch to code mode**, then paste following code, replace `[your-domain]` with your **ngrok domain**:
```
const options = {
  url: 'https://[your-domain].ngrok.io/api/v2/zapier-integration/test',
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${bundle.authData.access_token}`
  },
  params: {

  },
  json: {

  }
}

return z.request(options)
  .then((response) => {
    response.throwForStatus();
    const results = response.json;

    // You can do any parsing you need for results here before returning them

    return results;
  });
```  
 - Click **Save and Continue**
 - Expand **Step 5**, click **Sign in** to test Oauth 2 flow (login with hary williams or whatever)

#### 2.3. Setup trigger

 - Go to **Triggers** in **Zapier**, and click **Add trigger**
 - Input trigger name, description, ... then **Save**
 - Click **Input designer**, and optionally add **Trigger parameters**. Users who use this trigger will
need to provide data for the trigger to work. Also, **input key** can be used as variable in the next steps.
For more details, go to https://platform.zapier.com/docs/input-designer. In this example, we create an
input parameter with key `fundsub_id`, type `String` and check `Required`
 - For the next part, you must decide on these things:
    - A **type name** for the Trigger. For example `fundsub_investor`
    - A **group name**, which will normally be set based on the **Input parameters** in the previous step.
 - Click on **API configuration**
 - Check on **Rest hook**, and input:
   - **Subscribe**: Switch to code mode, then input (replace placeholders with real data):
```
const options = {
  url: 'https://[your-domain].ngrok.io/api/v2/zapier-integration/resthook-subscribe',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Authorization': `Bearer ${bundle.authData.access_token}`
  },
  params: {

  },
  json: {
    'hookUrl': bundle.targetUrl,
    'zapType': '[your-trigger-type]',
    'group': bundle.inputData.[input-key]
  }
}

return z.request(options)
  .then((response) => {
    response.throwForStatus();
    const results = response.json;

    // You can do any parsing you need for results here before returning them

    return results;
  });
```
   - **Unsubscribe**:
```
const options = {
  url: 'https://[your-domain].ngrok.io/api/v2/zapier-integration/resthook-unsubscribe',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Authorization': `Bearer ${bundle.authData.access_token}`
  },
  params: {

  },
  json: {
    'id': bundle.subscribeData.id
  }
}

return z.request(options)
  .then((response) => {
    response.throwForStatus();
    const results = response.json;

    // You can do any parsing you need for results here before returning them

    return results;
  });
```
   - **Perform list** and **Perform**: We will return to this later.
   - Click **Save and Continue**

### 3. Define our app logic

Based on actual use cases, we can have different implementations for how to push events to Zapier, but
a typical flow looks like this:

 - Define data schema for push event, for example:

```scala
@JsonCodec
final case class ZapierInvestorEvent(
  id: String,
  firstName: String,
  lastName: String,
  email: String,
  status: String,
  commitmentAmount: Double,
  commitmentUnit: String
)
```

 - Add new **ZapType** in **ZapierService.scala**:

```scala
...
case object FundsubInvestor extends ZapType("fundsub_investor")
...
```

 - When a user turns on a Zap using our trigger, Zapier will send request to our server using what we defined in **Subscribe** section.
If you following this walkthrough, a new Zap model will be created in the database (FoundationDB) with following data:
   
```protobuf
message Zap {
  string zapId = 1 [(scalapb.field).type = "ZapId"];
  string hookUrl = 2;
  string ownerId = 3 [(scalapb.field).type = "UserId"];
  string zapType = 4;
  string group = 5;
  InstantMessage createdAt = 6 [(scalapb.field).type = "Instant"];
}
```

 - This model is stored using record layer with index on ownerId, zapType and group field.
 - When the user turn off the Zap, Zapier will call our **Unsubscribe** API to delete the Zap in the database.
 - Now, based on the use case, we must decide when to send the event to Zapier. To do that, use following API in `ZapierService`

```scala
def sendEventsToZaps[E: Encoder](
    events: Seq[E],
    zapType: ZapierService.ZapType,
    group: String,
    filter: Zap => Task[Boolean] = _ => Task.now(true)
  ): Task[Unit]
```

 - Define an API endpoint for getting **Perform list**, or **sample events**. This API must return a JSON array. For example
we can use following `Endpoint`:
   
```scala
trait FundsubZapierEndpoints extends AuthenticationEndpoints {

  protected[this] val FundSubZapierPath: Path[Unit] = basePath / "fundsub-zapier"

  val newInvestorTriggerPerformList: RawEndpoint[NewInvestorTriggerPerformListParams, Seq[ZapierInvestorEvent]] = {
    rawEndpoint[NewInvestorTriggerPerformListParams, Seq[ZapierInvestorEvent]](
      FundSubZapierPath / "new-investors-perform-list"
    )
  }

}
```

## 4. Finalize the trigger

Go back to the trigger **API configuration**, and input:

 - **Perform list**: this depends on how we define **Perform list** API in previous section, an example
can look like this (remember to replace the placeholders):
   
```
const options = {
  url: 'https://[your-domain].ngrok.io/api/v2/fundsub-zapier/new-investors-perform-list',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Authorization': `Bearer ${bundle.authData.access_token}`
  },
  params: {

  },
  json: {
    'fundSubId': bundle.inputData.[your-input-key]
  }
}

return z.request(options)
  .then((response) => {
    response.throwForStatus();
    const results = response.json;

    // You can do any parsing you need for results here before returning them

    return results;
  });
```

 - **Perform** function: if you use `ZapierService.sendEventsToZaps` API, input following:

```
return bundle.cleanedRequest.data;
```

 - Click **Save and Continue**
 - Click on **Step 3: Define your output**
 - Enter a sample JSON of our event, then click **Generate output field definitions**.
 - Change output field type if necessary.
 - Click **Save output & Finish**

## 5. Test and debug.

 - Create a Zap with your trigger and a simple Action (for example Gmail) to test.
 - Use **Monitoring** page in **Manage** section to debug API Request / Response.
 - Tip: you cannot use `console.log` to print, but you can `throw` a string to make it appear in **Monitoring** log.
