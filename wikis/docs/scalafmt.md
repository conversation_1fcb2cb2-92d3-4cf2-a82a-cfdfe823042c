# Scalafmt - code formatter for Scala
## What
Scalafmt is a code formatter for Scala. You can probably read more about it [here](https://olafurpg.github.io/scalafmt/).
## Why
It allows us to somewhat enforce Anduin coding style. We already use many other code style checking and static analysis tools such as scalastyle, linter and wartRemoval but scalafmt offers unique set of features that others can't do. There are a bit of overlap between scalastyle and scalafmt but I think both work great together.
## Getting started
1. Install IntelliJ plugin for scalafmt
https://olafurpg.github.io/scalafmt/#IntelliJ
From there, you can format individual file using its hotkey. (Usually Code/reformat with scalafmt)
2. You can also reformat files in directories
```sh
./project/tools/scalafmt_auto.sh --version 0.4.7 --dir tools/scalafmt -c .scalafmt.conf -i -f <directory-1>,<directory-2>,...
```
3. To check if all files are complied with scalafmt
```sh
tools/ruby/bin/overcommit -r
```
Normally, every time you commit a change, the check will be run. The check will also be run in Anduin Jenkins for every PR.
NOTE: there is a limitation on scalafmt that it can only display 1 violation at a time. We will probably work with scalafmt author to fix that later.

## FAQS
### To update after the big PR [#3734](https://github.com/anduintransaction/stargazer/pull/3734)
1. First
```sh
git fetch upstream
OVERCOMMIT_DISABLE=1 git rebase upstream/master
./tools/ruby/bin/overcommit --sign
./tools/ruby/bin/overcommit --sign pre-commit
```
2. From now on, commit code as normal, all your new changes will be checked against scalafmt

### To update your outstanding commits/PRs after the big PR [#3734](https://github.com/anduintransaction/stargazer/pull/3734)
1. Make sure you can cherry pick all your local commits
2. Reset your local `HEAD` to the previous commit that are in the main branch of `upstream/master`
3. Follow previous steps to update your branch to `upstream/master`
4. Cherry pick your commits to the current branch and then use
```sh
./project/tools/scalafmt_auto.sh --version 0.4.7 --dir tools/scalafmt -c .scalafmt.conf -i -f <directory-1>,<directory-2>,...
```
to reformat them. You can do that per commit to keep the commit history or you can squash all your commits together and only have to do it once.
