## Internal implementation for SES integration

### Sending email

Sending email is straightforward: we build a `MimeMessage` then call `sendRawEmail`.

There are some tricky parts:

- `OutGoingVariables` is implemented with SES Message Tagging. However tag value must be alphanumeric, and shorter
than 256 characters. Therefore variable value is encoded using `String2Hex`. Also variable values should be shorter
than 128 characters.

- Amazon replaces `MessageID` with their own, so we must maintain a map from their message ID to ours. This map is
stored in couchbase.

- Email is sent with a `configuration set`, which will configure where the email events such as `click`, `open`, ...
are sent to. This is used in `Tracking bounce` feature

### Receiving email

When an email is sent to our registed domain (for example canary.anduintransact.email), we have configured a 'Receiving rule'
to push the raw email content into a S3 object. After successfully writing the email content, an event will be pushed to a
SNS Topic (Amazon Simple Notification Service), then to a SQS queue (Amazon Simple Queue Service). In gondor, we run a
akka stream to pull the event from the queue, then process the message.

### Tracking bounce

Similar to receiving email, we pull events from an SQS queue and process it.
