## How to import remote database to your local database

```bash
./project/tools/local-deps/import-remote-db.sh import canary
```

or

```bash
./project/tools/local-deps/import-remote-db.sh import internal
```

Add this to `backendConfig` section of your `local.conf`:

```
customDomainConfig {
  isEnabled = false
}
```

In case something wrong happened:

```bash
./project/tools/local-deps/import-remote-db.sh cancel
```
