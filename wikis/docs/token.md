## Token and authentication system

There are 3 kinds of token, each support one particular type of page protection:

 - Restricted token: protect private page which requires a registered (non zombie) user to login.
 - No-login token: protect no-login page, usually after a redirection from a link in email.
 - Anonymous token: for a public page that requires a token from bot user to interact with the API. Example: safe analyzer.

Token sharing and life cycle:

 - At the same time and for each browser tab, only one token is selected based on the current "context", which
is normally the current page
 - Restricted token is shared between browser tab, while no-login and anonymous token are not.
 - Restricted token lives until the user forcefully chooses to logout.
 - No-login token lives until user **closes** the tab.
 - Anonymous token lives until user **refresh** the tab.
 - So, user can do these things at the same time in different tabs:
   + Use private apps such as fundsub and dataroom
   + Use a no-login view such as signature
   + Use another no-login view as different user
   + Use public offering such as safe analyzer

Token refreshing and expiration:

 - Engineer can choose to run a periodic check and refresh token.
 It is recommended for restricted token and no-login token.
 - Token by default expires after 1 day if is not refreshed. For logged in user (using restricted token),
 the Heimdall app (login system) will automatically create a new token if this user still has valid cookie
 (which expires after 1 month).
 - For no-login page, user is required to click on the link in the email to re-visit the page.   

Relation to sync gateway token

 - Sync gateway token is refactored out from authentication token.
 - To use sync gateway, engineer must call an API to trade the authentication token for a sync gateway token.
 - This is already done automatically for private apps (fundsub, dataroom, ...)
 
Relation to GraphQL API

 - Because sync gateway token has been refactored out, GraphQL backend should no longer use sync gateway
public endpoint to query. All queries to sync gateway now should be done through admin API.
