## General Notes
- When a pull request is first created, the link to the associated CI job will be posted as a comment.
![CI Job Creation Message](_images/ci-job-creation-message.png)
  + Clicking on this link shows an overview of the PR on CI. ![CI Job](_images/ci-job.png)
  + FIXME: If you want to enable integration test right after creating a PR with `run integ test please`, wait for the link to appear first (Jenkins concurrency bug).
- Pushing new commits to a pull request builds incrementally.
  + First build of each pull request would still take a long time.
  + This means that making frequent updates to a pull request (e.g. WIP) is encouraged.
- Integration tests are not run until `ci/integ-test-enabled` label is added.
  + This label is automatically added by `run integ test please`, `lgtm`, `lgtqa` comments. ![CI Github Labels](_images/ci-github-labels.png)
- Updates to `master` and `release` trigger `canary` and `release` pipelines. The latter will also publish docker images.
  + https://ci.anduin.co/job/stargazer/job/branches/job/master/
  + https://ci.anduin.co/job/stargazer/job/branches/job/release/

### Misc details ###
- Builds run separately before tests.
- Unit tests and integration tests run in parallel.
- Pull requests get their own build environments.

## Auto-merging
- Pull requests are automatically merged when
  + All checks passed: `ci/style-check`, `ci/build`, `ci/unit-test`, `ci/integ-test`.
  + At least 1 QA approved with a `lgtqa` comment.
  + At least 1 reviewer approved.
- Approval can be skipped if you think that the pull request is simple enough, by a `lgtm` comment.
  + This will be recorded in the merge commit message.

## Re-triggering
- If the CI encounters intermittent issues, you can retrigger it by either
  + Add a`retest this please` comment. Note that this re-runs the whole PR pipeline, not just tests, so it can be used to rebuild upon a temporary failure.
  + Click the CI link, then click `Build Now`.
- Green phases will be skipped (i.e. it will not rebuild or rerun unit test if only integ test failed).
- Multiple triggers will be put in a wait queue. When executed, they will look at the latest commit.
- Incremental builds sometimes fail after a rebase (e.g. due to scalajs linking errors). A clean build can be triggered by a `build clean please` comment.

## WIP and Skipping
- `[WIP]` in the title prevents auto-merging.
- CI can be skipped with `ci/skipped` label.

## Docs-only Changes
- If a pull request changes only files under [wikis/docs](.), it will be automatically merged upon `lgtm` or approved review.
- Build-test-verify process is entirely bypassed.

## Github Notifications
- Successful/failed runs of `build`/`unit-test`/`integ-test` will and update associated commit statuses and post comments on the pull requests with details.
  + The comment includes an excerpt of the error log. ![CI Github Error Comment](_images/ci-github-error-comment.png) ![CI Log Excerpt](_images/ci-log-excerpt.png)
  + Clicking on the link show the full error log. ![CI Error Log](_images/ci-error-log.png)
- `build`/`unit-test`/`integ-test` on `master`/`release` will comment directly on the commits being built.
  + https://github.com/anduintransaction/stargazer/commits/master
