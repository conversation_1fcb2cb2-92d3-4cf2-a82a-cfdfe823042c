# Implement Models in Protocol Buffers

### What are Protocol Buffers?

1. Protocol Buffers (`protobuf`) are mechanisms for serializing structured data (like `XML`, `Json`, ...) but smaller, faster, and simpler [(from google)](https://developers.google.com/protocol-buffers/docs/overview#what-are-protocol-buffers).

2. Address problems of data schema evolutions in data formats like `Json` where old data is no longer in valid format. While in `protobuf`, once data is created, it is valid forever!

3. Can be used to generate structured data natively in many language like `C++`, `C#`, `Java`, `Go`, `Python`, ... Especially it supports `scala`, through [Protocol buffer compiler for Scala](https://github.com/trueaccord/ScalaPB).


### How can we adapt Protobuf to our codebase?

 * #### Old format:
     * We store data (`case class`) in `Json` format, which faces problems described as above.

 * #### New format:

     * ##### Composed of:
         * Our old Scala's `case classes`, containing only business logic.
         * Protocol Buffer's `messages`, which only contain data logic: serializing / deserializing, schema evolving, are used to compile to Protobuf `case classes` that we use in Scala.
         * And a bridge: `Isomorphism` (`Iso` for short) as typeclass that provides the isomorphic conversions between these two worlds.

     * ##### Structure:

          * Protobuf `messages` are stored in `model` project (in `stargazer/model/src/main/protobuf`).

          * When Protobuf `messages` are compiled it will produce Scala sources in project `stargazerModelJvm` and `stargazerModelJs`, located in

            ```
            stargazer/model/.jvm/target/scala-2.11/src_managed/main/compiled_protobuf
            ```

          * The order of project dependencies are:

            ```
            model -> core -> Js/Jvm
            ```

          * `model` project could be compiled independently by

            ```
            ./sbt stargazerModelJvm/compile
            ```

     * ##### Examples:
          * Scala `case class`:
            * Scala code:

              ```scala
              case class Foo(a: Int, b: String)
              ```

            * Protobuf message:

              ```protobuf
              // It is convention that we get Protobuf message name by appending
              // `Message` to Scala case class name
              message FooMessage {
                int32 a = 1; // Number used for schema evolutions
                string b = 2;
                // Note: `message` could contain other `message`s as its fields.
              }
              ```

            * Isomorphism:

              ```scala
              // `productIso` is syntastic sugar for defining Iso instances that
              // automatically pick up Iso instances for corresponding fields (in order)
              // of the two case classes
              implicit val fooIso = productIso[Foo, FooMessage]
              ```

          * Scala `trait` and `sealed trait`:
            * Scala code:

              ```scala
              sealed trait Hi
              case class Foo(x: Int) extends Hi
              case class Bar(y: String) extends Hi
              ```

            * Protobuf message:

              ```protobuf
              message FooMessage {
                int32 x = 1;
              }

              message BarMessage {
                string y = 1;
              }

              message HiBox { // Convention: Adding `Box` to sealed trait name
                oneof subtype_of_hi {
                  FooMessage foo_type = 1;
                  BarMessage bar_type = 2;
                }
                // Note: `subtype_of_bar`, `foo_type`, `bar_type` are protobuf identifier.
                // When compiled to scala sources, they'll be automatically converted to
                // scala identifier (lowerCamelCase), that are `subtypeOfBar`, `fooType`, etc.
              }
              ```

            * Iso:

              ```scala
              implicit val fooIso = productIso[Foo, FooMessage]
              implicit val barIso = productIso[Bar, BarMessage]

              // `boxIso` is syntastic sugar for manually defining Iso for `trait`
              // (though it is still quite complicated due to the way protobuf
              // case class are generated)
              implicit val hiIso = boxIso[Hi, HiBox] {
              // `isomorphInto` converts a value to some type that there is
              // a Iso typeclass evidence available.
                case m: Foo => FooType(m.isomorphInto[FooMessage])
                case m: Bar => BarType(m.isomorphInto[BarMessage])
              } {
                case FooType(m) => m.isomorphInto[Foo]
                case BarType(m) => m.isomorphInto[Bar]
              }
              ```

          * Enum:
            * Scala code:

              ```scala
              sealed trait Abc
              object Abc {
                case object A extends Abc
                case object B extends Abc
                case object C extends Abc
              }
              ```

            * Protobuf:
              ```protobuf
              enum AbcMessage {
                AMessage a = 1;
                BMessage b = 2;
                CMessage c = 3;
              }
              ```

            * Iso:
              ```text
              Pattern matching same as trait.
              ```

### Conclusion

* If you have any question related to `protobuf`, please talk to @ngthanhtrung or @nhap96 or @ngbinh
