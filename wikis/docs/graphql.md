### How to add new graphql query

All graphql query must be added as a `.graphql` file in folder `modules/rohan/rohan/shared/src/main/graphql`. There should be existing files there.
By doing so, all queries will be validated during the compilation of `rohanJVM`.

Also, all of the queries will be generated as a `case object` that extends `anduin.rohan.operation.AnduinQuery` trait. 
`QueryComponent` should use these `case object` to access `operationName` and `query`.
