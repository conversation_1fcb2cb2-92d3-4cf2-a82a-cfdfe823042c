## SES (Amazon Simple Email Service)

For in-depth guide about SES integration with gondor, please read [SES Internal](ses-internal.md)

For normal user, follow these steps:

- Stop deps and start deps
- Add following configuration after `mailgun` section in `local.conf`:

```
    ses {
      accessKeyId = "********************"
      secretAccessKey = "fDzEvmADXjij+5wND3KqJ5BT+8EJyqDoU1LHt3JD"
      s3Bucket = "gondor-ses-email-dev"
      configurationSet = "ses-dev-01" // You can change the suffix from 01 -> 05
      sqsReceiveQueue = "ses-receive-dev-01"
      sqsEventQueue = "ses-event-dev-01"
    }

    email {
      domain = "dev.anduintransact.email"
    }
```

- Change this configuration in `fronendConfig`:

```
    mail = {
      domain = ${?stargazer.backendConfig.email.domain}
    }
```

Sending email should work out-of-the-box. For receiving email and tracking bounce:

- Open another sbt shell, then run `receiveEmail`
- On local dev, if there are multiple engineers run `receiveEmail` at the same time, only 
one of them will receive the event when they are all subscribe to the same event queue in AWS.
However you can change the suffix of the event queue in your `local.conf` to subscribe to
another queue. There are 5 suffixes from `01` to `05`
