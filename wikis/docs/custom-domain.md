# How to setup new custom domain

- Add your new domain to your host file (`/etc/hosts`)
- Go to Custom domain manager in Admin portal and create new custom domain
- Switch to Manage offering custom domains tab and set your domain as both your offering domain and primary domain
- Create a main index for your domain (for example: `MainIndex`, `FundSubMainIndex`)
- Add your main index to `OfferingRoutes`

