# 1. Stargazer mill build

## 1.1 Getting started

1. It’s recommended that you start with a clean stargazer repo, i.e. clone stargazer into a new folder, let’s call it `stargazer-mill`, and cd into it.
2. Now you will use `./mill` instead of `./sbt`. Mill version is specified in `.mill-version`. You can try `./mill show apps.gondor.gondorAppServer.packageVersion` to download mill for the first time and it will print the current deployment version.
3. Run `./mill __.compile` to compile all projects. `__` is a wildcard placeholder in mill, similar to `*` in regex. You can read more [here](https://mill-build.org/mill/scalalib/builtin-commands.html) about mill special wildcards and built-in commands.
4. **Import project into IntelliJ IDEA** by following the steps below. Mill supports BSP and generates the IntelliJ project. We recommend using `GenIdea` instead of Mill built-in’s **[Build Server Protocol (BSP)](https://build-server-protocol.github.io/)**, but if you get errors when using `GenIdea`, give importing BSP project a try.

   **Generate IntelliJ project**

    - If you tried importing BSP project, run `rm -rf .idea .bsp` to clean first and remove stargazer from IntelliJ **Recent projects** menu
    - Run `./mill mill.idea.GenIdea/idea` to generate IntelliJ project. It may take up to 15 minutes to complete.
    - Open stargazer folder in IntelliJ
    - **Note**: You have to rerun GenIdea command whenever there are changes in build definition, otherwise IntelliJ might index incorrectly.
5. **Note**: `Find Usages` in `shared/src` can only find **JVM** usages, ****for **JS** usages you have to find cloned Scala files in `jsSharedSources.dest`
6. **Fix IntelliJ**: Sometimes you will find IntelliJ incorrectly highlights code or even stops highlighting, here are some steps to resolve this issue:
    1. Try to rerun `GenIdea` first because build definition might be changed
    2. Press `Shift` twice and search for `Project structure`, make sure that IntelliJ is using your latest JDK and `Language level` should be your JDK version (not 8)
    3. **Build Project:** Press `Command + F9` on macOS or `Ctrl + F9` on Linux or Go to menu bar and click on `Build -> Build Project` to force IntelliJ compilation. You may see compilation errors from a downstream module which preventing upstream stream module from compiling/highlighting. For example: If there’s a compile error in `gondorCore.jvm` , code in `fundSub.jvm` won’t be highlighted because it depends on `gondorCore.jvm` compile output. This issue might be fixed in the future with [Best effort compilation](https://dotty.epfl.ch/docs/internals/best-effort-compilation.html).
    4. If above steps not work for you, try to [invalidate IntelliJ caches](https://www.jetbrains.com/help/idea/invalidate-caches.html) or [repair IDE](https://www.jetbrains.com/help/idea/repair-ide.html).

## 1.2 Useful commands

Unlike sbt, mill has no shell interface to input command. Instead, we will input commands into `./mill` and currently it doesn’t have auto-completion supports. You can try various shell completions from [here](https://mill-build.com/mill/Thirdparty_Plugins.html).

- `startDevServer`: Start a local GondorAppServer. Can be terminated with `Ctrl + C`
- `initTestData`: Initialize test data such as test users for local integration tests.
- `runMigration`: Run migration scripts
- `runHotfixMigration`: Run hotfix migration scripts
- `workspaceJsPackageInstall`: Run `yarn install` in workspace.
- `devBuildWebResources`: Should be run once before any `devBuild...Client` command. It will build and copy bootstrap JS, web workers, build info and static web resources into `webTarget` folder.
- Other app client build commands:
    - `devBuildDataExtractClient`
    - `devBuildSignatureClient`
    - `devBuildInvestorProfileClient`
    - `devBuildDataroomClient`
    - `devBuildFundDataClient`
    - `devBuildPantheonClient`
    - `devBuildItoolsClient`
    - `devBuildNaryaClient`
    - `devBuildFundsubClient`
    - `devBuildMayaClient`
    - `devBuildGondorClient`
- `__.$project.jvm.it.testOnly $TEST_CLASS`: Run a single integration test suit, replace `$TEST_CLASS` with test suit reference, for example

  `__.gondorCore.jvm.it.testOnly "*FileServiceInteg"`

- `gondor.gondor.jvm.test.testOnly $TEST_CLASS`: Run a single unit test suit
- `__.test.test`: Run all unit tests
- `__.checkStyle`: Check scalafmt and scalafix on all projects
- `__.reformat`: Run scalafmt on all projects
- `clean`: Clean all modules
- You can use `resolve` to learn all available commands. **Warning: it can be a lot!**

- Use the following to bypass linter checks during agent execution:
    ```bash
    ANDUIN_BUILD_ENV=agent ./mill ...
    ```
  This allows the AI agent to prioritize core logic implementation without incurring overhead from linter-related tool calls.

## 1.3 The output directory

You can find all cached results and generated sources in `out` folder, its structure can be found [here](https://mill-build.com/mill/Out_Dir.html)

## 1.4 Project folder structure

Cross platform project in mill is a little bit different to sbt. Sbt’s cross platform project’s folder structure is look like this:

```bash
fundsubModel            # Just an example
├── js/
│   └── src/
│       ├── main/
│       │   ├── scala
│       │   └── protobuf
│       └── test/
│           └── scala
├── jvm/
│   └── src/
│       ├── main/
│       │   ├── resources
│       │   ├── scala
│       │   ├── java
│       │   └── protobuf
│       └── test/
│           ├── resources
│           └── scala
└── shared/
    └── src/
        ├── main/
        │   ├── resources
        │   ├── scala
        │   └── protobuf
        └── test/
            ├── resources
            └── scala
```

Above sbt cross project can be translated into mill cross project as below

```bash
fundsubModel
├── jvm/
│   ├── resources       # Only one resources folder as only JVM can use it
│   ├── src             # Can contains both java and scala sources
│   ├── protobuf
│   └── test/
│       ├── resources
│       └── src
├── js/
│   ├── src
│   ├── protobuf
│   └── test/
│       └── src
└── shared/
    ├── src
    ├── protobuf
    └── test/
        └── src
```

## 1.5 Update versions

Go to `./build/versions.mill` to update `AnduinDesign` and `AnduinGaia` versions.

# 2. Mill development

This section is for everyone who wants to write build stuffs. It’s recommended that you read [Mill doc](https://mill-build.com/mill/Intro_to_Mill.html) first. **Quick start** and **Mill in Depth** sections are must read to have an overview of mill build and its core concept.

This section can be treated as a brief overview of [osiris](https://github.com/anduintransaction/osiris), which contains all Anduin’s mill plugins implementation.

## 2.1 Scala

- `AnduinScalaCommonModule`: Define a common trait for all Scala modules, includes defining `scalacOptions`, `scalafix`, `scalafmt`, Anduin internal repositories and common settings.
- `AnduinScalaModule`: a trait for **JVM** Scala module, contains `AnduinScalaTests` trait to define unit tests.
- `AnduinScalaJSModule`: a trait for **JS** Scala module, contains `AnduinScalaJSTests` trait to define js unit tests.
- `AnduinCrossPlatformModule`: module trait for cross platform project describe in #1.4, contains `JvmModule/JvmTests` and `JsModule/JsTests` traits.

## 2.2 ScalaPB

- `AnduinScalaPBModule`: compile protobuf files in `protobuf` folder, also scan mill `moduleDeps` for protobuf include paths.
- `AnduinPlatformScalaPBModule`: extends `AnduinScalaPBModule` then includes `protobuf-js` and `protobuf-jvm` for platform protobuf files.

## 2.3 Publish

- `AnduinRepositoriesModule`: Include Anduin internal maven/ivy repositories for downloading internal artifacts.
- `AnduinPublishModule`: Publish artifact to Anduin’s Artifactory using `__.publishArtifactory` command.

## 2.4 JavaScript

- `AnduinJSNpmModule`: Define a ScalaJS npm module. It uses `yarn` for managing JS dependencies via `jsPackageInstall` command and `esbuild` for bundling JS package via `fastBuildJS/fullBuildJS` commands. Inspired by ScalaJSBundler and mill-bundler.
- `ScalablyTypedImportSourcesModule`: Generate Scala facades from JS dependencies in `jsDeps` field via `stImportSources` command, mainly used in Anduin Design. [sbt-converter](https://github.com/ScalablyTyped/Converter/tree/master/sbt-converter/src)
- `ScalaTsiModule`: Generate TypeScript models from Scala case classes via `generateTypescript` command. [Sbt-plugin](https://github.com/scala-tsi/scala-tsi/tree/master/plugin/src)

## 2.5 GraphQL

- `AnduinGraphQLGenModule`: Scan `graphql` folder for `.graphql` and `.fragment` files and generate Scala query case classes.
- `CalibanSourceGenModule`: Scan `graphql` folder for `graphql` files and generate Caliban scala sources. This plugin is converted from [sbt-codegen](https://github.com/ghostdogpr/caliban/tree/series/2.x/codegen-sbt/src/main/scala/caliban/codegen).

## 2.6 Packager

- `JavaAppPackagingModule`: package Java app and its jar dependencies into a zip file, also include bash startup scripts. Converted from [sbt-native-packager](https://github.com/sbt/sbt-native-packager/tree/master/src/main/scala/com/typesafe/sbt)