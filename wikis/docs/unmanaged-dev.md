# Unmanaged Dev Environment

### Components

Local dev environment include these following setup requirement:

- A local running K8s cluster (either minikube, k3s or k8s by kubeadm)
- Networking config to accessing K8s service and K8s pod directly from host OS using pod/service CIDR
- A local running Coredns instance to resolve helper dnses
- Local DNS config pointed to Coredns instance

### Existing Tooling

In general, local dev environment can be managed and spinned up automatically by using either 1 of these following tools:

- https://github.com/anduintransaction/anduin-kube
- https://github.com/anduintransaction/slash
- https://github.com/anduintransaction/local-dev

A brief description of each tools:

- Anduin kube: using minikube with Virtualbox driver to create a local K8s cluster. Bundled together with several install scripts
- Slash: using kubeadm to create a local K8s cluster. Running natively on host OS. Bundled together with several install scripts. Required host OS to be debian based or redhat Fedora for best supported.
- Local-dev: using minikube with docker driver to create a local K8s cluster. Not bundled together with install scripts. You are required to install binary by yourself. Most fitted for arch based environment and least instrusive setup

### Dev Environment (The Hard Way)

In case none of the above tools fit your need. You can follow these manual steps to spin up a local env yourself. Or use this to create your spin up scripts yourself.

#### K8s cluster

You can use any existing local K8s cluster solution, as long as the version is aligned (`1.20.8` at the docs written time). The most easiest way could be using minikube

```
minikube start \
  --addons=dashboard \
  --kubernetes-version=$K8S_VERSION \
  --force-systemd=true \
  --cpus=8
```

#### Inject network routing

The goal is to access K8s pod and K8s service directly via pod/service cidr by inject routing to host network

```
ip route replace *********/12 via $MINIKUBE_IP        # service cidr
ip route replace **********/16 via $MINIKUBE_IP       # pod cidr
```

NOTE:

- You can skip this step if you K8s cluster is setup using Kubeadm or K3s (or any method that running K8s directly in host OS)
- Service CIDR default is `*********/12`, modified if you have any special setup
- Minikube does not respect pod CIDR but using docker CIDR config. Modify `**********/16` according to your setup

#### Spinning up CoreDNS instance

You can spinning up CoreDNS instance either by using docker or CoreDNS binary itself. CoreDNS instance is expected to resolve these following zones:

- `kube:53, kube-local.io:53`: Resolved to K8s services. For example: if you have a `httpbin` service in `foo` namespace. It should be reachable using `httpbin.foo.svc.cluster.local` within cluster and `httbin.foo.svc.kube` or `httpbin.foo.svc.kube-local.io` from your host network
- Any zone specified in `$stargazer/project/tools/local-deps/dns-zones`. For example: `gondor-local.io`, `anduin-local.io`

An example Corefile is as follow

```
kube:53, kube-local.io:53 {
  bind {$DNS_BIND_ADDRESS}
  rewrite type ANY A
  kubernetes {
    endpoint {$KUBE_API_ADDRESS}
    tls /data/minikube/profiles/minikube/client.crt /data/minikube/profiles/minikube/client.key /data/minikube/ca.crt
  }
  log
}

.:53 {
  bind {$DNS_BIND_ADDRESS}
  auto {
    directory /usr/local/share/coredns-zones
  }
  forward . {$DNS_UPSTREAM}
  log
}
```

#### Host OS Dns Config

After you have the CoreDNS instance up and running. You need to config you host OS to use that instance. There are several ways to do this, either by modify `/etc/resolv.conf` directly or using `NetworkManager`, `systemd-resolved`. The easiest way is probably modify resolv.conf file with this following content

```
nameserver <coredns_ip>
```
