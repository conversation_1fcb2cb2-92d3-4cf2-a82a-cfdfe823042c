# Email Templating Engine Documentation

## MJML

- Directories: 
  + MJML templates will be declared in `gondor/jvm/src/main/mjml/`
  + MJML templates after linking phase can be viewed in `gondor/jvm/target/mjml/linked/`
  + Final HTML before data injection can be viewed in `gondor/jvm/target/scala-2.12/classes/emails/`
  + To use template in folder `gondor/jvm/src/main/mjml/flow/CancelReadyToSign/`, the Scala code will use the name 
  `flow/CancelReadyToSign`.
- A template is defined when `output` field in `index.conf` is defined to be `true`.
- When `extends` field in `index.conf` is declared, the values separated by space will be the list of packages that the
current package inherit. This mean during the linking phase, logically, every file in the inherited package after linked
will be copied to the inheriting package.
- Priority in inheritance:
  + In the list of inherited package, the latter one will override the former. 
  + Files in current package will override all and has the highest priority.
- The root layout for each template that MJML use to start the compilation will be `index.mjml`. This file can come from
inherited packages. 

## Mustache

### Dictionary

#### Transaction
- `investorName`: Lead investor name - "Formation 8"
- `companyName`: Company name - "Anduin"
- `trxnType`: Transaction Type - "Note"
- `trxnName`: Full name of transaction - "Anduin [ Note ] Formation 8"
- `trxnNameLeft`, `trxnNameRight`: Left and right part of transaction name - "Anduin", "Formation 8"

#### User
- `receiverEmailAddress`: Receiver's email address - "Hary Williams <<EMAIL>>"
- `unsubscribeUrl`: Unsubscribe url - "http://anduintransact.com/..."
