# How to query Foundationdb data using fdbcli

## Connect to foundationdb

Make sure foundationdb is running (check your anduin-kube or slash)

Run:

```
echo fdb:fdb@`dig +search +short foundationdb.gondor.svc.kube`:4500 > /tmp/fdb.cluster && fdbcli -C /tmp/fdb.cluster
```

Make sure the output read `The database is available.`

## Using the shell

Type `help` to show list of commands. Common commands are: `get`, `getrange`, `getrangekeys`, `set`, `clear`

`set` and `clear` command requires `writemode` to be turned on. Execute `writemode on` to turn it on.

## Query data

Foundationdb is a key-value database, with keys and values are typically in binary format. Furthermore, we are using
`Tuple Layer` with Binary Encoding to store keys and values so it is tricky to issue correct key to `get`-based command.

Foundationdb key is mostly readable string, but with some unreadable bytes to separate element inside a Tuple. For example
a DoubleIdIndex key for Composite role may look like this:

`\x02roles\x00\x02txn1qjw90w3qld24.ttm1y8lxp.trl\x00\x02txn1qjw90w3qld24.smd34o08r.srqyp3lmd.srrcan\x00`

This key encodes the Tuple `txn1qjw90w3qld24.ttm1y8lxp.trl` and `txn1qjw90w3qld24.smd34o08r.srqyp3lmd.srrcan`

To show the value of this key, just execute:

`get "\x02roles\x00\x02txn1qjw90w3qld24.ttm1y8lxp.trl\x00\x02txn1qjw90w3qld24.smd34o08r.srqyp3lmd.srrcan\x00"`

Because in foundationdb keys are sorted, so we can get all keys with a prefix like this:

`getrangekeys "\x02roles\x00\x02txn1qjw90w3qld24.ttm1y8lxp.trl" "\x02roles\x00\x02txn1qjw90w3qld24.ttm1y8lxp.trl\xff" 100`
