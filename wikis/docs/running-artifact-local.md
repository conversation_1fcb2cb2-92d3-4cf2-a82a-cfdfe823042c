## How to run a custom version on gondor on your local machine without building it

### List artifact tag

 - `./project/tools/artifact.sh list pr|master|release`

### Find tag to run for each PR

 - Go to Github PR, and find something like `Artifact with tag beta-12.0-candidate-329-g2715b77 was published`

### Run artifact from a PR

 - `./project/tools/artifact.sh run pr beta-12.0-candidate-329-g2715b77`

### Run artifact from master or release

- `./project/tools/artifact.sh run master|release <tag>`
