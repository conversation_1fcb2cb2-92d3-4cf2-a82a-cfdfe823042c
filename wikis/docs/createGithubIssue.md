# Github issue -  the good way

### Title
1. Title must have a clear meaning of what the issue is all about
2. You're encouraging to include `ADD/UPDATE/REMOVE/FIX` as a prefix of a title

### Content
1. If there's many smaller breakdown within an issue, better to use [Github markdown syntax](https://guides.github.com/features/mastering-markdown/) to create a list `- [ ] Name of a breakdown`
2. Design/front-end issue should include a screenshot/gif for best describing. For screen capturing into gif, suggest using [LiceCap](http://www.cockos.com/licecap/) 
