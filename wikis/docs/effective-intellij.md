## Everything Is A Command

One of the most useful aspects of IntelliJ is that almost all of what you can do with it are globally available as commands ("actions" in IntelliJ's jargon), which can be **searched-by-name-then-executed**, or **assigned-to-keyboard-shortcut**. Together with its smooth autocomplete UI, this allows user to very efficiently carry out frequent and semi-frequent tasks.

A useful mindset to have is trying to indentify repetitive/inconvenient tasks, asking "Is there a name for it?", then "Do I use it often enough? If so, maybe I can give it a keyboard shortcut?"

Note: "Everything" really means "everything", even like scrolling up/down, deleting words, moving by words/paragraphs, moving a line up/down, commenting out code...

### Doing it once every minute: search-by-name-then-execute ###

![search-then-execute](_images/search-then-execute.jpg)

This shows an autocomplete UI to do a **fuzzy search** of other commands, then execute the selected command. The entry point to this rather useful UI is hidden in an obscure place, under the menu `Help > Find Actions...`. You should assign this to an easy-to-use keyboard shortcut (e.g. <kbd>⌘</kbd><kbd>enter</kbd>, <kbd>⌘</kbd><kbd>M</kbd>), since this is likely the best interface for carrying out *semi-frequent* tasks. All you need is remembering what you want to do and *fuzzily* spelling it out.

Examples:
- See that git cli sucks → wonder if IntelliJ has some git support → search for `git` → there it is
- It's tiresome to organize imports and delete unused ones → this is a code formatting problem → search for `format import` → it's called `Optimize Imports`
- `Refactor Delete`, `Refactor Rename`, `Format Code`
- `Paste from History...`
- `Implicit Parameters`, `Implicit Conversions`
- `Find in Path...`
- `Compare with Branch...`, `git Branches...`, `git Stashes Changes...`

### Doing it once every ten seconds: keyboard shortcuts ###

By default, many useful commands have keyboard shortcuts, for frequent usage. However, many don't, resulting in them being used much less frequently than they should be. In the `Keymap` section under `Preferences...`, you can assign convenient keyboard shortcuts for them.

Some people avoid customizing too much, for fear of having to spend too much time configuring everything from scratch should the computer breaks down. This is actually not a problem, since you can either:
- Periodically back up all the custom settings with the command `Export Setting`.
- Keep all, or part of the custom configuration under version control (a git repo). IntelliJ keeps its user-specific configuration under `~/Library/Preferences/<Idea-something>`.

This is a list of useful commands that warrant their own keyboard shortcuts:
- `Symbols...`: Search-then-go-to a method/variable. If there are too many symbols with the same name, you can narrow down by prefixing with `<package>.`.
![search-namespaced-symbol](_images/search-namespaced-symbol.jpg)
- `Class...`: Search-then-go-to a class/object/trait.
- `File...`: Search-then-go-to a file. This is useful when you want to open e.g. a protobuf file.
- `Search Everywhere`: Combination of all 3 above.
- `Goto Declaration`: Either go to the definition of something (class, object, variable, field, method), go to one of the places where it is used. Coupled with `Back`, this enables very fast back-and-forth navigation which is very useful when trying to understand the codebase.
- `Goto Supermethod`, `Goto Implementations`: Pretty self-explanatory.
- `Next Highlighted Error`, `Previous Highlighted Error`: Navigate through issues with code in the current file.
- `Optimize Imports`: Remove unused imports built up when you were trying to make the code compile.
- `Type Info`, `Quick Definition`, `Quick Documentation`: Show-in-popup type/definition/doc of the thing under caret.
