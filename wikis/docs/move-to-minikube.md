## Moving to minikube from docker-machine

1. (MANDATORY) Install homebrew https://brew.sh/
2. (MANDATORY) Install wget `brew install wget`
3. Run `curl https://raw.githubusercontent.com/anduintransaction/anduin-kube/master/install.sh | sh`
4. Start `minikube` with `anduin-kube start`. This command is similar to `docker-machine start dev`, so run it only when you start your Mac.
5. Start all external services such as couchbase, sync gateway, fakes3 with `./project/tools/start-deps.sh`, stop them with `./project/tools/stop-deps.sh`, nuke db with `./project/tools/nuke-db.sh`
6. External dependencies now include both `couchbase/sync-gateway` and `fakes3`
7. Config changes for `couchbase`

```scala
   syncGateway {
      admin {
        host = "sync-gateway.gondor.svc.kube-local.io"
        ssl = false
        port = 4985
        version = "0"
        timeout = 30 seconds
        retryTimes = 20
        retryInterval = 3 seconds
      }
      public {
        host = "sync-gateway.gondor.svc.kube-local.io"
        ssl = false
        port = 80
        version = "0"
        timeout = 30 seconds
        retryTimes = 20
        retryInterval = 3 seconds
      }
      internalPublic {
        host = "sync-gateway.gondor.svc.kube-local.io"
        ssl = false
        port = 4984
        version = "0"
        timeout = 30 seconds
        retryTimes = 20
        retryInterval = 3 seconds
      }
      threadPoolSize = 20
      clientRequestTimeout = 30 seconds
    }

    keycloak {
      admin {
        host = "keycloak.gondor.svc.kube-local.io"
        ssl = false
        port = 80
        user = "admin"
        password = "password"
      }
      public {
        host = "keycloak.gondor.svc.kube-local.io"
        ssl = false
        port = 80
      }
    }

    couchbase {
      host = "couchbase.gondor.svc.kube-local.io"
      port = 8091
      timeout = 30 seconds
    }
```

6. Config changes for `fakes3` (Optional)

```
aws {
  S3 {
    bucket = "anduin-assets-document-dev"
    batchDownloadBucket = "gondor-batch-download-dev"
    accessKeyId = "********************"
    secretAccessKey = "XaM0SwlBbAt8tU+DQbjEZl9YPferWMbDIRo4qSgp"
    region = "us-east-1"
    developmentEndpoint = "http://fakes3.gondor.svc.kube:4567"
    tmpFolder = "/tmp"
  }
}
```

## (Advanced) What changes?

1. Dependencies (couchbase, fakes3) now run on kubernetes.
2. Domain name (eg. couchbase.gondor) is used instead of raw IP. And no more `/etc/hosts` mapping.
3. It is possible to run 2 or more instances of `couchbase` on a single local machine.
4. In most cases, you do not need to run `./project/tools/start-deps` after restarting your Mac. Running `anduin-kube start` is enough.

## (Advanced) How to run 2 instances of couchbase
1. Run `./project/tools/start-deps.sh` to start one instance
2. Run `./project/tools/start-deps.sh gondor2` to start another
3. Change all domain names in `local/local.conf` from `*.gondor` to `*.gondor2`.

## (Advanced) Using docker
1. Minikube includes docker, so you can run docker command as usual. The only change is instead of using `eval $(docker-machine env dev)`, use `eval $(minikube docker-env)`

## (Advanced) Troubleshooting

When something wrong happened:

1. Update anduin-kube with `anduin-kube update`
2. Try `anduin-kube fix`
3. Try `anduin-kube force-stop && anduin-kube start`
4. Try `anduin-kube delete && anduin-kube start`
5. Call @keimoon
